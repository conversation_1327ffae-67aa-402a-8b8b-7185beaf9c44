# Document: https://golangci-lint.run/usage/linters/
#
version: "2"

linters:
  default: none

  enable:
    - bodyclose
    - dogsled
    - dupl
    - errcheck
    # - exportloopref # 过期。使用 copyloopvar
    - copyloopvar
    - exhaustive
    - funlen
    - goconst
    - gocritic
    - revive
    - gosec
    - govet
    - ineffassign
    - misspell
    - nolintlint
    - nakedret
    - prealloc
    - predeclared
    - revive
    - staticcheck
    - thelper
    - tparallel
    - unconvert
    - unparam
    - whitespace
    - wsl_v5
    - asciicheck
    # - depguard 用来控制可引入的包的，暂时用不上
    - durationcheck
    - forcetypeassert
    - godox
    - goheader
    - gomoddirectives
    - gomodguard
    - goprintffuncname
    - importas
    - makezero
    - noctx
    - promlinter
    #    - rowserrcheck 过期
    #    - sqlclosecheck 过期
    - tagliatelle
    - unused
    #    - wastedassign 过期
    - errname
    # - goerr113 Go 1.13才引入
    # - errorlint Go 1.13才引入
    # - testpackage 没法测试内部函数了
    # - wrapcheck 暂时没用,看上去好像很有道理的样子
    # - cyclop 不需要分析
    # - exhaustivestruct 不需要,有些默认的就是 0
    # - forbidigo 不能用 fmt 有点炕
    # - gochecknoglobals 全局变量是要用的
    # - gochecknoinits 全局初始化是要用的
    # - gocognit 不需要分析复杂度
    - gocyclo
    # - godot 不需要每句都加个句号
    # - golint 过期
    - mnd
    # - interfacer 过期
    - lll
    # - maligned 过期
    # - nestif 有时候无解的 太理想了
    # - nilerr 有些是要忽略的
    # - nlreturn 没用
    # - paralleltest 没用
    # - scopelint 过期

  settings:
    errcheck:
      check-type-assertions: true

    exhaustive:
      # indicates that switch statements are to be considered exhaustive if a
      # 'default' case is present, even if all enum members aren't listed in the
      # switch
      default-signifies-exhaustive: true

    funlen:
      # Yunxiang 临时为了 ComputeEditStaffAndTips 接口扩张一下.
      lines: 210
      statements: 90

    goconst:
      min-len: 2
      min-occurrences: 3

    gocritic:
      enabled-tags:
        - diagnostic
        - experimental
        - opinionated
        - performance

    govet:
      enable-all: true
      disable:
        - fieldalignment

    nolintlint:
      require-explanation: true
      require-specific: true

    wsl_v5:
    # See https://github.com/bombsimon/wsl/blob/master/doc/configuration.md for
    # documentation of available settings. These are the defaults for
    # `golangci-lint`.
    #      allow-assign-and-anything: false
    #      allow-assign-and-call: true
    #      allow-cuddle-declarations: false
    #      allow-separated-leading-comment: false
    #      allow-trailing-comment: false
    #      force-case-trailing-whitespace: 0
    #      force-err-cuddling: true
    #      force-short-decl-cuddling: false
    #      strict-append: true

    tagliatelle:
      case:
        # Uses the struct field name to check the name of the struct tag.
        use-field-name: true
        rules:
          json: goCamel
          yaml: goCamel

    gomoddirectives:
      # Allow local `replace` directives.
      replace-local: true
      # List of allowed `replace` directives.
      # Default: []
      replace-allow-list:
        - github.com/spf13/viper
        - github.com/stretchr/testify
        - golang.org/x/net
        - google.golang.org/grpc
        - github.com/smartystreets/assertions

    gosec:
      excludes:
        - G501

    godox:
      # report any comments starting with keywords, this is useful for TODO or FIXME comments that
      # might be left in the code accidentally and should be resolved before merging
      keywords: # default keywords are TODO, BUG, and FIXME, these can be overwritten by this setting
        - OPTIMIZE # marks code that should be optimized before merging
        - HACK # marks hack-arounds that should be removed before merging
        - FIXME

    revive:
      rules:
        - name: exported
          disabled: true

  exclusions:
    generated: disable
    rules:
      # not needed
      - path: protocol/genproto/*.go
        linters:
          - errcheck
      - path: /*_test.go
        linters:
          - errcheck
          - forcetypeassert
          - lll
          - tagliatelle
          - goconst
      - path: /*_example_test.go
        linters:
          - forbidigo
      - path: /*_test.go
        linters:
          - dupl
          - funlen
          - wsl
          - gosec
      - path: /*client.go
        linters:
          - tagliatelle
      - path: internal/wire/wire_gen.go
        linters:
          - lll
    paths:
      - internal/script # 忽略脚本的目录，大部分都是自动生成的
      - internal/mocks # mock 的文件
      - internal/wire/wire_gen.go # 忽略 wire 自动生成的文件

formatters:
  enable:
    - gci
    - gofmt
    - gofumpt
    - goimports
  settings:
    gci:
      sections:
        - standard
        - default
        - prefix(github.com/MoeGolibrary)
      custom-order: true

issues:
  fix: true # 自动修复

run:
  issues-exit-code: 1
