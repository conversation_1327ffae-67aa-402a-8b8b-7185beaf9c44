init_hook:
	git config core.hooksPath ./hooks

install:
	@echo "Installing dependencies..."
	go install github.com/vektra/mockery/v2@latest
	go install github.com/golangci/golangci-lint/cmd/golangci-lint@latest
	@echo "Dependencies installed successfully!"

init: init_hook install
	@echo "Init done!"

lint:
	# 简化 go mod 文件
	go mod tidy
	# 校验代码规范
	golangci-lint run --fix

lint_changed:
	# 简化 go mod 文件
	go mod tidy
	# 校验代码规范
	golangci-lint run --fix --new-from-rev=HEAD

mock:
	mockery

wire:
	@echo "Generating wire code..."
	go run -mod=mod github.com/google/wire/cmd/wire ./internal/wire
build:
	@echo "Building..."
	go build -o server ./internal
run :
	@echo "Running..."
	go run ./internal
test:
	@echo "Testing..."
	go test -v ./internal/...
.PHONY: init
