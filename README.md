# moego-svc-order-v2

Server for go svc server template

## Development

1. init project

    ```bash
    make init
    ```
2. project structure
   当前项目的目录结构：

   ```
   .
   ├── CODEOWNERS
   ├── Makefile
   ├── README.md
   ├── go.mod
   ├── go.sum
   ├── .gitignore
   ├── .golangci.yml
   ├── ci
   │   ├── Dockerfile
   │   ├── ci.yaml
   │   └── lint.sh
   ├── config
   │   ├── config.go
   │   └── config.yaml
   ├── hooks
   │   ├── commit-msg
   │   ├── pre-commit
   │   └── pre-push
   └── internal
       ├── main.go
       ├── model
       ├── controller
       ├── repo
       │   ├── xxx_client.go      // HTTP/gRPC client
       │   ├── xxx_client_test.go
       │   ├── yyy_repo.go        // DAO/Repo
       │   └── yyy_repo_test.go
       ├── service
       │   ├── service.go
       │   └── xxx_service.go
       ├── wire
       │   │── wire.go
       │   └── wire_gen.go
       └── utils
   ```

其中:
- `CODEOWNERS` 包含对应目录的 Code Review necessary reviewers
- `Makefile` 包含一些常用命令
- `README.md` 项目说明
- `go.mod` go mod 文件
- `.gitignore` gitignore 文件
- `.golangci.yml` golangci-lint 配置文件
- `ci` 目录下是ci的配置文件，包括Dockerfile、ci.yaml、lint.sh，每个服务按照自己需要修改其中的服务名
- `config` 目录下是配置文件的结构体定义，以及配置文件的读取
- `hooks` 存放 git hooks
- `internal` 是项目的主要代码目录，其中包括了:
    - `main.go` 服务启动的入口
    - `model` 存放服务内部的实体定义
    - `repo` 提供外部依赖接口定义及实现，包括 rpc、db、cache 等
    - `controller` GRPC 接口的实现，wire.go 用于依赖注入，该层会将对外 proto 转化为 model 并调用 service 层
    - `service` 存放服务的业务逻辑，该层以 entity 定义进行操作，会调用 repo 获取外部资源
    - `test` 使用单元测试实现的接口测试，用来保证 t2 环境 production 分支制品的接口准确性
    - `util` 常用的工具函数
    - `wire` wire 依赖注入
