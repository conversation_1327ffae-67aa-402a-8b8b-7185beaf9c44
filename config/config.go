package config

import (
	"crypto/tls"
	"sync"
	"time"

	growthbook "github.com/growthbook/growthbook-golang"
	"go.uber.org/zap"

	"github.com/MoeGolibrary/go-lib/conf"
	eventbus "github.com/MoeGolibrary/go-lib/event-bus"
	"github.com/MoeGolibrary/go-lib/redis"
	"github.com/MoeGolibrary/go-lib/zlog"
)

var (
	config Config
	once   sync.Once
)

type Config struct {
	OrderDSN   string            `yaml:"orderDsn"`
	PaymentDSN string            `yaml:"paymentDsn"`
	EventBus   EventBusConfig    `yaml:"eventBus"`
	Redis      RedisConfig       `yaml:"redis"`
	GrowthBook *GrowthBookConfig `yaml:"growthBook"`
}

type EventBusConfig struct {
	Brokers    []string             `yaml:"brokers"`
	Credential *eventbus.Credential `yaml:"credential"`
	Consumers  struct {
		Payment     eventBusConsumerConfig `yaml:"payment"`
		Appointment eventBusConsumerConfig `yaml:"appointment"`
		Fulfillment eventBusConsumerConfig `yaml:"fulfillment"`
		Order       eventBusConsumerConfig `yaml:"order"`
		OrderTips   eventBusConsumerConfig `yaml:"orderTips"`
	} `yaml:"consumers"`
}

type eventBusConsumerConfig struct {
	Topic   string `yaml:"topic"`
	GroupID string `yaml:"groupID"`
}

type RedisConfig struct {
	Addr               string `yaml:"addr"`
	Password           string `yaml:"password"`
	TLS                bool   `yaml:"tls"`
	InsecureSkipVerify bool   `yaml:"insecureSkipVerify"`
}

type GrowthBookConfig struct {
	Host      string        `yaml:"host"`
	ClientKey string        `yaml:"clientKey"`
	Interval  time.Duration `yaml:"interval"`
}

// PaymentConsumer 获取 Payment 的 EventBus consumer 配置.
func (ebc *EventBusConfig) PaymentConsumer() *eventbus.Config {
	return ebc.buildConfig(ebc.Consumers.Payment)
}

// AppointmentConsumer 获取 Appointment 的 EventBus consumer 配置.
func (ebc *EventBusConfig) AppointmentConsumer() *eventbus.Config {
	return ebc.buildConfig(ebc.Consumers.Appointment)
}

// FulfillmentConsumer 获取 Fulfillment 的 EventBus consumer 配置.
func (ebc *EventBusConfig) FulfillmentConsumer() *eventbus.Config {
	return ebc.buildConfig(ebc.Consumers.Fulfillment)
}

// OrderConsumer 获取 Order 的 EventBus consumer 配置.
func (ebc *EventBusConfig) OrderConsumer() *eventbus.Config {
	return ebc.buildConfig(ebc.Consumers.Order)
}

// OrderTipsConsumer of tips 获取 Order 的 EventBus consumer 配置.
func (ebc *EventBusConfig) OrderTipsConsumer() *eventbus.Config {
	return ebc.buildConfig(ebc.Consumers.OrderTips)
}

func (ebc *EventBusConfig) buildConfig(ecc eventBusConsumerConfig) *eventbus.Config {
	consumerConf := &eventbus.Config{
		Brokers:    ebc.Brokers,
		Credential: ebc.Credential,
	}

	consumerConf.Consumer.Topics = []string{ecc.Topic}
	consumerConf.Consumer.GroupID = ecc.GroupID

	return consumerConf
}

func Init() {
	once.Do(
		func() {
			if err := conf.GetDefaultLoader().InitializeConfig(&config); err != nil {
				zlog.Default().Fatal("read config file failed", zap.Error(err))
			}
		},
	)
}

func OrderDSN() string { return config.OrderDSN }

func PaymentDSN() string { return config.PaymentDSN }

func EventBus() *EventBusConfig { return &config.EventBus }

func Redis() *redis.Options {
	redisConf := &redis.Options{
		Addr:     config.Redis.Addr,
		Password: config.Redis.Password,
		PoolFIFO: true,
	}

	if config.Redis.TLS {
		redisConf.TLSConfig = &tls.Config{
			MinVersion:         tls.VersionTLS12,
			InsecureSkipVerify: config.Redis.InsecureSkipVerify, //nolint:gosec // skip verify for local office network
		}
	}

	return redisConf
}

func GrowthBook() []growthbook.ClientOption {
	return []growthbook.ClientOption{
		growthbook.WithApiHost(config.GrowthBook.Host),
		growthbook.WithClientKey(config.GrowthBook.ClientKey),
		growthbook.WithPollDataSource(config.GrowthBook.Interval),
	}
}
