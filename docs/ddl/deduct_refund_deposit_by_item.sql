ALTER TABLE public."order" ADD COLUMN deposit_to_tips_amount NUMERIC(20, 2) DEFAULT 0.00 NOT NULL;
COMMENT ON COLUMN "order".deposit_to_tips_amount IS 'Tips amount deducted from the deposit';

ALTER TABLE public.refund_order
    ADD COLUMN refund_deposit_to_tips_amount numeric(20, 2) DEFAULT 0 NOT NULL;
COMMENT ON COLUMN refund_order.refund_deposit_to_tips_amount IS 'Refunded tips amount which is deducted from the deposit';

ALTER TABLE public.order_line_item
    ADD COLUMN refunded_deposit_amount numeric(20, 2) DEFAULT 0 NOT NULL,
    ADD COLUMN deposit_amount NUMERIC(20, 2) DEFAULT 0.00 NOT NULL;
COMMENT ON COLUMN order_line_item.refunded_deposit_amount IS 'Refunded deposit amount';
COMMENT ON COLUMN order_line_item.deposit_amount IS 'Deposit amount deducted to this item';

ALTER TABLE public.refund_order_item
    ADD COLUMN refund_deposit_amount numeric(20, 2) DEFAULT 0 NOT NULL;
COMMENT ON COLUMN refund_order_item.refund_deposit_amount IS 'Refunded deposit amount which is deducted to this item';
