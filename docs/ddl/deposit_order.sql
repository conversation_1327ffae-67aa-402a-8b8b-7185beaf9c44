ALTER TABLE "order" ADD COLUMN deposit_amount NUMERIC(20, 2) DEFAULT 0.00 NOT NULL;
COMMENT ON COLUMN "order".deposit_amount IS 'Deposit amount deducted in this order';

ALTER TABLE refund_order ADD COLUMN refund_deposit_amount NUMERIC(20, 2) DEFAULT 0.00 NOT NULL;
COMMENT ON COLUMN refund_order.refund_deposit_amount IS 'Deposit amount that deducted in the order and should be refunded';

CREATE TABLE IF NOT EXISTS deposit_change_log
(
    id                  BIGINT GENERATED ALWAYS AS IDENTITY PRIMARY KEY,
    deposit_order_id    BIGINT          DEFAULT 0       NOT NULL,
    change_type         TEXT            DEFAULT ''      NOT NULL,
    reason              TEXT            DEFAULT ''      NOT NULL,
    dest_order_id       BIGINT          DEFAULT 0       NOT NULL,
    changed_amount      NUMERIC(20, 2)  DEFAULT 0.00    NOT NULL,
    balance             NUMERIC(20, 2)  DEFAULT 0.00    NOT NULL,
    currency_code       TEXT            DEFAULT ''      NOT NULL,
    previous_log_id     BIGINT          DEFAULT 0       NOT NULL,
    company_id          BIGINT          DEFAULT 0       NOT NULL,
    business_id         BIGINT          DEFAULT 0       NOT NULL,
    customer_id         BIGINT          DEFAULT 0       NOT NULL,
    staff_id            BIGINT          DEFAULT 0       NOT NULL,
    create_time         TIMESTAMP       DEFAULT now()   NOT NULL,
    update_time         TIMESTAMP       DEFAULT now()   NOT NULL
);

CREATE UNIQUE INDEX deposit_change_log_uq_idx_deposit_order_id_previous_log_id ON deposit_change_log (deposit_order_id, previous_log_id);
CREATE INDEX deposit_change_log_idx_bid_cid ON deposit_change_log (business_id, customer_id);

CREATE TRIGGER auto_update_deposit_change_log_update_time
  AFTER UPDATE
  ON deposit_change_log
  FOR EACH ROW
EXECUTE PROCEDURE update_modified_column();

COMMENT ON TABLE deposit_change_log IS 'Deposit balance change log';
COMMENT ON COLUMN deposit_change_log.id IS 'Primary key';
COMMENT ON COLUMN deposit_change_log.deposit_order_id IS 'Deposit order id';
COMMENT ON COLUMN deposit_change_log.change_type IS 'Balance change direction (increase, decrease)';
COMMENT ON COLUMN deposit_change_log.reason IS 'Balance change reason';
COMMENT ON COLUMN deposit_change_log.dest_order_id IS 'ID of the order using this deposit (if applicable)';
COMMENT ON COLUMN deposit_change_log.changed_amount IS 'Changed amount';
COMMENT ON COLUMN deposit_change_log.balance IS 'Deposit balance after this change';
COMMENT ON COLUMN deposit_change_log.currency_code IS 'Uppercase alpha3 currency code';
COMMENT ON COLUMN deposit_change_log.previous_log_id IS 'id to the previous log for the changes to this deposit order';
COMMENT ON COLUMN deposit_change_log.company_id IS 'Company id';
COMMENT ON COLUMN deposit_change_log.business_id IS 'Business id';
COMMENT ON COLUMN deposit_change_log.customer_id IS 'Customer id';
COMMENT ON COLUMN deposit_change_log.staff_id IS 'Staff id';
COMMENT ON COLUMN deposit_change_log.create_time IS 'Create time';
COMMENT ON COLUMN deposit_change_log.update_time IS 'Update time';
