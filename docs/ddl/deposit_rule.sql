CREATE TABLE IF NOT EXISTS deposit_rule
(
    id                  BIGINT GENERATED ALWAYS AS IDENTITY PRIMARY KEY,
    name                TEXT            DEFAULT ''      NOT NULL,
    filters             TEXT            DEFAULT ''      NOT NULL,
    deposit_type        TEXT            DEFAULT ''      NOT NULL,
    deposit_currency    TEXT            DEFAULT ''      NOT NULL,
    deposit_amount      NUMERIC(20, 2)  DEFAULT 0.00    NOT NULL,
    deposit_percentage  NUMERIC(5, 2)   DEFAULT 0.00    NOT NULL,
    company_id          BIGINT          DEFAULT 0       NOT NULL,
    business_id         BIGINT          DEFAULT 0       NOT NULL,
    create_time         TIMESTAMP       DEFAULT now()   NOT NULL,
    update_time         TIMESTAMP       DEFAULT now()   NOT NULL
);

CREATE INDEX deposit_rule_idx_company_id ON deposit_rule (company_id);

CREATE TRIGGER auto_update_deposit_rule_update_time
    AFTER UPDATE
    ON deposit_rule
    FOR EACH ROW
EXECUTE PROCEDURE update_modified_column();

COMMENT ON TABLE deposit_rule IS 'Deposit rule';
COMMENT ON COLUMN deposit_rule.id IS 'Primary key';
COMMENT ON COLUMN deposit_rule.name IS 'Deposit rule name';
COMMENT ON COLUMN deposit_rule.filters IS 'Filters pb json';
COMMENT ON COLUMN deposit_rule.deposit_type IS 'Deposit type (fixed, percentage)';
COMMENT ON COLUMN deposit_rule.deposit_currency IS 'Deposit currency';
COMMENT ON COLUMN deposit_rule.deposit_amount IS 'Deposit fixed amount';
COMMENT ON COLUMN deposit_rule.deposit_percentage IS 'Deposit percentage';
COMMENT ON COLUMN deposit_rule.company_id IS 'Company id';
COMMENT ON COLUMN deposit_rule.business_id IS 'Business id';
COMMENT ON COLUMN deposit_rule.create_time IS 'Create time';
COMMENT ON COLUMN deposit_rule.update_time IS 'Update time';
