DROP TABLE IF EXISTS public.order_item_assigned_amounts;

CREATE TABLE IF NOT EXISTS public.order_item_assigned_amount (
    id           BIGINT GENERATED ALWAYS AS IDENTITY PRIMARY KEY,
    order_id     BIGINT DEFAULT 0 NOT NULL,
    item_id      BIGINT DEFAULT 0 NOT NULL,
    amount       NUMERIC(20, 2) DEFAULT 0.00 NOT NULL,
    currency_code TEXT DEFAULT '' NOT NULL,
    business_id   BIGINT DEFAULT 0 NOT NULL,
    created_at    TIMESTAMP DEFAULT NOW(),
    updated_at    TIMESTAMP DEFAULT NOW()
);

CREATE INDEX order_item_assigned_amounts_idx_order_id ON order_item_assigned_amounts (order_id);