CREATE TABLE IF NOT EXISTS order_item_price_detail
(
    id            BIGINT GENERATED ALWAYS AS IDENTITY PRIMARY KEY,
    order_id      BIGINT         DEFAULT 0     NOT NULL,
    order_item_id BIGINT         DEFAULT 0     NOT NULL,
    name          VARCHAR(150)   DEFAULT ''    NOT NULL,
    unit_price    DECIMAL(20, 2) DEFAULT 0.00  NOT NULL,
    currency_code VARCHAR(3)     DEFAULT ''    NOT NULL,
    quantity      INT            DEFAULT 0     NOT NULL,
    subtotal      DECIMAL(20, 2) DEFAULT 0.00  NOT NULL,
    operator      VARCHAR(50)    DEFAULT ''    NOT NULL,
    create_time   TIMESTAMP      DEFAULT now() NOT NULL,
    update_time   TIMESTAMP      DEFAULT now() NOT NULL
);

CREATE INDEX order_item_price_detail_idx_order_id ON order_item_price_detail (order_id);
CREATE INDEX order_item_price_detail_idx_order_item_id ON order_item_price_detail (order_item_id);

CREATE TRIGGER auto_update_order_item_price_detail_update_time
    AFTER UPDATE
    ON order_item_price_detail
    FOR EACH ROW
EXECUTE PROCEDURE update_modified_column();

COMMENT ON TABLE order_item_price_detail IS 'Item subtotal detail';
COMMENT ON COLUMN order_item_price_detail.id IS 'Primary key';
COMMENT ON COLUMN order_item_price_detail.order_id IS 'Order ID';
COMMENT ON COLUMN order_item_price_detail.order_item_id IS 'Order item ID';
COMMENT ON COLUMN order_item_price_detail.name IS 'Price item name';
COMMENT ON COLUMN order_item_price_detail.unit_price IS 'Unit price';
COMMENT ON COLUMN order_item_price_detail.currency_code IS 'Currency Code';
COMMENT ON COLUMN order_item_price_detail.quantity IS 'Quantity';
COMMENT ON COLUMN order_item_price_detail.subtotal IS 'Subtotal, equal to (quantity * unit_price)';
COMMENT ON COLUMN order_item_price_detail.operator IS 'ADD or SUBTRACT';
COMMENT ON COLUMN order_item_price_detail.create_time IS 'Create time';
COMMENT ON COLUMN order_item_price_detail.update_time IS 'Update time';