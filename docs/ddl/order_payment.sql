drop table if EXISTS order_payment;

create Table if not exists order_payment
(
    id                         bigint generated always as identity primary key,
    order_id                   bigint         default 0                  not null,
    company_id                 bigint         default 0                  not null,
    business_id                bigint         default 0                  not null,
    staff_id                   bigint         default 0                  not null,
    customer_id                bigint         default 0                  not null,
    payment_id                 bigint         default 0                  not null,
    payment_method_id          bigint         default 0                  not null,
    payment_method             TEXT           default ''                 not null,
    payment_method_extra       jsonb          default '{}'               not null,
    payment_method_vendor      TEXT           default ''                 not null,
    is_online                  BOOLEAN        default false              not null,
    is_deposit                 BOOLEAN        default false              not null,
    paid_by                    TEXT           default ''                 not null,
    currency                   TEXT           default ''                 not null,
    total_amount               numeric(20, 2) default 0.00               not null,
    amount                     numeric(20, 2) default 0.00               not null,
    refunded_amount            numeric(20, 2) default 0.00               not null,
    processing_fee             numeric(20, 2) default -1.00              not null,
    convenience_fee            numeric(20, 2) default 0.00               not null,
    payment_tips               numeric(20, 2) default -1.00              not null,
    payment_tips_before_create numeric(20, 2) default -1.00              not null,
    payment_tips_after_create  numeric(20, 2) default -1.00              not null,
    payment_status             TEXT           default 'TYPE_UNSPECIFIED' not null,
    reason                     TEXT           default ''                 not null,
    pay_time                   timestamp      default to_timestamp(0)    not null,
    cancel_time                timestamp      default to_timestamp(0)    not null,
    create_time                timestamp      default now()              not null,
    update_time                timestamp      default now()              not null
);

create index idx_order_id on order_payment (order_id);
create index idx_company_customer on order_payment (company_id, customer_id);


CREATE TRIGGER auto_update_order_payment_update_time
    AFTER UPDATE
    ON public.order_payment
    FOR EACH ROW
EXECUTE PROCEDURE update_modified_column();