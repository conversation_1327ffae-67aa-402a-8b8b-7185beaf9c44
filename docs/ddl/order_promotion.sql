CREATE TABLE IF NOT EXISTS order_promotion  (
    id BIGINT GENERATED ALWAYS AS IDENTITY PRIMARY KEY,
    create_time         TIMESTAMP       DEFAULT now()   NOT NULL,
    update_time         TIMESTAMP       DEFAULT now()   NOT NULL,
    order_id BIGINT          DEFAULT 0       NOT NULL,
    promotion_id BIGINT          DEFAULT 0       NOT NULL,
    source_type TEXT DEFAULT ''      NOT NULL,       
    source_id BIGINT          DEFAULT 0       NOT NULL,
    source_subject_id BIGINT          DEFAULT 0       NOT NULL,
    name TEXT DEFAULT ''      NOT NULL,
    discount_type TEXT DEFAULT ''      NOT NULL,     
    discount_value NUMERIC(20, 4) DEFAULT 0.00    NOT NULL,
    applied_amount NUMERIC(20, 2) DEFAULT 0.00    NOT NULL,
    status TEXT NOT NULL DEFAULT ''
);

CREATE INDEX order_promotion_idx_order_id ON order_promotion (order_id);
CREATE INDEX order_promotion_idx_status_create_time ON order_promotion (status, create_time);

CREATE TRIGGER auto_update_order_promotion_update_time
  AFTER UPDATE
  ON order_promotion
  FOR EACH ROW
EXECUTE PROCEDURE update_modified_column();

COMMENT ON TABLE order_promotion IS 'order promotion';
COMMENT ON COLUMN order_promotion.id IS 'Primary key';
COMMENT ON COLUMN order_promotion.create_time IS 'create time';
COMMENT ON COLUMN order_promotion.update_time IS 'update time';
COMMENT ON COLUMN order_promotion.order_id IS 'related order id';
COMMENT ON COLUMN order_promotion.promotion_id IS 'related promotion id';
COMMENT ON COLUMN order_promotion.source_type IS 'source promotion type, membership, package, discount etc';
COMMENT ON COLUMN order_promotion.source_id IS 'source promotion id';
COMMENT ON COLUMN order_promotion.source_subject_id IS 'source promotion subject id';
COMMENT ON COLUMN order_promotion.name IS 'package name,discount code,membership name, etc';
COMMENT ON COLUMN order_promotion.discount_type IS 'discount type, percentage, fixed_amount, item_deduction etc';
COMMENT ON COLUMN order_promotion.discount_value IS 'discount value, for example 10 means 10% or fixed amount';
COMMENT ON COLUMN order_promotion.applied_amount IS 'final applied amount';
COMMENT ON COLUMN order_promotion.status IS 'status';


CREATE TABLE IF NOT EXISTS order_promotion_item  (
    id BIGINT GENERATED ALWAYS AS IDENTITY PRIMARY KEY,
    create_time         TIMESTAMP       DEFAULT now()   NOT NULL,
    update_time         TIMESTAMP       DEFAULT now()   NOT NULL,
    order_promotion_id BIGINT          DEFAULT 0       NOT NULL,
    order_item_id BIGINT          DEFAULT 0       NOT NULL,
    applied_amount NUMERIC(20, 2) DEFAULT 0.00    NOT NULL
);

CREATE INDEX order_promotion_item_idx_order_promotion_id ON order_promotion_item (order_promotion_id);

CREATE TRIGGER auto_update_order_promotion_item_update_time
  AFTER UPDATE
  ON order_promotion_item
  FOR EACH ROW
EXECUTE PROCEDURE update_modified_column();

COMMENT ON TABLE order_promotion_item IS 'order promotion item';
COMMENT ON COLUMN order_promotion_item.id IS 'Primary key';
COMMENT ON COLUMN order_promotion_item.create_time IS 'create time';
COMMENT ON COLUMN order_promotion_item.update_time IS 'update time';
COMMENT ON COLUMN order_promotion_item.order_promotion_id IS 'related order promotion id';
COMMENT ON COLUMN order_promotion_item.order_item_id IS 'related order item id';
COMMENT ON COLUMN order_promotion_item.applied_amount IS 'final applied amount';