CREATE TABLE IF NOT EXISTS refund_order
(
    id                     BIGINT GENERATED ALWAYS AS IDENTITY PRIMARY KEY,
    order_id               BIGINT         DEFAULT 0                     NOT NULL,
    company_id             BIGINT         DEFAULT 0                     NOT NULL,
    business_id            BIGINT         DEFAULT 0                     NOT NULL,
    staff_id               BIGINT         DEFAULT 0                     NOT NULL,
    customer_id            BIGINT         DEFAULT 0                     NOT NULL,
    refund_mode            TEXT           DEFAULT ''                    NOT NULL,
    refund_reason          TEXT           DEFAULT ''                    NOT NULL,
    order_status_snapshot  TEXT           DEFAULT ''                    NOT NULL,
    currency_code          TEXT           DEFAULT ''                    NOT NULL,

    refund_total_amount    NUMERIC(20, 2) DEFAULT 0.00                  NOT NULL,
    refund_item_sub_total  NUMERIC(20, 2) DEFAULT 0.00                  NOT NULL,
    refund_discount_amount NUMERIC(20, 2) DEFAULT 0.00                  NOT NULL,
    refund_tips_amount     NUMERIC(20, 2) DEFAULT 0.00                  NOT NULL,
    refund_convenience_fee NUMERIC(20, 2) DEFAULT 0.00                  NOT NULL,
    refund_tax_amount      NUMERIC(20, 2) DEFAULT 0.00                  NOT NULL,

    refund_order_status    TEXT           DEFAULT ''                    NOT NULL,
    create_time            TIMESTAMP      DEFAULT now()                 NOT NULL,
    update_time            TIMESTAMP      DEFAULT now()                 NOT NULL,
    refund_time            TIMESTAMP      DEFAULT to_timestamp(0) NOT NULL
);

CREATE INDEX refund_order_idx_order_id ON refund_order (order_id);
CREATE INDEX refund_order_idx_bid_cid ON refund_order (business_id, customer_id);
