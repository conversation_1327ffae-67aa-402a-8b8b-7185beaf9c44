CREATE TABLE IF NOT EXISTS refund_order_item
(
    id                     BIGINT GENERATED ALWAYS AS IDENTITY PRIMARY KEY,
    order_id               BIGINT         DEFAULT 0     NOT NULL,
    order_item_id          BIGINT         DEFAULT 0     NOT NULL,
    refund_order_id        BIGINT         DEFAULT 0     NOT NULL,
    company_id             BIGINT         DEFAULT 0     NOT NULL,
    business_id            BIGINT         DEFAULT 0     NOT NULL,
    staff_id               BIGINT         DEFAULT 0     NOT NULL,
    customer_id            BIGINT         DEFAULT 0     NOT NULL,

    item_type              TEXT           DEFAULT ''    NOT NULL,
    item_id                BIGINT         DEFAULT 0     NOT NULL,
    item_name              TEXT           DEFAULT ''    NOT NULL,
    item_description       TEXT           DEFAULT ''    NOT NULL,
    item_unit_price        NUMERIC(20, 2) DEFAULT 0.00  NOT NULL,

    tax_id                 BIGINT         DEFAULT 0     NOT NULL,
    tax_name               TEXT           DEFAULT ''    NOT NULL,
    tax_rate               NUMERIC(20, 4) DEFAULT 0.00  NOT NULL,
    currency_code          TEXT           DEFAULT ''    NOT NULL,
    refund_item_mode       TEXT           DEFAULT ''    NOT NULL,
    refund_total_amount    NUMERIC(20, 2) DEFAULT 0.00  NOT NULL,
    refund_quantity        INT            DEFAULT 0     NOT NULL,
    refund_amount          NUMERIC(20, 2) DEFAULT 0.00  NOT NULL,
    refund_discount_amount NUMERIC(20, 2) DEFAULT 0.00  NOT NULL,
    refund_tax_amount      NUMERIC(20, 2) DEFAULT 0.00  NOT NULL,

    create_time            TIMESTAMP      DEFAULT now() NOT NULL
);

CREATE INDEX refund_order_item_idx_order_id ON refund_order_item (order_id);
CREATE INDEX refund_order_item_idx_refund_order_id ON refund_order_item (refund_order_id);
CREATE INDEX refund_order_item_idx_order_item_id ON refund_order_item (order_item_id);
CREATE INDEX refund_order_item_idx_bid_cid ON refund_order_item (business_id, customer_id);
