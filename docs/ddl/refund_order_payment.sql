CREATE TABLE IF NOT EXISTS refund_order_payment
(
    id                           BIGINT GENERATED ALWAYS AS IDENTITY PRIMARY KEY,
    order_id                     BIGINT         DEFAULT 0               NOT NULL,
    order_payment_id             BIGINT         DEFAULT 0               NOT NULL,
    refund_order_id              BIGINT         DEFAULT 0               NOT NULL,
    company_id                   BIGINT         DEFAULT 0               NOT NULL,
    business_id                  BIGINT         DEFAULT 0               NOT NULL,
    staff_id                     BIGINT         DEFAULT 0               NOT NULL,
    customer_id                  BIGINT         DEFAULT 0               NOT NULL,

    order_status_snapshot        TEXT           DEFAULT ''              NOT NULL,
    refund_payment_method_id     BIGINT         DEFAULT 0               NOT NULL,
    refund_payment_method        TEXT           DEFAULT ''              NOT NULL,
    refund_payment_method_extra  jsonb          DEFAULT '{}'            NOT NULL,
    refund_payment_method_vendor TEXT           DEFAULT ''              NOT NULL,

    currency_code                TEXT           DEFAULT ''              NOT NULL,
    refund_amount                NUMERIC(20, 2) DEFAULT 0.00            NOT NULL,
    refund_convenience_fee       NUMERIC(20, 2) DEFAULT 0.00            NOT NULL,

    description                  TEXT           DEFAULT ''              NOT NULL,
    refund_status                TEXT           DEFAULT ''              NOT NULL,
    refund_status_reason         TEXT           DEFAULT ''              NOT NULL,

    create_time                  TIMESTAMP      DEFAULT now()           NOT NULL,
    update_time                  TIMESTAMP      DEFAULT now()           NOT NULL,
    refund_time                  TIMESTAMP      DEFAULT to_timestamp(0) NOT NULL,
    fail_time                    TIMESTAMP      DEFAULT to_timestamp(0) NOT NULL,
    cancel_time                  TIMESTAMP      DEFAULT to_timestamp(0) NOT NULL
);

CREATE INDEX refund_order_payment_idx_order_id_order_status ON refund_order_payment (order_id, order_status_snapshot);
CREATE INDEX refund_order_payment_idx_refund_order_id ON refund_order_payment (refund_order_id);
CREATE INDEX refund_order_payment_idx_order_payment_id ON refund_order_payment (order_payment_id);
CREATE INDEX refund_order_payment_idx_bid_cid ON refund_order_payment (business_id, customer_id);
CREATE INDEX refund_order_payment_idx_refund_status_update_time_index ON refund_order_payment (refund_status, update_time);
COMMENT ON INDEX refund_order_payment_idx_refund_status_update_time_index IS '用于 CRON 补偿异常中断的 Refund 流程.';

