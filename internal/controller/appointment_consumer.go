package controller

import (
	"context"

	"go.uber.org/zap"
	"google.golang.org/protobuf/types/known/timestamppb"

	eventbus "github.com/MoeGolibrary/go-lib/event-bus"
	"github.com/MoeGolibrary/go-lib/zlog"
	eventbuspb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/event_bus/v1"
	orderpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/order/v1"
	marketingsvcpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/marketing/v1"
	"github.com/MoeGolibrary/moego-svc-order-v2/config"
	"github.com/MoeGolibrary/moego-svc-order-v2/internal/controller/internal/consumerhelper"
	"github.com/MoeGolibrary/moego-svc-order-v2/internal/service"
)

type AppointmentConsumer struct {
	orderSvc service.OrderService

	helper *consumerhelper.CancelOrderHelper
}

func newAppointmentConsumer(
	conf *config.EventBusConfig,
	orderSvc service.OrderService,
	refundOrderSvc service.RefundOrderService,
	cli marketingsvcpb.DiscountCodeServiceClient,
) *eventbus.Consumer {
	ac := &AppointmentConsumer{
		orderSvc: orderSvc,
		helper:   consumerhelper.NewCancelOrderHelper(orderSvc, refundOrderSvc, cli),
	}

	consumer, err := eventbus.NewConsumer(
		conf.AppointmentConsumer(),
		ac.handleEvent,
	)
	if err != nil {
		zlog.Default().Fatal("new appointment consumer failed", zap.Error(err))
	}

	return consumer
}

func (ac *AppointmentConsumer) handleEvent(
	ctx context.Context,
	eventID string,
	_ *timestamppb.Timestamp,
	eventType eventbuspb.EventType,
	eventData *eventbuspb.EventData,
) error {
	defer func() {
		if err := recover(); err != nil {
			zlog.Error(ctx, "panic in AppointmentConsumer", zap.Any("err", err))
		}
	}()

	if eventType != eventbuspb.EventType_APPOINTMENT_CANCELED {
		zlog.Info(
			ctx,
			"skip not interested event",
			zap.String("consumer", "AppointmentConsumer"),
			zap.String("eventID", eventID),
			zap.Stringer("eventType", eventType),
		)

		return nil
	}

	event := eventData.GetAppointmentCanceledEvent()
	handlerCtx := zlog.NewContext(
		ctx,
		zap.String("consumer", "AppointmentConsumer"),
		zap.String("eventID", eventID),
		zap.Stringer("eventType", eventType),
		zap.Stringer("sourceType", orderpb.OrderSourceType_APPOINTMENT),
		zap.Int64("sourceID", event.GetId()),
	)

	zlog.Info(handlerCtx, "received appointment canceled event")

	return ac.helper.CancelOrdersBySource(
		handlerCtx,
		orderpb.OrderSourceType_APPOINTMENT,
		event.GetId(),
		consumerhelper.CancelOrderOption{
			RefundDeposit:    event.GetRefundDeposit(),
			RefundOrder:      event.GetAutoRefundOrder(),
			ReturnDiscount:   true, // Appt should return discount.
			DiscountSourceID: event.GetId(),
		},
	)
}
