package controller

import (
	"context"

	ordersvcpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/temp_order/v1"
)

// implement AssignItemAmount method for ordersvcpb.AssignItemPaidAmountRequest
func (svr *AssignItemAmountServer) AssignItemPaidAmount(
	ctx context.Context,
	req *ordersvcpb.AssignItemPaidAmountRequest,
) (*ordersvcpb.AssignItemPaidAmountResponse, error) {
	return svr.itemAssignService.AssignItemAmount(ctx, req)
}

func (svr *AssignItemAmountServer) GetAssignedItemPaidAmount(ctx context.Context,
	req *ordersvcpb.GetAssignedItemPaidAmountRequest,
) (*ordersvcpb.GetAssignedItemPaidAmountResponse, error) {
	return svr.itemAssignService.GetItemAssignedAmount(ctx, req)
}
