package controller

import (
	"context"

	"google.golang.org/protobuf/types/known/emptypb"

	ordersvcpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/order/v1"
)

func (svr *OrderServer) SyncRefundOrderPayment(
	ctx context.Context,
	req *ordersvcpb.SyncRefundOrderPaymentRequest,
) (*ordersvcpb.SyncRefundOrderPaymentResponse, error) {
	total, synced, err := svr.refundOrderService.SyncRefundOrderPayment(ctx, req.GetRefundOrderPaymentId())
	if err != nil {
		return nil, err
	}

	return &ordersvcpb.SyncRefundOrderPaymentResponse{
		Total:  total,
		Synced: synced,
	}, nil
}

// 定时任务去 Redeem promotion
func (svr *OrderTaskServer) RetryRedeemPromotion(
	ctx context.Context,
	_ *emptypb.Empty,
) (*emptypb.Empty, error) {
	svr.promotionService.RetryRedeem(ctx)
	return nil, nil
}
