package controller

import (
	"context"

	ordersvcpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/order/v1"
)

func (svr *OrderServer) CreateDepositOrder(ctx context.Context, req *ordersvcpb.CreateDepositOrderRequest) (
	*ordersvcpb.CreateDepositOrderResponse, error,
) {
	orderDetail, err := svr.depositOrderService.CreateDepositOrder(ctx, req)
	if err != nil {
		return nil, err
	}

	return &ordersvcpb.CreateDepositOrderResponse{
		Order: orderDetail.ToPB(),
	}, nil
}

func (svr *OrderServer) GetDepositDetail(ctx context.Context,
	req *ordersvcpb.GetDepositDetailRequest,
) (*ordersvcpb.GetDepositDetailResponse, error) {
	order, err := svr.orderService.GetOrder(ctx, req.GetDepositOrderId())
	if err != nil {
		return nil, err
	}

	return svr.depositOrderService.GetDepositSummary(ctx, order)
}

func (svr *OrderServer) UpdateDepositOrderSource(
	ctx context.Context, req *ordersvcpb.UpdateDepositOrderSourceRequest,
) (*ordersvcpb.UpdateDepositOrderSourceResponse, error) {
	if err := svr.depositOrderService.UpdateDepositOrderSource(ctx, req); err != nil {
		return nil, err
	}

	return &ordersvcpb.UpdateDepositOrderSourceResponse{}, nil
}
