// NOTE:
// Deposit Rules 应当是上层业务逻辑，只是排期和人力等原因暂时放在 order-v2 服务里。不要过多耦合 order-v2 里的业务，以后可能要拿出去的。

package controller

import (
	"context"

	"github.com/samber/lo"
	"go.uber.org/zap"

	"github.com/MoeGolibrary/go-lib/zlog"
	orderpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/order/v1"
	ordersvcpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/order/v2"
	"github.com/MoeGolibrary/moego-svc-order-v2/internal/model"
	"github.com/MoeGolibrary/moego-svc-order-v2/internal/service"
)

type DepositRuleServer struct {
	ordersvcpb.UnimplementedDepositRuleServiceServer

	depositRuleService service.DepositRuleService
}

func NewDepositRuleServer(
	depositRulesService service.DepositRuleService,
) ordersvcpb.DepositRuleServiceServer {
	return &DepositRuleServer{
		depositRuleService: depositRulesService,
	}
}

func (s *DepositRuleServer) CreateDepositRule(
	ctx context.Context, req *ordersvcpb.CreateDepositRuleRequest,
) (*ordersvcpb.CreateDepositRuleResponse, error) {
	rule, err := s.depositRuleService.CreateDepositRule(ctx, req.GetCompanyId(), req.GetBusinessId(), req.GetRule())
	if err != nil {
		return nil, err
	}

	return &ordersvcpb.CreateDepositRuleResponse{
		Rule: rule.ToPB(),
	}, nil
}

func (s *DepositRuleServer) UpdateDepositRule(
	ctx context.Context, req *ordersvcpb.UpdateDepositRuleRequest,
) (*ordersvcpb.UpdateDepositRuleResponse, error) {
	rule, err := s.depositRuleService.UpdateDepositRule(ctx, req.GetRuleId(), req.GetRule())
	if err != nil {
		return nil, err
	}

	return &ordersvcpb.UpdateDepositRuleResponse{
		Rule: rule.ToPB(),
	}, nil
}

func (s *DepositRuleServer) DeleteDepositRule(
	ctx context.Context, req *ordersvcpb.DeleteDepositRuleRequest,
) (*ordersvcpb.DeleteDepositRuleResponse, error) {
	err := s.depositRuleService.DeleteDepositRule(ctx, req.GetRuleId())
	if err != nil {
		return nil, err
	}

	return &ordersvcpb.DeleteDepositRuleResponse{}, nil
}

func (s *DepositRuleServer) ListDepositRules(
	ctx context.Context, req *ordersvcpb.ListDepositRulesRequest,
) (*ordersvcpb.ListDepositRulesResponse, error) {
	rules, err := s.depositRuleService.ListDepositRules(ctx, req.GetBusinessId())
	if err != nil {
		return nil, err
	}

	return &ordersvcpb.ListDepositRulesResponse{
		Rules: lo.Map(rules, func(rule *model.DepositRule, _ int) *orderpb.DepositRule { return rule.ToPB() }),
	}, nil
}

func (s *DepositRuleServer) PreviewDepositOrder(
	ctx context.Context, req *ordersvcpb.PreviewDepositOrderRequest,
) (*ordersvcpb.PreviewDepositOrderResponse, error) {
	orderDetail, priceItems, err := s.depositRuleService.PreviewDepositOrder(ctx, req)
	if err != nil {
		return nil, err
	}

	return &ordersvcpb.PreviewDepositOrderResponse{
		DepositOrderDetailPreview: orderDetail.ToPB(),
		DepositOrderPriceItems:    priceItems,
	}, nil
}

func (s *DepositRuleServer) MigrateToDepositRules(
	ctx context.Context, req *ordersvcpb.MigrateToDepositRulesRequest,
) (*ordersvcpb.MigrateToDepositRulesResponse, error) {
	result, err := s.depositRuleService.MigrateToDepositRules(ctx, req.GetBusinessIds())
	if err != nil {
		return nil, err
	}

	var errors []*ordersvcpb.MigrateToDepositRulesResponse_Error

	for _, item := range result {
		if item.Error != nil {
			zlog.Error(ctx, "failed to migrate deposit rule", zap.Error(item.Error), zap.Int64("business_id", item.BusinessID))
			errors = append(errors, &ordersvcpb.MigrateToDepositRulesResponse_Error{
				BusinessId: item.BusinessID,
				Message:    item.Error.Error(),
			})
		}
	}

	return &ordersvcpb.MigrateToDepositRulesResponse{
		Errors: errors,
	}, nil
}
