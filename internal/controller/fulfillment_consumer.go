package controller

import (
	"context"

	"go.uber.org/zap"
	"google.golang.org/protobuf/types/known/timestamppb"

	eventbus "github.com/MoeGolibrary/go-lib/event-bus"
	"github.com/MoeGolibrary/go-lib/zlog"
	eventbuspb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/event_bus/v1"
	orderpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/order/v1"
	marketingsvcpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/marketing/v1"
	"github.com/MoeGolibrary/moego-svc-order-v2/config"
	"github.com/MoeGolibrary/moego-svc-order-v2/internal/controller/internal/consumerhelper"
	"github.com/MoeGolibrary/moego-svc-order-v2/internal/service"
)

type FulfillmentConsumer struct {
	orderSvc service.OrderService

	helper *consumerhelper.CancelOrderHelper
}

func newFulfillmentConsumer(
	conf *config.EventBusConfig,
	orderSvc service.OrderService,
	refundOrderSvc service.RefundOrderService,
	cli marketingsvcpb.DiscountCodeServiceClient,
) *eventbus.Consumer {
	fc := &FulfillmentConsumer{
		orderSvc: orderSvc,
		helper:   consumerhelper.NewCancelOrderHelper(orderSvc, refundOrderSvc, cli),
	}

	consumer, err := eventbus.NewConsumer(
		conf.FulfillmentConsumer(),
		fc.handleEvent,
	)
	if err != nil {
		zlog.Default().Fatal("new fulfillment consumer failed", zap.Error(err))
	}

	return consumer
}

func (fc *FulfillmentConsumer) handleEvent(
	ctx context.Context,
	eventID string,
	_ *timestamppb.Timestamp,
	eventType eventbuspb.EventType,
	eventData *eventbuspb.EventData,
) error {
	defer func() {
		if err := recover(); err != nil {
			zlog.Error(ctx, "panic in FulfillmentConsumer", zap.Any("err", err))
		}
	}()

	if eventType != eventbuspb.EventType_FULFILLMENT_CANCELED {
		zlog.Info(
			ctx,
			"skip not interested event",
			zap.String("consumer", "FulfillmentConsumer"),
			zap.String("eventID", eventID),
			zap.Stringer("eventType", eventType),
		)

		return nil
	}

	event := eventData.GetFulfillmentCanceledEvent()
	handlerCtx := zlog.NewContext(
		ctx,
		zap.String("consumer", "FulfillmentConsumer"),
		zap.String("eventID", eventID),
		zap.Stringer("eventType", eventType),
		zap.Stringer("sourceType", orderpb.OrderSourceType_FULFILLMENT),
		zap.Int64("sourceID", event.GetId()),
	)

	zlog.Info(handlerCtx, "received event")

	return fc.helper.CancelOrdersBySource(
		handlerCtx,
		orderpb.OrderSourceType_FULFILLMENT,
		event.GetId(),
		consumerhelper.CancelOrderOption{
			// Fulfillment 还是基于 Legacy 的，没有 Deposit.
			RefundDeposit: false,
			RefundOrder:   event.GetAutoRefundOrder(),
			// Fulfillment 暂时不支持 discount（see RedeemType），所以不需要 return discount code.
			ReturnDiscount: false,
		},
	)
}
