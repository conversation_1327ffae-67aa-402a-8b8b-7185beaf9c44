package controller

import (
	"context"
	"fmt"

	orderpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/order/v1"
	ordersvcpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/order/v2"
	"github.com/MoeGolibrary/moego-svc-order-v2/internal/service"
)

type MigrationServer struct {
	ordersvcpb.MigrationServiceServer

	orderService     service.OrderService
	migrationService service.MigrationService
}

func NewMigrationServer(
	orderService service.OrderService,
	migrationService service.MigrationService,
) ordersvcpb.MigrationServiceServer {
	return &MigrationServer{
		orderService:     orderService,
		migrationService: migrationService,
	}
}

func (ms *MigrationServer) MigrateToInvoiceV4(ctx context.Context, req *ordersvcpb.MigrateInvoiceRequest) (
	*ordersvcpb.MigrateInvoiceResponse, error,
) {
	switch req.GetSourceType() {
	case orderpb.OrderSourceType_APPOINTMENT:
		// pass

	default:
		return &ordersvcpb.MigrateInvoiceResponse{
			Success:         false,
			FailedReason:    fmt.Sprintf("unsupported sourceType: %s", req.GetSourceType()),
			RelatedOrderIds: nil,
		}, nil
	}

	orderDetails, err := ms.orderService.ListDetailBySource(ctx, req.GetSourceType(), req.GetSourceId())
	if err != nil {
		return nil, err
	}

	success, failedReason, relatedOrderIDs := ms.migrationService.MigrateToV4(ctx, req.GetSourceId(), orderDetails)

	return &ordersvcpb.MigrateInvoiceResponse{
		Success:         success,
		FailedReason:    failedReason,
		RelatedOrderIds: relatedOrderIDs,
	}, nil
}
