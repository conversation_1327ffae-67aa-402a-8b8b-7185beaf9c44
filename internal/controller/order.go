package controller

import (
	"context"
	"sort"

	"github.com/samber/lo"
	"github.com/shopspring/decimal"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	"github.com/MoeGolibrary/go-lib/merror"
	errorspb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/errors/v1"
	orderpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/order/v1"
	ordersvcpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/order/v1"
	"github.com/MoeGolibrary/moego-svc-order-v2/internal/model"
	"github.com/MoeGolibrary/moego-svc-order-v2/internal/repo"
)

// ListOrdersV1 gets the order list, which specified by the businessID and the orderID.
// It returns both origin orders and extra orders, and all refund orders.
// It doesn't contain any order / refund order payments and order / refund order items,
// please use ListOrderDetail instead if you need details.
func (svr *OrderServer) ListOrdersV1(ctx context.Context, req *ordersvcpb.ListOrdersV1Request) (
	*ordersvcpb.ListOrdersV1Response, error,
) {
	// List orders.
	order, err := svr.getRootOrder(ctx, req.GetOriginOrderId())
	if err != nil {
		return nil, err
	}

	if order.BusinessID != req.GetBusinessId() {
		return nil, merror.NewBizError(errorspb.Code_CODE_FORBIDDEN, "business id not match")
	}

	tailOrders, err := svr.orderService.ListTail(ctx, order.ID)
	if err != nil {
		return nil, err
	}

	allOrders := make([]*model.Order, 0, len(tailOrders)+1)
	allOrders = append(allOrders, order)
	allOrders = append(allOrders, tailOrders...)

	// List refund orders.
	allRefundOrders := make([]*model.RefundOrder, 0, len(allOrders))

	for _, od := range allOrders {
		refundOrders, err := svr.refundOrderService.ListRefund(ctx, od.ID)
		if err != nil {
			return nil, err
		}

		allRefundOrders = append(allRefundOrders, refundOrders...)
	}

	return &ordersvcpb.ListOrdersV1Response{
		Orders: lo.Map(
			allOrders,
			func(it *model.Order, _ int) *orderpb.OrderModelV1 { return it.ToPB() },
		),
		RefundOrders: lo.Map(
			allRefundOrders,
			func(it *model.RefundOrder, _ int) *orderpb.RefundOrderModel { return it.ToPB() },
		),
	}, nil
}

// ListOrderDetail gets the order detail, which specified by the orderID, and its refund orders.
// This function walks through the order ref and lists all the orders referring to the same head order.
func (svr *OrderServer) ListOrderDetail(ctx context.Context, req *ordersvcpb.ListOrderDetailRequest) (
	*ordersvcpb.ListOrderDetailResponse, error,
) {
	var allOrderDetails []*model.OrderDetail

	order, err := svr.getRootOrder(ctx, req.GetOriginOrderId())
	if err != nil {
		return nil, err
	}

	if order.BusinessID != req.GetBusinessId() {
		return nil, merror.NewBizError(errorspb.Code_CODE_FORBIDDEN, "business id not match")
	}

	orderDetail, err := svr.orderService.AttachDetail(ctx, order)
	if err != nil {
		return nil, err
	}

	tailDetails, err := svr.orderService.ListTailDetail(ctx, order.ID)
	if err != nil {
		return nil, err
	}

	allOrderDetails = append(allOrderDetails, tailDetails...)
	allOrderDetails = append(allOrderDetails, orderDetail)

	sort.Slice(
		allOrderDetails,
		func(i, j int) bool {
			// 新订单在前.
			return allOrderDetails[i].GetID() > allOrderDetails[j].GetID()
		},
	)

	// list refund order details.
	var allRefundOrders []*model.RefundOrderDetail

	for _, od := range allOrderDetails {
		refundOrders, err := svr.refundOrderService.ListRefundDetail(ctx, od.GetID())
		if err != nil {
			return nil, err
		}

		allRefundOrders = append(allRefundOrders, refundOrders...)
	}

	return &ordersvcpb.ListOrderDetailResponse{
		Orders: lo.Map(
			allOrderDetails,
			func(it *model.OrderDetail, _ int) *orderpb.OrderDetailModelV1 { return it.ToPB() },
		),
		RefundOrders: lo.Map(
			allRefundOrders,
			func(it *model.RefundOrderDetail, _ int) *orderpb.RefundOrderDetailModel { return it.ToPB() },
		),
	}, nil
}

func (svr *OrderServer) QueryOrderDetail(
	ctx context.Context, req *ordersvcpb.QueryOrderDetailRequest,
) (*ordersvcpb.QueryOrderDetailResponse, error) {
	rootOrder, err := svr.orderService.GetRootOrderBySource(ctx, req.GetSourceType(), req.GetSourceId())
	if repo.IsNotFound(err) {
		return &ordersvcpb.QueryOrderDetailResponse{}, nil
	} else if err != nil {
		return nil, err
	}

	if rootOrder.CompanyID != req.GetCompanyId() {
		return nil, merror.NewBizError(errorspb.Code_CODE_FORBIDDEN, "company id not match")
	}

	resp, err := svr.ListOrderDetail(
		ctx, &ordersvcpb.ListOrderDetailRequest{
			BusinessId:    rootOrder.BusinessID,
			OriginOrderId: rootOrder.ID,
		},
	)
	if err != nil {
		return nil, err
	}

	return &ordersvcpb.QueryOrderDetailResponse{
		Orders:       resp.GetOrders(),
		RefundOrders: resp.GetRefundOrders(),
	}, nil
}

func (svr *OrderServer) GetOrderDetailV1(ctx context.Context, req *ordersvcpb.GetOrderDetailRequest) (
	*ordersvcpb.GetOrderDetailResponse, error,
) {
	od, err := svr.orderService.GetDetail(ctx, req.GetOrderId())
	if err != nil {
		return nil, err
	}

	if od.Order.BusinessID != req.GetBusinessId() {
		return nil, merror.NewBizError(errorspb.Code_CODE_FORBIDDEN, "business id not match")
	}

	return &ordersvcpb.GetOrderDetailResponse{Order: od.ToPB()}, nil
}

func (svr *OrderServer) CreateNoShowOrder(ctx context.Context, req *ordersvcpb.CreateNoShowOrderRequest) (
	*ordersvcpb.CreateNoShowOrderResponse, error,
) {
	orderDetail, err := svr.orderService.CreateNoShowOrder(ctx, req)
	if err != nil {
		return nil, err
	}

	return &ordersvcpb.CreateNoShowOrderResponse{
		Order: orderDetail.ToPB(),
	}, nil
}

func (svr *OrderServer) CreateTipOrder(ctx context.Context, req *ordersvcpb.CreateTipOrderRequest) (
	*ordersvcpb.CreateTipOrderResponse, error,
) {
	orderDetail, err := svr.orderService.CreateTipOrder(ctx, req)
	if err != nil {
		return nil, err
	}

	return &ordersvcpb.CreateTipOrderResponse{
		Order: orderDetail.ToPB(),
	}, nil
}

func (svr *OrderServer) CancelOrder(ctx context.Context, req *ordersvcpb.CancelOrderRequest) (
	*ordersvcpb.CancelOrderResponse, error,
) {
	// Cancel Order 目前只用于 cancel no-show / deposit order.
	// 其他类型的还没有接入到 svc-order-v2 中.
	od, err := svr.orderService.GetDetail(ctx, req.GetOrderId())
	if err != nil {
		return nil, err
	}

	if !od.Order.IsNoShow() && !od.Order.IsDeposit() {
		return nil, status.Error(codes.InvalidArgument, "unsupported order type")
	}

	if od.Order.GetPaidAmount().GreaterThan(decimal.Zero) {
		return nil, status.Error(codes.FailedPrecondition, "order has paid / partially paid")
	}

	reason := "canceled no show"
	if od.Order.IsDeposit() {
		reason = "canceled deposit"
	}

	if err := svr.orderService.CancelOrder(ctx, od.GetID(), reason); err != nil {
		return nil, err
	}

	return &ordersvcpb.CancelOrderResponse{}, nil
}

func (svr *OrderServer) CreateInvoiceID(
	ctx context.Context, req *ordersvcpb.CreateInvoiceIDRequest,
) (*ordersvcpb.CreateInvoiceIDResponse, error) {
	if req.GetSourceType() != orderpb.OrderSourceType_APPOINTMENT {
		return nil, status.Error(codes.InvalidArgument, "unsupported source type")
	}

	invoiceID, err := svr.orderService.CreateInvoiceID(ctx, req)
	if err != nil {
		return nil, err
	}

	// TODO(chi): 这里来触发一下 Deposit PreAuth 的逻辑.

	return &ordersvcpb.CreateInvoiceIDResponse{InvoiceId: invoiceID}, nil
}

// GetRootOrder gets the root order, which doesn't refer to any other order.
func (svr *OrderServer) getRootOrder(ctx context.Context, orderID int64) (*model.Order, error) {
	order, err := svr.orderService.GetOrder(ctx, orderID)
	if err != nil {
		return nil, err
	}

	if order.OrderRefID == 0 {
		return order, nil
	}

	// 目前最多只有一层，所以这里先不用循环/递归.
	order, err = svr.orderService.GetOrder(ctx, order.OrderRefID)
	if err != nil {
		return nil, err
	}

	return order, nil
}
