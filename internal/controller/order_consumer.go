package controller

import (
	"context"

	"go.uber.org/zap"
	"google.golang.org/protobuf/types/known/timestamppb"

	eventbus "github.com/MoeGolibrary/go-lib/event-bus"
	"github.com/MoeGolibrary/go-lib/zlog"
	eventbuspb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/event_bus/v1"
	orderpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/order/v1"
	marketingsvcpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/marketing/v1"
	"github.com/MoeGolibrary/moego-svc-order-v2/config"
	"github.com/MoeGolibrary/moego-svc-order-v2/internal/model"
	"github.com/MoeGolibrary/moego-svc-order-v2/internal/service"
)

type OrderConsumer struct {
	orderSvc service.OrderService

	cli marketingsvcpb.DiscountCodeServiceClient
}

func newOrderConsumer(
	conf *config.EventBusConfig,
	order service.OrderService,
	cli marketingsvcpb.DiscountCodeServiceClient,
) *eventbus.Consumer {
	oc := &OrderConsumer{
		orderSvc: order,
		cli:      cli,
	}

	consumer, err := eventbus.NewConsumer(
		conf.OrderConsumer(),
		oc.handleEvent,
	)
	if err != nil {
		zlog.Default().Fatal("new order consumer error", zap.Error(err))
	}

	return consumer
}

func (oc *OrderConsumer) Name() string { return "OrderConsumer" }

func (oc *OrderConsumer) handleEvent(
	ctx context.Context,
	eventID string,
	_ *timestamppb.Timestamp,
	eventType eventbuspb.EventType,
	eventData *eventbuspb.EventData,
) error {
	switch eventType {
	case eventbuspb.EventType_ORDER_COMPLETED:
		return oc.handleOrderCompleted(ctx, eventID, eventType, eventData)

	case eventbuspb.EventType_REFUND_ORDER_COMPLETED:
		return oc.handleRefundOrderCompleted(ctx, eventID, eventType, eventData)

	default:
		zlog.Info(
			ctx,
			"skip not interested event",
			zap.String("consumer", oc.Name()),
			zap.String("eventID", eventID),
			zap.String("eventType", eventType.String()),
		)

		return nil
	}
}

func (oc *OrderConsumer) handleOrderCompleted(
	ctx context.Context,
	eventID string,
	eventType eventbuspb.EventType,
	eventData *eventbuspb.EventData,
) error {
	// 处理 V4 尾款单全额之后，依然残留有 Unpaid Deposit Order 的问题.
	// 为了避免在尾款单全额之后， Unpaid Deposit Order 的金额被计入 Customer 的未支付金额，之类直接将
	// 多余的 unpaid deposit order cancel。
	event := eventData.GetOrderEvent()
	handlerCtx := zlog.NewContext(
		ctx,
		zap.String("consumer", oc.Name()),
		zap.String("eventID", eventID),
		zap.Stringer("eventType", eventType),
		zap.Int64("businessID", event.GetBusinessId()),
		zap.Int64("companyID", event.GetCompanyId()),
		zap.Int64("orderID", event.GetId()),
	)

	// V4 及以后才有 Deposit Order
	if model.OrderVersion(event.GetOrderVersion()).Lt(model.OrderVersionImmutableOrder) {
		zlog.Info(
			handlerCtx, "skip unsupported order version",
			zap.Stringer("orderVersion", model.OrderVersion(event.GetOrderVersion())),
		)

		return nil
	}

	// 只有 ORIGIN order 才需要处理这个逻辑.
	// NoShow 和尾款单都是 ORIGIN order.
	if event.GetOrderType() != orderpb.OrderModel_ORIGIN {
		zlog.Info(
			handlerCtx, "skip unsupported order type",
			zap.Stringer("orderType", event.GetOrderType()),
		)

		return nil
	}

	// 目前只有 Appointment 有这个需求.
	// 后面可能有 Reservation / Fulfillment.
	switch event.GetSourceType() {
	case orderpb.OrderSourceType_APPOINTMENT, orderpb.OrderSourceType_NO_SHOW:
		// Pass.

	default:
		zlog.Info(
			handlerCtx, "skip unsupported source type",
			zap.Stringer("sourceType", event.GetSourceType()),
		)

		return nil
	}

	allOrders, err := oc.orderSvc.ListByAppointment(handlerCtx, event.GetSourceId())
	if err != nil {
		zlog.Error(handlerCtx, "failed to list orders by appointment", zap.Error(err))
		return err
	}

	for _, order := range allOrders {
		if !order.IsDeposit() || order.IsCompleted() {
			continue
		}

		// 未完成的 DepositOrder.
		if err := oc.orderSvc.CancelOrder(handlerCtx, order.ID, model.OrderCancelReasonInvoiceClosed); err != nil {
			zlog.Error(
				handlerCtx, "failed to cancel unfinished deposit order",
				zap.Error(err),
				zap.Int64("depositOrderID", order.ID),
			)

			return err
		}

		zlog.Info(handlerCtx, "cancel unfinished deposit order successful", zap.Int64("depositOrderID", order.ID))
	}

	return nil
}

func (oc *OrderConsumer) handleRefundOrderCompleted(
	ctx context.Context,
	eventID string,
	eventType eventbuspb.EventType,
	eventData *eventbuspb.EventData,
) error {
	// 处理 FullyRefund 的时候，退还 Discount 的逻辑。
	// 需要注意的是，目前 Discount 是 Apply 到 Appointment 上的，退 Discount 也是
	// 整个 Appointment 层面退。
	// 因此，目前一个 Appointment 对应多个 Order 时，任意一个 Order Fully Refund 都会
	// 导致整个 Appointment 的所有 Discount 的使用记录被删除（即 Discount 使用次数还原）
	// 但是不会影响其他订单上的记录。
	//
	// 这里预期在后面的版本，可以跟 ERP + CRM 一起协作修复，让 DiscountCode 的使用记录能
	// 关联 Order 层级。
	event := eventData.GetRefundOrderEvent()
	handlerCtx := zlog.NewContext(
		ctx,
		zap.String("consumer", oc.Name()),
		zap.String("eventID", eventID),
		zap.Stringer("eventType", eventType),
		zap.Int64("businessID", event.GetBusinessId()),
		zap.Int64("companyID", event.GetCompanyId()),
		zap.Int64("orderID", event.GetOrderId()),
		zap.Int64("refundOrderID", event.GetRefundOrderId()),
	)

	zlog.Info(handlerCtx, "received refund order completed event")

	order, err := oc.orderSvc.GetOrder(ctx, event.GetOrderId())
	if err != nil {
		zlog.Error(handlerCtx, "failed to get order detail", zap.Error(err))
		return err
	}

	if !order.IsFinalStatus() {
		zlog.Info(
			handlerCtx,
			"order is not final status, skip return discount codes",
			zap.Stringer("orderStatus", order.Status),
		)

		return nil
	}

	if !order.GetRefundableAmount().IsZero() {
		zlog.Info(
			handlerCtx,
			"order is not fully refunded, skip return discount codes",
			zap.Stringer("refundableAmount", order.GetRefundableAmount()),
		)

		return nil
	}

	if err := oc.returnDiscount(ctx, order); err != nil {
		zlog.Error(
			handlerCtx,
			"failed to return discount codes",
			zap.Int64("sourceID", order.SourceID),
			zap.Error(err),
		)

		return err
	}

	zlog.Info(
		handlerCtx,
		"discount codes returned",
		zap.Int64("sourceID", order.SourceID),
	)

	return nil
}

func (oc *OrderConsumer) returnDiscount(ctx context.Context, order *model.Order) error {
	_, err := oc.cli.DeleteDiscountCodeLog(
		ctx, &marketingsvcpb.DeleteDiscountCodeLogInput{
			RedeemId: order.SourceID,
		},
	)

	return err
}
