package controller

import (
	"context"

	ordersvcpb2 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/order/v2"
	"github.com/MoeGolibrary/moego-svc-order-v2/internal/service"
)

type ExportServer struct {
	ordersvcpb2.UnimplementedExportServiceServer

	orderService       service.OrderService
	refundOrderService service.RefundOrderService
}

func NewExportServer(
	orderService service.OrderService,
	refundOrderService service.RefundOrderService,
) ordersvcpb2.ExportServiceServer {
	return &ExportServer{
		orderService:       orderService,
		refundOrderService: refundOrderService,
	}
}

func (svr *ExportServer) ExportOrderPaymentDetailList(
	ctx context.Context, req *ordersvcpb2.ExportOrderPaymentDetailListRequest,
) (*ordersvcpb2.ExportOrderPaymentDetailListResponse, error) {
	fileID, err := svr.orderService.ExportOrderPaymentDetailList(ctx, req)
	if err != nil {
		return nil, err
	}

	return &ordersvcpb2.ExportOrderPaymentDetailListResponse{
		FileId: fileID,
	}, nil
}

func (svr *ExportServer) ExportRefundOrderPaymentDetailList(
	ctx context.Context, req *ordersvcpb2.ExportRefundOrderPaymentDetailListRequest,
) (*ordersvcpb2.ExportRefundOrderPaymentDetailListResponse, error) {
	fileID, err := svr.refundOrderService.ExportRefundOrderPaymentDetailList(ctx, req)
	if err != nil {
		return nil, err
	}

	return &ordersvcpb2.ExportRefundOrderPaymentDetailListResponse{
		FileId: fileID,
	}, nil
}
