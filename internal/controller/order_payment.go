package controller

import (
	"context"
	"sort"

	"github.com/samber/lo"
	"go.uber.org/zap"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	"github.com/MoeGolibrary/go-lib/zlog"
	orderpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/order/v1"
	ordersvcv1pb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/order/v1"
	ordersvcv2pb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/order/v2"
	"github.com/MoeGolibrary/moego-svc-order-v2/internal/model"
)

func (svr *OrderServerV2) CombinedPayOrder(ctx context.Context, req *ordersvcv2pb.CombinedPayOrderRequest) (
	*ordersvcv2pb.CombinedPayOrderResponse, error,
) {
	if len(req.GetCombinedItems()) <= 0 { //nolint:gocritic //pb定义的内容直接判断
		return nil, status.Errorf(codes.InvalidArgument, "combined item is empty")
	}

	txnID, opms, err := svr.orderPaymentService.CombinedPayOrder(ctx, req)
	if err != nil {
		return nil, err
	}

	return &ordersvcv2pb.CombinedPayOrderResponse{
		OrderPaymentModels: opms,
		TransactionId:      txnID,
	}, nil
}

func (svr *OrderServerV2) GetOrderGuid(ctx context.Context,
	req *ordersvcv2pb.GetOrderGuidRequest) (
	*ordersvcv2pb.GetOrderGuidResponse, error,
) {
	guid, err := svr.orderPaymentService.GetOrderGuid(ctx, req)
	if err != nil {
		return nil, err
	}

	return &ordersvcv2pb.GetOrderGuidResponse{
		Guid: guid,
	}, nil
}

func (svr *OrderServerV2) ListOrdersForCombinedPayment(
	ctx context.Context,
	req *ordersvcv2pb.ListOrdersForCombinedPaymentRequest,
) (
	*ordersvcv1pb.ListOrderDetailResponse, error,
) {
	// 查询指定 customer 下
	// 近 60 天 内（含今日）appointment end date 的所有 invoice
	// 且状态为 Unpaid / Partially Paid
	zlog.Info(ctx, "ListOrdersForCombinedPayment", zap.Int64("customer_id", req.GetCustomerId()))

	orders, err := svr.orderPaymentService.ListOrdersForCombinedPayment(ctx, req)
	if err != nil {
		return nil, err
	}

	orderDetails := make([]*model.OrderDetail, 0, len(orders))

	for _, od := range orders {
		orderDetail, attachErr := svr.orderService.AttachDetail(ctx, od)
		if attachErr != nil {
			return nil, attachErr
		}

		orderDetails = append(orderDetails, orderDetail)
	}

	return &ordersvcv1pb.ListOrderDetailResponse{
		Orders: lo.Map(
			orderDetails,
			func(it *model.OrderDetail, _ int) *orderpb.OrderDetailModelV1 { return it.ToPB() },
		),
		// TODO(ritchie):  退款明细
	}, nil
}

func (svr *OrderServerV2) ListOrderDetail(
	ctx context.Context,
	req *ordersvcv2pb.ListOrderDetailRequest,
) (
	*ordersvcv1pb.ListOrderDetailResponse, error,
) {
	orderIDs := req.GetOrderIds()
	if len(orderIDs) == 0 {
		if req.GetGuid() == "" { // 不能同时为空
			return nil, status.Errorf(codes.InvalidArgument, "order ids and guid are empty")
		}
		// 如果 guid 不为空，查询解析到的 order ids
		parsedOrderIDs, err := svr.orderPaymentService.GetOrderIDsByGUID(ctx, req.GetGuid())
		if err != nil {
			return nil, err
		}

		orderIDs = parsedOrderIDs
	}

	orders, err := svr.orderService.BatchGetOrders(ctx, orderIDs)
	if err != nil {
		return nil, err
	}

	orderDetails := make([]*model.OrderDetail, 0, len(orders))

	for _, od := range orders {
		orderDetail, attachErr := svr.orderService.AttachDetail(ctx, od)
		if attachErr != nil {
			return nil, attachErr
		}

		orderDetails = append(orderDetails, orderDetail)
	}

	sort.Slice(
		orderDetails,
		func(i, j int) bool {
			// 新订单在前.
			return orderDetails[i].GetID() > orderDetails[j].GetID()
		},
	)

	// TODO(ritchie):  当前没有 extra order 和 refund order，暂不考虑
	return &ordersvcv1pb.ListOrderDetailResponse{
		Orders: lo.Map(
			orderDetails,
			func(it *model.OrderDetail, _ int) *orderpb.OrderDetailModelV1 { return it.ToPB() },
		),
		// TODO(ritchie):  退款明细
	}, nil
}
