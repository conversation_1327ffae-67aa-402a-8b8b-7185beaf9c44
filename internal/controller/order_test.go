package controller

import (
	"context"
	"testing"

	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/suite"

	ordersvcpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/order/v1"
	mocks "github.com/MoeGolibrary/moego-svc-order-v2/internal/mocks/service"
	"github.com/MoeGolibrary/moego-svc-order-v2/internal/model"
)

type OrderControllerTestSuite struct {
	suite.Suite

	server             *OrderServer
	orderService       *mocks.OrderService
	refundOrderService *mocks.RefundOrderService
}

func TestOrderController(t *testing.T) {
	suite.Run(t, new(OrderControllerTestSuite))
}

func (ts *OrderControllerTestSuite) SetupTest() {
	ts.orderService = mocks.NewOrderService(ts.T())
	ts.refundOrderService = mocks.NewRefundOrderService(ts.T())

	ts.server = &OrderServer{
		orderService:       ts.orderService,
		refundOrderService: ts.refundOrderService,
	}
}

func (ts *OrderControllerTestSuite) TestListOrderDetail_with_origin_orderID() {
	req := &ordersvcpb.ListOrderDetailRequest{
		BusinessId:    23333,
		OriginOrderId: 123456,
	}

	mockOrder := &model.Order{ID: req.GetOriginOrderId(), OrderRefID: 0, BusinessID: req.GetBusinessId()}

	ts.orderService.EXPECT().GetOrder(mock.Anything, req.GetOriginOrderId()).
		Return(mockOrder, nil)

	ts.orderService.EXPECT().AttachDetail(mock.Anything, mock.Anything).
		Return(&model.OrderDetail{Order: mockOrder}, nil)

	ts.orderService.EXPECT().ListTailDetail(mock.Anything, mockOrder.ID).Return([]*model.OrderDetail{}, nil)
	ts.refundOrderService.EXPECT().ListRefundDetail(mock.Anything, mockOrder.ID).Return(
		[]*model.RefundOrderDetail{}, nil,
	)

	_, err := ts.server.ListOrderDetail(context.Background(), req)
	ts.Require().NoError(err)
}

func (ts *OrderControllerTestSuite) TestListOrderDetail_with_non_origin_orderID() {
	req := &ordersvcpb.ListOrderDetailRequest{
		BusinessId:    23333,
		OriginOrderId: 123456,
	}

	orderRefID := req.GetOriginOrderId() + 6666

	mockOrder := &model.Order{ID: req.GetOriginOrderId(), OrderRefID: orderRefID, BusinessID: req.GetBusinessId()}
	mockRootOrder := &model.Order{ID: orderRefID, OrderRefID: 0, BusinessID: req.GetBusinessId()}

	ts.orderService.EXPECT().GetOrder(mock.Anything, req.GetOriginOrderId()).Return(mockOrder, nil)
	ts.orderService.EXPECT().GetOrder(mock.Anything, orderRefID).Return(mockRootOrder, nil)

	ts.orderService.EXPECT().AttachDetail(mock.Anything, mock.Anything).
		Return(&model.OrderDetail{Order: mockRootOrder}, nil)

	ts.orderService.EXPECT().ListTailDetail(mock.Anything, mockRootOrder.ID).Return([]*model.OrderDetail{}, nil)
	ts.refundOrderService.EXPECT().ListRefundDetail(mock.Anything, mockRootOrder.ID).Return(
		[]*model.RefundOrderDetail{}, nil,
	)

	_, err := ts.server.ListOrderDetail(context.Background(), req)
	ts.Require().NoError(err)
}
