package controller

import (
	"context"

	"go.uber.org/zap"
	"google.golang.org/protobuf/types/known/timestamppb"

	eventbus "github.com/MoeGolibrary/go-lib/event-bus"
	"github.com/MoeGolibrary/go-lib/zlog"
	eventbuspb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/event_bus/v1"
	orderpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/order/v1"
	ordersvcv2pb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/order/v2"
	"github.com/MoeGolibrary/moego-svc-order-v2/config"
	"github.com/MoeGolibrary/moego-svc-order-v2/internal/model"
	"github.com/MoeGolibrary/moego-svc-order-v2/internal/service"
)

type OrderTipsConsumer struct {
	svc service.TipsSplitService
}

func NewOrderTipsConsumer(
	conf *config.EventBusConfig,
	tipsSplitSvc service.TipsSplitService,
) *eventbus.Consumer {
	oc := &OrderTipsConsumer{
		svc: tipsSplitSvc,
	}

	consumer, err := eventbus.NewConsumer(
		conf.OrderTipsConsumer(),
		oc.handleEvent,
	)
	if err != nil {
		zlog.Default().Fatal("new order tips consumer error", zap.Error(err))
	}

	return consumer
}

func (oc *OrderTipsConsumer) Name() string { return "OrderTipsConsumer" }

func (oc *OrderTipsConsumer) handleEvent(
	ctx context.Context,
	eventID string,
	_ *timestamppb.Timestamp,
	eventType eventbuspb.EventType,
	eventData *eventbuspb.EventData,
) error {
	defer func() {
		if err := recover(); err != nil {
			zlog.Error(ctx, "Panic in OrderTipsConsumer", zap.Any("err", err))
		}
	}()

	zlog.Info(ctx,
		"receive order completed event",
		zap.String("consumer", oc.Name()),
		zap.String("eventID", eventID),
		zap.String("eventType", eventType.String()),
	)

	if eventType != eventbuspb.EventType_ORDER_COMPLETED {
		return nil
	}

	event := eventData.GetOrderEvent()
	handlerCtx := zlog.NewContext(
		ctx,
		zap.String("consumer", oc.Name()),
		zap.String("eventID", eventID),
		zap.Stringer("eventType", eventType),
		zap.Int64("businessID", event.GetBusinessId()),
		zap.Int64("companyID", event.GetCompanyId()),
		zap.Int64("orderID", event.GetId()),
		zap.Stringer("sourceType", event.GetSourceType()),
		zap.Int32("orderVersion", event.GetOrderVersion()),
	)

	if event.GetOrderVersion() == int32(model.OrderVersionLegacy) ||
		event.GetSourceType() != orderpb.OrderSourceType_APPOINTMENT {
		zlog.Info(handlerCtx, "keep order level split for version 0 and non-appointment source type")
		return nil
	}
	// 0. 只针对走关单流程的 order 进行 appt level 的 split
	// 1. 无，则直接创建 tips split 记录, split method根据 legacy split record 推算：
	// 1.1 如果有order_tip_split_record记录，则记录为 customized
	// 1.2 如果没有order_tip_split_record记录, 按默认business payroll setting 配置生成 appt level 最新分配结果
	// 2. 有 根据保存的 split method（customize 时，按默认business payroll setting 配置）刷新分配规则，并累加到 tips split details
	currentModel, err := oc.svc.Get(ctx, event.GetSourceId(), event.GetSourceType())
	if err != nil {
		zlog.Error(handlerCtx, "failed to get tips split", zap.Error(err))
		return err
	}

	tipsSplitConfig := &orderpb.TipsSplitModel_TipsSplitConfig{}

	if currentModel.SplitConfig.SplitMethod == orderpb.SplitTipsMethod_SPLIT_TIPS_METHOD_BY_EQUALLY ||
		currentModel.SplitConfig.SplitMethod == orderpb.SplitTipsMethod_SPLIT_TIPS_METHOD_BY_SERVICE {
		tipsSplitConfig.SplitMethod = currentModel.SplitConfig.SplitMethod
	}

	req := &ordersvcv2pb.EditStaffAndTipsSplitRequest{
		BusinessId:      event.GetBusinessId(),
		CompanyId:       event.GetCompanyId(),
		SourceType:      event.GetSourceType(),
		SourceId:        event.GetSourceId(),
		TipsSplitConfig: tipsSplitConfig,
	}

	tipsSplitModel, err := oc.svc.EditStaffAndUpsertTipsSplit(ctx, req)
	if err != nil {
		zlog.Error(handlerCtx, "failed to upsert tips split", zap.Error(err))
		return err
	}

	// 设置更新通知信息到 redis
	err = oc.svc.SetStatus(handlerCtx, tipsSplitModel.SourceType, tipsSplitModel.SourceID)
	if err != nil {
		zlog.Error(handlerCtx,
			"failed to sync tips split status to redis",
			zap.Int64("tipsSplitID", tipsSplitModel.ID),
			zap.Error(err),
		)
	}

	return nil
}
