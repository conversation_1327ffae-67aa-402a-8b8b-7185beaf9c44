package controller

import (
	"context"

	"go.uber.org/zap"
	"google.golang.org/protobuf/types/known/timestamppb"

	eventbus "github.com/MoeGolibrary/go-lib/event-bus"
	"github.com/MoeGolibrary/go-lib/zlog"
	eventbuspb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/event_bus/v1"
	"github.com/MoeGolibrary/moego-svc-order-v2/config"
	"github.com/MoeGolibrary/moego-svc-order-v2/internal/service/handler"
)

type PaymentConsumer struct {
	paymentEventHandler *handler.PaymentEventHandler
}

func NewPaymentConsumer(
	conf *config.EventBusConfig,
	paymentEventHandler *handler.PaymentEventHandler,
) *eventbus.Consumer {
	pc := &PaymentConsumer{
		paymentEventHandler: paymentEventHandler,
	}

	consumer, err := eventbus.NewConsumer(
		conf.PaymentConsumer(),
		pc.handleEvent,
	)
	if err != nil {
		zlog.Default().Fatal("new payment consumer failed", zap.Error(err))
	}

	return consumer
}

func (pc *PaymentConsumer) handleEvent(
	ctx context.Context,
	eventID string,
	createTime *timestamppb.Timestamp,
	eventType eventbuspb.EventType,
	eventData *eventbuspb.EventData,
) error {
	return pc.paymentEventHandler.Consume(ctx, eventID, createTime, eventType, eventData)
}
