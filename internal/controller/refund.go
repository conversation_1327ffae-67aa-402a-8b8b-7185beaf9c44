package controller

import (
	"context"

	"github.com/MoeGolibrary/go-lib/merror"
	errorspb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/errors/v1"
	ordersvcpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/order/v1"
)

func (svr *OrderServer) GetRefundOrderDetail(ctx context.Context, req *ordersvcpb.GetRefundOrderDetailRequest) (
	*ordersvcpb.GetRefundOrderDetailResponse, error,
) {
	rod, err := svr.refundOrderService.GetRefundDetail(ctx, req.GetRefundOrderId())
	if err != nil {
		return nil, err
	}

	if rod.RefundOrder.BusinessID != req.GetBusinessId() {
		return nil, merror.NewBizError(errorspb.Code_CODE_FORBIDDEN, "business id not match")
	}

	return &ordersvcpb.GetRefundOrderDetailResponse{RefundOrder: rod.ToPB()}, nil
}

func (svr *OrderServer) PreviewRefundOrder(ctx context.Context, req *ordersvcpb.PreviewRefundOrderRequest) (
	*ordersvcpb.PreviewRefundOrderResponse, error,
) {
	od, err := svr.orderService.GetDetail(ctx, req.GetOrderId())
	if err != nil {
		return nil, err
	}

	return svr.refundOrderService.PreviewRefundOrder(ctx, od, req, svr.orderService)
}

func (svr *OrderServer) PreviewRefundOrderPayments(
	ctx context.Context,
	req *ordersvcpb.PreviewRefundOrderPaymentsRequest,
) (*ordersvcpb.PreviewRefundOrderPaymentsResponse, error) {
	return svr.refundOrderService.PreviewRefundOrderPayments(ctx, req)
}

func (svr *OrderServer) RefundOrder(ctx context.Context, req *ordersvcpb.RefundOrderRequest) (
	*ordersvcpb.RefundOrderResponse, error,
) {
	od, err := svr.orderService.GetDetail(ctx, req.GetOrderId())
	if err != nil {
		return nil, err
	}

	if od.Order.BusinessID != req.GetBusinessId() {
		return nil, merror.NewBizError(errorspb.Code_CODE_FORBIDDEN, "business id not match")
	}

	return svr.refundOrderService.RefundOrder(ctx, od, req, svr.orderService)
}
