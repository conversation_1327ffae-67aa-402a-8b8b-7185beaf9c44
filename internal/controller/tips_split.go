package controller

import (
	"context"

	"github.com/samber/lo"
	"go.uber.org/zap"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	"github.com/MoeGolibrary/go-lib/common/proto/money"
	"github.com/MoeGolibrary/go-lib/zlog"
	orderpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/order/v1"
	ordersvcv2pb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/order/v2"
	"github.com/MoeGolibrary/moego-svc-order-v2/internal/model"
)

func (svr *TipsSplitServiceServer) GetTipsSplitDetails(ctx context.Context, req *ordersvcv2pb.GetTipsSplitRequest) (
	*ordersvcv2pb.GetTipsSplitResponse, error,
) {
	if req.SourceType != orderpb.OrderSourceType_APPOINTMENT {
		return nil, status.Error(codes.InvalidArgument, "not supported source type")
	}

	tipsSplitModel, err := svr.tipsSplitService.Get(ctx, req.SourceId, req.SourceType)
	if err != nil {
		return nil, err
	}

	tipSplitMode := orderpb.TipsSplitMode_TIPS_SPLIT_MODE_APPT
	if tipsSplitModel.ID == 0 {
		tipSplitMode = orderpb.TipsSplitMode_TIPS_SPLIT_MODE_ORDER
	}

	// get collected orders
	orders, err := svr.orderService.BatchGetOrders(ctx, tipsSplitModel.CollectedOrderIDs)
	if err != nil {
		return nil, err
	}

	// get related refund orders
	allRefundOrders := make([]*model.RefundOrder, 0, len(orders))

	for _, od := range orders {
		refundOrders, err := svr.refundOrderService.ListRefund(ctx, od.ID)
		if err != nil {
			return nil, err
		}

		allRefundOrders = append(allRefundOrders, refundOrders...)
	}

	return &ordersvcv2pb.GetTipsSplitResponse{
		TipsSplitMode: tipSplitMode,
		TipsSplit:     tipsSplitModel.ToPB(),
		TipsSplitDetails: lo.Map(
			tipsSplitModel.SplitConfig.StaffConfigs,
			func(staffConfig *orderpb.StaffTipConfig, _ int) *orderpb.TipsSplitDetailModel {
				return (&model.TipsSplitDetail{
					StaffID:      staffConfig.StaffId,
					SplitAmount:  money.ToDecimal(staffConfig.Amount),
					CurrencyCode: staffConfig.Amount.GetCurrencyCode(),
				}).ToPB()
			},
		),
		Orders: lo.Map(
			orders,
			func(it *model.Order, _ int) *orderpb.OrderModelV1 { return it.ToPB() },
		),
		RefundOrders: lo.Map(
			allRefundOrders,
			func(it *model.RefundOrder, _ int) *orderpb.RefundOrderModel { return it.ToPB() },
		),
	}, nil
}

func (svr *TipsSplitServiceServer) PreviewEditTipsSplit(
	ctx context.Context, req *ordersvcv2pb.EditStaffAndTipsSplitRequest,
) (*ordersvcv2pb.PreviewEditTipsSplitResponse, error) {
	result := &ordersvcv2pb.PreviewEditTipsSplitResponse{}

	computedResult, err := svr.tipsSplitService.ComputeEditStaffAndTips(ctx, req)
	if err != nil {
		return nil, err
	}

	result.SplitConfig = &orderpb.TipsSplitModel_TipsSplitConfig{
		SplitMethod:       req.TipsSplitConfig.SplitMethod,
		BusinessTipAmount: computedResult.BusinessTipAmount,
		StaffConfigs:      computedResult.StaffTipsConfigs,
	}

	return result, nil
}

func (svr *TipsSplitServiceServer) EditTipsSplit(ctx context.Context, req *ordersvcv2pb.EditStaffAndTipsSplitRequest) (
	*ordersvcv2pb.EditTipsSplitResponse, error,
) {
	splitRecord, err := svr.tipsSplitService.EditStaffAndUpsertTipsSplit(ctx, req)
	if err != nil {
		return nil, err
	}

	// get split details
	splitDetails, err := svr.tipsSplitService.GetTipsSplitDetails(ctx, splitRecord.ID)
	if err != nil {
		return nil, err
	}

	// populate split details to response
	return &ordersvcv2pb.EditTipsSplitResponse{
		TipsSplitDetails: lo.Map(
			splitDetails,
			func(it *model.TipsSplitDetail, _ int) *orderpb.TipsSplitDetailModel { return it.ToPB() },
		),
		BusinessTipAmount: splitRecord.SplitConfig.GetBusinessTipAmount(),
	}, nil
}

func (svr *TipsSplitServiceServer) GetTipsSplitChangedStatus(
	ctx context.Context,
	req *ordersvcv2pb.GetTipsSplitChangedStatusRequest,
) (
	*ordersvcv2pb.GetTipsSplitChangedStatusResponse, error,
) {
	// get tips split changed status
	tipsSplitChangedStatus, err := svr.tipsSplitService.GetStatus(ctx, req.SourceType, req.SourceId)
	if err != nil {
		return nil, err
	}

	zlog.Info(
		ctx, "tips split changed status",
		zap.String("sourceType", req.SourceType.String()),
		zap.Int64("sourceId", req.SourceId),
		zap.Bool("isChanged", tipsSplitChangedStatus),
	)

	return &ordersvcv2pb.GetTipsSplitChangedStatusResponse{
		IsChanged: tipsSplitChangedStatus,
	}, nil
}

func (svr *TipsSplitServiceServer) ClearTipsSplitChangedStatus(
	ctx context.Context,
	req *ordersvcv2pb.ClearTipsSplitChangedStatusRequest,
) (
	*ordersvcv2pb.ClearTipsSplitChangedStatusResponse, error,
) {
	err := svr.tipsSplitService.DelStatus(ctx, req.SourceType, req.SourceId)
	if err != nil {
		return nil, err
	}

	return &ordersvcv2pb.ClearTipsSplitChangedStatusResponse{}, nil
}

func (svr *TipsSplitServiceServer) ListTipsSplitDetailsBySource(
	ctx context.Context, req *ordersvcv2pb.ListTipsSplitDetailsBySourceRequest,
) (*ordersvcv2pb.ListTipsSplitDetailsBySourceResponse, error) {
	sources := make(map[int64]orderpb.OrderSourceType, len(req.GetSources()))
	for _, src := range req.GetSources() {
		sources[src.GetSourceId()] = src.GetSourceType()
	}

	splitDetails, err := svr.tipsSplitService.ListTipsSplitDetailsBySource(ctx, sources)
	if err != nil {
		return nil, err
	}

	return &ordersvcv2pb.ListTipsSplitDetailsBySourceResponse{
		TipsSplitDetails: splitDetails,
	}, nil
}
