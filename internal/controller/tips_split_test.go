package controller

import (
	"context"
	"testing"

	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/suite"
	"google.golang.org/genproto/googleapis/type/money"

	orderpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/order/v1"
	ordersvcv2pb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/order/v2"
	mocks "github.com/MoeGolibrary/moego-svc-order-v2/internal/mocks/service"
	"github.com/MoeGolibrary/moego-svc-order-v2/internal/model"
)

type TipsSplitControllerTestSuite struct {
	suite.Suite

	TipsSplitServiceServer *TipsSplitServiceServer
	tipsSplitService       *mocks.TipsSplitService
	orderService           *mocks.OrderService
	refundOrderService     *mocks.RefundOrderService
}

func TestTipsSplitController(t *testing.T) {
	suite.Run(t, new(TipsSplitControllerTestSuite))
}

func (ts *TipsSplitControllerTestSuite) SetupTest() {
	ts.orderService = mocks.NewOrderService(ts.T())
	ts.refundOrderService = mocks.NewRefundOrderService(ts.T())
	ts.tipsSplitService = mocks.NewTipsSplitService(ts.T())

	ts.TipsSplitServiceServer = &TipsSplitServiceServer{
		tipsSplitService:   ts.tipsSplitService,
		orderService:       ts.orderService,
		refundOrderService: ts.refundOrderService,
	}
}

func (ts *TipsSplitControllerTestSuite) TestGetTipsSplitDetails() {
	req := &ordersvcv2pb.GetTipsSplitRequest{
		SourceId:   123456,
		SourceType: orderpb.OrderSourceType_APPOINTMENT,
	}
	mockTipsSplitModel := &model.TipsSplit{
		ID:         10,
		SourceID:   req.SourceId,
		SourceType: req.SourceType,
		SplitConfig: &orderpb.TipsSplitModel_TipsSplitConfig{
			StaffConfigs: []*orderpb.StaffTipConfig{
				{
					StaffId: 1,
					Amount: &money.Money{
						CurrencyCode: "USD",
						Units:        400,
						Nanos:        0,
					},
					Percentage: 0.4,
				},
				{
					StaffId: 2,
					Amount: &money.Money{
						CurrencyCode: "USD",
						Units:        600,
						Nanos:        0,
					},
					Percentage: 0.6,
				},
			},
		},
	}
	ts.tipsSplitService.EXPECT().Get(
		mock.Anything,
		req.SourceId,
		req.SourceType,
	).Return(mockTipsSplitModel, nil)

	ts.orderService.EXPECT().BatchGetOrders(
		mock.Anything,
		mockTipsSplitModel.CollectedOrderIDs,
	).Return(nil, nil)

	resp, err := ts.TipsSplitServiceServer.GetTipsSplitDetails(context.Background(), req)

	ts.Nil(err)
	ts.Equal(resp.TipsSplitMode, orderpb.TipsSplitMode_TIPS_SPLIT_MODE_APPT)
	ts.Equal(len(resp.TipsSplitDetails), len(mockTipsSplitModel.SplitConfig.StaffConfigs))
}
