package core

import (
	"github.com/samber/lo"
	"github.com/shopspring/decimal"
)

func CalculateTax(taxBase, rate decimal.Decimal) decimal.Decimal {
	return RoundPostTaxAmount(taxBase.Mul(RateForCal(rate)))
}

func CalculateSubTotal(unitPrice decimal.Decimal, quantity int32) decimal.Decimal {
	unitPrice = RoundPreTaxAmount(unitPrice)

	// Unit price 不含税
	return RoundPreTaxAmount(unitPrice.Mul(decimal.NewFromInt32(quantity)))
}

// RateForCal 转换费率用于计算，各种费率（x%） 都只传入了百分号前的部分，在进行计算之前需要转换成无百分号的形式.
func RateForCal(rate decimal.Decimal) decimal.Decimal {
	return RoundRate(rate.Shift(-2))
}

// RoundRate 处理比例相关的舍入.
func RoundRate(rate decimal.Decimal) decimal.Decimal {
	return rate.Round(RatePrecision)
}

// RoundPostTaxAmount 处理含税金额的舍入, 含税金额使用四舍五入进行舍入.
func RoundPostTaxAmount(amount decimal.Decimal) decimal.Decimal {
	return amount.Round(AmountPrecision)
}

// RoundPreTaxAmount 处理非税金额的舍入，不含税的金额使用银行家算法舍入.
func RoundPreTaxAmount(amount decimal.Decimal) decimal.Decimal {
	return amount.RoundBank(AmountPrecision)
}

// summarizeAmount 用于聚合各种类型的某个金额字段.
func summarizeAmount[T any](
	slice []T,
	getter func(it T) decimal.Decimal,
) decimal.Decimal {
	return lo.Reduce(
		slice,
		func(amount decimal.Decimal, item T, _ int) decimal.Decimal {
			return amount.Add(getter(item))
		},
		decimal.Zero,
	)
}
