package core

import (
	"github.com/samber/lo"
	"github.com/shopspring/decimal"

	orderpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/order/v1"
	"github.com/MoeGolibrary/moego-svc-order-v2/internal/model"
)

// depositEngine 处理 deposit 分配相关逻辑，目前只有 OrderEngine 用到，暂时不导出。
type depositEngine struct {
	order         *model.Order
	depositDetail *model.DepositDetail
}

func newDepositEngine(order *model.Order, depositDetail *model.DepositDetail) *depositEngine {
	return &depositEngine{
		order:         order,
		depositDetail: depositDetail,
	}
}

// AllocateDeposit distributes the deposit amount to the items and the tips.
func (de *depositEngine) AllocateDeposit(items []*model.OrderItem, tips decimal.Decimal) (
	totalDeducted decimal.Decimal, tipsDeducted decimal.Decimal, changeLog *model.DepositChangeLog,
) {
	if de.depositDetail == nil || de.depositDetail.LatestChangeLog == nil {
		return decimal.Zero, decimal.Zero, nil
	}

	balance := de.depositDetail.LatestChangeLog.Balance
	if balance.IsZero() {
		return decimal.Zero, decimal.Zero, nil
	}

	totalDeducted = decimal.Zero
	tipsDeducted = decimal.Zero

	// 1. Allocate to the items by PriceItem mapping
	// For previously created deposit order, there's no price item mapping, the deposit will be allocated in step 2.
	matchedTargets := de.buildMatchedItemAllocationTargets(items)
	itemsDepositTotal := de.allocateAmountToItemsProportionally(balance, matchedTargets)
	balance = balance.Sub(itemsDepositTotal)
	totalDeducted = totalDeducted.Add(itemsDepositTotal)

	if balance.LessThanOrEqual(decimal.Zero) {
		return totalDeducted, tipsDeducted, de.buildDeductChangeLog(totalDeducted, balance)
	}

	// 2. Allocate the rest balance to the rest items
	restTargets := de.buildRestItemAllocationTargets(items)
	itemsRestDepositTotal := de.allocateAmountToItemsProportionally(balance, restTargets)
	balance = balance.Sub(itemsRestDepositTotal)
	totalDeducted = totalDeducted.Add(itemsRestDepositTotal)

	if balance.LessThanOrEqual(decimal.Zero) {
		return totalDeducted, tipsDeducted, de.buildDeductChangeLog(totalDeducted, balance)
	}

	// Finally allocate the rest balance to the tips
	if balance.LessThan(tips) {
		tipsDeducted = balance
	} else {
		tipsDeducted = tips
	}

	balance = balance.Sub(tipsDeducted)
	totalDeducted = totalDeducted.Add(tipsDeducted)

	return totalDeducted, tipsDeducted, de.buildDeductChangeLog(totalDeducted, balance)
}

type amountAllocationTarget interface {
	GetRequestedAmount() decimal.Decimal
	AllocateAmount(decimal.Decimal)
}

func (de *depositEngine) buildMatchedItemAllocationTargets(items []*model.OrderItem) []amountAllocationTarget {
	if len(de.depositDetail.DepositPriceItems) == 0 {
		return nil
	}

	type objectKey struct {
		ObjectType orderpb.ItemType
		ObjectID   int64
	}

	priceItemMap := lo.MapValues(
		lo.GroupBy(de.depositDetail.DepositPriceItems, func(item *model.PriceItem) objectKey {
			return objectKey{
				ObjectType: item.ObjectType,
				ObjectID:   item.ObjectID,
			}
		}),
		func(items []*model.PriceItem, _ objectKey) *model.PriceItem {
			// oe.depositDetail.DepositPriceItems is already normalized, should exactly have one item.
			return items[0]
		},
	)

	targets := make([]amountAllocationTarget, 0, len(items))

	groupedItems := lo.GroupBy(items, func(item *model.OrderItem) objectKey {
		return objectKey{
			ObjectType: item.GetItemType(),
			ObjectID:   item.ObjectID,
		}
	})
	for key, itemsOfKey := range groupedItems {
		priceItem := priceItemMap[key]
		if priceItem == nil ||
			priceItem.Operator != orderpb.PriceDetailModel_PriceItem_ADD ||
			!priceItem.Subtotal.IsPositive() {
			continue
		}

		// 一个 PriceItem 对应多个 OrderItem 的情况下，也有可能 SubTotal 不足，所以也要按比例分配 PriceItem.SubTotal 到每个 OrderItem
		requestTargets := lo.Map(itemsOfKey, func(item *model.OrderItem, _ int) amountAllocationTarget {
			return &priceDepositAllocationTarget{
				OrderItem:          item,
				MaxRequestedAmount: item.GetTotalAmount().Add(item.GetTaxAmount()).Sub(item.DepositAmount),
			}
		})
		de.allocateAmountToItemsProportionally(priceItem.Subtotal, requestTargets)
		targets = append(
			targets,
			lo.Map(requestTargets, func(target amountAllocationTarget, _ int) amountAllocationTarget {
				t, ok := target.(*priceDepositAllocationTarget)
				if !ok {
					// 理论上不会走到这里，上面构造的就是 priceDepositAllocationTarget
					return nil
				}

				return &depositAllocationOrderItemTarget{
					OrderItem:       t.OrderItem,
					RequestedAmount: t.RequestedAmount,
				}
			})...,
		)
	}

	return targets
}

func (de *depositEngine) buildRestItemAllocationTargets(items []*model.OrderItem) []amountAllocationTarget {
	targets := make([]amountAllocationTarget, 0, len(items))

	for _, item := range items {
		requestAmount := item.GetTotalAmount().Add(item.GetTaxAmount()).Sub(item.DepositAmount)
		if !requestAmount.IsPositive() {
			continue
		}

		targets = append(targets, &depositAllocationOrderItemTarget{
			OrderItem:       item,
			RequestedAmount: requestAmount,
		})
	}

	return targets
}

// 这个表示的是一个 PriceItem 对应多个 OrderItem 的时候，PriceItem 所 request 的金额的分配目标。
type priceDepositAllocationTarget struct {
	// 被分配的 OrderItem
	OrderItem *model.OrderItem
	// 可以分配的最大金额，也就是 OrderItem 剩余的金额
	MaxRequestedAmount decimal.Decimal
	// 从 PriceItem 实际分配下来的金额
	RequestedAmount decimal.Decimal
}

func (i *priceDepositAllocationTarget) GetRequestedAmount() decimal.Decimal {
	return i.MaxRequestedAmount
}

func (i *priceDepositAllocationTarget) AllocateAmount(amount decimal.Decimal) {
	i.RequestedAmount = amount
}

type depositAllocationOrderItemTarget struct {
	OrderItem       *model.OrderItem
	RequestedAmount decimal.Decimal
}

func (i *depositAllocationOrderItemTarget) GetRequestedAmount() decimal.Decimal {
	return i.RequestedAmount
}

func (i *depositAllocationOrderItemTarget) AllocateAmount(amount decimal.Decimal) {
	i.OrderItem.DepositAmount = i.OrderItem.DepositAmount.Add(amount)
}

// allocateAmountToItemsProportionally allocates the maximum amount to the items proportionally.
// Each target has a requested amount. If the total requested amount is greater than the maximum amount, the maximum
// amount is distributed to the targets proportionally. Otherwise, every target gets the requested amount.
// It fills the allocated amount to the targets and returns the total allocated amount.
func (de *depositEngine) allocateAmountToItemsProportionally(
	maximum decimal.Decimal, targets []amountAllocationTarget,
) decimal.Decimal {
	if len(targets) == 0 {
		return decimal.Zero
	}

	requestedTotal := decimal.Zero

	for _, target := range targets {
		requestedTotal = requestedTotal.Add(target.GetRequestedAmount())
	}

	if requestedTotal.IsZero() {
		return decimal.Zero
	}

	// 待分配的足够，按 request 的分配
	if requestedTotal.LessThan(maximum) {
		maximum = requestedTotal
	}

	// 处理轧差用
	requestedInMaximum := decimal.Zero

	for i, target := range targets {
		var depositAmount decimal.Decimal

		if i == len(targets)-1 {
			// 最后一项直接轧
			depositAmount = maximum.Sub(requestedInMaximum)
		} else {
			depositAmount = RoundPostTaxAmount(maximum.Div(requestedTotal).Mul(target.GetRequestedAmount()))
		}

		if depositAmount.IsNegative() {
			depositAmount = decimal.Zero
		}

		target.AllocateAmount(depositAmount)
		requestedInMaximum = requestedInMaximum.Add(depositAmount)
	}

	// 注意前面修改过这个值了
	return maximum
}

func (de *depositEngine) buildDeductChangeLog(changedAmount, balance decimal.Decimal) *model.DepositChangeLog {
	return &model.DepositChangeLog{
		DepositOrderID: de.depositDetail.LatestChangeLog.DepositOrderID,
		ChangeType:     orderpb.DepositChangeType_DECREASE,
		Reason:         orderpb.DepositChangeReason_DEDUCTION,
		ChangedAmount:  changedAmount,
		Balance:        balance,
		CurrencyCode:   de.order.CurrencyCode,
		PreviousLogID:  de.depositDetail.LatestChangeLog.ID,
		CompanyID:      de.order.CompanyID,
		BusinessID:     de.order.BusinessID,
		CustomerID:     de.order.CustomerID,
		StaffID:        de.order.CreateBy,
	}
}
