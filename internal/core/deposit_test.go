package core

import (
	"testing"

	"github.com/shopspring/decimal"
	"github.com/stretchr/testify/assert"

	orderpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/order/v1"
	"github.com/MoeGolibrary/moego-svc-order-v2/internal/model"
)

// Normal case: deposit amount doesn't cover total item amount and items not changed
func TestOrderEngine_PreviewOrderDetail_deductDepositToItems_Normal(t *testing.T) {
	order := &model.Order{}
	items := []*model.OrderItem{
		{
			ID:          2001,
			ItemType:    model.ItemTypeService,
			ObjectID:    1001,
			TotalAmount: decimal.NewFromInt(100),
			SubTotalItems: []*model.PriceItem{
				{
					UnitPrice: decimal.NewFromInt(100),
					Quantity:  1,
					Operator:  orderpb.PriceDetailModel_PriceItem_ADD,
					Subtotal:  decimal.NewFromInt(100),
				},
			},
			Tax: model.Tax{
				Rate:   decimal.NewFromInt(10),
				Amount: decimal.NewFromInt(10),
			},
		},
		{
			ID:          2002,
			ItemType:    model.ItemTypeService,
			ObjectID:    1002,
			TotalAmount: decimal.NewFromInt(120),
			SubTotalItems: []*model.PriceItem{
				{
					UnitPrice: decimal.NewFromInt(120),
					Quantity:  1,
					Operator:  orderpb.PriceDetailModel_PriceItem_ADD,
					Subtotal:  decimal.NewFromInt(120),
				},
			},
		},
	}
	depositChangeLogs := []*model.DepositChangeLog{
		{
			Reason:         orderpb.DepositChangeReason_TOP_UP,
			ChangeType:     orderpb.DepositChangeType_INCREASE,
			ChangedAmount:  decimal.NewFromInt(66),
			Balance:        decimal.NewFromInt(66),
			CurrencyCode:   "USD",
			PreviousLogID:  0,
			DepositOrderID: 10001,
		},
	}
	depositPriceItems := []*model.PriceItem{
		{
			UnitPrice:  decimal.NewFromInt(30),
			Quantity:   1,
			Operator:   orderpb.PriceDetailModel_PriceItem_ADD,
			Subtotal:   decimal.NewFromInt(30),
			ObjectType: orderpb.ItemType_ITEM_TYPE_SERVICE,
			ObjectID:   1001,
		},
		{
			UnitPrice:  decimal.NewFromInt(36),
			Quantity:   1,
			Operator:   orderpb.PriceDetailModel_PriceItem_ADD,
			Subtotal:   decimal.NewFromInt(36),
			ObjectType: orderpb.ItemType_ITEM_TYPE_SERVICE,
			ObjectID:   1002,
		},
	}
	depositDetail := &model.DepositDetail{
		DepositChangeLogs: depositChangeLogs,
		LatestChangeLog:   depositChangeLogs[0],
		DepositPriceItems: depositPriceItems,
	}
	oe := NewOrderEngine(order, items).
		WithDepositDetail(depositDetail).
		WithTipAmount(decimal.NewFromInt(15))
	od := oe.PreviewOrderDetail()

	assert.Equal(t, "179", od.Order.TotalAmount.String())
	assert.Equal(t, "66", od.Order.DepositAmount.String())
	assert.Equal(t, "15", od.Order.TipsAmount.String())
	assert.Equal(t, "0", od.Order.DepositToTipsAmount.String())
	assert.Equal(t, "100", od.OrderItems[0].TotalAmount.String())
	assert.Equal(t, "10", od.OrderItems[0].GetTaxAmount().String())
	assert.Equal(t, "30", od.OrderItems[0].DepositAmount.String())
	assert.Equal(t, "120", od.OrderItems[1].TotalAmount.String())
	assert.Equal(t, "36", od.OrderItems[1].DepositAmount.String())
	assert.Equal(t, "66", od.DepositChangeLog.ChangedAmount.String())
	assert.Equal(t, "0", od.DepositChangeLog.Balance.String())
}

// Normal case: deposit amount doesn't cover total item amount and items not changed
// Two pets with the same item
func TestOrderEngine_PreviewOrderDetail_deductDepositToItems_NormalSameItemForMultiplePets(t *testing.T) {
	order := &model.Order{}
	items := []*model.OrderItem{
		{
			ID:          2001,
			ItemType:    model.ItemTypeService,
			ObjectID:    1001,
			TotalAmount: decimal.NewFromInt(100),
			SubTotalItems: []*model.PriceItem{
				{
					UnitPrice: decimal.NewFromInt(100),
					Quantity:  1,
					Operator:  orderpb.PriceDetailModel_PriceItem_ADD,
					Subtotal:  decimal.NewFromInt(100),
				},
			},
			Tax: model.Tax{
				Rate:   decimal.NewFromInt(10),
				Amount: decimal.NewFromInt(10),
			},
		},
		{
			ID:          2002,
			ItemType:    model.ItemTypeService,
			ObjectID:    1002,
			TotalAmount: decimal.NewFromInt(120),
			SubTotalItems: []*model.PriceItem{
				{
					UnitPrice: decimal.NewFromInt(120),
					Quantity:  1,
					Operator:  orderpb.PriceDetailModel_PriceItem_ADD,
					Subtotal:  decimal.NewFromInt(120),
				},
			},
		},
		{
			ID:       2012,
			ItemType: model.ItemTypeService,
			ObjectID: 1002,
			// Even different price
			TotalAmount: decimal.NewFromInt(150),
			SubTotalItems: []*model.PriceItem{
				{
					UnitPrice: decimal.NewFromInt(150),
					Quantity:  1,
					Operator:  orderpb.PriceDetailModel_PriceItem_ADD,
					Subtotal:  decimal.NewFromInt(150),
				},
			},
		},
	}
	depositChangeLogs := []*model.DepositChangeLog{
		{
			Reason:         orderpb.DepositChangeReason_TOP_UP,
			ChangeType:     orderpb.DepositChangeType_INCREASE,
			ChangedAmount:  decimal.NewFromInt(111),
			Balance:        decimal.NewFromInt(111),
			CurrencyCode:   "USD",
			PreviousLogID:  0,
			DepositOrderID: 10001,
		},
	}
	depositPriceItems := []*model.PriceItem{
		{
			UnitPrice:  decimal.NewFromInt(30),
			Quantity:   1,
			Operator:   orderpb.PriceDetailModel_PriceItem_ADD,
			Subtotal:   decimal.NewFromInt(30),
			ObjectType: orderpb.ItemType_ITEM_TYPE_SERVICE,
			ObjectID:   1001,
		},
		{
			UnitPrice:  decimal.NewFromInt(81),
			Quantity:   1,
			Operator:   orderpb.PriceDetailModel_PriceItem_ADD,
			Subtotal:   decimal.NewFromInt(81),
			ObjectType: orderpb.ItemType_ITEM_TYPE_SERVICE,
			ObjectID:   1002,
		},
	}
	depositDetail := &model.DepositDetail{
		DepositChangeLogs: depositChangeLogs,
		LatestChangeLog:   depositChangeLogs[0],
		DepositPriceItems: depositPriceItems,
	}
	oe := NewOrderEngine(order, items).
		WithDepositDetail(depositDetail).
		WithTipAmount(decimal.NewFromInt(15))
	od := oe.PreviewOrderDetail()

	assert.Equal(t, "284", od.Order.TotalAmount.String())
	assert.Equal(t, "111", od.Order.DepositAmount.String())
	assert.Equal(t, "15", od.Order.TipsAmount.String())
	assert.Equal(t, "0", od.Order.DepositToTipsAmount.String())
	assert.Equal(t, "100", od.OrderItems[0].TotalAmount.String())
	assert.Equal(t, "10", od.OrderItems[0].GetTaxAmount().String())
	assert.Equal(t, "30", od.OrderItems[0].DepositAmount.String())
	assert.Equal(t, "120", od.OrderItems[1].TotalAmount.String())
	assert.Equal(t, "36", od.OrderItems[1].DepositAmount.String())
	assert.Equal(t, "150", od.OrderItems[2].TotalAmount.String())
	assert.Equal(t, "45", od.OrderItems[2].DepositAmount.String())
	assert.Equal(t, "111", od.DepositChangeLog.ChangedAmount.String())
	assert.Equal(t, "0", od.DepositChangeLog.Balance.String())
}

// New item case: deposit amount doesn't cover total item amount and new item added on checking out
func TestOrderEngine_PreviewOrderDetail_deductDepositToItems_NewItemsAdded(t *testing.T) {
	order := &model.Order{}
	items := []*model.OrderItem{
		{
			ID:          2001,
			ItemType:    model.ItemTypeService,
			ObjectID:    1001,
			TotalAmount: decimal.NewFromInt(100),
			SubTotalItems: []*model.PriceItem{
				{
					UnitPrice: decimal.NewFromInt(100),
					Quantity:  1,
					Operator:  orderpb.PriceDetailModel_PriceItem_ADD,
					Subtotal:  decimal.NewFromInt(100),
				},
			},
			Tax: model.Tax{
				Rate:   decimal.NewFromInt(10),
				Amount: decimal.NewFromInt(10),
			},
		},
		{
			ID:          2002,
			ItemType:    model.ItemTypeService,
			ObjectID:    1002,
			TotalAmount: decimal.NewFromInt(120),
			SubTotalItems: []*model.PriceItem{
				{
					UnitPrice: decimal.NewFromInt(120),
					Quantity:  1,
					Operator:  orderpb.PriceDetailModel_PriceItem_ADD,
					Subtotal:  decimal.NewFromInt(120),
				},
			},
		},
		{
			ID:       2003,
			ItemType: model.ItemTypeService,
			ObjectID: 1003,
			// Even different price
			TotalAmount: decimal.NewFromInt(150),
			SubTotalItems: []*model.PriceItem{
				{
					UnitPrice: decimal.NewFromInt(150),
					Quantity:  1,
					Operator:  orderpb.PriceDetailModel_PriceItem_ADD,
					Subtotal:  decimal.NewFromInt(150),
				},
			},
		},
	}
	depositChangeLogs := []*model.DepositChangeLog{
		{
			Reason:         orderpb.DepositChangeReason_TOP_UP,
			ChangeType:     orderpb.DepositChangeType_INCREASE,
			ChangedAmount:  decimal.NewFromInt(66),
			Balance:        decimal.NewFromInt(66),
			CurrencyCode:   "USD",
			PreviousLogID:  0,
			DepositOrderID: 10001,
		},
	}
	depositPriceItems := []*model.PriceItem{
		{
			UnitPrice:  decimal.NewFromInt(30),
			Quantity:   1,
			Operator:   orderpb.PriceDetailModel_PriceItem_ADD,
			Subtotal:   decimal.NewFromInt(30),
			ObjectType: orderpb.ItemType_ITEM_TYPE_SERVICE,
			ObjectID:   1001,
		},
		{
			UnitPrice:  decimal.NewFromInt(36),
			Quantity:   1,
			Operator:   orderpb.PriceDetailModel_PriceItem_ADD,
			Subtotal:   decimal.NewFromInt(36),
			ObjectType: orderpb.ItemType_ITEM_TYPE_SERVICE,
			ObjectID:   1002,
		},
	}
	depositDetail := &model.DepositDetail{
		DepositChangeLogs: depositChangeLogs,
		LatestChangeLog:   depositChangeLogs[0],
		DepositPriceItems: depositPriceItems,
	}
	oe := NewOrderEngine(order, items).
		WithDepositDetail(depositDetail).
		WithTipAmount(decimal.NewFromInt(15))
	od := oe.PreviewOrderDetail()

	assert.Equal(t, "329", od.Order.TotalAmount.String())
	assert.Equal(t, "66", od.Order.DepositAmount.String())
	assert.Equal(t, "15", od.Order.TipsAmount.String())
	assert.Equal(t, "0", od.Order.DepositToTipsAmount.String())
	assert.Equal(t, "100", od.OrderItems[0].TotalAmount.String())
	assert.Equal(t, "10", od.OrderItems[0].GetTaxAmount().String())
	assert.Equal(t, "30", od.OrderItems[0].DepositAmount.String())
	assert.Equal(t, "120", od.OrderItems[1].TotalAmount.String())
	assert.Equal(t, "36", od.OrderItems[1].DepositAmount.String())
	assert.Equal(t, "150", od.OrderItems[2].TotalAmount.String())
	assert.Equal(t, "0", od.OrderItems[2].DepositAmount.String())
	assert.Equal(t, "66", od.DepositChangeLog.ChangedAmount.String())
	assert.Equal(t, "0", od.DepositChangeLog.Balance.String())
}

// Remove item case: deposit amount doesn't cover total item amount and some item removed on checking out
// The deposit amount of the removed item is allocated to the other items (including the existing items that already
// allocated the deposit amount)
func TestOrderEngine_PreviewOrderDetail_deductDepositToItems_ItemRemoved(t *testing.T) {
	order := &model.Order{}
	items := []*model.OrderItem{
		{
			ID:          2001,
			ItemType:    model.ItemTypeService,
			ObjectID:    1001,
			TotalAmount: decimal.NewFromInt(100),
			SubTotalItems: []*model.PriceItem{
				{
					UnitPrice: decimal.NewFromInt(100),
					Quantity:  1,
					Operator:  orderpb.PriceDetailModel_PriceItem_ADD,
					Subtotal:  decimal.NewFromInt(100),
				},
			},
			Tax: model.Tax{
				Rate:   decimal.NewFromInt(10),
				Amount: decimal.NewFromInt(10),
			},
		},
		{
			ID:       2003,
			ItemType: model.ItemTypeService,
			ObjectID: 1003,
			// Even different price
			TotalAmount: decimal.NewFromInt(150),
			SubTotalItems: []*model.PriceItem{
				{
					UnitPrice: decimal.NewFromInt(150),
					Quantity:  1,
					Operator:  orderpb.PriceDetailModel_PriceItem_ADD,
					Subtotal:  decimal.NewFromInt(150),
				},
			},
		},
	}
	depositChangeLogs := []*model.DepositChangeLog{
		{
			Reason:         orderpb.DepositChangeReason_TOP_UP,
			ChangeType:     orderpb.DepositChangeType_INCREASE,
			ChangedAmount:  decimal.NewFromInt(66),
			Balance:        decimal.NewFromInt(66),
			CurrencyCode:   "USD",
			PreviousLogID:  0,
			DepositOrderID: 10001,
		},
	}
	depositPriceItems := []*model.PriceItem{
		{
			UnitPrice:  decimal.NewFromInt(30),
			Quantity:   1,
			Operator:   orderpb.PriceDetailModel_PriceItem_ADD,
			Subtotal:   decimal.NewFromInt(30),
			ObjectType: orderpb.ItemType_ITEM_TYPE_SERVICE,
			ObjectID:   1001,
		},
		{
			UnitPrice:  decimal.NewFromInt(36),
			Quantity:   1,
			Operator:   orderpb.PriceDetailModel_PriceItem_ADD,
			Subtotal:   decimal.NewFromInt(36),
			ObjectType: orderpb.ItemType_ITEM_TYPE_SERVICE,
			ObjectID:   1002,
		},
	}
	depositDetail := &model.DepositDetail{
		DepositChangeLogs: depositChangeLogs,
		LatestChangeLog:   depositChangeLogs[0],
		DepositPriceItems: depositPriceItems,
	}
	oe := NewOrderEngine(order, items).
		WithDepositDetail(depositDetail).
		WithTipAmount(decimal.NewFromInt(15))
	od := oe.PreviewOrderDetail()

	assert.Equal(t, "209", od.Order.TotalAmount.String())
	assert.Equal(t, "66", od.Order.DepositAmount.String())
	assert.Equal(t, "15", od.Order.TipsAmount.String())
	assert.Equal(t, "0", od.Order.DepositToTipsAmount.String())
	assert.Equal(t, "100", od.OrderItems[0].TotalAmount.String())
	assert.Equal(t, "10", od.OrderItems[0].GetTaxAmount().String())
	assert.Equal(t, "42.52", od.OrderItems[0].DepositAmount.String())
	assert.Equal(t, "150", od.OrderItems[1].TotalAmount.String())
	assert.Equal(t, "23.48", od.OrderItems[1].DepositAmount.String())
	assert.Equal(t, "66", od.DepositChangeLog.ChangedAmount.String())
	assert.Equal(t, "0", od.DepositChangeLog.Balance.String())
}

// Remove item case: deposit amount doesn't cover total item amount and some item partially removed on checking out
// The removing causes the deposit amount of that item overflow, so it is allocated to the other items (including the
// existing items that already allocated the deposit amount)
func TestOrderEngine_PreviewOrderDetail_deductDepositToItems_ItemPartiallyRemoved(t *testing.T) {
	order := &model.Order{}
	items := []*model.OrderItem{
		{
			ID:          2001,
			ItemType:    model.ItemTypeService,
			ObjectID:    1001,
			TotalAmount: decimal.NewFromInt(100),
			SubTotalItems: []*model.PriceItem{
				{
					UnitPrice: decimal.NewFromInt(100),
					Quantity:  1,
					Operator:  orderpb.PriceDetailModel_PriceItem_ADD,
					Subtotal:  decimal.NewFromInt(100),
				},
			},
			Tax: model.Tax{
				Rate:   decimal.NewFromInt(10),
				Amount: decimal.NewFromInt(10),
			},
		},
		{
			ID:          2002,
			ItemType:    model.ItemTypeService,
			ObjectID:    1002,
			TotalAmount: decimal.NewFromInt(120),
			SubTotalItems: []*model.PriceItem{
				{
					UnitPrice: decimal.NewFromInt(120),
					Quantity:  1,
					Operator:  orderpb.PriceDetailModel_PriceItem_ADD,
					Subtotal:  decimal.NewFromInt(120),
				},
			},
		},
		{
			ID:          2003,
			ItemType:    model.ItemTypeService,
			ObjectID:    1003,
			TotalAmount: decimal.NewFromInt(150),
			SubTotalItems: []*model.PriceItem{
				{
					UnitPrice: decimal.NewFromInt(150),
					Quantity:  1,
					Operator:  orderpb.PriceDetailModel_PriceItem_ADD,
					Subtotal:  decimal.NewFromInt(150),
				},
			},
		},
	}
	depositChangeLogs := []*model.DepositChangeLog{
		{
			Reason:         orderpb.DepositChangeReason_TOP_UP,
			ChangeType:     orderpb.DepositChangeType_INCREASE,
			ChangedAmount:  decimal.NewFromInt(222),
			Balance:        decimal.NewFromInt(222),
			CurrencyCode:   "USD",
			PreviousLogID:  0,
			DepositOrderID: 10001,
		},
	}
	depositPriceItems := []*model.PriceItem{
		{
			UnitPrice:  decimal.NewFromInt(30),
			Quantity:   1,
			Operator:   orderpb.PriceDetailModel_PriceItem_ADD,
			Subtotal:   decimal.NewFromInt(30),
			ObjectType: orderpb.ItemType_ITEM_TYPE_SERVICE,
			ObjectID:   1001,
		},
		{
			// On take deposit: 120 * 2 * 80%
			UnitPrice:  decimal.NewFromInt(192),
			Quantity:   1,
			Operator:   orderpb.PriceDetailModel_PriceItem_ADD,
			Subtotal:   decimal.NewFromInt(192),
			ObjectType: orderpb.ItemType_ITEM_TYPE_SERVICE,
			ObjectID:   1002,
		},
	}
	depositDetail := &model.DepositDetail{
		DepositChangeLogs: depositChangeLogs,
		LatestChangeLog:   depositChangeLogs[0],
		DepositPriceItems: depositPriceItems,
	}
	oe := NewOrderEngine(order, items).
		WithDepositDetail(depositDetail).
		WithTipAmount(decimal.NewFromInt(15))
	od := oe.PreviewOrderDetail()

	assert.Equal(t, "173", od.Order.TotalAmount.String())
	assert.Equal(t, "222", od.Order.DepositAmount.String())
	assert.Equal(t, "15", od.Order.TipsAmount.String())
	assert.Equal(t, "0", od.Order.DepositToTipsAmount.String())
	assert.Equal(t, "100", od.OrderItems[0].TotalAmount.String())
	assert.Equal(t, "10", od.OrderItems[0].GetTaxAmount().String())
	assert.Equal(t, "55.04", od.OrderItems[0].DepositAmount.String())
	assert.Equal(t, "120", od.OrderItems[1].TotalAmount.String())
	assert.Equal(t, "120", od.OrderItems[1].DepositAmount.String())
	assert.Equal(t, "150", od.OrderItems[2].TotalAmount.String())
	assert.Equal(t, "46.96", od.OrderItems[2].DepositAmount.String())
	assert.Equal(t, "222", od.DepositChangeLog.ChangedAmount.String())
	assert.Equal(t, "0", od.DepositChangeLog.Balance.String())
}

// Remove item case: deposit amount of the removed item exactly allocated to all the other items.
func TestOrderEngine_PreviewOrderDetail_deductDepositToItems_ItemRemovedAllItemsFullyAllocated(t *testing.T) {
	order := &model.Order{}
	items := []*model.OrderItem{
		{
			ID:          2001,
			ItemType:    model.ItemTypeService,
			ObjectID:    1001,
			TotalAmount: decimal.NewFromInt(100),
			SubTotalItems: []*model.PriceItem{
				{
					UnitPrice: decimal.NewFromInt(100),
					Quantity:  1,
					Operator:  orderpb.PriceDetailModel_PriceItem_ADD,
					Subtotal:  decimal.NewFromInt(100),
				},
			},
			Tax: model.Tax{
				Rate:   decimal.NewFromInt(10),
				Amount: decimal.NewFromInt(10),
			},
		},
		{
			ID:       2003,
			ItemType: model.ItemTypeService,
			ObjectID: 1003,
			// Smaller amount item
			TotalAmount: decimal.NewFromInt(40),
			SubTotalItems: []*model.PriceItem{
				{
					UnitPrice: decimal.NewFromInt(40),
					Quantity:  1,
					Operator:  orderpb.PriceDetailModel_PriceItem_ADD,
					Subtotal:  decimal.NewFromInt(40),
				},
			},
		},
	}
	depositChangeLogs := []*model.DepositChangeLog{
		{
			Reason:         orderpb.DepositChangeReason_TOP_UP,
			ChangeType:     orderpb.DepositChangeType_INCREASE,
			ChangedAmount:  decimal.NewFromInt(150),
			Balance:        decimal.NewFromInt(150),
			CurrencyCode:   "USD",
			PreviousLogID:  0,
			DepositOrderID: 10001,
		},
	}
	depositPriceItems := []*model.PriceItem{
		{
			UnitPrice:  decimal.NewFromInt(30),
			Quantity:   1,
			Operator:   orderpb.PriceDetailModel_PriceItem_ADD,
			Subtotal:   decimal.NewFromInt(30),
			ObjectType: orderpb.ItemType_ITEM_TYPE_SERVICE,
			ObjectID:   1001,
		},
		{
			UnitPrice:  decimal.NewFromInt(120),
			Quantity:   1,
			Operator:   orderpb.PriceDetailModel_PriceItem_ADD,
			Subtotal:   decimal.NewFromInt(120),
			ObjectType: orderpb.ItemType_ITEM_TYPE_SERVICE,
			ObjectID:   1002, // This item is removed (not in current items)
		},
	}
	depositDetail := &model.DepositDetail{
		DepositChangeLogs: depositChangeLogs,
		LatestChangeLog:   depositChangeLogs[0],
		DepositPriceItems: depositPriceItems,
	}
	oe := NewOrderEngine(order, items).
		WithDepositDetail(depositDetail).
		WithTipAmount(decimal.NewFromInt(15))
	od := oe.PreviewOrderDetail()

	assert.Equal(t, "15", od.Order.TotalAmount.String())
	assert.Equal(t, "150", od.Order.DepositAmount.String())
	assert.Equal(t, "15", od.Order.TipsAmount.String())
	assert.Equal(t, "0", od.Order.DepositToTipsAmount.String())
	assert.Equal(t, "100", od.OrderItems[0].TotalAmount.String())
	assert.Equal(t, "10", od.OrderItems[0].GetTaxAmount().String())
	assert.Equal(t, "110", od.OrderItems[0].DepositAmount.String())
	assert.Equal(t, "40", od.OrderItems[1].TotalAmount.String())
	assert.Equal(t, "40", od.OrderItems[1].DepositAmount.String())
	assert.Equal(t, "150", od.DepositChangeLog.ChangedAmount.String())
	assert.Equal(t, "0", od.DepositChangeLog.Balance.String())
}

// Remove item case: deposit amount of the removed item is not only allocated to all the other items, but also covers
// partial tips.
func TestOrderEngine_PreviewOrderDetail_deductDepositToItems_ItemRemovedDepositCoversPartialTips(t *testing.T) {
	order := &model.Order{}
	items := []*model.OrderItem{
		{
			ID:          2001,
			ItemType:    model.ItemTypeService,
			ObjectID:    1001,
			TotalAmount: decimal.NewFromInt(100),
			SubTotalItems: []*model.PriceItem{
				{
					UnitPrice: decimal.NewFromInt(100),
					Quantity:  1,
					Operator:  orderpb.PriceDetailModel_PriceItem_ADD,
					Subtotal:  decimal.NewFromInt(100),
				},
			},
			Tax: model.Tax{
				Rate:   decimal.NewFromInt(10),
				Amount: decimal.NewFromInt(10),
			},
		},
		{
			ID:       2003,
			ItemType: model.ItemTypeService,
			ObjectID: 1003,
			// Smaller amount item
			TotalAmount: decimal.NewFromInt(40),
			SubTotalItems: []*model.PriceItem{
				{
					UnitPrice: decimal.NewFromInt(40),
					Quantity:  1,
					Operator:  orderpb.PriceDetailModel_PriceItem_ADD,
					Subtotal:  decimal.NewFromInt(40),
				},
			},
		},
	}
	depositChangeLogs := []*model.DepositChangeLog{
		{
			Reason:         orderpb.DepositChangeReason_TOP_UP,
			ChangeType:     orderpb.DepositChangeType_INCREASE,
			ChangedAmount:  decimal.NewFromInt(160),
			Balance:        decimal.NewFromInt(160),
			CurrencyCode:   "USD",
			PreviousLogID:  0,
			DepositOrderID: 10001,
		},
	}
	depositPriceItems := []*model.PriceItem{
		{
			UnitPrice:  decimal.NewFromInt(30),
			Quantity:   1,
			Operator:   orderpb.PriceDetailModel_PriceItem_ADD,
			Subtotal:   decimal.NewFromInt(30),
			ObjectType: orderpb.ItemType_ITEM_TYPE_SERVICE,
			ObjectID:   1001,
		},
		{
			UnitPrice:  decimal.NewFromInt(130),
			Quantity:   1,
			Operator:   orderpb.PriceDetailModel_PriceItem_ADD,
			Subtotal:   decimal.NewFromInt(130),
			ObjectType: orderpb.ItemType_ITEM_TYPE_SERVICE,
			ObjectID:   1002, // This item is removed (not in current items)
		},
	}
	depositDetail := &model.DepositDetail{
		DepositChangeLogs: depositChangeLogs,
		LatestChangeLog:   depositChangeLogs[0],
		DepositPriceItems: depositPriceItems,
	}
	oe := NewOrderEngine(order, items).
		WithDepositDetail(depositDetail).
		WithTipAmount(decimal.NewFromInt(15))
	od := oe.PreviewOrderDetail()

	assert.Equal(t, "5", od.Order.TotalAmount.String())
	assert.Equal(t, "160", od.Order.DepositAmount.String())
	assert.Equal(t, "15", od.Order.TipsAmount.String())
	assert.Equal(t, "10", od.Order.DepositToTipsAmount.String())
	assert.Equal(t, "100", od.OrderItems[0].TotalAmount.String())
	assert.Equal(t, "10", od.OrderItems[0].GetTaxAmount().String())
	assert.Equal(t, "110", od.OrderItems[0].DepositAmount.String())
	assert.Equal(t, "40", od.OrderItems[1].TotalAmount.String())
	assert.Equal(t, "40", od.OrderItems[1].DepositAmount.String())
	assert.Equal(t, "160", od.DepositChangeLog.ChangedAmount.String())
	assert.Equal(t, "0", od.DepositChangeLog.Balance.String())
}

// Remove item case: deposit amount of the removed item is not only allocated to all the other items, but also covers
// partial tips.
func TestOrderEngine_PreviewOrderDetail_deductDepositToItems_ItemRemovedDepositOverpaid(t *testing.T) {
	order := &model.Order{}
	items := []*model.OrderItem{
		{
			ID:          2001,
			ItemType:    model.ItemTypeService,
			ObjectID:    1001,
			TotalAmount: decimal.NewFromInt(100),
			SubTotalItems: []*model.PriceItem{
				{
					UnitPrice: decimal.NewFromInt(100),
					Quantity:  1,
					Operator:  orderpb.PriceDetailModel_PriceItem_ADD,
					Subtotal:  decimal.NewFromInt(100),
				},
			},
			Tax: model.Tax{
				Rate:   decimal.NewFromInt(10),
				Amount: decimal.NewFromInt(10),
			},
		},
		{
			ID:       2003,
			ItemType: model.ItemTypeService,
			ObjectID: 1003,
			// Smaller amount item
			TotalAmount: decimal.NewFromInt(40),
			SubTotalItems: []*model.PriceItem{
				{
					UnitPrice: decimal.NewFromInt(40),
					Quantity:  1,
					Operator:  orderpb.PriceDetailModel_PriceItem_ADD,
					Subtotal:  decimal.NewFromInt(40),
				},
			},
		},
	}
	depositChangeLogs := []*model.DepositChangeLog{
		{
			Reason:         orderpb.DepositChangeReason_TOP_UP,
			ChangeType:     orderpb.DepositChangeType_INCREASE,
			ChangedAmount:  decimal.NewFromInt(180),
			Balance:        decimal.NewFromInt(180),
			CurrencyCode:   "USD",
			PreviousLogID:  0,
			DepositOrderID: 10001,
		},
	}
	depositPriceItems := []*model.PriceItem{
		{
			UnitPrice:  decimal.NewFromInt(30),
			Quantity:   1,
			Operator:   orderpb.PriceDetailModel_PriceItem_ADD,
			Subtotal:   decimal.NewFromInt(30),
			ObjectType: orderpb.ItemType_ITEM_TYPE_SERVICE,
			ObjectID:   1001,
		},
		{
			UnitPrice:  decimal.NewFromInt(150),
			Quantity:   1,
			Operator:   orderpb.PriceDetailModel_PriceItem_ADD,
			Subtotal:   decimal.NewFromInt(150),
			ObjectType: orderpb.ItemType_ITEM_TYPE_SERVICE,
			ObjectID:   1002, // This item is removed (not in current items)
		},
	}
	depositDetail := &model.DepositDetail{
		DepositChangeLogs: depositChangeLogs,
		LatestChangeLog:   depositChangeLogs[0],
		DepositPriceItems: depositPriceItems,
	}
	oe := NewOrderEngine(order, items).
		WithDepositDetail(depositDetail).
		WithTipAmount(decimal.NewFromInt(15))
	od := oe.PreviewOrderDetail()

	assert.Equal(t, "0", od.Order.TotalAmount.String())
	assert.Equal(t, "165", od.Order.DepositAmount.String())
	assert.Equal(t, "15", od.Order.TipsAmount.String())
	assert.Equal(t, "15", od.Order.DepositToTipsAmount.String())
	assert.Equal(t, "100", od.OrderItems[0].TotalAmount.String())
	assert.Equal(t, "10", od.OrderItems[0].GetTaxAmount().String())
	assert.Equal(t, "110", od.OrderItems[0].DepositAmount.String())
	assert.Equal(t, "40", od.OrderItems[1].TotalAmount.String())
	assert.Equal(t, "40", od.OrderItems[1].DepositAmount.String())
	assert.Equal(t, "165", od.DepositChangeLog.ChangedAmount.String())
	assert.Equal(t, "15", od.DepositChangeLog.Balance.String())
}
