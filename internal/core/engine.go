package core

import (
	"github.com/shopspring/decimal"

	"github.com/MoeGolibrary/moego-svc-order-v2/internal/model"
)

// OrderEngine 是 V4 版本订单的计算引擎。
// 它定义和实现了所有 V4 版本订单关于钱的计算逻辑。
type OrderEngine struct {
	order *model.Order
	items []*model.OrderItem

	tipAmount     decimal.Decimal
	promotions    []*model.OrderPromotion
	orderPayments []*model.OrderPayment

	depositEngine *depositEngine
}

func NewOrderEngine(order *model.Order, items []*model.OrderItem) *OrderEngine {
	return &OrderEngine{
		order: order,
		items: items,

		tipAmount: decimal.Zero,

		// 避免 nil pointer
		depositEngine: newDepositEngine(order, nil),
	}
}

func (oe *OrderEngine) WithDepositDetail(depositDetail *model.DepositDetail) *OrderEngine {
	oe.depositEngine = newDepositEngine(oe.order, depositDetail)

	return oe
}
