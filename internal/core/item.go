package core

import (
	"github.com/shopspring/decimal"

	orderpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/order/v1"
	"github.com/MoeGolibrary/moego-svc-order-v2/internal/model"
)

func getSubQuantity(it *model.OrderItem) int32 {
	// SubTotal 只包含需要支付的数量.
	quantity := it.Quantity
	if quantity <= 0 {
		quantity = 1
	}

	purchasedQuantity := it.PurchasedQuantity

	switch {
	case purchasedQuantity < 0:
		purchasedQuantity = 0
	case purchasedQuantity > quantity:
		purchasedQuantity = quantity
	}

	return quantity - purchasedQuantity
}

// calculateItem 计算单个 item 的价格
func (oe *OrderEngine) calculateItem(
	it *model.OrderItem,
	discountHelper orderItemDiscountHelper,
) *model.OrderItem {
	unitPrice, subTotalItems := oe.calculateUnitPrice(it)
	it.SubTotalItems = subTotalItems

	// 先更新 discount 相关的字段.
	it.DiscountAmount = it.GetDiscountAmount().Add(discountHelper.DiscountAmount)
	it.PurchasedQuantity += discountHelper.PurchasedQuantity

	// SubTotal 只包含需要支付的数量.
	subQuantity := getSubQuantity(it)
	subTotal := CalculateSubTotal(unitPrice, subQuantity)

	// NOTE: DiscountAmount 应当在调用 calculateItem 之前计算.

	// Tax 基于优惠后的金额计算
	taxBase := subTotal.Sub(it.GetDiscountAmount())
	taxAmount := CalculateTax(taxBase, it.Tax.Rate)

	return &model.OrderItem{
		ID:                it.ID,
		OrderID:           it.OrderID,
		BusinessID:        it.BusinessID,
		StaffID:           it.StaffID,
		StaffIDs:          it.StaffIDs,
		ItemType:          it.ItemType,
		ObjectID:          it.ObjectID,
		ExternalUUID:      it.ExternalUUID,
		Name:              it.Name,
		Description:       it.Description,
		UnitPrice:         unitPrice,     // 可能被重算，这里需要用计算值.
		SubTotalItems:     subTotalItems, // 可能被轧差，需要用计算返回的结果.
		Quantity:          it.Quantity,
		PurchasedQuantity: it.PurchasedQuantity,
		PetID:             it.PetID,
		CurrencyCode:      it.CurrencyCode,
		Tax: model.Tax{
			ID:     it.Tax.ID,
			Name:   it.Tax.Name,
			Rate:   it.Tax.Rate,
			Amount: taxAmount,
		},
		TipsAmount:             decimal.Zero,
		DiscountAmount:         it.GetDiscountAmount(),
		SubTotalAmount:         subTotal,
		TotalAmount:            taxBase,
		RefundedQuantity:       0,
		RefundedAmount:         decimal.Zero,
		RefundedTaxAmount:      decimal.Zero,
		RefundedDiscountAmount: decimal.Zero,
		IsDeleted:              false,
		CreateTime:             it.CreateTime,
		UpdateTime:             it.UpdateTime,
		ItemDiscounts:          it.ItemDiscounts,
	}
}

func (oe *OrderEngine) calculateUnitPrice(it *model.OrderItem) (decimal.Decimal, []*model.PriceItem) {
	if len(it.SubTotalItems) == 0 {
		return it.UnitPrice, nil
	}

	subTotal := decimal.Zero

	for _, upi := range it.SubTotalItems {
		st := CalculateSubTotal(upi.UnitPrice, upi.Quantity)

		if upi.Operator == orderpb.PriceDetailModel_PriceItem_ADD {
			subTotal = subTotal.Add(st)
			continue
		}

		subTotal = subTotal.Sub(st)
	}

	quantity := decimal.NewFromInt(int64(it.Quantity))
	if !quantity.IsPositive() {
		quantity = decimal.NewFromInt(1)
	}

	unitPrice := RoundPreTaxAmount(subTotal.Div(quantity))

	// 刚好除尽，不需要处理差额.
	reCalculatedSubTotal := RoundPreTaxAmount(unitPrice.Mul(quantity))
	if reCalculatedSubTotal.Equal(subTotal) {
		return unitPrice, it.SubTotalItems
	}

	// 处理差额的情况，多退少补，需要确保 unitPrice * quantity = subTotal.
	delta := &model.PriceItem{
		ID:          0, // No need.
		OrderItemID: it.ID,
		Name:        "netting",
		UnitPrice:   decimal.Decimal{}, // Set below.
		Quantity:    1,
		Operator:    0, // Set below.
	}

	// 反算之后，需要往 Detail 里面补差价的情况.
	if reCalculatedSubTotal.GreaterThan(subTotal) {
		delta.Operator = orderpb.PriceDetailModel_PriceItem_ADD
		delta.UnitPrice = reCalculatedSubTotal.Sub(subTotal)

		return unitPrice, append(it.SubTotalItems, delta)
	}

	// 反算之后，需要从 Detail 里面扣差价的情况.
	delta.Operator = orderpb.PriceDetailModel_PriceItem_SUBTRACT
	delta.UnitPrice = subTotal.Sub(reCalculatedSubTotal)

	return unitPrice, append(it.SubTotalItems, delta)
}
