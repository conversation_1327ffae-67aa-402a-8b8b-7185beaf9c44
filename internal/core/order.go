package core

import (
	"database/sql"

	"github.com/samber/lo"
	"github.com/shopspring/decimal"

	orderpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/order/v1"
	"github.com/MoeGolibrary/moego-svc-order-v2/internal/model"
)

func (oe *OrderEngine) PreviewOrderDetail() *model.OrderDetail {
	// Apply promotions. 计算 discounts.
	discounts, totalDiscountAmount, externalUUIDToHelper := oe.calculatePromotions()

	// Re-Calculate items.
	orderItems := lo.Map(
		oe.items, func(it *model.OrderItem, _ int) *model.OrderItem {
			helper := externalUUIDToHelper[it.ExternalUUID]
			return oe.calculateItem(it, helper)
		},
	)

	// Add extra fees.
	// Summerize to order.
	taxAmount := summarizeAmount(
		orderItems, func(it *model.OrderItem) decimal.Decimal { return it.GetTaxAmount() },
	)

	// TipBasedAmount 是 Item SubTotal 折前的价格.
	tipBasedAmount := summarizeAmount(
		orderItems, func(it *model.OrderItem) decimal.Decimal { return it.GetSubTotalAmount() },
	)

	tipAmount := oe.tipAmount

	// 订单上记录的 Item SubTotal 是折扣前的金额.
	subTotal := summarizeAmount(
		orderItems, func(it *model.OrderItem) decimal.Decimal { return it.GetSubTotalAmount() },
	)

	// 计算 Deposit 可抵扣的金额.
	totalAmount := subTotal.
		Add(taxAmount).
		Add(tipAmount).
		Sub(totalDiscountAmount)

	depositAmount, tipsByDepositAmount, depositChangeLog := oe.depositEngine.AllocateDeposit(orderItems, tipAmount)

	// 扣除 Deposit 抵扣的部分.
	totalAmount = totalAmount.Sub(depositAmount)

	// Preview 阶段，不会有 Order Payment.
	paidAmount := decimal.Zero
	convenienceFee := decimal.Zero

	totalAmount = totalAmount.Add(convenienceFee)

	// 计算待支付金额.
	remainAmount := totalAmount.Sub(paidAmount)
	if remainAmount.IsNegative() {
		remainAmount = decimal.Zero
	}

	order := &model.Order{
		ID:                  oe.order.ID,
		OrderVersion:        oe.order.OrderVersion,
		CompanyID:           oe.order.CompanyID,
		BusinessID:          oe.order.BusinessID,
		CustomerID:          oe.order.CustomerID,
		CreateBy:            oe.order.CreateBy,
		UpdateBy:            oe.order.UpdateBy,
		GUID:                oe.order.GUID,
		Title:               oe.order.Title,
		Description:         oe.order.Description,
		Status:              orderpb.OrderStatus_CREATED, // update below.
		FulfillmentStatus:   oe.order.FulfillmentStatus,
		PaymentStatus:       orderpb.OrderModel_UNPAID, // update below.
		OrderType:           oe.order.OrderType,
		OrderRefID:          oe.order.OrderRefID,
		ExtraChargeReason:   oe.order.ExtraChargeReason,
		SourceType:          oe.order.SourceType,
		SourceID:            oe.order.SourceID,
		Version:             oe.order.Version,
		TaxRoundMode:        oe.order.TaxRoundMode,
		CurrencyCode:        oe.order.CurrencyCode,
		LineItemTypes:       oe.calculateItemTypes(orderItems),
		TipsAmount:          tipAmount,
		DepositToTipsAmount: tipsByDepositAmount,
		TaxAmount:           taxAmount,
		DiscountAmount:      totalDiscountAmount,
		DepositAmount:       depositAmount,
		ConvenienceFee:      convenienceFee,
		SubTotalAmount:      subTotal,
		TipsBasedAmount:     tipBasedAmount,
		TotalAmount:         totalAmount,
		PaidAmount:          paidAmount,
		RemainAmount:        remainAmount,
		// Preview 阶段不会有 Refund Order Payment.
		RefundedAmount:  decimal.Zero,
		CompleteTime:    sql.NullTime{},
		CreateTime:      oe.order.CreateTime,
		UpdateTime:      oe.order.UpdateTime,
		RefundableModes: nil,
	}

	// 顺序很重要.
	order.PaymentStatus = oe.calculatePaymentStatus(order)
	order.TransitStatus(oe.calculateOrderStatus(order))

	return &model.OrderDetail{
		Order:               order,
		OrderItems:          orderItems,
		OrderDiscount:       discounts,
		OrderPayments:       nil,
		RefundOrderPayments: nil,
		OrderPromotions:     oe.promotions,
		DepositChangeLog:    depositChangeLog,
	}
}

func (oe *OrderEngine) calculatePaymentStatus(order *model.Order) orderpb.OrderModel_PaymentStatus {
	if order == nil {
		return orderpb.OrderModel_UNPAID
	}

	// 不需要付钱.
	if order.GetRemainAmount().Equal(decimal.Zero) {
		return orderpb.OrderModel_PAID
	}

	// 付过钱，并且还需要付钱.
	if order.GetRemainAmount().IsPositive() && order.GetPaidAmount().IsPositive() {
		return orderpb.OrderModel_PARTIAL_PAID
	}

	return orderpb.OrderModel_UNPAID
}

func (oe *OrderEngine) calculateOrderStatus(order *model.Order) orderpb.OrderStatus {
	if order == nil {
		return orderpb.OrderStatus_CREATED
	}

	// 不应当预期从这里会创建一个 REMOVED 的 order.
	switch order.PaymentStatus {
	case orderpb.OrderModel_PAID:
		// 全额支付即关单.
		return orderpb.OrderStatus_COMPLETED

	case orderpb.OrderModel_PARTIAL_PAID:
		// 部分支付扭转为 Processing，
		return orderpb.OrderStatus_PROCESSING

	default:
		return orderpb.OrderStatus_CREATED
	}
}

func (oe *OrderEngine) calculateItemTypes(items []*model.OrderItem) int32 {
	itemTypes := int32(0)

	for _, it := range items {
		typ := int32(it.GetItemType())
		if typ == 0 {
			continue
		}

		itemTypes |= 1 << (typ - 1)
	}

	return itemTypes
}
