package core

import (
	"sort"

	"github.com/samber/lo"
	"github.com/shopspring/decimal"

	orderpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/order/v1"
	"github.com/MoeGolibrary/moego-svc-order-v2/internal/model"
)

func (oe *OrderEngine) WithPromotion(promotions []*model.OrderPromotion) *OrderEngine {
	oe.promotions = promotions

	return oe
}

func (oe *OrderEngine) calculateDeduction(
	op *model.OrderPromotion,
	itemMap map[string]*model.OrderItem,
) {
	for _, opi := range op.PromotionItems {
		item, ok := itemMap[opi.ExternalUUID]
		if !ok {
			continue
		}

		item.PurchasedQuantity += opi.DeductQuantity

		if item.PurchasedQuantity > item.Quantity {
			item.PurchasedQuantity = item.Quantity // 不能超过数量.
			// 超过了修改一下 promotion item 的 deduct quantity
			opi.DeductQuantity = opi.DeductQuantity - item.PurchasedQuantity + item.Quantity
		}
	}
}

func (oe *OrderEngine) calculatePercentage(
	op *model.OrderPromotion,
	itemMap map[string]*model.OrderItem,
) []*model.OrderLineDiscount {
	// 计算总的 discount 金额
	currentAppliedAmount := decimal.Zero
	discounts := make([]*model.OrderLineDiscount, 0, len(op.PromotionItems))

	for _, opi := range op.PromotionItems {
		orderItem, ok := itemMap[opi.ExternalUUID]
		if !ok {
			continue // 如果没有找到对应的 item，跳过
		}

		// 当前 item 已经没有金额 或者 被删除了，不能再享受优惠
		if orderItem.CannotApplyDiscount() {
			continue
		}

		currentDiscountAmount := orderItem.GetTotalAmount().
			Mul(op.DiscountValue).
			Div(MinorUnits). // 这里需要除以 100 转成元
			RoundBank(AmountPrecision)
		currentAppliedAmount = currentAppliedAmount.Add(currentDiscountAmount)
		opi.AppliedAmount = currentDiscountAmount

		// 更新 order item 的金额
		orderItem.DiscountAmount = orderItem.GetDiscountAmount().Add(currentDiscountAmount)
		orderItem.TotalAmount = orderItem.GetTotalAmount().Sub(currentDiscountAmount)

		discounts = append(
			discounts, &model.OrderLineDiscount{
				BusinessID:            oe.order.BusinessID,
				OrderID:               oe.order.ID,
				OrderItemID:           opi.OrderItemID,
				DiscountType:          model.DiscountTypePercentage,
				DiscountAmount:        currentDiscountAmount,
				DiscountRate:          op.DiscountValue,
				OrderItemExternalUUID: opi.ExternalUUID,
				IsDeleted:             false,
				ApplyType:             model.ApplyTypeItem,
				DiscountCodeID:        opi.DiscountCodeID,
			},
		)
	}

	op.AppliedAmount = currentAppliedAmount

	return discounts
}

func (oe *OrderEngine) calculateFixedAmount(
	op *model.OrderPromotion,
	itemMap map[string]*model.OrderItem,
) []*model.OrderLineDiscount {
	// 先找到当前应用了的 item
	currentPromotionOrderItemTotalAmount := decimal.Zero
	currentPromotionOrderItems := make([]*model.OrderItem, 0, len(op.PromotionItems))
	discounts := make([]*model.OrderLineDiscount, 0, len(op.PromotionItems))

	for _, opi := range op.PromotionItems {
		if item, ok := itemMap[opi.ExternalUUID]; ok {
			item.DiscountCodeID = opi.DiscountCodeID
			currentPromotionOrderItems = append(currentPromotionOrderItems, item)
			currentPromotionOrderItemTotalAmount = currentPromotionOrderItemTotalAmount.
				Add(item.GetTotalAmount())
		}
	}

	// 如果没有找到对应的 item，跳过
	if len(currentPromotionOrderItems) == 0 {
		op.PromotionItems = nil
		op.AppliedAmount = decimal.Zero

		return nil
	}

	// 按照金额从小到大排序, 降低优惠金额的轧差风险
	sort.SliceStable(currentPromotionOrderItems,
		func(i, j int) bool {
			return currentPromotionOrderItems[i].GetTotalAmount().GreaterThan(
				currentPromotionOrderItems[j].GetTotalAmount(),
			)
		},
	)

	externalUUIDToDiscount, restDiscountAmount := oe.nettingForFixAmount(
		op.AppliedAmount,
		currentPromotionOrderItemTotalAmount,
		currentPromotionOrderItems)

	// 将所有的 OrderLineDiscount 添加到 discounts 中
	for _, discount := range externalUUIDToDiscount {
		// discount 金额大于0的再添加进去
		if discount.DiscountAmount.GreaterThan(decimal.Zero) {
			discounts = append(discounts, discount)
		}
	}

	// 这里就是有无法分配的金额，比如 3个 0.33 元的item，分配 1 元的优惠金额，最后会剩下 0.01 元
	op.AppliedAmount = op.AppliedAmount.Sub(restDiscountAmount)
	op.DiscountValue = op.AppliedAmount
	op.PromotionItems = make([]*model.OrderPromotionItem, 0, len(discounts))

	for _, d := range discounts {
		op.PromotionItems = append(op.PromotionItems, &model.OrderPromotionItem{
			OrderPromotionID: op.ID,
			OrderItemID:      d.OrderItemID,
			AppliedAmount:    d.DiscountAmount,
			DeductQuantity:   0, // Fixed amount 不需要扣除数量
			ExternalUUID:     d.OrderItemExternalUUID,
		})
	}

	return discounts
}

// 分配金额，做轧差
func (oe *OrderEngine) nettingForFixAmount(
	originalDiscountAmount decimal.Decimal,
	currentPromotionOrderItemTotalAmount decimal.Decimal,
	currentPromotionOrderItems []*model.OrderItem,
) (map[string]*model.OrderLineDiscount, decimal.Decimal) {
	restDiscountAmount := originalDiscountAmount
	externalUUIDToDiscount := make(map[string]*model.OrderLineDiscount, len(currentPromotionOrderItems))

	for idx, orderItem := range currentPromotionOrderItems {
		// 当前 item 已经没有金额 或者 被删除了，不能再享受优惠
		if orderItem.CannotApplyDiscount() {
			continue
		}

		itemSplitRate := orderItem.GetTotalAmount().
			Div(currentPromotionOrderItemTotalAmount).
			Round(RatePrecision)
		itemDiscountAmount := originalDiscountAmount.Mul(itemSplitRate).RoundBank(AmountPrecision)

		// 最后一个 item，直接使用剩余的折扣金额
		if idx == len(currentPromotionOrderItems)-1 {
			itemDiscountAmount = restDiscountAmount
		}

		// 如果分配到的金额超过了当前 item 的金额，使用当前 item 的金额
		if itemDiscountAmount.GreaterThan(orderItem.GetTotalAmount()) {
			itemDiscountAmount = orderItem.GetTotalAmount()
		}

		orderItem.DiscountAmount = orderItem.GetDiscountAmount().Add(itemDiscountAmount)
		orderItem.TotalAmount = orderItem.GetTotalAmount().Sub(itemDiscountAmount)

		restDiscountAmount = restDiscountAmount.Sub(itemDiscountAmount)

		externalUUIDToDiscount[orderItem.ExternalUUID] = &model.OrderLineDiscount{
			BusinessID:            oe.order.BusinessID,
			OrderID:               oe.order.ID,
			OrderItemID:           orderItem.ID,
			DiscountType:          model.DiscountTypeAmount,
			DiscountAmount:        itemDiscountAmount,
			DiscountRate:          itemSplitRate,
			OrderItemExternalUUID: orderItem.ExternalUUID,
			IsDeleted:             false,
			ApplyType:             model.ApplyTypeItem,
			DiscountCodeID:        orderItem.DiscountCodeID,
		}
	}

	// 如果还有剩余的折扣金额，继续轧差
	for !restDiscountAmount.IsZero() {
		for i := 0; i < len(currentPromotionOrderItems) && !restDiscountAmount.IsZero(); i++ {
			if currentPromotionOrderItems[i].CannotApplyDiscount() {
				continue // 如果当前 item 已经没有金额了，跳过
			}

			// 每次轧差 0.01 元
			minAmount := decimal.NewFromInt(1).Shift(-AmountPrecision)
			currentPromotionOrderItems[i].DiscountAmount = currentPromotionOrderItems[i].GetDiscountAmount().
				Add(minAmount)
			currentPromotionOrderItems[i].TotalAmount = currentPromotionOrderItems[i].GetTotalAmount().
				Sub(minAmount)
			restDiscountAmount = restDiscountAmount.Sub(minAmount)

			// 更新折扣金额到对应的 OrderLineDiscount
			currentDiscount := externalUUIDToDiscount[currentPromotionOrderItems[i].ExternalUUID]
			currentDiscount.DiscountAmount = currentDiscount.GetDiscountAmount().Add(minAmount)
		}

		isAllItemTotalAmountZero := true

		for _, it := range currentPromotionOrderItems {
			if !it.CannotApplyDiscount() {
				isAllItemTotalAmountZero = false

				break
			}
		}

		if isAllItemTotalAmountZero {
			// 如果所有的 item 都没有金额了，直接退出
			break
		}
	}

	return externalUUIDToDiscount, restDiscountAmount
}

func (oe *OrderEngine) calculateOneTimeDiscount(
	op *model.OrderPromotion,
	orderItemList []*model.OrderItem,
) []*model.OrderLineDiscount {
	uuidSet := lo.SliceToMap(op.PromotionItems, func(opi *model.OrderPromotionItem) (string, struct{}) {
		return opi.ExternalUUID, struct{}{}
	})

	if op.DiscountType == orderpb.DiscountType_PERCENTAGE {
		lineDiscounts := make([]*model.OrderLineDiscount, 0, len(op.PromotionItems))
		oneTimeDiscountAmount := decimal.Zero
		totalDiscountAmount := decimal.Zero

		for _, orderItem := range orderItemList {
			if _, ok := uuidSet[orderItem.ExternalUUID]; !ok {
				continue // 如果当前 item 的 external uuid 不在 promotion items 中，跳过
			}

			// 如果已经抵扣完成了的，直接跳过
			if orderItem.CannotApplyDiscount() {
				continue
			}

			itemDiscountAmount := orderItem.GetTotalAmount().Mul(op.DiscountValue).Div(MinorUnits).RoundBank(AmountPrecision)
			totalDiscountAmount = totalDiscountAmount.Add(itemDiscountAmount)
			orderItem.DiscountAmount = orderItem.GetDiscountAmount().Add(itemDiscountAmount)
			orderItem.TotalAmount = orderItem.GetTotalAmount().Sub(itemDiscountAmount)
			oneTimeDiscountAmount = oneTimeDiscountAmount.Add(itemDiscountAmount)

			lineDiscounts = append(lineDiscounts, &model.OrderLineDiscount{
				BusinessID:            oe.order.BusinessID,
				OrderID:               oe.order.ID,
				OrderItemID:           orderItem.ID,
				DiscountType:          model.DiscountTypePercentage,
				DiscountAmount:        itemDiscountAmount,
				DiscountRate:          op.DiscountValue,
				ApplyType:             model.ApplyTypeItem,
				OrderItemExternalUUID: orderItem.ExternalUUID,
				IsDeleted:             false,
			})
		}

		// 重新计算 applied amount
		op.AppliedAmount = totalDiscountAmount

		return lineDiscounts
	}

	itemMap := make(map[string]*model.OrderItem, len(orderItemList))

	for _, it := range orderItemList {
		if _, ok := uuidSet[it.ExternalUUID]; !ok {
			continue // 如果当前 item 的 external uuid 不在 promotion items 中，跳过
		}

		// 如果已经抵扣完成了的，直接跳过
		if it.CannotApplyDiscount() {
			continue
		}

		if it.ExternalUUID != "" {
			itemMap[it.ExternalUUID] = it
		}
	}

	return oe.calculateFixedAmount(op, itemMap)
}

func (oe *OrderEngine) calculateForOneTimeFixedAmount(
	op *model.OrderPromotion,
	orderItemList []*model.OrderItem,
	discountType string,
) *model.OrderLineDiscount {
	orderItemTotalAmount := lo.Reduce(orderItemList,
		func(amount decimal.Decimal, it *model.OrderItem, _ int,
		) decimal.Decimal {
			// 如果已经抵扣完成了的，直接不累加
			if it.CannotApplyDiscount() {
				return amount
			}

			return amount.Add(it.GetTotalAmount())
		}, decimal.Zero)
	// 如果 order item 全部被抵扣完了，那就不需要往下计算了
	if orderItemTotalAmount.LessThanOrEqual(decimal.Zero) {
		op.AppliedAmount = decimal.Zero
		return nil
	}

	_, restDiscountAmount := oe.nettingForFixAmount(op.AppliedAmount, orderItemTotalAmount, orderItemList)
	totalDiscountAmount := op.AppliedAmount.Sub(restDiscountAmount).RoundBank(AmountPrecision)
	op.AppliedAmount = totalDiscountAmount
	op.DiscountValue = totalDiscountAmount

	return &model.OrderLineDiscount{
		BusinessID:     oe.order.BusinessID,
		OrderID:        oe.order.ID,
		OrderItemID:    0,
		DiscountType:   discountType,
		DiscountAmount: totalDiscountAmount,
		DiscountRate: totalDiscountAmount.
			Mul(MinorUnits).
			DivRound(orderItemTotalAmount, RatePrecision),
		ApplyType: model.ApplyTypeAll,
		IsDeleted: false,
	}
}

func (oe *OrderEngine) calculatePromotions() (
	[]*model.OrderLineDiscount,
	decimal.Decimal,
	map[string]orderItemDiscountHelper,
) {
	// 深拷贝一次便于运算
	orderItemList := lo.Map(oe.items, func(it *model.OrderItem, _ int) *model.OrderItem {
		orderItem := it.Clone()
		unitPrice, subTotalItems := oe.calculateUnitPrice(orderItem)

		subQuantity := getSubQuantity(it)
		subTotal := CalculateSubTotal(unitPrice, subQuantity)
		taxBase := subTotal.Sub(it.GetDiscountAmount())

		orderItem.UnitPrice = unitPrice
		orderItem.SubTotalItems = subTotalItems
		orderItem.SubTotalAmount = subTotal
		orderItem.TotalAmount = taxBase

		return orderItem
	})

	discounts := make([]*model.OrderLineDiscount, 0, len(orderItemList))
	itemMap := make(map[string]*model.OrderItem, len(orderItemList))

	for _, it := range orderItemList {
		if it.ExternalUUID != "" {
			itemMap[it.ExternalUUID] = it
		}
	}

	// 排序，让 package 类型的 promotion 先处理
	sort.SliceStable(oe.promotions, func(i, j int) bool {
		return oe.promotions[i].IsDeduction() &&
			!oe.promotions[j].IsDeduction()
	})

	for _, op := range oe.promotions {
		if op.IsOneTimeDiscount() {
			if d := oe.calculateOneTimeDiscount(op, orderItemList); d != nil {
				discounts = append(discounts, d...)
			}

			continue
		}

		if op.IsStoreCredit() {
			if d := oe.calculateForOneTimeFixedAmount(op, orderItemList, model.DiscountTypeCredit); d != nil {
				discounts = append(discounts, d)
			}

			continue
		}

		switch op.DiscountType {
		// deduction 的需要修改 purchase
		case orderpb.DiscountType_ITEM_DEDUCTION:
			oe.calculateDeduction(op, itemMap)
		// 其他的需要同步到 discounts , 同时需要计算 item 的 discount amount
		case orderpb.DiscountType_PERCENTAGE:
			discounts = append(discounts, oe.calculatePercentage(op, itemMap)...)
		case orderpb.DiscountType_FIXED_AMOUNT:
			discounts = append(discounts, oe.calculateFixedAmount(op, itemMap)...)
		default:
		}
	}

	// 过滤掉 applied amount 为0的 promotion，说明是不能使用上的
	oe.promotions = lo.Filter(oe.promotions, func(p *model.OrderPromotion, _ int) bool {
		return p.AppliedAmount.GreaterThan(decimal.Zero)
	})

	// 计算总的折扣金额
	totalDiscountAmount := lo.Reduce(discounts,
		func(amount decimal.Decimal, it *model.OrderLineDiscount, _ int) decimal.Decimal {
			return amount.Add(it.DiscountAmount)
		}, decimal.Zero)

	// 将 discounts 转换为 orderItemDiscountHelper
	externalUUIDToHelper := lo.Associate(orderItemList,
		func(it *model.OrderItem) (string, orderItemDiscountHelper) {
			return it.ExternalUUID, orderItemDiscountHelper{
				ExternalUUID:      it.ExternalUUID,
				OrderItemID:       it.ID,
				DiscountAmount:    it.GetDiscountAmount(),
				PurchasedQuantity: it.PurchasedQuantity,
			}
		},
	)

	return discounts, totalDiscountAmount, externalUUIDToHelper
}

type orderItemDiscountHelper struct {
	ExternalUUID      string
	OrderItemID       int64
	DiscountAmount    decimal.Decimal
	PurchasedQuantity int32
}
