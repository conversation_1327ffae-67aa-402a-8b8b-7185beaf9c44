package core

import (
	"testing"

	"github.com/shopspring/decimal"
	"github.com/stretchr/testify/suite"

	orderpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/order/v1"
	"github.com/MoeGolibrary/moego-svc-order-v2/internal/model"
)

type CalculateOneTimeDiscountTestSuite struct {
	suite.Suite
	orderEngine *OrderEngine
	mockOrder   *model.Order
}

func TestCalculateOneTimeDiscountSuite(t *testing.T) {
	suite.Run(t, new(CalculateOneTimeDiscountTestSuite))
}

func (ts *CalculateOneTimeDiscountTestSuite) SetupTest() {
	ts.mockOrder = &model.Order{
		ID:         12345,
		BusinessID: 67890,
	}
	ts.orderEngine = &OrderEngine{
		order: ts.mockOrder,
	}
}

func (ts *CalculateOneTimeDiscountTestSuite) TestCalculateOneTimeDiscount_PercentageWithSpecificItems() {
	op := &model.OrderPromotion{
		DiscountType:  orderpb.DiscountType_PERCENTAGE,
		DiscountValue: decimal.NewFromInt(10), // 10%
		PromotionItems: []*model.OrderPromotionItem{
			{ExternalUUID: "item-001"},
			{ExternalUUID: "item-002"},
		},
	}

	orderItems := []*model.OrderItem{
		{
			ID:                1,
			ExternalUUID:      "item-001",
			TotalAmount:       decimal.NewFromInt(100),
			DiscountAmount:    decimal.Zero,
			CurrencyCode:      "USD",
			ItemType:          "service",
			Quantity:          1,
			PurchasedQuantity: 0,
		},
		{
			ID:                2,
			ExternalUUID:      "item-002",
			TotalAmount:       decimal.NewFromInt(200),
			DiscountAmount:    decimal.Zero,
			CurrencyCode:      "USD",
			ItemType:          "service",
			Quantity:          1,
			PurchasedQuantity: 0,
		},
		{
			ID:                3,
			ExternalUUID:      "item-003",
			TotalAmount:       decimal.NewFromInt(300),
			DiscountAmount:    decimal.Zero,
			CurrencyCode:      "USD",
			ItemType:          "service",
			Quantity:          1,
			PurchasedQuantity: 0,
		},
	}

	result := ts.orderEngine.calculateOneTimeDiscount(op, orderItems)

	ts.Require().Len(result, 2, "Should return 2 discount records")

	ts.Equal(int64(1), result[0].OrderItemID)
	ts.Equal("item-001", result[0].OrderItemExternalUUID)
	ts.Equal(model.DiscountTypePercentage, result[0].DiscountType)
	ts.Equal(model.ApplyTypeItem, result[0].ApplyType)
	ts.Equal(decimal.New(1000, -2), result[0].DiscountAmount) // 100 * 10% = 10.00

	ts.Equal(int64(2), result[1].OrderItemID)
	ts.Equal("item-002", result[1].OrderItemExternalUUID)
	ts.Equal(decimal.New(2000, -2), result[1].DiscountAmount) // 200 * 10% = 20.00

	ts.Equal(decimal.New(3000, -2), op.AppliedAmount) // 10 + 20 = 30
}

func (ts *CalculateOneTimeDiscountTestSuite) TestCalculateOneTimeDiscount_FixedAmountAllocation() {
	op := &model.OrderPromotion{
		DiscountType:  orderpb.DiscountType_FIXED_AMOUNT,
		DiscountValue: decimal.NewFromInt(60),
		AppliedAmount: decimal.NewFromInt(60),
		PromotionItems: []*model.OrderPromotionItem{
			{ExternalUUID: "item-A"},
			{ExternalUUID: "item-B"},
		},
	}

	orderItems := []*model.OrderItem{
		{
			ID:                1,
			ExternalUUID:      "item-A",
			TotalAmount:       decimal.NewFromInt(100),
			DiscountAmount:    decimal.Zero,
			CurrencyCode:      "USD",
			ItemType:          "service",
			Quantity:          1,
			PurchasedQuantity: 0,
		},
		{
			ID:                2,
			ExternalUUID:      "item-B",
			TotalAmount:       decimal.NewFromInt(200),
			DiscountAmount:    decimal.Zero,
			CurrencyCode:      "USD",
			ItemType:          "service",
			Quantity:          1,
			PurchasedQuantity: 0,
		},
		{
			ID:                3,
			ExternalUUID:      "item-C", // Not in promotion items
			TotalAmount:       decimal.NewFromInt(300),
			DiscountAmount:    decimal.Zero,
			CurrencyCode:      "USD",
			ItemType:          "service",
			Quantity:          1,
			PurchasedQuantity: 0,
		},
	}

	result := ts.orderEngine.calculateOneTimeDiscount(op, orderItems)

	ts.NotNil(result, "Should return discount records")

	for _, discount := range result {
		switch discount.OrderItemExternalUUID {
		case "item-A":
			ts.Equal(int64(1), discount.OrderItemID, "Should match item-A ID")
			ts.True(decimal.NewFromInt(20).Equal(discount.DiscountAmount), "Should allocate 20 to item-A")
		case "item-B":
			ts.Equal(int64(2), discount.OrderItemID, "Should match item-B ID")
			ts.True(decimal.NewFromInt(40).Equal(discount.DiscountAmount), "Should allocate 40 to item-B")
		}

		ts.True(discount.OrderItemExternalUUID == "item-A" || discount.OrderItemExternalUUID == "item-B",
			"Should only process items in promotion list")
		ts.NotEqual("item-C", discount.OrderItemExternalUUID, "Should not process item-C")
	}
}

func (ts *CalculateOneTimeDiscountTestSuite) TestCalculateOneTimeDiscount_UUIDFilteringLogic() {
	op := &model.OrderPromotion{
		DiscountType:  orderpb.DiscountType_PERCENTAGE,
		DiscountValue: decimal.NewFromInt(15), // 15%
		PromotionItems: []*model.OrderPromotionItem{
			{ExternalUUID: "valid-uuid-1"},
			{ExternalUUID: "valid-uuid-2"},
		},
	}

	orderItems := []*model.OrderItem{
		{
			ID:                1,
			ExternalUUID:      "valid-uuid-1",
			TotalAmount:       decimal.NewFromInt(100),
			DiscountAmount:    decimal.Zero,
			CurrencyCode:      "USD",
			ItemType:          "service",
			Quantity:          1,
			PurchasedQuantity: 0,
		},
		{
			ID:                2,
			ExternalUUID:      "invalid-uuid",
			TotalAmount:       decimal.NewFromInt(200),
			DiscountAmount:    decimal.Zero,
			CurrencyCode:      "USD",
			ItemType:          "service",
			Quantity:          1,
			PurchasedQuantity: 0,
		},
		{
			ID:                3,
			ExternalUUID:      "valid-uuid-2",
			TotalAmount:       decimal.NewFromInt(150),
			DiscountAmount:    decimal.Zero,
			CurrencyCode:      "USD",
			ItemType:          "service",
			Quantity:          1,
			PurchasedQuantity: 0,
		},
		{
			ID:                4,
			ExternalUUID:      "",
			TotalAmount:       decimal.NewFromInt(50),
			DiscountAmount:    decimal.Zero,
			CurrencyCode:      "USD",
			ItemType:          "service",
			Quantity:          1,
			PurchasedQuantity: 0,
		},
	}

	result := ts.orderEngine.calculateOneTimeDiscount(op, orderItems)

	ts.Require().Len(result, 2, "Should only process items with matching UUIDs")

	uuids := make([]string, len(result))
	for i, discount := range result {
		uuids[i] = discount.OrderItemExternalUUID
	}

	ts.Contains(uuids, "valid-uuid-1")
	ts.Contains(uuids, "valid-uuid-2")
	ts.NotContains(uuids, "invalid-uuid")
	ts.NotContains(uuids, "")
}

func (ts *CalculateOneTimeDiscountTestSuite) TestCalculateOneTimeDiscount_SkipFullyDiscountedItems() {
	op := &model.OrderPromotion{
		DiscountType:  orderpb.DiscountType_PERCENTAGE,
		DiscountValue: decimal.NewFromInt(20), // 20%
		PromotionItems: []*model.OrderPromotionItem{
			{ExternalUUID: "item-1"},
			{ExternalUUID: "item-2"},
		},
	}

	orderItems := []*model.OrderItem{
		{
			ID:                1,
			ExternalUUID:      "item-1",
			TotalAmount:       decimal.NewFromInt(100),
			DiscountAmount:    decimal.Zero,
			CurrencyCode:      "USD",
			ItemType:          "service",
			Quantity:          1,
			PurchasedQuantity: 0,
		},
		{
			ID:                2,
			ExternalUUID:      "item-2",
			TotalAmount:       decimal.Zero, // Zero amount means CannotApplyDiscount() returns true
			DiscountAmount:    decimal.Zero,
			CurrencyCode:      "USD",
			ItemType:          "service",
			Quantity:          1,
			PurchasedQuantity: 0,
		},
	}

	result := ts.orderEngine.calculateOneTimeDiscount(op, orderItems)

	ts.Require().Len(result, 1, "Should only return discount for item that can be discounted")
	ts.Equal("item-1", result[0].OrderItemExternalUUID)
	ts.Equal(int64(1), result[0].OrderItemID)
}

func (ts *CalculateOneTimeDiscountTestSuite) TestCalculateOneTimeDiscount_EmptyPromotionItems() {
	op := &model.OrderPromotion{
		DiscountType:   orderpb.DiscountType_PERCENTAGE,
		DiscountValue:  decimal.NewFromInt(10),
		PromotionItems: []*model.OrderPromotionItem{}, // Empty array
	}

	orderItems := []*model.OrderItem{
		{
			ID:                1,
			ExternalUUID:      "item-1",
			TotalAmount:       decimal.NewFromInt(100),
			DiscountAmount:    decimal.Zero,
			CurrencyCode:      "USD",
			ItemType:          "service",
			Quantity:          1,
			PurchasedQuantity: 0,
		},
	}

	result := ts.orderEngine.calculateOneTimeDiscount(op, orderItems)

	ts.Require().Empty(result, "Should return empty array when no promotion items")
	ts.Equal(decimal.Zero, op.AppliedAmount, "Applied amount should be zero")
}

func (ts *CalculateOneTimeDiscountTestSuite) TestCalculateOneTimeDiscount_SingleItemFullDiscount() {
	op := &model.OrderPromotion{
		DiscountType:  orderpb.DiscountType_PERCENTAGE,
		DiscountValue: decimal.NewFromInt(15), // 15%
		PromotionItems: []*model.OrderPromotionItem{
			{ExternalUUID: "single-item"},
		},
	}

	orderItems := []*model.OrderItem{
		{
			ID:                1,
			ExternalUUID:      "single-item",
			TotalAmount:       decimal.NewFromInt(99),
			DiscountAmount:    decimal.Zero,
			CurrencyCode:      "USD",
			ItemType:          "service",
			Quantity:          1,
			PurchasedQuantity: 0,
		},
	}

	result := ts.orderEngine.calculateOneTimeDiscount(op, orderItems)

	ts.Require().Len(result, 1, "Should return 1 discount record")

	discount := result[0]
	ts.Equal(ts.mockOrder.BusinessID, discount.BusinessID)
	ts.Equal(ts.mockOrder.ID, discount.OrderID)
	ts.Equal(int64(1), discount.OrderItemID)
	ts.Equal(model.DiscountTypePercentage, discount.DiscountType)
	ts.Equal(model.ApplyTypeItem, discount.ApplyType)
	ts.Equal("single-item", discount.OrderItemExternalUUID)
	ts.Equal(false, discount.IsDeleted)
	ts.Equal(decimal.NewFromInt(15), discount.DiscountRate)

	expectedDiscount := decimal.NewFromInt(99).Mul(decimal.NewFromInt(15)).Div(decimal.NewFromInt(100)).RoundBank(2)
	ts.Equal(expectedDiscount, discount.DiscountAmount)
}

func (ts *CalculateOneTimeDiscountTestSuite) TestCalculateOneTimeDiscount_ReturnStructureValidation() {
	op := &model.OrderPromotion{
		DiscountType:  orderpb.DiscountType_PERCENTAGE,
		DiscountValue: decimal.NewFromInt(25), // 25%
		PromotionItems: []*model.OrderPromotionItem{
			{ExternalUUID: "test-item-1"},
			{ExternalUUID: "test-item-2"},
		},
	}

	orderItems := []*model.OrderItem{
		{
			ID:                101,
			ExternalUUID:      "test-item-1",
			TotalAmount:       decimal.NewFromInt(80),
			DiscountAmount:    decimal.Zero,
			CurrencyCode:      "USD",
			ItemType:          "service",
			Quantity:          1,
			PurchasedQuantity: 0,
		},
		{
			ID:                102,
			ExternalUUID:      "test-item-2",
			TotalAmount:       decimal.NewFromInt(120),
			DiscountAmount:    decimal.Zero,
			CurrencyCode:      "USD",
			ItemType:          "service",
			Quantity:          1,
			PurchasedQuantity: 0,
		},
	}

	result := ts.orderEngine.calculateOneTimeDiscount(op, orderItems)

	ts.Require().Len(result, 2, "Should return 2 discount records")

	for i, discount := range result {
		ts.Equal(ts.mockOrder.BusinessID, discount.BusinessID, "BusinessID should match order")
		ts.Equal(ts.mockOrder.ID, discount.OrderID, "OrderID should match order")
		ts.Equal(orderItems[i].ID, discount.OrderItemID, "OrderItemID should match item")
		ts.Equal(model.DiscountTypePercentage, discount.DiscountType, "DiscountType should be percentage")
		ts.Equal(model.ApplyTypeItem, discount.ApplyType, "ApplyType should be item-level")
		ts.Equal(orderItems[i].ExternalUUID, discount.OrderItemExternalUUID, "ExternalUUID should match")
		ts.Equal(false, discount.IsDeleted, "IsDeleted should be false")
		ts.Equal(decimal.NewFromInt(25), discount.DiscountRate, "DiscountRate should match promotion value")
		ts.True(discount.DiscountAmount.GreaterThan(decimal.Zero), "DiscountAmount should be positive")
	}
}

func (ts *CalculateOneTimeDiscountTestSuite) TestCalculateOneTimeDiscount_ZeroAmountItems() {
	op := &model.OrderPromotion{
		DiscountType:  orderpb.DiscountType_PERCENTAGE,
		DiscountValue: decimal.NewFromInt(10), // 10%
		PromotionItems: []*model.OrderPromotionItem{
			{ExternalUUID: "zero-item"},
			{ExternalUUID: "normal-item"},
		},
	}

	orderItems := []*model.OrderItem{
		{
			ID:                1,
			ExternalUUID:      "zero-item",
			TotalAmount:       decimal.Zero, // Zero amount - will be skipped by CannotApplyDiscount()
			DiscountAmount:    decimal.Zero,
			CurrencyCode:      "USD",
			ItemType:          "service",
			Quantity:          1,
			PurchasedQuantity: 0,
		},
		{
			ID:                2,
			ExternalUUID:      "normal-item",
			TotalAmount:       decimal.NewFromInt(100),
			DiscountAmount:    decimal.Zero,
			CurrencyCode:      "USD",
			ItemType:          "service",
			Quantity:          1,
			PurchasedQuantity: 0,
		},
	}

	result := ts.orderEngine.calculateOneTimeDiscount(op, orderItems)

	ts.Require().Len(result, 1, "Should return 1 discount record (zero-amount item skipped)")

	ts.Equal("normal-item", result[0].OrderItemExternalUUID)
	ts.Equal(decimal.New(1000, -2), result[0].DiscountAmount, "Normal item should have calculated discount")
}
