package main

import (
	"context"
	"flag"
	"os"
	"os/signal"
	"syscall"

	"github.com/MoeGolibrary/go-lib/gorm"
	"github.com/MoeGolibrary/go-lib/grpc"
	"github.com/MoeGolibrary/go-lib/redis"
	marketingsvcpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/marketing/v1"
	ordersvcpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/order/v2"
	"github.com/MoeGolibrary/moego-svc-order-v2/internal/controller"
	"github.com/MoeGolibrary/moego-svc-order-v2/internal/repo"
	"github.com/MoeGolibrary/moego-svc-order-v2/internal/repo/business"
	"github.com/MoeGolibrary/moego-svc-order-v2/internal/repo/depositrule"
	"github.com/MoeGolibrary/moego-svc-order-v2/internal/repo/grooming"
	"github.com/MoeGolibrary/moego-svc-order-v2/internal/repo/message"
	"github.com/MoeGolibrary/moego-svc-order-v2/internal/repo/promotion"
	"github.com/MoeGolibrary/moego-svc-order-v2/internal/repo/subscription"
	"github.com/MoeGolibrary/moego-svc-order-v2/internal/service"
	"github.com/MoeGolibrary/moego-svc-order-v2/internal/service/handler"
	"github.com/MoeGolibrary/moego-svc-order-v2/internal/service/helper"
	"github.com/growthbook/growthbook-golang"
	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"
	"gopkg.in/DataDog/dd-trace-go.v1/profiler"

	"github.com/MoeGolibrary/go-lib/zlog"
	"github.com/MoeGolibrary/moego-svc-order-v2/config"
	"github.com/MoeGolibrary/moego-svc-order-v2/internal/wire"
)

func main() {
	flag.Parse()

	zlog.InitLogger(zlog.NewConfig(zlog.WithLevel(zapcore.DebugLevel)))
	config.Init()

	server := wire.NewServer()
	server.Start()
	zlog.Default().Info("server started")

	// Datadog Profiler.
	startProfiler()

	// 手动调用 internal/controller/order_tips_consumer.go:44 方法
	// 参数从 

	signalChan := make(chan os.Signal, 1)
	signal.Notify(signalChan, syscall.SIGINT, syscall.SIGTERM)

	for sig := range signalChan {
		zlog.Default().Info(
			"received signal",
			zap.String("signal", sig.String()),
		)

		server.Stop()
		zlog.Default().Info("server stopped")
		profiler.Stop()

		break
	}
}

func startProfiler() {
	// Datadog Profiler
	if err := profiler.Start(
		profiler.WithProfileTypes(profiler.CPUProfile, profiler.HeapProfile),
	); err != nil {
		zlog.Default().Error("start DD continue profiler failed", zap.Error(err))
	}
}

func newOrderDB() *gorm.DB {
	return gorm.Must(gorm.OpenPostgres(config.OrderDSN(), nil))
}

func newPaymentDB() *repo.PaymentDB {
	return (*repo.PaymentDB)(gorm.Must(gorm.OpenMySQL(config.PaymentDSN(), nil)))
}

func newRedisClient() redis.UniversalClient { return redis.NewClient(config.Redis()) }

func newDiscountClient() marketingsvcpb.DiscountCodeServiceClient {
	return grpc.NewClient("moego-svc-marketing:9090", marketingsvcpb.NewDiscountCodeServiceClient)
}

func newGrowthBookClient() *growthbook.Client {
	gb, err := growthbook.NewClient(context.Background(), config.GrowthBook()...)
	if err != nil {
		panic(err)
	}
	return gb
}
