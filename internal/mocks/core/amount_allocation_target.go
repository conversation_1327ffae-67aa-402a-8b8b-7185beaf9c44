// Code generated by mockery v2.53.4. DO NOT EDIT.

package mocks

import (
	decimal "github.com/shopspring/decimal"
	mock "github.com/stretchr/testify/mock"
)

// amountAllocationTarget is an autogenerated mock type for the amountAllocationTarget type
type amountAllocationTarget struct {
	mock.Mock
}

type amountAllocationTarget_Expecter struct {
	mock *mock.Mock
}

func (_m *amountAllocationTarget) EXPECT() *amountAllocationTarget_Expecter {
	return &amountAllocationTarget_Expecter{mock: &_m.Mock}
}

// AllocateAmount provides a mock function with given fields: _a0
func (_m *amountAllocationTarget) AllocateAmount(_a0 decimal.Decimal) {
	_m.Called(_a0)
}

// amountAllocationTarget_AllocateAmount_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'AllocateAmount'
type amountAllocationTarget_AllocateAmount_Call struct {
	*mock.Call
}

// AllocateAmount is a helper method to define mock.On call
//   - _a0 decimal.Decimal
func (_e *amountAllocationTarget_Expecter) AllocateAmount(_a0 interface{}) *amountAllocationTarget_AllocateAmount_Call {
	return &amountAllocationTarget_AllocateAmount_Call{Call: _e.mock.On("AllocateAmount", _a0)}
}

func (_c *amountAllocationTarget_AllocateAmount_Call) Run(run func(_a0 decimal.Decimal)) *amountAllocationTarget_AllocateAmount_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(decimal.Decimal))
	})
	return _c
}

func (_c *amountAllocationTarget_AllocateAmount_Call) Return() *amountAllocationTarget_AllocateAmount_Call {
	_c.Call.Return()
	return _c
}

func (_c *amountAllocationTarget_AllocateAmount_Call) RunAndReturn(run func(decimal.Decimal)) *amountAllocationTarget_AllocateAmount_Call {
	_c.Run(run)
	return _c
}

// GetRequestedAmount provides a mock function with no fields
func (_m *amountAllocationTarget) GetRequestedAmount() decimal.Decimal {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for GetRequestedAmount")
	}

	var r0 decimal.Decimal
	if rf, ok := ret.Get(0).(func() decimal.Decimal); ok {
		r0 = rf()
	} else {
		r0 = ret.Get(0).(decimal.Decimal)
	}

	return r0
}

// amountAllocationTarget_GetRequestedAmount_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetRequestedAmount'
type amountAllocationTarget_GetRequestedAmount_Call struct {
	*mock.Call
}

// GetRequestedAmount is a helper method to define mock.On call
func (_e *amountAllocationTarget_Expecter) GetRequestedAmount() *amountAllocationTarget_GetRequestedAmount_Call {
	return &amountAllocationTarget_GetRequestedAmount_Call{Call: _e.mock.On("GetRequestedAmount")}
}

func (_c *amountAllocationTarget_GetRequestedAmount_Call) Run(run func()) *amountAllocationTarget_GetRequestedAmount_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *amountAllocationTarget_GetRequestedAmount_Call) Return(_a0 decimal.Decimal) *amountAllocationTarget_GetRequestedAmount_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *amountAllocationTarget_GetRequestedAmount_Call) RunAndReturn(run func() decimal.Decimal) *amountAllocationTarget_GetRequestedAmount_Call {
	_c.Call.Return(run)
	return _c
}

// newAmountAllocationTarget creates a new instance of amountAllocationTarget. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func newAmountAllocationTarget(t interface {
	mock.TestingT
	Cleanup(func())
}) *amountAllocationTarget {
	mock := &amountAllocationTarget{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
