// Code generated by mockery v2.53.4. DO NOT EDIT.

package mocks

import (
	mock "github.com/stretchr/testify/mock"

	orderpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/order/v1"
)

// RefundPaymentor is an autogenerated mock type for the RefundPaymentor type
type RefundPaymentor struct {
	mock.Mock
}

type RefundPaymentor_Expecter struct {
	mock *mock.Mock
}

func (_m *RefundPaymentor) EXPECT() *RefundPaymentor_Expecter {
	return &RefundPaymentor_Expecter{mock: &_m.Mock}
}

// GetError provides a mock function with no fields
func (_m *RefundPaymentor) GetError() string {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for GetError")
	}

	var r0 string
	if rf, ok := ret.Get(0).(func() string); ok {
		r0 = rf()
	} else {
		r0 = ret.Get(0).(string)
	}

	return r0
}

// RefundPaymentor_GetError_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetError'
type RefundPaymentor_GetError_Call struct {
	*mock.Call
}

// GetError is a helper method to define mock.On call
func (_e *RefundPaymentor_Expecter) GetError() *RefundPaymentor_GetError_Call {
	return &RefundPaymentor_GetError_Call{Call: _e.mock.On("GetError")}
}

func (_c *RefundPaymentor_GetError_Call) Run(run func()) *RefundPaymentor_GetError_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *RefundPaymentor_GetError_Call) Return(_a0 string) *RefundPaymentor_GetError_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *RefundPaymentor_GetError_Call) RunAndReturn(run func() string) *RefundPaymentor_GetError_Call {
	_c.Call.Return(run)
	return _c
}

// GetID provides a mock function with no fields
func (_m *RefundPaymentor) GetID() int64 {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for GetID")
	}

	var r0 int64
	if rf, ok := ret.Get(0).(func() int64); ok {
		r0 = rf()
	} else {
		r0 = ret.Get(0).(int64)
	}

	return r0
}

// RefundPaymentor_GetID_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetID'
type RefundPaymentor_GetID_Call struct {
	*mock.Call
}

// GetID is a helper method to define mock.On call
func (_e *RefundPaymentor_Expecter) GetID() *RefundPaymentor_GetID_Call {
	return &RefundPaymentor_GetID_Call{Call: _e.mock.On("GetID")}
}

func (_c *RefundPaymentor_GetID_Call) Run(run func()) *RefundPaymentor_GetID_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *RefundPaymentor_GetID_Call) Return(_a0 int64) *RefundPaymentor_GetID_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *RefundPaymentor_GetID_Call) RunAndReturn(run func() int64) *RefundPaymentor_GetID_Call {
	_c.Call.Return(run)
	return _c
}

// GetRefundOrderPaymentID provides a mock function with no fields
func (_m *RefundPaymentor) GetRefundOrderPaymentID() int64 {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for GetRefundOrderPaymentID")
	}

	var r0 int64
	if rf, ok := ret.Get(0).(func() int64); ok {
		r0 = rf()
	} else {
		r0 = ret.Get(0).(int64)
	}

	return r0
}

// RefundPaymentor_GetRefundOrderPaymentID_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetRefundOrderPaymentID'
type RefundPaymentor_GetRefundOrderPaymentID_Call struct {
	*mock.Call
}

// GetRefundOrderPaymentID is a helper method to define mock.On call
func (_e *RefundPaymentor_Expecter) GetRefundOrderPaymentID() *RefundPaymentor_GetRefundOrderPaymentID_Call {
	return &RefundPaymentor_GetRefundOrderPaymentID_Call{Call: _e.mock.On("GetRefundOrderPaymentID")}
}

func (_c *RefundPaymentor_GetRefundOrderPaymentID_Call) Run(run func()) *RefundPaymentor_GetRefundOrderPaymentID_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *RefundPaymentor_GetRefundOrderPaymentID_Call) Return(_a0 int64) *RefundPaymentor_GetRefundOrderPaymentID_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *RefundPaymentor_GetRefundOrderPaymentID_Call) RunAndReturn(run func() int64) *RefundPaymentor_GetRefundOrderPaymentID_Call {
	_c.Call.Return(run)
	return _c
}

// GetRefundStatus provides a mock function with no fields
func (_m *RefundPaymentor) GetRefundStatus() orderpb.RefundOrderPaymentStatus {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for GetRefundStatus")
	}

	var r0 orderpb.RefundOrderPaymentStatus
	if rf, ok := ret.Get(0).(func() orderpb.RefundOrderPaymentStatus); ok {
		r0 = rf()
	} else {
		r0 = ret.Get(0).(orderpb.RefundOrderPaymentStatus)
	}

	return r0
}

// RefundPaymentor_GetRefundStatus_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetRefundStatus'
type RefundPaymentor_GetRefundStatus_Call struct {
	*mock.Call
}

// GetRefundStatus is a helper method to define mock.On call
func (_e *RefundPaymentor_Expecter) GetRefundStatus() *RefundPaymentor_GetRefundStatus_Call {
	return &RefundPaymentor_GetRefundStatus_Call{Call: _e.mock.On("GetRefundStatus")}
}

func (_c *RefundPaymentor_GetRefundStatus_Call) Run(run func()) *RefundPaymentor_GetRefundStatus_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *RefundPaymentor_GetRefundStatus_Call) Return(_a0 orderpb.RefundOrderPaymentStatus) *RefundPaymentor_GetRefundStatus_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *RefundPaymentor_GetRefundStatus_Call) RunAndReturn(run func() orderpb.RefundOrderPaymentStatus) *RefundPaymentor_GetRefundStatus_Call {
	_c.Call.Return(run)
	return _c
}

// NewRefundPaymentor creates a new instance of RefundPaymentor. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewRefundPaymentor(t interface {
	mock.TestingT
	Cleanup(func())
}) *RefundPaymentor {
	mock := &RefundPaymentor{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
