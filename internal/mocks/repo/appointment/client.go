// Code generated by mockery v2.53.4. DO NOT EDIT.

package mocks

import (
	appointmentpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/appointment/v1"
	appointmentsvcpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/appointment/v1"

	context "context"

	mock "github.com/stretchr/testify/mock"
)

// Client is an autogenerated mock type for the Client type
type Client struct {
	mock.Mock
}

type Client_Expecter struct {
	mock *mock.Mock
}

func (_m *Client) EXPECT() *Client_Expecter {
	return &Client_Expecter{mock: &_m.Mock}
}

// ListAppointmentsForCustomers provides a mock function with given fields: ctx, customerID, companyID, businessID, apptStatusList
func (_m *Client) ListAppointmentsForCustomers(ctx context.Context, customerID int64, companyID int64, businessID *int64, apptStatusList []appointmentpb.AppointmentPaymentStatus) (*appointmentsvcpb.ListAppointmentsResponse, error) {
	ret := _m.Called(ctx, customerID, companyID, businessID, apptStatusList)

	if len(ret) == 0 {
		panic("no return value specified for ListAppointmentsForCustomers")
	}

	var r0 *appointmentsvcpb.ListAppointmentsResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, int64, int64, *int64, []appointmentpb.AppointmentPaymentStatus) (*appointmentsvcpb.ListAppointmentsResponse, error)); ok {
		return rf(ctx, customerID, companyID, businessID, apptStatusList)
	}
	if rf, ok := ret.Get(0).(func(context.Context, int64, int64, *int64, []appointmentpb.AppointmentPaymentStatus) *appointmentsvcpb.ListAppointmentsResponse); ok {
		r0 = rf(ctx, customerID, companyID, businessID, apptStatusList)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*appointmentsvcpb.ListAppointmentsResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, int64, int64, *int64, []appointmentpb.AppointmentPaymentStatus) error); ok {
		r1 = rf(ctx, customerID, companyID, businessID, apptStatusList)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// Client_ListAppointmentsForCustomers_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ListAppointmentsForCustomers'
type Client_ListAppointmentsForCustomers_Call struct {
	*mock.Call
}

// ListAppointmentsForCustomers is a helper method to define mock.On call
//   - ctx context.Context
//   - customerID int64
//   - companyID int64
//   - businessID *int64
//   - apptStatusList []appointmentpb.AppointmentPaymentStatus
func (_e *Client_Expecter) ListAppointmentsForCustomers(ctx interface{}, customerID interface{}, companyID interface{}, businessID interface{}, apptStatusList interface{}) *Client_ListAppointmentsForCustomers_Call {
	return &Client_ListAppointmentsForCustomers_Call{Call: _e.mock.On("ListAppointmentsForCustomers", ctx, customerID, companyID, businessID, apptStatusList)}
}

func (_c *Client_ListAppointmentsForCustomers_Call) Run(run func(ctx context.Context, customerID int64, companyID int64, businessID *int64, apptStatusList []appointmentpb.AppointmentPaymentStatus)) *Client_ListAppointmentsForCustomers_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(int64), args[2].(int64), args[3].(*int64), args[4].([]appointmentpb.AppointmentPaymentStatus))
	})
	return _c
}

func (_c *Client_ListAppointmentsForCustomers_Call) Return(_a0 *appointmentsvcpb.ListAppointmentsResponse, _a1 error) *Client_ListAppointmentsForCustomers_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *Client_ListAppointmentsForCustomers_Call) RunAndReturn(run func(context.Context, int64, int64, *int64, []appointmentpb.AppointmentPaymentStatus) (*appointmentsvcpb.ListAppointmentsResponse, error)) *Client_ListAppointmentsForCustomers_Call {
	_c.Call.Return(run)
	return _c
}

// ListPetDetailByGroomingID provides a mock function with given fields: ctx, groomingID
func (_m *Client) ListPetDetailByGroomingID(ctx context.Context, groomingID int64) (*appointmentsvcpb.GetPetDetailListResponse, error) {
	ret := _m.Called(ctx, groomingID)

	if len(ret) == 0 {
		panic("no return value specified for ListPetDetailByGroomingID")
	}

	var r0 *appointmentsvcpb.GetPetDetailListResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, int64) (*appointmentsvcpb.GetPetDetailListResponse, error)); ok {
		return rf(ctx, groomingID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, int64) *appointmentsvcpb.GetPetDetailListResponse); ok {
		r0 = rf(ctx, groomingID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*appointmentsvcpb.GetPetDetailListResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, int64) error); ok {
		r1 = rf(ctx, groomingID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// Client_ListPetDetailByGroomingID_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ListPetDetailByGroomingID'
type Client_ListPetDetailByGroomingID_Call struct {
	*mock.Call
}

// ListPetDetailByGroomingID is a helper method to define mock.On call
//   - ctx context.Context
//   - groomingID int64
func (_e *Client_Expecter) ListPetDetailByGroomingID(ctx interface{}, groomingID interface{}) *Client_ListPetDetailByGroomingID_Call {
	return &Client_ListPetDetailByGroomingID_Call{Call: _e.mock.On("ListPetDetailByGroomingID", ctx, groomingID)}
}

func (_c *Client_ListPetDetailByGroomingID_Call) Run(run func(ctx context.Context, groomingID int64)) *Client_ListPetDetailByGroomingID_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(int64))
	})
	return _c
}

func (_c *Client_ListPetDetailByGroomingID_Call) Return(_a0 *appointmentsvcpb.GetPetDetailListResponse, _a1 error) *Client_ListPetDetailByGroomingID_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *Client_ListPetDetailByGroomingID_Call) RunAndReturn(run func(context.Context, int64) (*appointmentsvcpb.GetPetDetailListResponse, error)) *Client_ListPetDetailByGroomingID_Call {
	_c.Call.Return(run)
	return _c
}

// NewClient creates a new instance of Client. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewClient(t interface {
	mock.TestingT
	Cleanup(func())
}) *Client {
	mock := &Client{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
