// Code generated by mockery v2.53.4. DO NOT EDIT.

package mocks

import (
	context "context"

	mock "github.com/stretchr/testify/mock"

	organizationpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/organization/v1"
)

// OrganizationRepo is an autogenerated mock type for the OrganizationRepo type
type OrganizationRepo struct {
	mock.Mock
}

type OrganizationRepo_Expecter struct {
	mock *mock.Mock
}

func (_m *OrganizationRepo) EXPECT() *OrganizationRepo_Expecter {
	return &OrganizationRepo_Expecter{mock: &_m.Mock}
}

// BatchGetLocation provides a mock function with given fields: ctx, locationIDs
func (_m *OrganizationRepo) BatchGetLocation(ctx context.Context, locationIDs []int64) ([]*organizationpb.LocationModel, error) {
	ret := _m.Called(ctx, locationIDs)

	if len(ret) == 0 {
		panic("no return value specified for BatchGetLocation")
	}

	var r0 []*organizationpb.LocationModel
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, []int64) ([]*organizationpb.LocationModel, error)); ok {
		return rf(ctx, locationIDs)
	}
	if rf, ok := ret.Get(0).(func(context.Context, []int64) []*organizationpb.LocationModel); ok {
		r0 = rf(ctx, locationIDs)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*organizationpb.LocationModel)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, []int64) error); ok {
		r1 = rf(ctx, locationIDs)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// OrganizationRepo_BatchGetLocation_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'BatchGetLocation'
type OrganizationRepo_BatchGetLocation_Call struct {
	*mock.Call
}

// BatchGetLocation is a helper method to define mock.On call
//   - ctx context.Context
//   - locationIDs []int64
func (_e *OrganizationRepo_Expecter) BatchGetLocation(ctx interface{}, locationIDs interface{}) *OrganizationRepo_BatchGetLocation_Call {
	return &OrganizationRepo_BatchGetLocation_Call{Call: _e.mock.On("BatchGetLocation", ctx, locationIDs)}
}

func (_c *OrganizationRepo_BatchGetLocation_Call) Run(run func(ctx context.Context, locationIDs []int64)) *OrganizationRepo_BatchGetLocation_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].([]int64))
	})
	return _c
}

func (_c *OrganizationRepo_BatchGetLocation_Call) Return(_a0 []*organizationpb.LocationModel, _a1 error) *OrganizationRepo_BatchGetLocation_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *OrganizationRepo_BatchGetLocation_Call) RunAndReturn(run func(context.Context, []int64) ([]*organizationpb.LocationModel, error)) *OrganizationRepo_BatchGetLocation_Call {
	_c.Call.Return(run)
	return _c
}

// GetCompanyPreference provides a mock function with given fields: ctx, companyID
func (_m *OrganizationRepo) GetCompanyPreference(ctx context.Context, companyID int64) (*organizationpb.CompanyPreferenceSettingModel, error) {
	ret := _m.Called(ctx, companyID)

	if len(ret) == 0 {
		panic("no return value specified for GetCompanyPreference")
	}

	var r0 *organizationpb.CompanyPreferenceSettingModel
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, int64) (*organizationpb.CompanyPreferenceSettingModel, error)); ok {
		return rf(ctx, companyID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, int64) *organizationpb.CompanyPreferenceSettingModel); ok {
		r0 = rf(ctx, companyID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*organizationpb.CompanyPreferenceSettingModel)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, int64) error); ok {
		r1 = rf(ctx, companyID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// OrganizationRepo_GetCompanyPreference_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetCompanyPreference'
type OrganizationRepo_GetCompanyPreference_Call struct {
	*mock.Call
}

// GetCompanyPreference is a helper method to define mock.On call
//   - ctx context.Context
//   - companyID int64
func (_e *OrganizationRepo_Expecter) GetCompanyPreference(ctx interface{}, companyID interface{}) *OrganizationRepo_GetCompanyPreference_Call {
	return &OrganizationRepo_GetCompanyPreference_Call{Call: _e.mock.On("GetCompanyPreference", ctx, companyID)}
}

func (_c *OrganizationRepo_GetCompanyPreference_Call) Run(run func(ctx context.Context, companyID int64)) *OrganizationRepo_GetCompanyPreference_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(int64))
	})
	return _c
}

func (_c *OrganizationRepo_GetCompanyPreference_Call) Return(_a0 *organizationpb.CompanyPreferenceSettingModel, _a1 error) *OrganizationRepo_GetCompanyPreference_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *OrganizationRepo_GetCompanyPreference_Call) RunAndReturn(run func(context.Context, int64) (*organizationpb.CompanyPreferenceSettingModel, error)) *OrganizationRepo_GetCompanyPreference_Call {
	_c.Call.Return(run)
	return _c
}

// NewOrganizationRepo creates a new instance of OrganizationRepo. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewOrganizationRepo(t interface {
	mock.TestingT
	Cleanup(func())
}) *OrganizationRepo {
	mock := &OrganizationRepo{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
