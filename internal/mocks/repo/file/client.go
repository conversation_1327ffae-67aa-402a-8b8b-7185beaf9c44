// Code generated by mockery v2.53.4. DO NOT EDIT.

package mocks

import (
	context "context"

	mock "github.com/stretchr/testify/mock"
)

// Client is an autogenerated mock type for the Client type
type Client struct {
	mock.Mock
}

type Client_Expecter struct {
	mock *mock.Mock
}

func (_m *Client) EXPECT() *Client_Expecter {
	return &Client_Expecter{mock: &_m.Mock}
}

// Create provides a mock function with given fields: ctx, staffID, companyID, fileName
func (_m *Client) Create(ctx context.Context, staffID int64, companyID int64, fileName string) (int64, error) {
	ret := _m.Called(ctx, staffID, companyID, fileName)

	if len(ret) == 0 {
		panic("no return value specified for Create")
	}

	var r0 int64
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, int64, int64, string) (int64, error)); ok {
		return rf(ctx, staffID, companyID, fileName)
	}
	if rf, ok := ret.Get(0).(func(context.Context, int64, int64, string) int64); ok {
		r0 = rf(ctx, staffID, companyID, fileName)
	} else {
		r0 = ret.Get(0).(int64)
	}

	if rf, ok := ret.Get(1).(func(context.Context, int64, int64, string) error); ok {
		r1 = rf(ctx, staffID, companyID, fileName)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// Client_Create_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Create'
type Client_Create_Call struct {
	*mock.Call
}

// Create is a helper method to define mock.On call
//   - ctx context.Context
//   - staffID int64
//   - companyID int64
//   - fileName string
func (_e *Client_Expecter) Create(ctx interface{}, staffID interface{}, companyID interface{}, fileName interface{}) *Client_Create_Call {
	return &Client_Create_Call{Call: _e.mock.On("Create", ctx, staffID, companyID, fileName)}
}

func (_c *Client_Create_Call) Run(run func(ctx context.Context, staffID int64, companyID int64, fileName string)) *Client_Create_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(int64), args[2].(int64), args[3].(string))
	})
	return _c
}

func (_c *Client_Create_Call) Return(_a0 int64, _a1 error) *Client_Create_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *Client_Create_Call) RunAndReturn(run func(context.Context, int64, int64, string) (int64, error)) *Client_Create_Call {
	_c.Call.Return(run)
	return _c
}

// Upload provides a mock function with given fields: ctx, fileID, content
func (_m *Client) Upload(ctx context.Context, fileID int64, content []byte) error {
	ret := _m.Called(ctx, fileID, content)

	if len(ret) == 0 {
		panic("no return value specified for Upload")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, int64, []byte) error); ok {
		r0 = rf(ctx, fileID, content)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// Client_Upload_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Upload'
type Client_Upload_Call struct {
	*mock.Call
}

// Upload is a helper method to define mock.On call
//   - ctx context.Context
//   - fileID int64
//   - content []byte
func (_e *Client_Expecter) Upload(ctx interface{}, fileID interface{}, content interface{}) *Client_Upload_Call {
	return &Client_Upload_Call{Call: _e.mock.On("Upload", ctx, fileID, content)}
}

func (_c *Client_Upload_Call) Run(run func(ctx context.Context, fileID int64, content []byte)) *Client_Upload_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(int64), args[2].([]byte))
	})
	return _c
}

func (_c *Client_Upload_Call) Return(_a0 error) *Client_Upload_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *Client_Upload_Call) RunAndReturn(run func(context.Context, int64, []byte) error) *Client_Upload_Call {
	_c.Call.Return(run)
	return _c
}

// NewClient creates a new instance of Client. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewClient(t interface {
	mock.TestingT
	Cleanup(func())
}) *Client {
	mock := &Client{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
