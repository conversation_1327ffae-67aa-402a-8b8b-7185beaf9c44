// Code generated by mockery v2.53.4. DO NOT EDIT.

package mocks

import (
	context "context"

	model "github.com/MoeGolibrary/moego-svc-order-v2/internal/model"
	mock "github.com/stretchr/testify/mock"

	ordersvcpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/order/v2"

	paymentpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/payment/v1"

	repo "github.com/MoeGolibrary/moego-svc-order-v2/internal/repo"
)

// LegacyPaymentRepo is an autogenerated mock type for the LegacyPaymentRepo type
type LegacyPaymentRepo struct {
	mock.Mock
}

type LegacyPaymentRepo_Expecter struct {
	mock *mock.Mock
}

func (_m *LegacyPaymentRepo) EXPECT() *LegacyPaymentRepo_Expecter {
	return &LegacyPaymentRepo_Expecter{mock: &_m.Mock}
}

// BatchGet provides a mock function with given fields: ctx, ids
func (_m *LegacyPaymentRepo) BatchGet(ctx context.Context, ids []int64) ([]*model.OrderPayment, error) {
	ret := _m.Called(ctx, ids)

	if len(ret) == 0 {
		panic("no return value specified for BatchGet")
	}

	var r0 []*model.OrderPayment
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, []int64) ([]*model.OrderPayment, error)); ok {
		return rf(ctx, ids)
	}
	if rf, ok := ret.Get(0).(func(context.Context, []int64) []*model.OrderPayment); ok {
		r0 = rf(ctx, ids)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*model.OrderPayment)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, []int64) error); ok {
		r1 = rf(ctx, ids)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// LegacyPaymentRepo_BatchGet_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'BatchGet'
type LegacyPaymentRepo_BatchGet_Call struct {
	*mock.Call
}

// BatchGet is a helper method to define mock.On call
//   - ctx context.Context
//   - ids []int64
func (_e *LegacyPaymentRepo_Expecter) BatchGet(ctx interface{}, ids interface{}) *LegacyPaymentRepo_BatchGet_Call {
	return &LegacyPaymentRepo_BatchGet_Call{Call: _e.mock.On("BatchGet", ctx, ids)}
}

func (_c *LegacyPaymentRepo_BatchGet_Call) Run(run func(ctx context.Context, ids []int64)) *LegacyPaymentRepo_BatchGet_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].([]int64))
	})
	return _c
}

func (_c *LegacyPaymentRepo_BatchGet_Call) Return(_a0 []*model.OrderPayment, _a1 error) *LegacyPaymentRepo_BatchGet_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *LegacyPaymentRepo_BatchGet_Call) RunAndReturn(run func(context.Context, []int64) ([]*model.OrderPayment, error)) *LegacyPaymentRepo_BatchGet_Call {
	_c.Call.Return(run)
	return _c
}

// CountByConditions provides a mock function with given fields: ctx, companyID, conditions
func (_m *LegacyPaymentRepo) CountByConditions(ctx context.Context, companyID int64, conditions *repo.ListLegacyPaymentConditions) (int64, error) {
	ret := _m.Called(ctx, companyID, conditions)

	if len(ret) == 0 {
		panic("no return value specified for CountByConditions")
	}

	var r0 int64
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, int64, *repo.ListLegacyPaymentConditions) (int64, error)); ok {
		return rf(ctx, companyID, conditions)
	}
	if rf, ok := ret.Get(0).(func(context.Context, int64, *repo.ListLegacyPaymentConditions) int64); ok {
		r0 = rf(ctx, companyID, conditions)
	} else {
		r0 = ret.Get(0).(int64)
	}

	if rf, ok := ret.Get(1).(func(context.Context, int64, *repo.ListLegacyPaymentConditions) error); ok {
		r1 = rf(ctx, companyID, conditions)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// LegacyPaymentRepo_CountByConditions_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CountByConditions'
type LegacyPaymentRepo_CountByConditions_Call struct {
	*mock.Call
}

// CountByConditions is a helper method to define mock.On call
//   - ctx context.Context
//   - companyID int64
//   - conditions *repo.ListLegacyPaymentConditions
func (_e *LegacyPaymentRepo_Expecter) CountByConditions(ctx interface{}, companyID interface{}, conditions interface{}) *LegacyPaymentRepo_CountByConditions_Call {
	return &LegacyPaymentRepo_CountByConditions_Call{Call: _e.mock.On("CountByConditions", ctx, companyID, conditions)}
}

func (_c *LegacyPaymentRepo_CountByConditions_Call) Run(run func(ctx context.Context, companyID int64, conditions *repo.ListLegacyPaymentConditions)) *LegacyPaymentRepo_CountByConditions_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(int64), args[2].(*repo.ListLegacyPaymentConditions))
	})
	return _c
}

func (_c *LegacyPaymentRepo_CountByConditions_Call) Return(_a0 int64, _a1 error) *LegacyPaymentRepo_CountByConditions_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *LegacyPaymentRepo_CountByConditions_Call) RunAndReturn(run func(context.Context, int64, *repo.ListLegacyPaymentConditions) (int64, error)) *LegacyPaymentRepo_CountByConditions_Call {
	_c.Call.Return(run)
	return _c
}

// ListByIDsAndConditions provides a mock function with given fields: ctx, ids, method, vendor
func (_m *LegacyPaymentRepo) ListByIDsAndConditions(ctx context.Context, ids []int64, method paymentpb.PaymentMethod, vendor string) ([]*model.OrderPayment, error) {
	ret := _m.Called(ctx, ids, method, vendor)

	if len(ret) == 0 {
		panic("no return value specified for ListByIDsAndConditions")
	}

	var r0 []*model.OrderPayment
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, []int64, paymentpb.PaymentMethod, string) ([]*model.OrderPayment, error)); ok {
		return rf(ctx, ids, method, vendor)
	}
	if rf, ok := ret.Get(0).(func(context.Context, []int64, paymentpb.PaymentMethod, string) []*model.OrderPayment); ok {
		r0 = rf(ctx, ids, method, vendor)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*model.OrderPayment)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, []int64, paymentpb.PaymentMethod, string) error); ok {
		r1 = rf(ctx, ids, method, vendor)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// LegacyPaymentRepo_ListByIDsAndConditions_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ListByIDsAndConditions'
type LegacyPaymentRepo_ListByIDsAndConditions_Call struct {
	*mock.Call
}

// ListByIDsAndConditions is a helper method to define mock.On call
//   - ctx context.Context
//   - ids []int64
//   - method paymentpb.PaymentMethod
//   - vendor string
func (_e *LegacyPaymentRepo_Expecter) ListByIDsAndConditions(ctx interface{}, ids interface{}, method interface{}, vendor interface{}) *LegacyPaymentRepo_ListByIDsAndConditions_Call {
	return &LegacyPaymentRepo_ListByIDsAndConditions_Call{Call: _e.mock.On("ListByIDsAndConditions", ctx, ids, method, vendor)}
}

func (_c *LegacyPaymentRepo_ListByIDsAndConditions_Call) Run(run func(ctx context.Context, ids []int64, method paymentpb.PaymentMethod, vendor string)) *LegacyPaymentRepo_ListByIDsAndConditions_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].([]int64), args[2].(paymentpb.PaymentMethod), args[3].(string))
	})
	return _c
}

func (_c *LegacyPaymentRepo_ListByIDsAndConditions_Call) Return(_a0 []*model.OrderPayment, _a1 error) *LegacyPaymentRepo_ListByIDsAndConditions_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *LegacyPaymentRepo_ListByIDsAndConditions_Call) RunAndReturn(run func(context.Context, []int64, paymentpb.PaymentMethod, string) ([]*model.OrderPayment, error)) *LegacyPaymentRepo_ListByIDsAndConditions_Call {
	_c.Call.Return(run)
	return _c
}

// ListByOrderID provides a mock function with given fields: ctx, orderID
func (_m *LegacyPaymentRepo) ListByOrderID(ctx context.Context, orderID int64) ([]*model.OrderPayment, error) {
	ret := _m.Called(ctx, orderID)

	if len(ret) == 0 {
		panic("no return value specified for ListByOrderID")
	}

	var r0 []*model.OrderPayment
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, int64) ([]*model.OrderPayment, error)); ok {
		return rf(ctx, orderID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, int64) []*model.OrderPayment); ok {
		r0 = rf(ctx, orderID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*model.OrderPayment)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, int64) error); ok {
		r1 = rf(ctx, orderID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// LegacyPaymentRepo_ListByOrderID_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ListByOrderID'
type LegacyPaymentRepo_ListByOrderID_Call struct {
	*mock.Call
}

// ListByOrderID is a helper method to define mock.On call
//   - ctx context.Context
//   - orderID int64
func (_e *LegacyPaymentRepo_Expecter) ListByOrderID(ctx interface{}, orderID interface{}) *LegacyPaymentRepo_ListByOrderID_Call {
	return &LegacyPaymentRepo_ListByOrderID_Call{Call: _e.mock.On("ListByOrderID", ctx, orderID)}
}

func (_c *LegacyPaymentRepo_ListByOrderID_Call) Run(run func(ctx context.Context, orderID int64)) *LegacyPaymentRepo_ListByOrderID_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(int64))
	})
	return _c
}

func (_c *LegacyPaymentRepo_ListByOrderID_Call) Return(_a0 []*model.OrderPayment, _a1 error) *LegacyPaymentRepo_ListByOrderID_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *LegacyPaymentRepo_ListByOrderID_Call) RunAndReturn(run func(context.Context, int64) ([]*model.OrderPayment, error)) *LegacyPaymentRepo_ListByOrderID_Call {
	_c.Call.Return(run)
	return _c
}

// ListRawByConditions provides a mock function with given fields: ctx, companyID, conditions, offset, limit, orderBys
func (_m *LegacyPaymentRepo) ListRawByConditions(ctx context.Context, companyID int64, conditions *repo.ListLegacyPaymentConditions, offset *int, limit *int, orderBys []*ordersvcpb.ListOrderPaymentDetailRequest_OrderBy) ([]repo.RawOrderPayment, error) {
	ret := _m.Called(ctx, companyID, conditions, offset, limit, orderBys)

	if len(ret) == 0 {
		panic("no return value specified for ListRawByConditions")
	}

	var r0 []repo.RawOrderPayment
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, int64, *repo.ListLegacyPaymentConditions, *int, *int, []*ordersvcpb.ListOrderPaymentDetailRequest_OrderBy) ([]repo.RawOrderPayment, error)); ok {
		return rf(ctx, companyID, conditions, offset, limit, orderBys)
	}
	if rf, ok := ret.Get(0).(func(context.Context, int64, *repo.ListLegacyPaymentConditions, *int, *int, []*ordersvcpb.ListOrderPaymentDetailRequest_OrderBy) []repo.RawOrderPayment); ok {
		r0 = rf(ctx, companyID, conditions, offset, limit, orderBys)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]repo.RawOrderPayment)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, int64, *repo.ListLegacyPaymentConditions, *int, *int, []*ordersvcpb.ListOrderPaymentDetailRequest_OrderBy) error); ok {
		r1 = rf(ctx, companyID, conditions, offset, limit, orderBys)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// LegacyPaymentRepo_ListRawByConditions_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ListRawByConditions'
type LegacyPaymentRepo_ListRawByConditions_Call struct {
	*mock.Call
}

// ListRawByConditions is a helper method to define mock.On call
//   - ctx context.Context
//   - companyID int64
//   - conditions *repo.ListLegacyPaymentConditions
//   - offset *int
//   - limit *int
//   - orderBys []*ordersvcpb.ListOrderPaymentDetailRequest_OrderBy
func (_e *LegacyPaymentRepo_Expecter) ListRawByConditions(ctx interface{}, companyID interface{}, conditions interface{}, offset interface{}, limit interface{}, orderBys interface{}) *LegacyPaymentRepo_ListRawByConditions_Call {
	return &LegacyPaymentRepo_ListRawByConditions_Call{Call: _e.mock.On("ListRawByConditions", ctx, companyID, conditions, offset, limit, orderBys)}
}

func (_c *LegacyPaymentRepo_ListRawByConditions_Call) Run(run func(ctx context.Context, companyID int64, conditions *repo.ListLegacyPaymentConditions, offset *int, limit *int, orderBys []*ordersvcpb.ListOrderPaymentDetailRequest_OrderBy)) *LegacyPaymentRepo_ListRawByConditions_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(int64), args[2].(*repo.ListLegacyPaymentConditions), args[3].(*int), args[4].(*int), args[5].([]*ordersvcpb.ListOrderPaymentDetailRequest_OrderBy))
	})
	return _c
}

func (_c *LegacyPaymentRepo_ListRawByConditions_Call) Return(_a0 []repo.RawOrderPayment, _a1 error) *LegacyPaymentRepo_ListRawByConditions_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *LegacyPaymentRepo_ListRawByConditions_Call) RunAndReturn(run func(context.Context, int64, *repo.ListLegacyPaymentConditions, *int, *int, []*ordersvcpb.ListOrderPaymentDetailRequest_OrderBy) ([]repo.RawOrderPayment, error)) *LegacyPaymentRepo_ListRawByConditions_Call {
	_c.Call.Return(run)
	return _c
}

// NewLegacyPaymentRepo creates a new instance of LegacyPaymentRepo. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewLegacyPaymentRepo(t interface {
	mock.TestingT
	Cleanup(func())
}) *LegacyPaymentRepo {
	mock := &LegacyPaymentRepo{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
