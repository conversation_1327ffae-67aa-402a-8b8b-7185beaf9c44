// Code generated by mockery v2.53.4. DO NOT EDIT.

package mocks

import (
	context "context"

	model "github.com/MoeGolibrary/moego-svc-order-v2/internal/model"
	mock "github.com/stretchr/testify/mock"

	repo "github.com/MoeGolibrary/moego-svc-order-v2/internal/repo"
)

// LegacyRefundRepo is an autogenerated mock type for the LegacyRefundRepo type
type LegacyRefundRepo struct {
	mock.Mock
}

type LegacyRefundRepo_Expecter struct {
	mock *mock.Mock
}

func (_m *LegacyRefundRepo) EXPECT() *LegacyRefundRepo_Expecter {
	return &LegacyRefundRepo_Expecter{mock: &_m.Mock}
}

// BatchGet provides a mock function with given fields: ctx, ids
func (_m *LegacyRefundRepo) BatchGet(ctx context.Context, ids []int64) ([]*model.RefundOrderPayment, error) {
	ret := _m.Called(ctx, ids)

	if len(ret) == 0 {
		panic("no return value specified for BatchGet")
	}

	var r0 []*model.RefundOrderPayment
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, []int64) ([]*model.RefundOrderPayment, error)); ok {
		return rf(ctx, ids)
	}
	if rf, ok := ret.Get(0).(func(context.Context, []int64) []*model.RefundOrderPayment); ok {
		r0 = rf(ctx, ids)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*model.RefundOrderPayment)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, []int64) error); ok {
		r1 = rf(ctx, ids)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// LegacyRefundRepo_BatchGet_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'BatchGet'
type LegacyRefundRepo_BatchGet_Call struct {
	*mock.Call
}

// BatchGet is a helper method to define mock.On call
//   - ctx context.Context
//   - ids []int64
func (_e *LegacyRefundRepo_Expecter) BatchGet(ctx interface{}, ids interface{}) *LegacyRefundRepo_BatchGet_Call {
	return &LegacyRefundRepo_BatchGet_Call{Call: _e.mock.On("BatchGet", ctx, ids)}
}

func (_c *LegacyRefundRepo_BatchGet_Call) Run(run func(ctx context.Context, ids []int64)) *LegacyRefundRepo_BatchGet_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].([]int64))
	})
	return _c
}

func (_c *LegacyRefundRepo_BatchGet_Call) Return(_a0 []*model.RefundOrderPayment, _a1 error) *LegacyRefundRepo_BatchGet_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *LegacyRefundRepo_BatchGet_Call) RunAndReturn(run func(context.Context, []int64) ([]*model.RefundOrderPayment, error)) *LegacyRefundRepo_BatchGet_Call {
	_c.Call.Return(run)
	return _c
}

// ListByConditions provides a mock function with given fields: ctx, companyID, conditions, excludeNonZeroROPID
func (_m *LegacyRefundRepo) ListByConditions(ctx context.Context, companyID int64, conditions *repo.ListRefundConditions, excludeNonZeroROPID bool) ([]*model.RefundOrderPayment, error) {
	ret := _m.Called(ctx, companyID, conditions, excludeNonZeroROPID)

	if len(ret) == 0 {
		panic("no return value specified for ListByConditions")
	}

	var r0 []*model.RefundOrderPayment
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, int64, *repo.ListRefundConditions, bool) ([]*model.RefundOrderPayment, error)); ok {
		return rf(ctx, companyID, conditions, excludeNonZeroROPID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, int64, *repo.ListRefundConditions, bool) []*model.RefundOrderPayment); ok {
		r0 = rf(ctx, companyID, conditions, excludeNonZeroROPID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*model.RefundOrderPayment)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, int64, *repo.ListRefundConditions, bool) error); ok {
		r1 = rf(ctx, companyID, conditions, excludeNonZeroROPID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// LegacyRefundRepo_ListByConditions_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ListByConditions'
type LegacyRefundRepo_ListByConditions_Call struct {
	*mock.Call
}

// ListByConditions is a helper method to define mock.On call
//   - ctx context.Context
//   - companyID int64
//   - conditions *repo.ListRefundConditions
//   - excludeNonZeroROPID bool
func (_e *LegacyRefundRepo_Expecter) ListByConditions(ctx interface{}, companyID interface{}, conditions interface{}, excludeNonZeroROPID interface{}) *LegacyRefundRepo_ListByConditions_Call {
	return &LegacyRefundRepo_ListByConditions_Call{Call: _e.mock.On("ListByConditions", ctx, companyID, conditions, excludeNonZeroROPID)}
}

func (_c *LegacyRefundRepo_ListByConditions_Call) Run(run func(ctx context.Context, companyID int64, conditions *repo.ListRefundConditions, excludeNonZeroROPID bool)) *LegacyRefundRepo_ListByConditions_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(int64), args[2].(*repo.ListRefundConditions), args[3].(bool))
	})
	return _c
}

func (_c *LegacyRefundRepo_ListByConditions_Call) Return(_a0 []*model.RefundOrderPayment, _a1 error) *LegacyRefundRepo_ListByConditions_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *LegacyRefundRepo_ListByConditions_Call) RunAndReturn(run func(context.Context, int64, *repo.ListRefundConditions, bool) ([]*model.RefundOrderPayment, error)) *LegacyRefundRepo_ListByConditions_Call {
	_c.Call.Return(run)
	return _c
}

// ListByOrderID provides a mock function with given fields: ctx, orderID
func (_m *LegacyRefundRepo) ListByOrderID(ctx context.Context, orderID int64) ([]*model.RefundOrderPayment, error) {
	ret := _m.Called(ctx, orderID)

	if len(ret) == 0 {
		panic("no return value specified for ListByOrderID")
	}

	var r0 []*model.RefundOrderPayment
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, int64) ([]*model.RefundOrderPayment, error)); ok {
		return rf(ctx, orderID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, int64) []*model.RefundOrderPayment); ok {
		r0 = rf(ctx, orderID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*model.RefundOrderPayment)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, int64) error); ok {
		r1 = rf(ctx, orderID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// LegacyRefundRepo_ListByOrderID_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ListByOrderID'
type LegacyRefundRepo_ListByOrderID_Call struct {
	*mock.Call
}

// ListByOrderID is a helper method to define mock.On call
//   - ctx context.Context
//   - orderID int64
func (_e *LegacyRefundRepo_Expecter) ListByOrderID(ctx interface{}, orderID interface{}) *LegacyRefundRepo_ListByOrderID_Call {
	return &LegacyRefundRepo_ListByOrderID_Call{Call: _e.mock.On("ListByOrderID", ctx, orderID)}
}

func (_c *LegacyRefundRepo_ListByOrderID_Call) Run(run func(ctx context.Context, orderID int64)) *LegacyRefundRepo_ListByOrderID_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(int64))
	})
	return _c
}

func (_c *LegacyRefundRepo_ListByOrderID_Call) Return(_a0 []*model.RefundOrderPayment, _a1 error) *LegacyRefundRepo_ListByOrderID_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *LegacyRefundRepo_ListByOrderID_Call) RunAndReturn(run func(context.Context, int64) ([]*model.RefundOrderPayment, error)) *LegacyRefundRepo_ListByOrderID_Call {
	_c.Call.Return(run)
	return _c
}

// ListByPaymentIDs provides a mock function with given fields: ctx, paymentIDs
func (_m *LegacyRefundRepo) ListByPaymentIDs(ctx context.Context, paymentIDs []int64) ([]*model.RefundOrderPayment, error) {
	ret := _m.Called(ctx, paymentIDs)

	if len(ret) == 0 {
		panic("no return value specified for ListByPaymentIDs")
	}

	var r0 []*model.RefundOrderPayment
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, []int64) ([]*model.RefundOrderPayment, error)); ok {
		return rf(ctx, paymentIDs)
	}
	if rf, ok := ret.Get(0).(func(context.Context, []int64) []*model.RefundOrderPayment); ok {
		r0 = rf(ctx, paymentIDs)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*model.RefundOrderPayment)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, []int64) error); ok {
		r1 = rf(ctx, paymentIDs)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// LegacyRefundRepo_ListByPaymentIDs_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ListByPaymentIDs'
type LegacyRefundRepo_ListByPaymentIDs_Call struct {
	*mock.Call
}

// ListByPaymentIDs is a helper method to define mock.On call
//   - ctx context.Context
//   - paymentIDs []int64
func (_e *LegacyRefundRepo_Expecter) ListByPaymentIDs(ctx interface{}, paymentIDs interface{}) *LegacyRefundRepo_ListByPaymentIDs_Call {
	return &LegacyRefundRepo_ListByPaymentIDs_Call{Call: _e.mock.On("ListByPaymentIDs", ctx, paymentIDs)}
}

func (_c *LegacyRefundRepo_ListByPaymentIDs_Call) Run(run func(ctx context.Context, paymentIDs []int64)) *LegacyRefundRepo_ListByPaymentIDs_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].([]int64))
	})
	return _c
}

func (_c *LegacyRefundRepo_ListByPaymentIDs_Call) Return(_a0 []*model.RefundOrderPayment, _a1 error) *LegacyRefundRepo_ListByPaymentIDs_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *LegacyRefundRepo_ListByPaymentIDs_Call) RunAndReturn(run func(context.Context, []int64) ([]*model.RefundOrderPayment, error)) *LegacyRefundRepo_ListByPaymentIDs_Call {
	_c.Call.Return(run)
	return _c
}

// NewLegacyRefundRepo creates a new instance of LegacyRefundRepo. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewLegacyRefundRepo(t interface {
	mock.TestingT
	Cleanup(func())
}) *LegacyRefundRepo {
	mock := &LegacyRefundRepo{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
