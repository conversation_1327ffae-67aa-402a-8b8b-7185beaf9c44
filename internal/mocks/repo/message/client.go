// Code generated by mockery v2.53.4. DO NOT EDIT.

package mocks

import (
	context "context"

	message "github.com/MoeGolibrary/moego-svc-order-v2/internal/repo/message"
	mock "github.com/stretchr/testify/mock"
)

// Client is an autogenerated mock type for the Client type
type Client struct {
	mock.Mock
}

type Client_Expecter struct {
	mock *mock.Mock
}

func (_m *Client) EXPECT() *Client_Expecter {
	return &Client_Expecter{mock: &_m.Mock}
}

// NotifyPaymentRefunded provides a mock function with given fields: ctx, params
func (_m *Client) NotifyPaymentRefunded(ctx context.Context, params *message.NotifyPaymentRefundedParams) error {
	ret := _m.Called(ctx, params)

	if len(ret) == 0 {
		panic("no return value specified for NotifyPaymentRefunded")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *message.NotifyPaymentRefundedParams) error); ok {
		r0 = rf(ctx, params)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// Client_NotifyPaymentRefunded_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'NotifyPaymentRefunded'
type Client_NotifyPaymentRefunded_Call struct {
	*mock.Call
}

// NotifyPaymentRefunded is a helper method to define mock.On call
//   - ctx context.Context
//   - params *message.NotifyPaymentRefundedParams
func (_e *Client_Expecter) NotifyPaymentRefunded(ctx interface{}, params interface{}) *Client_NotifyPaymentRefunded_Call {
	return &Client_NotifyPaymentRefunded_Call{Call: _e.mock.On("NotifyPaymentRefunded", ctx, params)}
}

func (_c *Client_NotifyPaymentRefunded_Call) Run(run func(ctx context.Context, params *message.NotifyPaymentRefundedParams)) *Client_NotifyPaymentRefunded_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*message.NotifyPaymentRefundedParams))
	})
	return _c
}

func (_c *Client_NotifyPaymentRefunded_Call) Return(_a0 error) *Client_NotifyPaymentRefunded_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *Client_NotifyPaymentRefunded_Call) RunAndReturn(run func(context.Context, *message.NotifyPaymentRefundedParams) error) *Client_NotifyPaymentRefunded_Call {
	_c.Call.Return(run)
	return _c
}

// NewClient creates a new instance of Client. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewClient(t interface {
	mock.TestingT
	Cleanup(func())
}) *Client {
	mock := &Client{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
