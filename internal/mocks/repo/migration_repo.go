// Code generated by mockery v2.53.4. DO NOT EDIT.

package mocks

import (
	context "context"

	model "github.com/MoeGolibrary/moego-svc-order-v2/internal/model"
	mock "github.com/stretchr/testify/mock"
)

// MigrationRepo is an autogenerated mock type for the MigrationRepo type
type MigrationRepo struct {
	mock.Mock
}

type MigrationRepo_Expecter struct {
	mock *mock.Mock
}

func (_m *MigrationRepo) EXPECT() *MigrationRepo_Expecter {
	return &MigrationRepo_Expecter{mock: &_m.Mock}
}

// MigrateToV4 provides a mock function with given fields: ctx, orders
func (_m *MigrationRepo) MigrateToV4(ctx context.Context, orders []*model.Order) error {
	ret := _m.Called(ctx, orders)

	if len(ret) == 0 {
		panic("no return value specified for MigrateToV4")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, []*model.Order) error); ok {
		r0 = rf(ctx, orders)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MigrationRepo_MigrateToV4_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'MigrateToV4'
type MigrationRepo_MigrateToV4_Call struct {
	*mock.Call
}

// MigrateToV4 is a helper method to define mock.On call
//   - ctx context.Context
//   - orders []*model.Order
func (_e *MigrationRepo_Expecter) MigrateToV4(ctx interface{}, orders interface{}) *MigrationRepo_MigrateToV4_Call {
	return &MigrationRepo_MigrateToV4_Call{Call: _e.mock.On("MigrateToV4", ctx, orders)}
}

func (_c *MigrationRepo_MigrateToV4_Call) Run(run func(ctx context.Context, orders []*model.Order)) *MigrationRepo_MigrateToV4_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].([]*model.Order))
	})
	return _c
}

func (_c *MigrationRepo_MigrateToV4_Call) Return(_a0 error) *MigrationRepo_MigrateToV4_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MigrationRepo_MigrateToV4_Call) RunAndReturn(run func(context.Context, []*model.Order) error) *MigrationRepo_MigrateToV4_Call {
	_c.Call.Return(run)
	return _c
}

// NewMigrationRepo creates a new instance of MigrationRepo. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMigrationRepo(t interface {
	mock.TestingT
	Cleanup(func())
}) *MigrationRepo {
	mock := &MigrationRepo{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
