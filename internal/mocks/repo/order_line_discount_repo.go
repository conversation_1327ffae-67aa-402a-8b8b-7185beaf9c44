// Code generated by mockery v2.53.4. DO NOT EDIT.

package mocks

import (
	context "context"

	model "github.com/MoeGolibrary/moego-svc-order-v2/internal/model"
	mock "github.com/stretchr/testify/mock"
)

// OrderLineDiscountRepo is an autogenerated mock type for the OrderLineDiscountRepo type
type OrderLineDiscountRepo struct {
	mock.Mock
}

type OrderLineDiscountRepo_Expecter struct {
	mock *mock.Mock
}

func (_m *OrderLineDiscountRepo) EXPECT() *OrderLineDiscountRepo_Expecter {
	return &OrderLineDiscountRepo_Expecter{mock: &_m.Mock}
}

// BatchCreate provides a mock function with given fields: ctx, orderLineDiscounts
func (_m *OrderLineDiscountRepo) BatchCreate(ctx context.Context, orderLineDiscounts []*model.OrderLineDiscount) error {
	ret := _m.Called(ctx, orderLineDiscounts)

	if len(ret) == 0 {
		panic("no return value specified for BatchCreate")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, []*model.OrderLineDiscount) error); ok {
		r0 = rf(ctx, orderLineDiscounts)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// OrderLineDiscountRepo_BatchCreate_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'BatchCreate'
type OrderLineDiscountRepo_BatchCreate_Call struct {
	*mock.Call
}

// BatchCreate is a helper method to define mock.On call
//   - ctx context.Context
//   - orderLineDiscounts []*model.OrderLineDiscount
func (_e *OrderLineDiscountRepo_Expecter) BatchCreate(ctx interface{}, orderLineDiscounts interface{}) *OrderLineDiscountRepo_BatchCreate_Call {
	return &OrderLineDiscountRepo_BatchCreate_Call{Call: _e.mock.On("BatchCreate", ctx, orderLineDiscounts)}
}

func (_c *OrderLineDiscountRepo_BatchCreate_Call) Run(run func(ctx context.Context, orderLineDiscounts []*model.OrderLineDiscount)) *OrderLineDiscountRepo_BatchCreate_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].([]*model.OrderLineDiscount))
	})
	return _c
}

func (_c *OrderLineDiscountRepo_BatchCreate_Call) Return(_a0 error) *OrderLineDiscountRepo_BatchCreate_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *OrderLineDiscountRepo_BatchCreate_Call) RunAndReturn(run func(context.Context, []*model.OrderLineDiscount) error) *OrderLineDiscountRepo_BatchCreate_Call {
	_c.Call.Return(run)
	return _c
}

// ListByOrderID provides a mock function with given fields: ctx, orderID
func (_m *OrderLineDiscountRepo) ListByOrderID(ctx context.Context, orderID int64) ([]*model.OrderLineDiscount, error) {
	ret := _m.Called(ctx, orderID)

	if len(ret) == 0 {
		panic("no return value specified for ListByOrderID")
	}

	var r0 []*model.OrderLineDiscount
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, int64) ([]*model.OrderLineDiscount, error)); ok {
		return rf(ctx, orderID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, int64) []*model.OrderLineDiscount); ok {
		r0 = rf(ctx, orderID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*model.OrderLineDiscount)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, int64) error); ok {
		r1 = rf(ctx, orderID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// OrderLineDiscountRepo_ListByOrderID_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ListByOrderID'
type OrderLineDiscountRepo_ListByOrderID_Call struct {
	*mock.Call
}

// ListByOrderID is a helper method to define mock.On call
//   - ctx context.Context
//   - orderID int64
func (_e *OrderLineDiscountRepo_Expecter) ListByOrderID(ctx interface{}, orderID interface{}) *OrderLineDiscountRepo_ListByOrderID_Call {
	return &OrderLineDiscountRepo_ListByOrderID_Call{Call: _e.mock.On("ListByOrderID", ctx, orderID)}
}

func (_c *OrderLineDiscountRepo_ListByOrderID_Call) Run(run func(ctx context.Context, orderID int64)) *OrderLineDiscountRepo_ListByOrderID_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(int64))
	})
	return _c
}

func (_c *OrderLineDiscountRepo_ListByOrderID_Call) Return(_a0 []*model.OrderLineDiscount, _a1 error) *OrderLineDiscountRepo_ListByOrderID_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *OrderLineDiscountRepo_ListByOrderID_Call) RunAndReturn(run func(context.Context, int64) ([]*model.OrderLineDiscount, error)) *OrderLineDiscountRepo_ListByOrderID_Call {
	_c.Call.Return(run)
	return _c
}

// NewOrderLineDiscountRepo creates a new instance of OrderLineDiscountRepo. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewOrderLineDiscountRepo(t interface {
	mock.TestingT
	Cleanup(func())
}) *OrderLineDiscountRepo {
	mock := &OrderLineDiscountRepo{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
