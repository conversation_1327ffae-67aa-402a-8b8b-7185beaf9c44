// Code generated by mockery v2.53.4. DO NOT EDIT.

package mocks

import (
	context "context"

	model "github.com/MoeGolibrary/moego-svc-order-v2/internal/model"
	mock "github.com/stretchr/testify/mock"

	orderpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/order/v1"

	time "time"
)

// OrderPromotionRepo is an autogenerated mock type for the OrderPromotionRepo type
type OrderPromotionRepo struct {
	mock.Mock
}

type OrderPromotionRepo_Expecter struct {
	mock *mock.Mock
}

func (_m *OrderPromotionRepo) EXPECT() *OrderPromotionRepo_Expecter {
	return &OrderPromotionRepo_Expecter{mock: &_m.Mock}
}

// BatchCreate provides a mock function with given fields: ctx, orderPromotions
func (_m *OrderPromotionRepo) BatchCreate(ctx context.Context, orderPromotions []*model.OrderPromotion) error {
	ret := _m.Called(ctx, orderPromotions)

	if len(ret) == 0 {
		panic("no return value specified for BatchCreate")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, []*model.OrderPromotion) error); ok {
		r0 = rf(ctx, orderPromotions)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// OrderPromotionRepo_BatchCreate_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'BatchCreate'
type OrderPromotionRepo_BatchCreate_Call struct {
	*mock.Call
}

// BatchCreate is a helper method to define mock.On call
//   - ctx context.Context
//   - orderPromotions []*model.OrderPromotion
func (_e *OrderPromotionRepo_Expecter) BatchCreate(ctx interface{}, orderPromotions interface{}) *OrderPromotionRepo_BatchCreate_Call {
	return &OrderPromotionRepo_BatchCreate_Call{Call: _e.mock.On("BatchCreate", ctx, orderPromotions)}
}

func (_c *OrderPromotionRepo_BatchCreate_Call) Run(run func(ctx context.Context, orderPromotions []*model.OrderPromotion)) *OrderPromotionRepo_BatchCreate_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].([]*model.OrderPromotion))
	})
	return _c
}

func (_c *OrderPromotionRepo_BatchCreate_Call) Return(_a0 error) *OrderPromotionRepo_BatchCreate_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *OrderPromotionRepo_BatchCreate_Call) RunAndReturn(run func(context.Context, []*model.OrderPromotion) error) *OrderPromotionRepo_BatchCreate_Call {
	_c.Call.Return(run)
	return _c
}

// BatchUpdateToApplied provides a mock function with given fields: ctx, orderPromotionIDList, src
func (_m *OrderPromotionRepo) BatchUpdateToApplied(ctx context.Context, orderPromotionIDList []int64, src []orderpb.OrderPromotionModel_Status) error {
	ret := _m.Called(ctx, orderPromotionIDList, src)

	if len(ret) == 0 {
		panic("no return value specified for BatchUpdateToApplied")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, []int64, []orderpb.OrderPromotionModel_Status) error); ok {
		r0 = rf(ctx, orderPromotionIDList, src)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// OrderPromotionRepo_BatchUpdateToApplied_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'BatchUpdateToApplied'
type OrderPromotionRepo_BatchUpdateToApplied_Call struct {
	*mock.Call
}

// BatchUpdateToApplied is a helper method to define mock.On call
//   - ctx context.Context
//   - orderPromotionIDList []int64
//   - src []orderpb.OrderPromotionModel_Status
func (_e *OrderPromotionRepo_Expecter) BatchUpdateToApplied(ctx interface{}, orderPromotionIDList interface{}, src interface{}) *OrderPromotionRepo_BatchUpdateToApplied_Call {
	return &OrderPromotionRepo_BatchUpdateToApplied_Call{Call: _e.mock.On("BatchUpdateToApplied", ctx, orderPromotionIDList, src)}
}

func (_c *OrderPromotionRepo_BatchUpdateToApplied_Call) Run(run func(ctx context.Context, orderPromotionIDList []int64, src []orderpb.OrderPromotionModel_Status)) *OrderPromotionRepo_BatchUpdateToApplied_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].([]int64), args[2].([]orderpb.OrderPromotionModel_Status))
	})
	return _c
}

func (_c *OrderPromotionRepo_BatchUpdateToApplied_Call) Return(_a0 error) *OrderPromotionRepo_BatchUpdateToApplied_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *OrderPromotionRepo_BatchUpdateToApplied_Call) RunAndReturn(run func(context.Context, []int64, []orderpb.OrderPromotionModel_Status) error) *OrderPromotionRepo_BatchUpdateToApplied_Call {
	_c.Call.Return(run)
	return _c
}

// ListByOrderID provides a mock function with given fields: ctx, orderID
func (_m *OrderPromotionRepo) ListByOrderID(ctx context.Context, orderID int64) ([]*model.OrderPromotion, error) {
	ret := _m.Called(ctx, orderID)

	if len(ret) == 0 {
		panic("no return value specified for ListByOrderID")
	}

	var r0 []*model.OrderPromotion
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, int64) ([]*model.OrderPromotion, error)); ok {
		return rf(ctx, orderID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, int64) []*model.OrderPromotion); ok {
		r0 = rf(ctx, orderID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*model.OrderPromotion)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, int64) error); ok {
		r1 = rf(ctx, orderID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// OrderPromotionRepo_ListByOrderID_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ListByOrderID'
type OrderPromotionRepo_ListByOrderID_Call struct {
	*mock.Call
}

// ListByOrderID is a helper method to define mock.On call
//   - ctx context.Context
//   - orderID int64
func (_e *OrderPromotionRepo_Expecter) ListByOrderID(ctx interface{}, orderID interface{}) *OrderPromotionRepo_ListByOrderID_Call {
	return &OrderPromotionRepo_ListByOrderID_Call{Call: _e.mock.On("ListByOrderID", ctx, orderID)}
}

func (_c *OrderPromotionRepo_ListByOrderID_Call) Run(run func(ctx context.Context, orderID int64)) *OrderPromotionRepo_ListByOrderID_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(int64))
	})
	return _c
}

func (_c *OrderPromotionRepo_ListByOrderID_Call) Return(_a0 []*model.OrderPromotion, _a1 error) *OrderPromotionRepo_ListByOrderID_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *OrderPromotionRepo_ListByOrderID_Call) RunAndReturn(run func(context.Context, int64) ([]*model.OrderPromotion, error)) *OrderPromotionRepo_ListByOrderID_Call {
	_c.Call.Return(run)
	return _c
}

// ListByStatus provides a mock function with given fields: ctx, statusList, offset
func (_m *OrderPromotionRepo) ListByStatus(ctx context.Context, statusList []orderpb.OrderPromotionModel_Status, offset time.Duration) ([]*model.OrderPromotion, error) {
	ret := _m.Called(ctx, statusList, offset)

	if len(ret) == 0 {
		panic("no return value specified for ListByStatus")
	}

	var r0 []*model.OrderPromotion
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, []orderpb.OrderPromotionModel_Status, time.Duration) ([]*model.OrderPromotion, error)); ok {
		return rf(ctx, statusList, offset)
	}
	if rf, ok := ret.Get(0).(func(context.Context, []orderpb.OrderPromotionModel_Status, time.Duration) []*model.OrderPromotion); ok {
		r0 = rf(ctx, statusList, offset)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*model.OrderPromotion)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, []orderpb.OrderPromotionModel_Status, time.Duration) error); ok {
		r1 = rf(ctx, statusList, offset)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// OrderPromotionRepo_ListByStatus_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ListByStatus'
type OrderPromotionRepo_ListByStatus_Call struct {
	*mock.Call
}

// ListByStatus is a helper method to define mock.On call
//   - ctx context.Context
//   - statusList []orderpb.OrderPromotionModel_Status
//   - offset time.Duration
func (_e *OrderPromotionRepo_Expecter) ListByStatus(ctx interface{}, statusList interface{}, offset interface{}) *OrderPromotionRepo_ListByStatus_Call {
	return &OrderPromotionRepo_ListByStatus_Call{Call: _e.mock.On("ListByStatus", ctx, statusList, offset)}
}

func (_c *OrderPromotionRepo_ListByStatus_Call) Run(run func(ctx context.Context, statusList []orderpb.OrderPromotionModel_Status, offset time.Duration)) *OrderPromotionRepo_ListByStatus_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].([]orderpb.OrderPromotionModel_Status), args[2].(time.Duration))
	})
	return _c
}

func (_c *OrderPromotionRepo_ListByStatus_Call) Return(_a0 []*model.OrderPromotion, _a1 error) *OrderPromotionRepo_ListByStatus_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *OrderPromotionRepo_ListByStatus_Call) RunAndReturn(run func(context.Context, []orderpb.OrderPromotionModel_Status, time.Duration) ([]*model.OrderPromotion, error)) *OrderPromotionRepo_ListByStatus_Call {
	_c.Call.Return(run)
	return _c
}

// NewOrderPromotionRepo creates a new instance of OrderPromotionRepo. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewOrderPromotionRepo(t interface {
	mock.TestingT
	Cleanup(func())
}) *OrderPromotionRepo {
	mock := &OrderPromotionRepo{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
