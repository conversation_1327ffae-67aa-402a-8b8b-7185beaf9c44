// Code generated by mockery v2.53.4. DO NOT EDIT.

package mocks

import (
	repo "github.com/MoeGolibrary/moego-svc-order-v2/internal/repo"
	mock "github.com/stretchr/testify/mock"
)

// OrderTX is an autogenerated mock type for the OrderTX type
type OrderTX struct {
	mock.Mock
}

type OrderTX_Expecter struct {
	mock *mock.Mock
}

func (_m *OrderTX) EXPECT() *OrderTX_Expecter {
	return &OrderTX_Expecter{mock: &_m.Mock}
}

// DepositChangeLog provides a mock function with no fields
func (_m *OrderTX) DepositChangeLog() repo.DepositChangeLogRepo {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for DepositChangeLog")
	}

	var r0 repo.DepositChangeLogRepo
	if rf, ok := ret.Get(0).(func() repo.DepositChangeLogRepo); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(repo.DepositChangeLogRepo)
		}
	}

	return r0
}

// OrderTX_DepositChangeLog_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DepositChangeLog'
type OrderTX_DepositChangeLog_Call struct {
	*mock.Call
}

// DepositChangeLog is a helper method to define mock.On call
func (_e *OrderTX_Expecter) DepositChangeLog() *OrderTX_DepositChangeLog_Call {
	return &OrderTX_DepositChangeLog_Call{Call: _e.mock.On("DepositChangeLog")}
}

func (_c *OrderTX_DepositChangeLog_Call) Run(run func()) *OrderTX_DepositChangeLog_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *OrderTX_DepositChangeLog_Call) Return(_a0 repo.DepositChangeLogRepo) *OrderTX_DepositChangeLog_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *OrderTX_DepositChangeLog_Call) RunAndReturn(run func() repo.DepositChangeLogRepo) *OrderTX_DepositChangeLog_Call {
	_c.Call.Return(run)
	return _c
}

// MessageDeliveryRepo provides a mock function with no fields
func (_m *OrderTX) MessageDeliveryRepo() repo.MessageDeliveryRepo {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for MessageDeliveryRepo")
	}

	var r0 repo.MessageDeliveryRepo
	if rf, ok := ret.Get(0).(func() repo.MessageDeliveryRepo); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(repo.MessageDeliveryRepo)
		}
	}

	return r0
}

// OrderTX_MessageDeliveryRepo_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'MessageDeliveryRepo'
type OrderTX_MessageDeliveryRepo_Call struct {
	*mock.Call
}

// MessageDeliveryRepo is a helper method to define mock.On call
func (_e *OrderTX_Expecter) MessageDeliveryRepo() *OrderTX_MessageDeliveryRepo_Call {
	return &OrderTX_MessageDeliveryRepo_Call{Call: _e.mock.On("MessageDeliveryRepo")}
}

func (_c *OrderTX_MessageDeliveryRepo_Call) Run(run func()) *OrderTX_MessageDeliveryRepo_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *OrderTX_MessageDeliveryRepo_Call) Return(_a0 repo.MessageDeliveryRepo) *OrderTX_MessageDeliveryRepo_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *OrderTX_MessageDeliveryRepo_Call) RunAndReturn(run func() repo.MessageDeliveryRepo) *OrderTX_MessageDeliveryRepo_Call {
	_c.Call.Return(run)
	return _c
}

// MigrationRepo provides a mock function with no fields
func (_m *OrderTX) MigrationRepo() repo.MigrationRepo {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for MigrationRepo")
	}

	var r0 repo.MigrationRepo
	if rf, ok := ret.Get(0).(func() repo.MigrationRepo); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(repo.MigrationRepo)
		}
	}

	return r0
}

// OrderTX_MigrationRepo_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'MigrationRepo'
type OrderTX_MigrationRepo_Call struct {
	*mock.Call
}

// MigrationRepo is a helper method to define mock.On call
func (_e *OrderTX_Expecter) MigrationRepo() *OrderTX_MigrationRepo_Call {
	return &OrderTX_MigrationRepo_Call{Call: _e.mock.On("MigrationRepo")}
}

func (_c *OrderTX_MigrationRepo_Call) Run(run func()) *OrderTX_MigrationRepo_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *OrderTX_MigrationRepo_Call) Return(_a0 repo.MigrationRepo) *OrderTX_MigrationRepo_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *OrderTX_MigrationRepo_Call) RunAndReturn(run func() repo.MigrationRepo) *OrderTX_MigrationRepo_Call {
	_c.Call.Return(run)
	return _c
}

// Order provides a mock function with no fields
func (_m *OrderTX) Order() repo.OrderRepo {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for Order")
	}

	var r0 repo.OrderRepo
	if rf, ok := ret.Get(0).(func() repo.OrderRepo); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(repo.OrderRepo)
		}
	}

	return r0
}

// OrderTX_Order_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Order'
type OrderTX_Order_Call struct {
	*mock.Call
}

// Order is a helper method to define mock.On call
func (_e *OrderTX_Expecter) Order() *OrderTX_Order_Call {
	return &OrderTX_Order_Call{Call: _e.mock.On("Order")}
}

func (_c *OrderTX_Order_Call) Run(run func()) *OrderTX_Order_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *OrderTX_Order_Call) Return(_a0 repo.OrderRepo) *OrderTX_Order_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *OrderTX_Order_Call) RunAndReturn(run func() repo.OrderRepo) *OrderTX_Order_Call {
	_c.Call.Return(run)
	return _c
}

// OrderItem provides a mock function with no fields
func (_m *OrderTX) OrderItem() repo.OrderItemRepo {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for OrderItem")
	}

	var r0 repo.OrderItemRepo
	if rf, ok := ret.Get(0).(func() repo.OrderItemRepo); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(repo.OrderItemRepo)
		}
	}

	return r0
}

// OrderTX_OrderItem_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'OrderItem'
type OrderTX_OrderItem_Call struct {
	*mock.Call
}

// OrderItem is a helper method to define mock.On call
func (_e *OrderTX_Expecter) OrderItem() *OrderTX_OrderItem_Call {
	return &OrderTX_OrderItem_Call{Call: _e.mock.On("OrderItem")}
}

func (_c *OrderTX_OrderItem_Call) Run(run func()) *OrderTX_OrderItem_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *OrderTX_OrderItem_Call) Return(_a0 repo.OrderItemRepo) *OrderTX_OrderItem_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *OrderTX_OrderItem_Call) RunAndReturn(run func() repo.OrderItemRepo) *OrderTX_OrderItem_Call {
	_c.Call.Return(run)
	return _c
}

// OrderLineDiscount provides a mock function with no fields
func (_m *OrderTX) OrderLineDiscount() repo.OrderLineDiscountRepo {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for OrderLineDiscount")
	}

	var r0 repo.OrderLineDiscountRepo
	if rf, ok := ret.Get(0).(func() repo.OrderLineDiscountRepo); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(repo.OrderLineDiscountRepo)
		}
	}

	return r0
}

// OrderTX_OrderLineDiscount_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'OrderLineDiscount'
type OrderTX_OrderLineDiscount_Call struct {
	*mock.Call
}

// OrderLineDiscount is a helper method to define mock.On call
func (_e *OrderTX_Expecter) OrderLineDiscount() *OrderTX_OrderLineDiscount_Call {
	return &OrderTX_OrderLineDiscount_Call{Call: _e.mock.On("OrderLineDiscount")}
}

func (_c *OrderTX_OrderLineDiscount_Call) Run(run func()) *OrderTX_OrderLineDiscount_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *OrderTX_OrderLineDiscount_Call) Return(_a0 repo.OrderLineDiscountRepo) *OrderTX_OrderLineDiscount_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *OrderTX_OrderLineDiscount_Call) RunAndReturn(run func() repo.OrderLineDiscountRepo) *OrderTX_OrderLineDiscount_Call {
	_c.Call.Return(run)
	return _c
}

// OrderPayment provides a mock function with no fields
func (_m *OrderTX) OrderPayment() repo.OrderPaymentRepo {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for OrderPayment")
	}

	var r0 repo.OrderPaymentRepo
	if rf, ok := ret.Get(0).(func() repo.OrderPaymentRepo); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(repo.OrderPaymentRepo)
		}
	}

	return r0
}

// OrderTX_OrderPayment_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'OrderPayment'
type OrderTX_OrderPayment_Call struct {
	*mock.Call
}

// OrderPayment is a helper method to define mock.On call
func (_e *OrderTX_Expecter) OrderPayment() *OrderTX_OrderPayment_Call {
	return &OrderTX_OrderPayment_Call{Call: _e.mock.On("OrderPayment")}
}

func (_c *OrderTX_OrderPayment_Call) Run(run func()) *OrderTX_OrderPayment_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *OrderTX_OrderPayment_Call) Return(_a0 repo.OrderPaymentRepo) *OrderTX_OrderPayment_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *OrderTX_OrderPayment_Call) RunAndReturn(run func() repo.OrderPaymentRepo) *OrderTX_OrderPayment_Call {
	_c.Call.Return(run)
	return _c
}

// OrderPromotion provides a mock function with no fields
func (_m *OrderTX) OrderPromotion() repo.OrderPromotionRepo {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for OrderPromotion")
	}

	var r0 repo.OrderPromotionRepo
	if rf, ok := ret.Get(0).(func() repo.OrderPromotionRepo); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(repo.OrderPromotionRepo)
		}
	}

	return r0
}

// OrderTX_OrderPromotion_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'OrderPromotion'
type OrderTX_OrderPromotion_Call struct {
	*mock.Call
}

// OrderPromotion is a helper method to define mock.On call
func (_e *OrderTX_Expecter) OrderPromotion() *OrderTX_OrderPromotion_Call {
	return &OrderTX_OrderPromotion_Call{Call: _e.mock.On("OrderPromotion")}
}

func (_c *OrderTX_OrderPromotion_Call) Run(run func()) *OrderTX_OrderPromotion_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *OrderTX_OrderPromotion_Call) Return(_a0 repo.OrderPromotionRepo) *OrderTX_OrderPromotion_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *OrderTX_OrderPromotion_Call) RunAndReturn(run func() repo.OrderPromotionRepo) *OrderTX_OrderPromotion_Call {
	_c.Call.Return(run)
	return _c
}

// OrderPromotionItem provides a mock function with no fields
func (_m *OrderTX) OrderPromotionItem() repo.OrderPromotionItemRepo {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for OrderPromotionItem")
	}

	var r0 repo.OrderPromotionItemRepo
	if rf, ok := ret.Get(0).(func() repo.OrderPromotionItemRepo); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(repo.OrderPromotionItemRepo)
		}
	}

	return r0
}

// OrderTX_OrderPromotionItem_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'OrderPromotionItem'
type OrderTX_OrderPromotionItem_Call struct {
	*mock.Call
}

// OrderPromotionItem is a helper method to define mock.On call
func (_e *OrderTX_Expecter) OrderPromotionItem() *OrderTX_OrderPromotionItem_Call {
	return &OrderTX_OrderPromotionItem_Call{Call: _e.mock.On("OrderPromotionItem")}
}

func (_c *OrderTX_OrderPromotionItem_Call) Run(run func()) *OrderTX_OrderPromotionItem_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *OrderTX_OrderPromotionItem_Call) Return(_a0 repo.OrderPromotionItemRepo) *OrderTX_OrderPromotionItem_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *OrderTX_OrderPromotionItem_Call) RunAndReturn(run func() repo.OrderPromotionItemRepo) *OrderTX_OrderPromotionItem_Call {
	_c.Call.Return(run)
	return _c
}

// RefundItem provides a mock function with no fields
func (_m *OrderTX) RefundItem() repo.RefundOrderItemRepo {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for RefundItem")
	}

	var r0 repo.RefundOrderItemRepo
	if rf, ok := ret.Get(0).(func() repo.RefundOrderItemRepo); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(repo.RefundOrderItemRepo)
		}
	}

	return r0
}

// OrderTX_RefundItem_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'RefundItem'
type OrderTX_RefundItem_Call struct {
	*mock.Call
}

// RefundItem is a helper method to define mock.On call
func (_e *OrderTX_Expecter) RefundItem() *OrderTX_RefundItem_Call {
	return &OrderTX_RefundItem_Call{Call: _e.mock.On("RefundItem")}
}

func (_c *OrderTX_RefundItem_Call) Run(run func()) *OrderTX_RefundItem_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *OrderTX_RefundItem_Call) Return(_a0 repo.RefundOrderItemRepo) *OrderTX_RefundItem_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *OrderTX_RefundItem_Call) RunAndReturn(run func() repo.RefundOrderItemRepo) *OrderTX_RefundItem_Call {
	_c.Call.Return(run)
	return _c
}

// RefundOrder provides a mock function with no fields
func (_m *OrderTX) RefundOrder() repo.RefundOrderRepo {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for RefundOrder")
	}

	var r0 repo.RefundOrderRepo
	if rf, ok := ret.Get(0).(func() repo.RefundOrderRepo); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(repo.RefundOrderRepo)
		}
	}

	return r0
}

// OrderTX_RefundOrder_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'RefundOrder'
type OrderTX_RefundOrder_Call struct {
	*mock.Call
}

// RefundOrder is a helper method to define mock.On call
func (_e *OrderTX_Expecter) RefundOrder() *OrderTX_RefundOrder_Call {
	return &OrderTX_RefundOrder_Call{Call: _e.mock.On("RefundOrder")}
}

func (_c *OrderTX_RefundOrder_Call) Run(run func()) *OrderTX_RefundOrder_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *OrderTX_RefundOrder_Call) Return(_a0 repo.RefundOrderRepo) *OrderTX_RefundOrder_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *OrderTX_RefundOrder_Call) RunAndReturn(run func() repo.RefundOrderRepo) *OrderTX_RefundOrder_Call {
	_c.Call.Return(run)
	return _c
}

// RefundPayment provides a mock function with no fields
func (_m *OrderTX) RefundPayment() repo.RefundOrderPaymentRepo {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for RefundPayment")
	}

	var r0 repo.RefundOrderPaymentRepo
	if rf, ok := ret.Get(0).(func() repo.RefundOrderPaymentRepo); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(repo.RefundOrderPaymentRepo)
		}
	}

	return r0
}

// OrderTX_RefundPayment_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'RefundPayment'
type OrderTX_RefundPayment_Call struct {
	*mock.Call
}

// RefundPayment is a helper method to define mock.On call
func (_e *OrderTX_Expecter) RefundPayment() *OrderTX_RefundPayment_Call {
	return &OrderTX_RefundPayment_Call{Call: _e.mock.On("RefundPayment")}
}

func (_c *OrderTX_RefundPayment_Call) Run(run func()) *OrderTX_RefundPayment_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *OrderTX_RefundPayment_Call) Return(_a0 repo.RefundOrderPaymentRepo) *OrderTX_RefundPayment_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *OrderTX_RefundPayment_Call) RunAndReturn(run func() repo.RefundOrderPaymentRepo) *OrderTX_RefundPayment_Call {
	_c.Call.Return(run)
	return _c
}

// NewOrderTX creates a new instance of OrderTX. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewOrderTX(t interface {
	mock.TestingT
	Cleanup(func())
}) *OrderTX {
	mock := &OrderTX{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
