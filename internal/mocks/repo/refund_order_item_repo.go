// Code generated by mockery v2.53.4. DO NOT EDIT.

package mocks

import (
	context "context"

	model "github.com/MoeGolibrary/moego-svc-order-v2/internal/model"
	mock "github.com/stretchr/testify/mock"
)

// RefundOrderItemRepo is an autogenerated mock type for the RefundOrderItemRepo type
type RefundOrderItemRepo struct {
	mock.Mock
}

type RefundOrderItemRepo_Expecter struct {
	mock *mock.Mock
}

func (_m *RefundOrderItemRepo) EXPECT() *RefundOrderItemRepo_Expecter {
	return &RefundOrderItemRepo_Expecter{mock: &_m.Mock}
}

// BatchCreate provides a mock function with given fields: ctx, refundOrderItems
func (_m *RefundOrderItemRepo) BatchCreate(ctx context.Context, refundOrderItems []*model.RefundOrderItem) error {
	ret := _m.Called(ctx, refundOrderItems)

	if len(ret) == 0 {
		panic("no return value specified for BatchCreate")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, []*model.RefundOrderItem) error); ok {
		r0 = rf(ctx, refundOrderItems)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// RefundOrderItemRepo_BatchCreate_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'BatchCreate'
type RefundOrderItemRepo_BatchCreate_Call struct {
	*mock.Call
}

// BatchCreate is a helper method to define mock.On call
//   - ctx context.Context
//   - refundOrderItems []*model.RefundOrderItem
func (_e *RefundOrderItemRepo_Expecter) BatchCreate(ctx interface{}, refundOrderItems interface{}) *RefundOrderItemRepo_BatchCreate_Call {
	return &RefundOrderItemRepo_BatchCreate_Call{Call: _e.mock.On("BatchCreate", ctx, refundOrderItems)}
}

func (_c *RefundOrderItemRepo_BatchCreate_Call) Run(run func(ctx context.Context, refundOrderItems []*model.RefundOrderItem)) *RefundOrderItemRepo_BatchCreate_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].([]*model.RefundOrderItem))
	})
	return _c
}

func (_c *RefundOrderItemRepo_BatchCreate_Call) Return(_a0 error) *RefundOrderItemRepo_BatchCreate_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *RefundOrderItemRepo_BatchCreate_Call) RunAndReturn(run func(context.Context, []*model.RefundOrderItem) error) *RefundOrderItemRepo_BatchCreate_Call {
	_c.Call.Return(run)
	return _c
}

// ListByOrderID provides a mock function with given fields: ctx, orderID
func (_m *RefundOrderItemRepo) ListByOrderID(ctx context.Context, orderID int64) ([]*model.RefundOrderItem, error) {
	ret := _m.Called(ctx, orderID)

	if len(ret) == 0 {
		panic("no return value specified for ListByOrderID")
	}

	var r0 []*model.RefundOrderItem
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, int64) ([]*model.RefundOrderItem, error)); ok {
		return rf(ctx, orderID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, int64) []*model.RefundOrderItem); ok {
		r0 = rf(ctx, orderID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*model.RefundOrderItem)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, int64) error); ok {
		r1 = rf(ctx, orderID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// RefundOrderItemRepo_ListByOrderID_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ListByOrderID'
type RefundOrderItemRepo_ListByOrderID_Call struct {
	*mock.Call
}

// ListByOrderID is a helper method to define mock.On call
//   - ctx context.Context
//   - orderID int64
func (_e *RefundOrderItemRepo_Expecter) ListByOrderID(ctx interface{}, orderID interface{}) *RefundOrderItemRepo_ListByOrderID_Call {
	return &RefundOrderItemRepo_ListByOrderID_Call{Call: _e.mock.On("ListByOrderID", ctx, orderID)}
}

func (_c *RefundOrderItemRepo_ListByOrderID_Call) Run(run func(ctx context.Context, orderID int64)) *RefundOrderItemRepo_ListByOrderID_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(int64))
	})
	return _c
}

func (_c *RefundOrderItemRepo_ListByOrderID_Call) Return(_a0 []*model.RefundOrderItem, _a1 error) *RefundOrderItemRepo_ListByOrderID_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *RefundOrderItemRepo_ListByOrderID_Call) RunAndReturn(run func(context.Context, int64) ([]*model.RefundOrderItem, error)) *RefundOrderItemRepo_ListByOrderID_Call {
	_c.Call.Return(run)
	return _c
}

// ListByRefundOrderID provides a mock function with given fields: ctx, refundOrderID
func (_m *RefundOrderItemRepo) ListByRefundOrderID(ctx context.Context, refundOrderID int64) ([]*model.RefundOrderItem, error) {
	ret := _m.Called(ctx, refundOrderID)

	if len(ret) == 0 {
		panic("no return value specified for ListByRefundOrderID")
	}

	var r0 []*model.RefundOrderItem
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, int64) ([]*model.RefundOrderItem, error)); ok {
		return rf(ctx, refundOrderID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, int64) []*model.RefundOrderItem); ok {
		r0 = rf(ctx, refundOrderID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*model.RefundOrderItem)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, int64) error); ok {
		r1 = rf(ctx, refundOrderID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// RefundOrderItemRepo_ListByRefundOrderID_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ListByRefundOrderID'
type RefundOrderItemRepo_ListByRefundOrderID_Call struct {
	*mock.Call
}

// ListByRefundOrderID is a helper method to define mock.On call
//   - ctx context.Context
//   - refundOrderID int64
func (_e *RefundOrderItemRepo_Expecter) ListByRefundOrderID(ctx interface{}, refundOrderID interface{}) *RefundOrderItemRepo_ListByRefundOrderID_Call {
	return &RefundOrderItemRepo_ListByRefundOrderID_Call{Call: _e.mock.On("ListByRefundOrderID", ctx, refundOrderID)}
}

func (_c *RefundOrderItemRepo_ListByRefundOrderID_Call) Run(run func(ctx context.Context, refundOrderID int64)) *RefundOrderItemRepo_ListByRefundOrderID_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(int64))
	})
	return _c
}

func (_c *RefundOrderItemRepo_ListByRefundOrderID_Call) Return(_a0 []*model.RefundOrderItem, _a1 error) *RefundOrderItemRepo_ListByRefundOrderID_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *RefundOrderItemRepo_ListByRefundOrderID_Call) RunAndReturn(run func(context.Context, int64) ([]*model.RefundOrderItem, error)) *RefundOrderItemRepo_ListByRefundOrderID_Call {
	_c.Call.Return(run)
	return _c
}

// NewRefundOrderItemRepo creates a new instance of RefundOrderItemRepo. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewRefundOrderItemRepo(t interface {
	mock.TestingT
	Cleanup(func())
}) *RefundOrderItemRepo {
	mock := &RefundOrderItemRepo{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
