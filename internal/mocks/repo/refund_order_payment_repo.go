// Code generated by mockery v2.53.4. DO NOT EDIT.

package mocks

import (
	context "context"

	model "github.com/MoeGolibrary/moego-svc-order-v2/internal/model"
	mock "github.com/stretchr/testify/mock"

	orderpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/order/v1"

	repo "github.com/MoeGolibrary/moego-svc-order-v2/internal/repo"
)

// RefundOrderPaymentRepo is an autogenerated mock type for the RefundOrderPaymentRepo type
type RefundOrderPaymentRepo struct {
	mock.Mock
}

type RefundOrderPaymentRepo_Expecter struct {
	mock *mock.Mock
}

func (_m *RefundOrderPaymentRepo) EXPECT() *RefundOrderPaymentRepo_Expecter {
	return &RefundOrderPaymentRepo_Expecter{mock: &_m.Mock}
}

// BatchCreate provides a mock function with given fields: ctx, refundOrderPayments
func (_m *RefundOrderPaymentRepo) BatchCreate(ctx context.Context, refundOrderPayments []*model.RefundOrderPayment) error {
	ret := _m.Called(ctx, refundOrderPayments)

	if len(ret) == 0 {
		panic("no return value specified for BatchCreate")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, []*model.RefundOrderPayment) error); ok {
		r0 = rf(ctx, refundOrderPayments)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// RefundOrderPaymentRepo_BatchCreate_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'BatchCreate'
type RefundOrderPaymentRepo_BatchCreate_Call struct {
	*mock.Call
}

// BatchCreate is a helper method to define mock.On call
//   - ctx context.Context
//   - refundOrderPayments []*model.RefundOrderPayment
func (_e *RefundOrderPaymentRepo_Expecter) BatchCreate(ctx interface{}, refundOrderPayments interface{}) *RefundOrderPaymentRepo_BatchCreate_Call {
	return &RefundOrderPaymentRepo_BatchCreate_Call{Call: _e.mock.On("BatchCreate", ctx, refundOrderPayments)}
}

func (_c *RefundOrderPaymentRepo_BatchCreate_Call) Run(run func(ctx context.Context, refundOrderPayments []*model.RefundOrderPayment)) *RefundOrderPaymentRepo_BatchCreate_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].([]*model.RefundOrderPayment))
	})
	return _c
}

func (_c *RefundOrderPaymentRepo_BatchCreate_Call) Return(_a0 error) *RefundOrderPaymentRepo_BatchCreate_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *RefundOrderPaymentRepo_BatchCreate_Call) RunAndReturn(run func(context.Context, []*model.RefundOrderPayment) error) *RefundOrderPaymentRepo_BatchCreate_Call {
	_c.Call.Return(run)
	return _c
}

// BatchGet provides a mock function with given fields: ctx, ids
func (_m *RefundOrderPaymentRepo) BatchGet(ctx context.Context, ids []int64) ([]*model.RefundOrderPayment, error) {
	ret := _m.Called(ctx, ids)

	if len(ret) == 0 {
		panic("no return value specified for BatchGet")
	}

	var r0 []*model.RefundOrderPayment
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, []int64) ([]*model.RefundOrderPayment, error)); ok {
		return rf(ctx, ids)
	}
	if rf, ok := ret.Get(0).(func(context.Context, []int64) []*model.RefundOrderPayment); ok {
		r0 = rf(ctx, ids)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*model.RefundOrderPayment)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, []int64) error); ok {
		r1 = rf(ctx, ids)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// RefundOrderPaymentRepo_BatchGet_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'BatchGet'
type RefundOrderPaymentRepo_BatchGet_Call struct {
	*mock.Call
}

// BatchGet is a helper method to define mock.On call
//   - ctx context.Context
//   - ids []int64
func (_e *RefundOrderPaymentRepo_Expecter) BatchGet(ctx interface{}, ids interface{}) *RefundOrderPaymentRepo_BatchGet_Call {
	return &RefundOrderPaymentRepo_BatchGet_Call{Call: _e.mock.On("BatchGet", ctx, ids)}
}

func (_c *RefundOrderPaymentRepo_BatchGet_Call) Run(run func(ctx context.Context, ids []int64)) *RefundOrderPaymentRepo_BatchGet_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].([]int64))
	})
	return _c
}

func (_c *RefundOrderPaymentRepo_BatchGet_Call) Return(_a0 []*model.RefundOrderPayment, _a1 error) *RefundOrderPaymentRepo_BatchGet_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *RefundOrderPaymentRepo_BatchGet_Call) RunAndReturn(run func(context.Context, []int64) ([]*model.RefundOrderPayment, error)) *RefundOrderPaymentRepo_BatchGet_Call {
	_c.Call.Return(run)
	return _c
}

// Get provides a mock function with given fields: ctx, id
func (_m *RefundOrderPaymentRepo) Get(ctx context.Context, id int64) (*model.RefundOrderPayment, error) {
	ret := _m.Called(ctx, id)

	if len(ret) == 0 {
		panic("no return value specified for Get")
	}

	var r0 *model.RefundOrderPayment
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, int64) (*model.RefundOrderPayment, error)); ok {
		return rf(ctx, id)
	}
	if rf, ok := ret.Get(0).(func(context.Context, int64) *model.RefundOrderPayment); ok {
		r0 = rf(ctx, id)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*model.RefundOrderPayment)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, int64) error); ok {
		r1 = rf(ctx, id)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// RefundOrderPaymentRepo_Get_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Get'
type RefundOrderPaymentRepo_Get_Call struct {
	*mock.Call
}

// Get is a helper method to define mock.On call
//   - ctx context.Context
//   - id int64
func (_e *RefundOrderPaymentRepo_Expecter) Get(ctx interface{}, id interface{}) *RefundOrderPaymentRepo_Get_Call {
	return &RefundOrderPaymentRepo_Get_Call{Call: _e.mock.On("Get", ctx, id)}
}

func (_c *RefundOrderPaymentRepo_Get_Call) Run(run func(ctx context.Context, id int64)) *RefundOrderPaymentRepo_Get_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(int64))
	})
	return _c
}

func (_c *RefundOrderPaymentRepo_Get_Call) Return(_a0 *model.RefundOrderPayment, _a1 error) *RefundOrderPaymentRepo_Get_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *RefundOrderPaymentRepo_Get_Call) RunAndReturn(run func(context.Context, int64) (*model.RefundOrderPayment, error)) *RefundOrderPaymentRepo_Get_Call {
	_c.Call.Return(run)
	return _c
}

// GetForUpdate provides a mock function with given fields: ctx, id
func (_m *RefundOrderPaymentRepo) GetForUpdate(ctx context.Context, id int64) (*model.RefundOrderPayment, error) {
	ret := _m.Called(ctx, id)

	if len(ret) == 0 {
		panic("no return value specified for GetForUpdate")
	}

	var r0 *model.RefundOrderPayment
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, int64) (*model.RefundOrderPayment, error)); ok {
		return rf(ctx, id)
	}
	if rf, ok := ret.Get(0).(func(context.Context, int64) *model.RefundOrderPayment); ok {
		r0 = rf(ctx, id)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*model.RefundOrderPayment)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, int64) error); ok {
		r1 = rf(ctx, id)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// RefundOrderPaymentRepo_GetForUpdate_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetForUpdate'
type RefundOrderPaymentRepo_GetForUpdate_Call struct {
	*mock.Call
}

// GetForUpdate is a helper method to define mock.On call
//   - ctx context.Context
//   - id int64
func (_e *RefundOrderPaymentRepo_Expecter) GetForUpdate(ctx interface{}, id interface{}) *RefundOrderPaymentRepo_GetForUpdate_Call {
	return &RefundOrderPaymentRepo_GetForUpdate_Call{Call: _e.mock.On("GetForUpdate", ctx, id)}
}

func (_c *RefundOrderPaymentRepo_GetForUpdate_Call) Run(run func(ctx context.Context, id int64)) *RefundOrderPaymentRepo_GetForUpdate_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(int64))
	})
	return _c
}

func (_c *RefundOrderPaymentRepo_GetForUpdate_Call) Return(_a0 *model.RefundOrderPayment, _a1 error) *RefundOrderPaymentRepo_GetForUpdate_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *RefundOrderPaymentRepo_GetForUpdate_Call) RunAndReturn(run func(context.Context, int64) (*model.RefundOrderPayment, error)) *RefundOrderPaymentRepo_GetForUpdate_Call {
	_c.Call.Return(run)
	return _c
}

// ListByConditions provides a mock function with given fields: ctx, companyID, conditions
func (_m *RefundOrderPaymentRepo) ListByConditions(ctx context.Context, companyID int64, conditions *repo.ListRefundConditions) ([]*model.RefundOrderPayment, error) {
	ret := _m.Called(ctx, companyID, conditions)

	if len(ret) == 0 {
		panic("no return value specified for ListByConditions")
	}

	var r0 []*model.RefundOrderPayment
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, int64, *repo.ListRefundConditions) ([]*model.RefundOrderPayment, error)); ok {
		return rf(ctx, companyID, conditions)
	}
	if rf, ok := ret.Get(0).(func(context.Context, int64, *repo.ListRefundConditions) []*model.RefundOrderPayment); ok {
		r0 = rf(ctx, companyID, conditions)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*model.RefundOrderPayment)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, int64, *repo.ListRefundConditions) error); ok {
		r1 = rf(ctx, companyID, conditions)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// RefundOrderPaymentRepo_ListByConditions_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ListByConditions'
type RefundOrderPaymentRepo_ListByConditions_Call struct {
	*mock.Call
}

// ListByConditions is a helper method to define mock.On call
//   - ctx context.Context
//   - companyID int64
//   - conditions *repo.ListRefundConditions
func (_e *RefundOrderPaymentRepo_Expecter) ListByConditions(ctx interface{}, companyID interface{}, conditions interface{}) *RefundOrderPaymentRepo_ListByConditions_Call {
	return &RefundOrderPaymentRepo_ListByConditions_Call{Call: _e.mock.On("ListByConditions", ctx, companyID, conditions)}
}

func (_c *RefundOrderPaymentRepo_ListByConditions_Call) Run(run func(ctx context.Context, companyID int64, conditions *repo.ListRefundConditions)) *RefundOrderPaymentRepo_ListByConditions_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(int64), args[2].(*repo.ListRefundConditions))
	})
	return _c
}

func (_c *RefundOrderPaymentRepo_ListByConditions_Call) Return(_a0 []*model.RefundOrderPayment, _a1 error) *RefundOrderPaymentRepo_ListByConditions_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *RefundOrderPaymentRepo_ListByConditions_Call) RunAndReturn(run func(context.Context, int64, *repo.ListRefundConditions) ([]*model.RefundOrderPayment, error)) *RefundOrderPaymentRepo_ListByConditions_Call {
	_c.Call.Return(run)
	return _c
}

// ListByOrderID provides a mock function with given fields: ctx, orderID
func (_m *RefundOrderPaymentRepo) ListByOrderID(ctx context.Context, orderID int64) ([]*model.RefundOrderPayment, error) {
	ret := _m.Called(ctx, orderID)

	if len(ret) == 0 {
		panic("no return value specified for ListByOrderID")
	}

	var r0 []*model.RefundOrderPayment
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, int64) ([]*model.RefundOrderPayment, error)); ok {
		return rf(ctx, orderID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, int64) []*model.RefundOrderPayment); ok {
		r0 = rf(ctx, orderID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*model.RefundOrderPayment)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, int64) error); ok {
		r1 = rf(ctx, orderID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// RefundOrderPaymentRepo_ListByOrderID_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ListByOrderID'
type RefundOrderPaymentRepo_ListByOrderID_Call struct {
	*mock.Call
}

// ListByOrderID is a helper method to define mock.On call
//   - ctx context.Context
//   - orderID int64
func (_e *RefundOrderPaymentRepo_Expecter) ListByOrderID(ctx interface{}, orderID interface{}) *RefundOrderPaymentRepo_ListByOrderID_Call {
	return &RefundOrderPaymentRepo_ListByOrderID_Call{Call: _e.mock.On("ListByOrderID", ctx, orderID)}
}

func (_c *RefundOrderPaymentRepo_ListByOrderID_Call) Run(run func(ctx context.Context, orderID int64)) *RefundOrderPaymentRepo_ListByOrderID_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(int64))
	})
	return _c
}

func (_c *RefundOrderPaymentRepo_ListByOrderID_Call) Return(_a0 []*model.RefundOrderPayment, _a1 error) *RefundOrderPaymentRepo_ListByOrderID_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *RefundOrderPaymentRepo_ListByOrderID_Call) RunAndReturn(run func(context.Context, int64) ([]*model.RefundOrderPayment, error)) *RefundOrderPaymentRepo_ListByOrderID_Call {
	_c.Call.Return(run)
	return _c
}

// ListByOrderPaymentIDs provides a mock function with given fields: ctx, opIDs
func (_m *RefundOrderPaymentRepo) ListByOrderPaymentIDs(ctx context.Context, opIDs []int64) ([]*model.RefundOrderPayment, error) {
	ret := _m.Called(ctx, opIDs)

	if len(ret) == 0 {
		panic("no return value specified for ListByOrderPaymentIDs")
	}

	var r0 []*model.RefundOrderPayment
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, []int64) ([]*model.RefundOrderPayment, error)); ok {
		return rf(ctx, opIDs)
	}
	if rf, ok := ret.Get(0).(func(context.Context, []int64) []*model.RefundOrderPayment); ok {
		r0 = rf(ctx, opIDs)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*model.RefundOrderPayment)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, []int64) error); ok {
		r1 = rf(ctx, opIDs)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// RefundOrderPaymentRepo_ListByOrderPaymentIDs_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ListByOrderPaymentIDs'
type RefundOrderPaymentRepo_ListByOrderPaymentIDs_Call struct {
	*mock.Call
}

// ListByOrderPaymentIDs is a helper method to define mock.On call
//   - ctx context.Context
//   - opIDs []int64
func (_e *RefundOrderPaymentRepo_Expecter) ListByOrderPaymentIDs(ctx interface{}, opIDs interface{}) *RefundOrderPaymentRepo_ListByOrderPaymentIDs_Call {
	return &RefundOrderPaymentRepo_ListByOrderPaymentIDs_Call{Call: _e.mock.On("ListByOrderPaymentIDs", ctx, opIDs)}
}

func (_c *RefundOrderPaymentRepo_ListByOrderPaymentIDs_Call) Run(run func(ctx context.Context, opIDs []int64)) *RefundOrderPaymentRepo_ListByOrderPaymentIDs_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].([]int64))
	})
	return _c
}

func (_c *RefundOrderPaymentRepo_ListByOrderPaymentIDs_Call) Return(_a0 []*model.RefundOrderPayment, _a1 error) *RefundOrderPaymentRepo_ListByOrderPaymentIDs_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *RefundOrderPaymentRepo_ListByOrderPaymentIDs_Call) RunAndReturn(run func(context.Context, []int64) ([]*model.RefundOrderPayment, error)) *RefundOrderPaymentRepo_ListByOrderPaymentIDs_Call {
	_c.Call.Return(run)
	return _c
}

// ListByRefundOrderID provides a mock function with given fields: ctx, refundOrderID
func (_m *RefundOrderPaymentRepo) ListByRefundOrderID(ctx context.Context, refundOrderID int64) ([]*model.RefundOrderPayment, error) {
	ret := _m.Called(ctx, refundOrderID)

	if len(ret) == 0 {
		panic("no return value specified for ListByRefundOrderID")
	}

	var r0 []*model.RefundOrderPayment
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, int64) ([]*model.RefundOrderPayment, error)); ok {
		return rf(ctx, refundOrderID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, int64) []*model.RefundOrderPayment); ok {
		r0 = rf(ctx, refundOrderID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*model.RefundOrderPayment)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, int64) error); ok {
		r1 = rf(ctx, refundOrderID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// RefundOrderPaymentRepo_ListByRefundOrderID_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ListByRefundOrderID'
type RefundOrderPaymentRepo_ListByRefundOrderID_Call struct {
	*mock.Call
}

// ListByRefundOrderID is a helper method to define mock.On call
//   - ctx context.Context
//   - refundOrderID int64
func (_e *RefundOrderPaymentRepo_Expecter) ListByRefundOrderID(ctx interface{}, refundOrderID interface{}) *RefundOrderPaymentRepo_ListByRefundOrderID_Call {
	return &RefundOrderPaymentRepo_ListByRefundOrderID_Call{Call: _e.mock.On("ListByRefundOrderID", ctx, refundOrderID)}
}

func (_c *RefundOrderPaymentRepo_ListByRefundOrderID_Call) Run(run func(ctx context.Context, refundOrderID int64)) *RefundOrderPaymentRepo_ListByRefundOrderID_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(int64))
	})
	return _c
}

func (_c *RefundOrderPaymentRepo_ListByRefundOrderID_Call) Return(_a0 []*model.RefundOrderPayment, _a1 error) *RefundOrderPaymentRepo_ListByRefundOrderID_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *RefundOrderPaymentRepo_ListByRefundOrderID_Call) RunAndReturn(run func(context.Context, int64) ([]*model.RefundOrderPayment, error)) *RefundOrderPaymentRepo_ListByRefundOrderID_Call {
	_c.Call.Return(run)
	return _c
}

// ListByStatusAndMaxUpdateTime provides a mock function with given fields: ctx, refundStatus, updateTime
func (_m *RefundOrderPaymentRepo) ListByStatusAndMaxUpdateTime(ctx context.Context, refundStatus orderpb.RefundOrderPaymentStatus, updateTime int64) ([]*model.RefundOrderPayment, error) {
	ret := _m.Called(ctx, refundStatus, updateTime)

	if len(ret) == 0 {
		panic("no return value specified for ListByStatusAndMaxUpdateTime")
	}

	var r0 []*model.RefundOrderPayment
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, orderpb.RefundOrderPaymentStatus, int64) ([]*model.RefundOrderPayment, error)); ok {
		return rf(ctx, refundStatus, updateTime)
	}
	if rf, ok := ret.Get(0).(func(context.Context, orderpb.RefundOrderPaymentStatus, int64) []*model.RefundOrderPayment); ok {
		r0 = rf(ctx, refundStatus, updateTime)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*model.RefundOrderPayment)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, orderpb.RefundOrderPaymentStatus, int64) error); ok {
		r1 = rf(ctx, refundStatus, updateTime)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// RefundOrderPaymentRepo_ListByStatusAndMaxUpdateTime_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ListByStatusAndMaxUpdateTime'
type RefundOrderPaymentRepo_ListByStatusAndMaxUpdateTime_Call struct {
	*mock.Call
}

// ListByStatusAndMaxUpdateTime is a helper method to define mock.On call
//   - ctx context.Context
//   - refundStatus orderpb.RefundOrderPaymentStatus
//   - updateTime int64
func (_e *RefundOrderPaymentRepo_Expecter) ListByStatusAndMaxUpdateTime(ctx interface{}, refundStatus interface{}, updateTime interface{}) *RefundOrderPaymentRepo_ListByStatusAndMaxUpdateTime_Call {
	return &RefundOrderPaymentRepo_ListByStatusAndMaxUpdateTime_Call{Call: _e.mock.On("ListByStatusAndMaxUpdateTime", ctx, refundStatus, updateTime)}
}

func (_c *RefundOrderPaymentRepo_ListByStatusAndMaxUpdateTime_Call) Run(run func(ctx context.Context, refundStatus orderpb.RefundOrderPaymentStatus, updateTime int64)) *RefundOrderPaymentRepo_ListByStatusAndMaxUpdateTime_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(orderpb.RefundOrderPaymentStatus), args[2].(int64))
	})
	return _c
}

func (_c *RefundOrderPaymentRepo_ListByStatusAndMaxUpdateTime_Call) Return(_a0 []*model.RefundOrderPayment, _a1 error) *RefundOrderPaymentRepo_ListByStatusAndMaxUpdateTime_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *RefundOrderPaymentRepo_ListByStatusAndMaxUpdateTime_Call) RunAndReturn(run func(context.Context, orderpb.RefundOrderPaymentStatus, int64) ([]*model.RefundOrderPayment, error)) *RefundOrderPaymentRepo_ListByStatusAndMaxUpdateTime_Call {
	_c.Call.Return(run)
	return _c
}

// UpdateFailed provides a mock function with given fields: ctx, refundOrderPayment
func (_m *RefundOrderPaymentRepo) UpdateFailed(ctx context.Context, refundOrderPayment *model.RefundOrderPayment) error {
	ret := _m.Called(ctx, refundOrderPayment)

	if len(ret) == 0 {
		panic("no return value specified for UpdateFailed")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *model.RefundOrderPayment) error); ok {
		r0 = rf(ctx, refundOrderPayment)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// RefundOrderPaymentRepo_UpdateFailed_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateFailed'
type RefundOrderPaymentRepo_UpdateFailed_Call struct {
	*mock.Call
}

// UpdateFailed is a helper method to define mock.On call
//   - ctx context.Context
//   - refundOrderPayment *model.RefundOrderPayment
func (_e *RefundOrderPaymentRepo_Expecter) UpdateFailed(ctx interface{}, refundOrderPayment interface{}) *RefundOrderPaymentRepo_UpdateFailed_Call {
	return &RefundOrderPaymentRepo_UpdateFailed_Call{Call: _e.mock.On("UpdateFailed", ctx, refundOrderPayment)}
}

func (_c *RefundOrderPaymentRepo_UpdateFailed_Call) Run(run func(ctx context.Context, refundOrderPayment *model.RefundOrderPayment)) *RefundOrderPaymentRepo_UpdateFailed_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*model.RefundOrderPayment))
	})
	return _c
}

func (_c *RefundOrderPaymentRepo_UpdateFailed_Call) Return(_a0 error) *RefundOrderPaymentRepo_UpdateFailed_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *RefundOrderPaymentRepo_UpdateFailed_Call) RunAndReturn(run func(context.Context, *model.RefundOrderPayment) error) *RefundOrderPaymentRepo_UpdateFailed_Call {
	_c.Call.Return(run)
	return _c
}

// UpdateReason provides a mock function with given fields: ctx, id, reason, refundStatus
func (_m *RefundOrderPaymentRepo) UpdateReason(ctx context.Context, id int64, reason string, refundStatus orderpb.RefundOrderPaymentStatus) error {
	ret := _m.Called(ctx, id, reason, refundStatus)

	if len(ret) == 0 {
		panic("no return value specified for UpdateReason")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, int64, string, orderpb.RefundOrderPaymentStatus) error); ok {
		r0 = rf(ctx, id, reason, refundStatus)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// RefundOrderPaymentRepo_UpdateReason_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateReason'
type RefundOrderPaymentRepo_UpdateReason_Call struct {
	*mock.Call
}

// UpdateReason is a helper method to define mock.On call
//   - ctx context.Context
//   - id int64
//   - reason string
//   - refundStatus orderpb.RefundOrderPaymentStatus
func (_e *RefundOrderPaymentRepo_Expecter) UpdateReason(ctx interface{}, id interface{}, reason interface{}, refundStatus interface{}) *RefundOrderPaymentRepo_UpdateReason_Call {
	return &RefundOrderPaymentRepo_UpdateReason_Call{Call: _e.mock.On("UpdateReason", ctx, id, reason, refundStatus)}
}

func (_c *RefundOrderPaymentRepo_UpdateReason_Call) Run(run func(ctx context.Context, id int64, reason string, refundStatus orderpb.RefundOrderPaymentStatus)) *RefundOrderPaymentRepo_UpdateReason_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(int64), args[2].(string), args[3].(orderpb.RefundOrderPaymentStatus))
	})
	return _c
}

func (_c *RefundOrderPaymentRepo_UpdateReason_Call) Return(_a0 error) *RefundOrderPaymentRepo_UpdateReason_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *RefundOrderPaymentRepo_UpdateReason_Call) RunAndReturn(run func(context.Context, int64, string, orderpb.RefundOrderPaymentStatus) error) *RefundOrderPaymentRepo_UpdateReason_Call {
	_c.Call.Return(run)
	return _c
}

// UpdateRefunded provides a mock function with given fields: ctx, refundOrderPayment
func (_m *RefundOrderPaymentRepo) UpdateRefunded(ctx context.Context, refundOrderPayment *model.RefundOrderPayment) error {
	ret := _m.Called(ctx, refundOrderPayment)

	if len(ret) == 0 {
		panic("no return value specified for UpdateRefunded")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *model.RefundOrderPayment) error); ok {
		r0 = rf(ctx, refundOrderPayment)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// RefundOrderPaymentRepo_UpdateRefunded_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateRefunded'
type RefundOrderPaymentRepo_UpdateRefunded_Call struct {
	*mock.Call
}

// UpdateRefunded is a helper method to define mock.On call
//   - ctx context.Context
//   - refundOrderPayment *model.RefundOrderPayment
func (_e *RefundOrderPaymentRepo_Expecter) UpdateRefunded(ctx interface{}, refundOrderPayment interface{}) *RefundOrderPaymentRepo_UpdateRefunded_Call {
	return &RefundOrderPaymentRepo_UpdateRefunded_Call{Call: _e.mock.On("UpdateRefunded", ctx, refundOrderPayment)}
}

func (_c *RefundOrderPaymentRepo_UpdateRefunded_Call) Run(run func(ctx context.Context, refundOrderPayment *model.RefundOrderPayment)) *RefundOrderPaymentRepo_UpdateRefunded_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*model.RefundOrderPayment))
	})
	return _c
}

func (_c *RefundOrderPaymentRepo_UpdateRefunded_Call) Return(_a0 error) *RefundOrderPaymentRepo_UpdateRefunded_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *RefundOrderPaymentRepo_UpdateRefunded_Call) RunAndReturn(run func(context.Context, *model.RefundOrderPayment) error) *RefundOrderPaymentRepo_UpdateRefunded_Call {
	_c.Call.Return(run)
	return _c
}

// UpdateTrxCreated provides a mock function with given fields: ctx, refundOrderPayment
func (_m *RefundOrderPaymentRepo) UpdateTrxCreated(ctx context.Context, refundOrderPayment *model.RefundOrderPayment) error {
	ret := _m.Called(ctx, refundOrderPayment)

	if len(ret) == 0 {
		panic("no return value specified for UpdateTrxCreated")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *model.RefundOrderPayment) error); ok {
		r0 = rf(ctx, refundOrderPayment)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// RefundOrderPaymentRepo_UpdateTrxCreated_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateTrxCreated'
type RefundOrderPaymentRepo_UpdateTrxCreated_Call struct {
	*mock.Call
}

// UpdateTrxCreated is a helper method to define mock.On call
//   - ctx context.Context
//   - refundOrderPayment *model.RefundOrderPayment
func (_e *RefundOrderPaymentRepo_Expecter) UpdateTrxCreated(ctx interface{}, refundOrderPayment interface{}) *RefundOrderPaymentRepo_UpdateTrxCreated_Call {
	return &RefundOrderPaymentRepo_UpdateTrxCreated_Call{Call: _e.mock.On("UpdateTrxCreated", ctx, refundOrderPayment)}
}

func (_c *RefundOrderPaymentRepo_UpdateTrxCreated_Call) Run(run func(ctx context.Context, refundOrderPayment *model.RefundOrderPayment)) *RefundOrderPaymentRepo_UpdateTrxCreated_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*model.RefundOrderPayment))
	})
	return _c
}

func (_c *RefundOrderPaymentRepo_UpdateTrxCreated_Call) Return(_a0 error) *RefundOrderPaymentRepo_UpdateTrxCreated_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *RefundOrderPaymentRepo_UpdateTrxCreated_Call) RunAndReturn(run func(context.Context, *model.RefundOrderPayment) error) *RefundOrderPaymentRepo_UpdateTrxCreated_Call {
	_c.Call.Return(run)
	return _c
}

// NewRefundOrderPaymentRepo creates a new instance of RefundOrderPaymentRepo. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewRefundOrderPaymentRepo(t interface {
	mock.TestingT
	Cleanup(func())
}) *RefundOrderPaymentRepo {
	mock := &RefundOrderPaymentRepo{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
