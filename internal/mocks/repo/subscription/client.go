// Code generated by mockery v2.53.4. DO NOT EDIT.

package mocks

import (
	context "context"

	mock "github.com/stretchr/testify/mock"
)

// Client is an autogenerated mock type for the Client type
type Client struct {
	mock.Mock
}

type Client_Expecter struct {
	mock *mock.Mock
}

func (_m *Client) EXPECT() *Client_Expecter {
	return &Client_Expecter{mock: &_m.Mock}
}

// RedeemStoreCredit provides a mock function with given fields: ctx, amount, orderID, customerID
func (_m *Client) RedeemStoreCredit(ctx context.Context, amount int64, orderID int64, customerID int64) error {
	ret := _m.Called(ctx, amount, orderID, customerID)

	if len(ret) == 0 {
		panic("no return value specified for RedeemStoreCredit")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, int64, int64, int64) error); ok {
		r0 = rf(ctx, amount, orderID, customerID)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// Client_RedeemStoreCredit_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'RedeemStoreCredit'
type Client_RedeemStoreCredit_Call struct {
	*mock.Call
}

// RedeemStoreCredit is a helper method to define mock.On call
//   - ctx context.Context
//   - amount int64
//   - orderID int64
//   - customerID int64
func (_e *Client_Expecter) RedeemStoreCredit(ctx interface{}, amount interface{}, orderID interface{}, customerID interface{}) *Client_RedeemStoreCredit_Call {
	return &Client_RedeemStoreCredit_Call{Call: _e.mock.On("RedeemStoreCredit", ctx, amount, orderID, customerID)}
}

func (_c *Client_RedeemStoreCredit_Call) Run(run func(ctx context.Context, amount int64, orderID int64, customerID int64)) *Client_RedeemStoreCredit_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(int64), args[2].(int64), args[3].(int64))
	})
	return _c
}

func (_c *Client_RedeemStoreCredit_Call) Return(_a0 error) *Client_RedeemStoreCredit_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *Client_RedeemStoreCredit_Call) RunAndReturn(run func(context.Context, int64, int64, int64) error) *Client_RedeemStoreCredit_Call {
	_c.Call.Return(run)
	return _c
}

// NewClient creates a new instance of Client. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewClient(t interface {
	mock.TestingT
	Cleanup(func())
}) *Client {
	mock := &Client{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
