// Code generated by mockery v2.53.4. DO NOT EDIT.

package mocks

import (
	context "context"

	model "github.com/MoeGolibrary/moego-svc-order-v2/internal/model"
	mock "github.com/stretchr/testify/mock"
)

// TipsSplitDetailRepo is an autogenerated mock type for the TipsSplitDetailRepo type
type TipsSplitDetailRepo struct {
	mock.Mock
}

type TipsSplitDetailRepo_Expecter struct {
	mock *mock.Mock
}

func (_m *TipsSplitDetailRepo) EXPECT() *TipsSplitDetailRepo_Expecter {
	return &TipsSplitDetailRepo_Expecter{mock: &_m.Mock}
}

// ListByIDs provides a mock function with given fields: ctx, tipsSplitID
func (_m *TipsSplitDetailRepo) ListByIDs(ctx context.Context, tipsSplitID ...int64) ([]*model.TipsSplitDetail, error) {
	_va := make([]interface{}, len(tipsSplitID))
	for _i := range tipsSplitID {
		_va[_i] = tipsSplitID[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, ctx)
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	if len(ret) == 0 {
		panic("no return value specified for ListByIDs")
	}

	var r0 []*model.TipsSplitDetail
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, ...int64) ([]*model.TipsSplitDetail, error)); ok {
		return rf(ctx, tipsSplitID...)
	}
	if rf, ok := ret.Get(0).(func(context.Context, ...int64) []*model.TipsSplitDetail); ok {
		r0 = rf(ctx, tipsSplitID...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*model.TipsSplitDetail)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, ...int64) error); ok {
		r1 = rf(ctx, tipsSplitID...)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// TipsSplitDetailRepo_ListByIDs_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ListByIDs'
type TipsSplitDetailRepo_ListByIDs_Call struct {
	*mock.Call
}

// ListByIDs is a helper method to define mock.On call
//   - ctx context.Context
//   - tipsSplitID ...int64
func (_e *TipsSplitDetailRepo_Expecter) ListByIDs(ctx interface{}, tipsSplitID ...interface{}) *TipsSplitDetailRepo_ListByIDs_Call {
	return &TipsSplitDetailRepo_ListByIDs_Call{Call: _e.mock.On("ListByIDs",
		append([]interface{}{ctx}, tipsSplitID...)...)}
}

func (_c *TipsSplitDetailRepo_ListByIDs_Call) Run(run func(ctx context.Context, tipsSplitID ...int64)) *TipsSplitDetailRepo_ListByIDs_Call {
	_c.Call.Run(func(args mock.Arguments) {
		variadicArgs := make([]int64, len(args)-1)
		for i, a := range args[1:] {
			if a != nil {
				variadicArgs[i] = a.(int64)
			}
		}
		run(args[0].(context.Context), variadicArgs...)
	})
	return _c
}

func (_c *TipsSplitDetailRepo_ListByIDs_Call) Return(_a0 []*model.TipsSplitDetail, _a1 error) *TipsSplitDetailRepo_ListByIDs_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *TipsSplitDetailRepo_ListByIDs_Call) RunAndReturn(run func(context.Context, ...int64) ([]*model.TipsSplitDetail, error)) *TipsSplitDetailRepo_ListByIDs_Call {
	_c.Call.Return(run)
	return _c
}

// Upsert provides a mock function with given fields: ctx, tipsSplitID, tipsSplitDetail
func (_m *TipsSplitDetailRepo) Upsert(ctx context.Context, tipsSplitID int64, tipsSplitDetail []*model.TipsSplitDetail) error {
	ret := _m.Called(ctx, tipsSplitID, tipsSplitDetail)

	if len(ret) == 0 {
		panic("no return value specified for Upsert")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, int64, []*model.TipsSplitDetail) error); ok {
		r0 = rf(ctx, tipsSplitID, tipsSplitDetail)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// TipsSplitDetailRepo_Upsert_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Upsert'
type TipsSplitDetailRepo_Upsert_Call struct {
	*mock.Call
}

// Upsert is a helper method to define mock.On call
//   - ctx context.Context
//   - tipsSplitID int64
//   - tipsSplitDetail []*model.TipsSplitDetail
func (_e *TipsSplitDetailRepo_Expecter) Upsert(ctx interface{}, tipsSplitID interface{}, tipsSplitDetail interface{}) *TipsSplitDetailRepo_Upsert_Call {
	return &TipsSplitDetailRepo_Upsert_Call{Call: _e.mock.On("Upsert", ctx, tipsSplitID, tipsSplitDetail)}
}

func (_c *TipsSplitDetailRepo_Upsert_Call) Run(run func(ctx context.Context, tipsSplitID int64, tipsSplitDetail []*model.TipsSplitDetail)) *TipsSplitDetailRepo_Upsert_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(int64), args[2].([]*model.TipsSplitDetail))
	})
	return _c
}

func (_c *TipsSplitDetailRepo_Upsert_Call) Return(_a0 error) *TipsSplitDetailRepo_Upsert_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *TipsSplitDetailRepo_Upsert_Call) RunAndReturn(run func(context.Context, int64, []*model.TipsSplitDetail) error) *TipsSplitDetailRepo_Upsert_Call {
	_c.Call.Return(run)
	return _c
}

// NewTipsSplitDetailRepo creates a new instance of TipsSplitDetailRepo. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewTipsSplitDetailRepo(t interface {
	mock.TestingT
	Cleanup(func())
}) *TipsSplitDetailRepo {
	mock := &TipsSplitDetailRepo{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
