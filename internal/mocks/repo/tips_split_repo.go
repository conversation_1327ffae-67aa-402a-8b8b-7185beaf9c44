// Code generated by mockery v2.53.4. DO NOT EDIT.

package mocks

import (
	context "context"

	model "github.com/MoeGolibrary/moego-svc-order-v2/internal/model"
	mock "github.com/stretchr/testify/mock"

	orderpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/order/v1"
)

// TipsSplitRepo is an autogenerated mock type for the TipsSplitRepo type
type TipsSplitRepo struct {
	mock.Mock
}

type TipsSplitRepo_Expecter struct {
	mock *mock.Mock
}

func (_m *TipsSplitRepo) EXPECT() *TipsSplitRepo_Expecter {
	return &TipsSplitRepo_Expecter{mock: &_m.Mock}
}

// Create provides a mock function with given fields: ctx, tipsSplit
func (_m *TipsSplitRepo) Create(ctx context.Context, tipsSplit *model.TipsSplit) error {
	ret := _m.Called(ctx, tipsSplit)

	if len(ret) == 0 {
		panic("no return value specified for Create")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *model.TipsSplit) error); ok {
		r0 = rf(ctx, tipsSplit)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// TipsSplitRepo_Create_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Create'
type TipsSplitRepo_Create_Call struct {
	*mock.Call
}

// Create is a helper method to define mock.On call
//   - ctx context.Context
//   - tipsSplit *model.TipsSplit
func (_e *TipsSplitRepo_Expecter) Create(ctx interface{}, tipsSplit interface{}) *TipsSplitRepo_Create_Call {
	return &TipsSplitRepo_Create_Call{Call: _e.mock.On("Create", ctx, tipsSplit)}
}

func (_c *TipsSplitRepo_Create_Call) Run(run func(ctx context.Context, tipsSplit *model.TipsSplit)) *TipsSplitRepo_Create_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*model.TipsSplit))
	})
	return _c
}

func (_c *TipsSplitRepo_Create_Call) Return(_a0 error) *TipsSplitRepo_Create_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *TipsSplitRepo_Create_Call) RunAndReturn(run func(context.Context, *model.TipsSplit) error) *TipsSplitRepo_Create_Call {
	_c.Call.Return(run)
	return _c
}

// DeleteLegacyTipSplitRecords provides a mock function with given fields: ctx, id
func (_m *TipsSplitRepo) DeleteLegacyTipSplitRecords(ctx context.Context, id int64) error {
	ret := _m.Called(ctx, id)

	if len(ret) == 0 {
		panic("no return value specified for DeleteLegacyTipSplitRecords")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, int64) error); ok {
		r0 = rf(ctx, id)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// TipsSplitRepo_DeleteLegacyTipSplitRecords_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DeleteLegacyTipSplitRecords'
type TipsSplitRepo_DeleteLegacyTipSplitRecords_Call struct {
	*mock.Call
}

// DeleteLegacyTipSplitRecords is a helper method to define mock.On call
//   - ctx context.Context
//   - id int64
func (_e *TipsSplitRepo_Expecter) DeleteLegacyTipSplitRecords(ctx interface{}, id interface{}) *TipsSplitRepo_DeleteLegacyTipSplitRecords_Call {
	return &TipsSplitRepo_DeleteLegacyTipSplitRecords_Call{Call: _e.mock.On("DeleteLegacyTipSplitRecords", ctx, id)}
}

func (_c *TipsSplitRepo_DeleteLegacyTipSplitRecords_Call) Run(run func(ctx context.Context, id int64)) *TipsSplitRepo_DeleteLegacyTipSplitRecords_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(int64))
	})
	return _c
}

func (_c *TipsSplitRepo_DeleteLegacyTipSplitRecords_Call) Return(_a0 error) *TipsSplitRepo_DeleteLegacyTipSplitRecords_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *TipsSplitRepo_DeleteLegacyTipSplitRecords_Call) RunAndReturn(run func(context.Context, int64) error) *TipsSplitRepo_DeleteLegacyTipSplitRecords_Call {
	_c.Call.Return(run)
	return _c
}

// Get provides a mock function with given fields: ctx, id
func (_m *TipsSplitRepo) Get(ctx context.Context, id int64) (*model.TipsSplit, error) {
	ret := _m.Called(ctx, id)

	if len(ret) == 0 {
		panic("no return value specified for Get")
	}

	var r0 *model.TipsSplit
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, int64) (*model.TipsSplit, error)); ok {
		return rf(ctx, id)
	}
	if rf, ok := ret.Get(0).(func(context.Context, int64) *model.TipsSplit); ok {
		r0 = rf(ctx, id)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*model.TipsSplit)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, int64) error); ok {
		r1 = rf(ctx, id)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// TipsSplitRepo_Get_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Get'
type TipsSplitRepo_Get_Call struct {
	*mock.Call
}

// Get is a helper method to define mock.On call
//   - ctx context.Context
//   - id int64
func (_e *TipsSplitRepo_Expecter) Get(ctx interface{}, id interface{}) *TipsSplitRepo_Get_Call {
	return &TipsSplitRepo_Get_Call{Call: _e.mock.On("Get", ctx, id)}
}

func (_c *TipsSplitRepo_Get_Call) Run(run func(ctx context.Context, id int64)) *TipsSplitRepo_Get_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(int64))
	})
	return _c
}

func (_c *TipsSplitRepo_Get_Call) Return(_a0 *model.TipsSplit, _a1 error) *TipsSplitRepo_Get_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *TipsSplitRepo_Get_Call) RunAndReturn(run func(context.Context, int64) (*model.TipsSplit, error)) *TipsSplitRepo_Get_Call {
	_c.Call.Return(run)
	return _c
}

// GetBySourceIDAndType provides a mock function with given fields: ctx, sourceID, sourceType
func (_m *TipsSplitRepo) GetBySourceIDAndType(ctx context.Context, sourceID int64, sourceType orderpb.OrderSourceType) (*model.TipsSplit, error) {
	ret := _m.Called(ctx, sourceID, sourceType)

	if len(ret) == 0 {
		panic("no return value specified for GetBySourceIDAndType")
	}

	var r0 *model.TipsSplit
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, int64, orderpb.OrderSourceType) (*model.TipsSplit, error)); ok {
		return rf(ctx, sourceID, sourceType)
	}
	if rf, ok := ret.Get(0).(func(context.Context, int64, orderpb.OrderSourceType) *model.TipsSplit); ok {
		r0 = rf(ctx, sourceID, sourceType)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*model.TipsSplit)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, int64, orderpb.OrderSourceType) error); ok {
		r1 = rf(ctx, sourceID, sourceType)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// TipsSplitRepo_GetBySourceIDAndType_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetBySourceIDAndType'
type TipsSplitRepo_GetBySourceIDAndType_Call struct {
	*mock.Call
}

// GetBySourceIDAndType is a helper method to define mock.On call
//   - ctx context.Context
//   - sourceID int64
//   - sourceType orderpb.OrderSourceType
func (_e *TipsSplitRepo_Expecter) GetBySourceIDAndType(ctx interface{}, sourceID interface{}, sourceType interface{}) *TipsSplitRepo_GetBySourceIDAndType_Call {
	return &TipsSplitRepo_GetBySourceIDAndType_Call{Call: _e.mock.On("GetBySourceIDAndType", ctx, sourceID, sourceType)}
}

func (_c *TipsSplitRepo_GetBySourceIDAndType_Call) Run(run func(ctx context.Context, sourceID int64, sourceType orderpb.OrderSourceType)) *TipsSplitRepo_GetBySourceIDAndType_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(int64), args[2].(orderpb.OrderSourceType))
	})
	return _c
}

func (_c *TipsSplitRepo_GetBySourceIDAndType_Call) Return(_a0 *model.TipsSplit, _a1 error) *TipsSplitRepo_GetBySourceIDAndType_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *TipsSplitRepo_GetBySourceIDAndType_Call) RunAndReturn(run func(context.Context, int64, orderpb.OrderSourceType) (*model.TipsSplit, error)) *TipsSplitRepo_GetBySourceIDAndType_Call {
	_c.Call.Return(run)
	return _c
}

// ListBySources provides a mock function with given fields: ctx, sourceIDToType
func (_m *TipsSplitRepo) ListBySources(ctx context.Context, sourceIDToType map[int64]orderpb.OrderSourceType) ([]*model.TipsSplit, error) {
	ret := _m.Called(ctx, sourceIDToType)

	if len(ret) == 0 {
		panic("no return value specified for ListBySources")
	}

	var r0 []*model.TipsSplit
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, map[int64]orderpb.OrderSourceType) ([]*model.TipsSplit, error)); ok {
		return rf(ctx, sourceIDToType)
	}
	if rf, ok := ret.Get(0).(func(context.Context, map[int64]orderpb.OrderSourceType) []*model.TipsSplit); ok {
		r0 = rf(ctx, sourceIDToType)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*model.TipsSplit)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, map[int64]orderpb.OrderSourceType) error); ok {
		r1 = rf(ctx, sourceIDToType)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// TipsSplitRepo_ListBySources_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ListBySources'
type TipsSplitRepo_ListBySources_Call struct {
	*mock.Call
}

// ListBySources is a helper method to define mock.On call
//   - ctx context.Context
//   - sourceIDToType map[int64]orderpb.OrderSourceType
func (_e *TipsSplitRepo_Expecter) ListBySources(ctx interface{}, sourceIDToType interface{}) *TipsSplitRepo_ListBySources_Call {
	return &TipsSplitRepo_ListBySources_Call{Call: _e.mock.On("ListBySources", ctx, sourceIDToType)}
}

func (_c *TipsSplitRepo_ListBySources_Call) Run(run func(ctx context.Context, sourceIDToType map[int64]orderpb.OrderSourceType)) *TipsSplitRepo_ListBySources_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(map[int64]orderpb.OrderSourceType))
	})
	return _c
}

func (_c *TipsSplitRepo_ListBySources_Call) Return(_a0 []*model.TipsSplit, _a1 error) *TipsSplitRepo_ListBySources_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *TipsSplitRepo_ListBySources_Call) RunAndReturn(run func(context.Context, map[int64]orderpb.OrderSourceType) ([]*model.TipsSplit, error)) *TipsSplitRepo_ListBySources_Call {
	_c.Call.Return(run)
	return _c
}

// ListLegacyTipSplitRecordsByOrderIDs provides a mock function with given fields: ctx, businessID, orderIDs
func (_m *TipsSplitRepo) ListLegacyTipSplitRecordsByOrderIDs(ctx context.Context, businessID int64, orderIDs []int64) ([]*model.OrderTipsSplitRecord, error) {
	ret := _m.Called(ctx, businessID, orderIDs)

	if len(ret) == 0 {
		panic("no return value specified for ListLegacyTipSplitRecordsByOrderIDs")
	}

	var r0 []*model.OrderTipsSplitRecord
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, int64, []int64) ([]*model.OrderTipsSplitRecord, error)); ok {
		return rf(ctx, businessID, orderIDs)
	}
	if rf, ok := ret.Get(0).(func(context.Context, int64, []int64) []*model.OrderTipsSplitRecord); ok {
		r0 = rf(ctx, businessID, orderIDs)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*model.OrderTipsSplitRecord)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, int64, []int64) error); ok {
		r1 = rf(ctx, businessID, orderIDs)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// TipsSplitRepo_ListLegacyTipSplitRecordsByOrderIDs_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ListLegacyTipSplitRecordsByOrderIDs'
type TipsSplitRepo_ListLegacyTipSplitRecordsByOrderIDs_Call struct {
	*mock.Call
}

// ListLegacyTipSplitRecordsByOrderIDs is a helper method to define mock.On call
//   - ctx context.Context
//   - businessID int64
//   - orderIDs []int64
func (_e *TipsSplitRepo_Expecter) ListLegacyTipSplitRecordsByOrderIDs(ctx interface{}, businessID interface{}, orderIDs interface{}) *TipsSplitRepo_ListLegacyTipSplitRecordsByOrderIDs_Call {
	return &TipsSplitRepo_ListLegacyTipSplitRecordsByOrderIDs_Call{Call: _e.mock.On("ListLegacyTipSplitRecordsByOrderIDs", ctx, businessID, orderIDs)}
}

func (_c *TipsSplitRepo_ListLegacyTipSplitRecordsByOrderIDs_Call) Run(run func(ctx context.Context, businessID int64, orderIDs []int64)) *TipsSplitRepo_ListLegacyTipSplitRecordsByOrderIDs_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(int64), args[2].([]int64))
	})
	return _c
}

func (_c *TipsSplitRepo_ListLegacyTipSplitRecordsByOrderIDs_Call) Return(_a0 []*model.OrderTipsSplitRecord, _a1 error) *TipsSplitRepo_ListLegacyTipSplitRecordsByOrderIDs_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *TipsSplitRepo_ListLegacyTipSplitRecordsByOrderIDs_Call) RunAndReturn(run func(context.Context, int64, []int64) ([]*model.OrderTipsSplitRecord, error)) *TipsSplitRepo_ListLegacyTipSplitRecordsByOrderIDs_Call {
	_c.Call.Return(run)
	return _c
}

// Update provides a mock function with given fields: ctx, tipsSplit
func (_m *TipsSplitRepo) Update(ctx context.Context, tipsSplit *model.TipsSplit) (int64, error) {
	ret := _m.Called(ctx, tipsSplit)

	if len(ret) == 0 {
		panic("no return value specified for Update")
	}

	var r0 int64
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *model.TipsSplit) (int64, error)); ok {
		return rf(ctx, tipsSplit)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *model.TipsSplit) int64); ok {
		r0 = rf(ctx, tipsSplit)
	} else {
		r0 = ret.Get(0).(int64)
	}

	if rf, ok := ret.Get(1).(func(context.Context, *model.TipsSplit) error); ok {
		r1 = rf(ctx, tipsSplit)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// TipsSplitRepo_Update_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Update'
type TipsSplitRepo_Update_Call struct {
	*mock.Call
}

// Update is a helper method to define mock.On call
//   - ctx context.Context
//   - tipsSplit *model.TipsSplit
func (_e *TipsSplitRepo_Expecter) Update(ctx interface{}, tipsSplit interface{}) *TipsSplitRepo_Update_Call {
	return &TipsSplitRepo_Update_Call{Call: _e.mock.On("Update", ctx, tipsSplit)}
}

func (_c *TipsSplitRepo_Update_Call) Run(run func(ctx context.Context, tipsSplit *model.TipsSplit)) *TipsSplitRepo_Update_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*model.TipsSplit))
	})
	return _c
}

func (_c *TipsSplitRepo_Update_Call) Return(_a0 int64, _a1 error) *TipsSplitRepo_Update_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *TipsSplitRepo_Update_Call) RunAndReturn(run func(context.Context, *model.TipsSplit) (int64, error)) *TipsSplitRepo_Update_Call {
	_c.Call.Return(run)
	return _c
}

// NewTipsSplitRepo creates a new instance of TipsSplitRepo. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewTipsSplitRepo(t interface {
	mock.TestingT
	Cleanup(func())
}) *TipsSplitRepo {
	mock := &TipsSplitRepo{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
