// Code generated by mockery v2.53.4. DO NOT EDIT.

package mocks

import (
	repo "github.com/MoeGolibrary/moego-svc-order-v2/internal/repo"
	mock "github.com/stretchr/testify/mock"
)

// TXRepo is an autogenerated mock type for the TXRepo type
type TXRepo struct {
	mock.Mock
}

type TXRepo_Expecter struct {
	mock *mock.Mock
}

func (_m *TXRepo) EXPECT() *TXRepo_Expecter {
	return &TXRepo_Expecter{mock: &_m.Mock}
}

// Tx provides a mock function with given fields: _a0
func (_m *TXRepo) Tx(_a0 func(repo.OrderTX) error) error {
	ret := _m.Called(_a0)

	if len(ret) == 0 {
		panic("no return value specified for Tx")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(func(repo.OrderTX) error) error); ok {
		r0 = rf(_a0)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// TXRepo_Tx_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Tx'
type TXRepo_Tx_Call struct {
	*mock.Call
}

// Tx is a helper method to define mock.On call
//   - _a0 func(repo.OrderTX) error
func (_e *TXRepo_Expecter) Tx(_a0 interface{}) *TXRepo_Tx_Call {
	return &TXRepo_Tx_Call{Call: _e.mock.On("Tx", _a0)}
}

func (_c *TXRepo_Tx_Call) Run(run func(_a0 func(repo.OrderTX) error)) *TXRepo_Tx_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(func(repo.OrderTX) error))
	})
	return _c
}

func (_c *TXRepo_Tx_Call) Return(_a0 error) *TXRepo_Tx_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *TXRepo_Tx_Call) RunAndReturn(run func(func(repo.OrderTX) error) error) *TXRepo_Tx_Call {
	_c.Call.Return(run)
	return _c
}

// NewTXRepo creates a new instance of TXRepo. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewTXRepo(t interface {
	mock.TestingT
	Cleanup(func())
}) *TXRepo {
	mock := &TXRepo{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
