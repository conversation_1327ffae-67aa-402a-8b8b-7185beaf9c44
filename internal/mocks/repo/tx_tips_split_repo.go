// Code generated by mockery v2.53.4. DO NOT EDIT.

package mocks

import (
	repo "github.com/MoeGolibrary/moego-svc-order-v2/internal/repo"
	mock "github.com/stretchr/testify/mock"
)

// TxTipsSplitRepo is an autogenerated mock type for the TxTipsSplitRepo type
type TxTipsSplitRepo struct {
	mock.Mock
}

type TxTipsSplitRepo_Expecter struct {
	mock *mock.Mock
}

func (_m *TxTipsSplitRepo) EXPECT() *TxTipsSplitRepo_Expecter {
	return &TxTipsSplitRepo_Expecter{mock: &_m.Mock}
}

// Tx provides a mock function with given fields: _a0
func (_m *TxTipsSplitRepo) Tx(_a0 func(repo.TipsSplitTx) error) error {
	ret := _m.Called(_a0)

	if len(ret) == 0 {
		panic("no return value specified for Tx")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(func(repo.TipsSplitTx) error) error); ok {
		r0 = rf(_a0)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// TxTipsSplitRepo_Tx_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Tx'
type TxTipsSplitRepo_Tx_Call struct {
	*mock.Call
}

// Tx is a helper method to define mock.On call
//   - _a0 func(repo.TipsSplitTx) error
func (_e *TxTipsSplitRepo_Expecter) Tx(_a0 interface{}) *TxTipsSplitRepo_Tx_Call {
	return &TxTipsSplitRepo_Tx_Call{Call: _e.mock.On("Tx", _a0)}
}

func (_c *TxTipsSplitRepo_Tx_Call) Run(run func(_a0 func(repo.TipsSplitTx) error)) *TxTipsSplitRepo_Tx_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(func(repo.TipsSplitTx) error))
	})
	return _c
}

func (_c *TxTipsSplitRepo_Tx_Call) Return(_a0 error) *TxTipsSplitRepo_Tx_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *TxTipsSplitRepo_Tx_Call) RunAndReturn(run func(func(repo.TipsSplitTx) error) error) *TxTipsSplitRepo_Tx_Call {
	_c.Call.Return(run)
	return _c
}

// NewTxTipsSplitRepo creates a new instance of TxTipsSplitRepo. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewTxTipsSplitRepo(t interface {
	mock.TestingT
	Cleanup(func())
}) *TxTipsSplitRepo {
	mock := &TxTipsSplitRepo{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
