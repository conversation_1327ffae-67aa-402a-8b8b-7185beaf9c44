// Code generated by mockery v2.53.4. DO NOT EDIT.

package mocks

import (
	context "context"

	service "github.com/MoeGolibrary/moego-svc-order-v2/internal/service"
	mock "github.com/stretchr/testify/mock"
)

// DepositRuleMatcher is an autogenerated mock type for the DepositRuleMatcher type
type DepositRuleMatcher struct {
	mock.Mock
}

type DepositRuleMatcher_Expecter struct {
	mock *mock.Mock
}

func (_m *DepositRuleMatcher) EXPECT() *DepositRuleMatcher_Expecter {
	return &DepositRuleMatcher_Expecter{mock: &_m.Mock}
}

// Match provides a mock function with given fields: ctx, candidate
func (_m *DepositRuleMatcher) Match(ctx context.Context, candidate *service.FilterCandidate) bool {
	ret := _m.Called(ctx, candidate)

	if len(ret) == 0 {
		panic("no return value specified for Match")
	}

	var r0 bool
	if rf, ok := ret.Get(0).(func(context.Context, *service.FilterCandidate) bool); ok {
		r0 = rf(ctx, candidate)
	} else {
		r0 = ret.Get(0).(bool)
	}

	return r0
}

// DepositRuleMatcher_Match_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Match'
type DepositRuleMatcher_Match_Call struct {
	*mock.Call
}

// Match is a helper method to define mock.On call
//   - ctx context.Context
//   - candidate *service.FilterCandidate
func (_e *DepositRuleMatcher_Expecter) Match(ctx interface{}, candidate interface{}) *DepositRuleMatcher_Match_Call {
	return &DepositRuleMatcher_Match_Call{Call: _e.mock.On("Match", ctx, candidate)}
}

func (_c *DepositRuleMatcher_Match_Call) Run(run func(ctx context.Context, candidate *service.FilterCandidate)) *DepositRuleMatcher_Match_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*service.FilterCandidate))
	})
	return _c
}

func (_c *DepositRuleMatcher_Match_Call) Return(_a0 bool) *DepositRuleMatcher_Match_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *DepositRuleMatcher_Match_Call) RunAndReturn(run func(context.Context, *service.FilterCandidate) bool) *DepositRuleMatcher_Match_Call {
	_c.Call.Return(run)
	return _c
}

// NewDepositRuleMatcher creates a new instance of DepositRuleMatcher. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewDepositRuleMatcher(t interface {
	mock.TestingT
	Cleanup(func())
}) *DepositRuleMatcher {
	mock := &DepositRuleMatcher{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
