// Code generated by mockery v2.53.4. DO NOT EDIT.

package mocks

import (
	export "github.com/MoeGolibrary/moego-svc-order-v2/internal/service/export"
	mock "github.com/stretchr/testify/mock"
)

// Tabler is an autogenerated mock type for the Tabler type
type Tabler struct {
	mock.Mock
}

type Tabler_Expecter struct {
	mock *mock.Mock
}

func (_m *Tabler) EXPECT() *Tabler_Expecter {
	return &Tabler_Expecter{mock: &_m.Mock}
}

// CellText provides a mock function with given fields: row, col
func (_m *Tabler) CellText(row int, col int) string {
	ret := _m.Called(row, col)

	if len(ret) == 0 {
		panic("no return value specified for CellText")
	}

	var r0 string
	if rf, ok := ret.Get(0).(func(int, int) string); ok {
		r0 = rf(row, col)
	} else {
		r0 = ret.Get(0).(string)
	}

	return r0
}

// Tabler_CellText_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CellText'
type Tabler_CellText_Call struct {
	*mock.Call
}

// CellText is a helper method to define mock.On call
//   - row int
//   - col int
func (_e *Tabler_Expecter) CellText(row interface{}, col interface{}) *Tabler_CellText_Call {
	return &Tabler_CellText_Call{Call: _e.mock.On("CellText", row, col)}
}

func (_c *Tabler_CellText_Call) Run(run func(row int, col int)) *Tabler_CellText_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(int), args[1].(int))
	})
	return _c
}

func (_c *Tabler_CellText_Call) Return(_a0 string) *Tabler_CellText_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *Tabler_CellText_Call) RunAndReturn(run func(int, int) string) *Tabler_CellText_Call {
	_c.Call.Return(run)
	return _c
}

// ColumnConfig provides a mock function with given fields: col
func (_m *Tabler) ColumnConfig(col int) export.ColumnConfig {
	ret := _m.Called(col)

	if len(ret) == 0 {
		panic("no return value specified for ColumnConfig")
	}

	var r0 export.ColumnConfig
	if rf, ok := ret.Get(0).(func(int) export.ColumnConfig); ok {
		r0 = rf(col)
	} else {
		r0 = ret.Get(0).(export.ColumnConfig)
	}

	return r0
}

// Tabler_ColumnConfig_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ColumnConfig'
type Tabler_ColumnConfig_Call struct {
	*mock.Call
}

// ColumnConfig is a helper method to define mock.On call
//   - col int
func (_e *Tabler_Expecter) ColumnConfig(col interface{}) *Tabler_ColumnConfig_Call {
	return &Tabler_ColumnConfig_Call{Call: _e.mock.On("ColumnConfig", col)}
}

func (_c *Tabler_ColumnConfig_Call) Run(run func(col int)) *Tabler_ColumnConfig_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(int))
	})
	return _c
}

func (_c *Tabler_ColumnConfig_Call) Return(_a0 export.ColumnConfig) *Tabler_ColumnConfig_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *Tabler_ColumnConfig_Call) RunAndReturn(run func(int) export.ColumnConfig) *Tabler_ColumnConfig_Call {
	_c.Call.Return(run)
	return _c
}

// ColumnCount provides a mock function with no fields
func (_m *Tabler) ColumnCount() int {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for ColumnCount")
	}

	var r0 int
	if rf, ok := ret.Get(0).(func() int); ok {
		r0 = rf()
	} else {
		r0 = ret.Get(0).(int)
	}

	return r0
}

// Tabler_ColumnCount_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ColumnCount'
type Tabler_ColumnCount_Call struct {
	*mock.Call
}

// ColumnCount is a helper method to define mock.On call
func (_e *Tabler_Expecter) ColumnCount() *Tabler_ColumnCount_Call {
	return &Tabler_ColumnCount_Call{Call: _e.mock.On("ColumnCount")}
}

func (_c *Tabler_ColumnCount_Call) Run(run func()) *Tabler_ColumnCount_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *Tabler_ColumnCount_Call) Return(_a0 int) *Tabler_ColumnCount_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *Tabler_ColumnCount_Call) RunAndReturn(run func() int) *Tabler_ColumnCount_Call {
	_c.Call.Return(run)
	return _c
}

// RowCount provides a mock function with no fields
func (_m *Tabler) RowCount() int {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for RowCount")
	}

	var r0 int
	if rf, ok := ret.Get(0).(func() int); ok {
		r0 = rf()
	} else {
		r0 = ret.Get(0).(int)
	}

	return r0
}

// Tabler_RowCount_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'RowCount'
type Tabler_RowCount_Call struct {
	*mock.Call
}

// RowCount is a helper method to define mock.On call
func (_e *Tabler_Expecter) RowCount() *Tabler_RowCount_Call {
	return &Tabler_RowCount_Call{Call: _e.mock.On("RowCount")}
}

func (_c *Tabler_RowCount_Call) Run(run func()) *Tabler_RowCount_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *Tabler_RowCount_Call) Return(_a0 int) *Tabler_RowCount_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *Tabler_RowCount_Call) RunAndReturn(run func() int) *Tabler_RowCount_Call {
	_c.Call.Return(run)
	return _c
}

// NewTabler creates a new instance of Tabler. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewTabler(t interface {
	mock.TestingT
	Cleanup(func())
}) *Tabler {
	mock := &Tabler{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
