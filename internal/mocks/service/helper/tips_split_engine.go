// Code generated by mockery v2.53.4. DO NOT EDIT.

package mocks

import (
	grooming "github.com/MoeGolibrary/moego-svc-order-v2/internal/repo/grooming"
	decimal "github.com/shopspring/decimal"

	mock "github.com/stretchr/testify/mock"

	orderpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/order/v1"
)

// TipsSplitEngine is an autogenerated mock type for the TipsSplitEngine type
type TipsSplitEngine struct {
	mock.Mock
}

type TipsSplitEngine_Expecter struct {
	mock *mock.Mock
}

func (_m *TipsSplitEngine) EXPECT() *TipsSplitEngine_Expecter {
	return &TipsSplitEngine_Expecter{mock: &_m.Mock}
}

// CalculateTipsSplit provides a mock function with given fields: apptDetailList, apptSetting, totalTipsAmount, currencyCode, productAssignedStaffs
func (_m *TipsSplitEngine) CalculateTipsSplit(apptDetailList []*grooming.PetDetailDTO, apptSetting *orderpb.TipsSplitModel_TipsSplitConfig, totalTipsAmount decimal.Decimal, currencyCode string, productAssignedStaffs []int64) (*orderpb.TipsSplitModel_TipsSplitConfig, error) {
	ret := _m.Called(apptDetailList, apptSetting, totalTipsAmount, currencyCode, productAssignedStaffs)

	if len(ret) == 0 {
		panic("no return value specified for CalculateTipsSplit")
	}

	var r0 *orderpb.TipsSplitModel_TipsSplitConfig
	var r1 error
	if rf, ok := ret.Get(0).(func([]*grooming.PetDetailDTO, *orderpb.TipsSplitModel_TipsSplitConfig, decimal.Decimal, string, []int64) (*orderpb.TipsSplitModel_TipsSplitConfig, error)); ok {
		return rf(apptDetailList, apptSetting, totalTipsAmount, currencyCode, productAssignedStaffs)
	}
	if rf, ok := ret.Get(0).(func([]*grooming.PetDetailDTO, *orderpb.TipsSplitModel_TipsSplitConfig, decimal.Decimal, string, []int64) *orderpb.TipsSplitModel_TipsSplitConfig); ok {
		r0 = rf(apptDetailList, apptSetting, totalTipsAmount, currencyCode, productAssignedStaffs)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*orderpb.TipsSplitModel_TipsSplitConfig)
		}
	}

	if rf, ok := ret.Get(1).(func([]*grooming.PetDetailDTO, *orderpb.TipsSplitModel_TipsSplitConfig, decimal.Decimal, string, []int64) error); ok {
		r1 = rf(apptDetailList, apptSetting, totalTipsAmount, currencyCode, productAssignedStaffs)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// TipsSplitEngine_CalculateTipsSplit_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CalculateTipsSplit'
type TipsSplitEngine_CalculateTipsSplit_Call struct {
	*mock.Call
}

// CalculateTipsSplit is a helper method to define mock.On call
//   - apptDetailList []*grooming.PetDetailDTO
//   - apptSetting *orderpb.TipsSplitModel_TipsSplitConfig
//   - totalTipsAmount decimal.Decimal
//   - currencyCode string
//   - productAssignedStaffs []int64
func (_e *TipsSplitEngine_Expecter) CalculateTipsSplit(apptDetailList interface{}, apptSetting interface{}, totalTipsAmount interface{}, currencyCode interface{}, productAssignedStaffs interface{}) *TipsSplitEngine_CalculateTipsSplit_Call {
	return &TipsSplitEngine_CalculateTipsSplit_Call{Call: _e.mock.On("CalculateTipsSplit", apptDetailList, apptSetting, totalTipsAmount, currencyCode, productAssignedStaffs)}
}

func (_c *TipsSplitEngine_CalculateTipsSplit_Call) Run(run func(apptDetailList []*grooming.PetDetailDTO, apptSetting *orderpb.TipsSplitModel_TipsSplitConfig, totalTipsAmount decimal.Decimal, currencyCode string, productAssignedStaffs []int64)) *TipsSplitEngine_CalculateTipsSplit_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].([]*grooming.PetDetailDTO), args[1].(*orderpb.TipsSplitModel_TipsSplitConfig), args[2].(decimal.Decimal), args[3].(string), args[4].([]int64))
	})
	return _c
}

func (_c *TipsSplitEngine_CalculateTipsSplit_Call) Return(_a0 *orderpb.TipsSplitModel_TipsSplitConfig, _a1 error) *TipsSplitEngine_CalculateTipsSplit_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *TipsSplitEngine_CalculateTipsSplit_Call) RunAndReturn(run func([]*grooming.PetDetailDTO, *orderpb.TipsSplitModel_TipsSplitConfig, decimal.Decimal, string, []int64) (*orderpb.TipsSplitModel_TipsSplitConfig, error)) *TipsSplitEngine_CalculateTipsSplit_Call {
	_c.Call.Return(run)
	return _c
}

// NewTipsSplitEngine creates a new instance of TipsSplitEngine. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewTipsSplitEngine(t interface {
	mock.TestingT
	Cleanup(func())
}) *TipsSplitEngine {
	mock := &TipsSplitEngine{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
