// Code generated by mockery v2.53.4. DO NOT EDIT.

package mocks

import (
	context "context"

	ordersvcpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/temp_order/v1"
	mock "github.com/stretchr/testify/mock"
)

// ItemAssignService is an autogenerated mock type for the ItemAssignService type
type ItemAssignService struct {
	mock.Mock
}

type ItemAssignService_Expecter struct {
	mock *mock.Mock
}

func (_m *ItemAssignService) EXPECT() *ItemAssignService_Expecter {
	return &ItemAssignService_Expecter{mock: &_m.Mock}
}

// AssignItemAmount provides a mock function with given fields: ctx, req
func (_m *ItemAssignService) AssignItemAmount(ctx context.Context, req *ordersvcpb.AssignItemPaidAmountRequest) (*ordersvcpb.AssignItemPaidAmountResponse, error) {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for AssignItemAmount")
	}

	var r0 *ordersvcpb.AssignItemPaidAmountResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *ordersvcpb.AssignItemPaidAmountRequest) (*ordersvcpb.AssignItemPaidAmountResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *ordersvcpb.AssignItemPaidAmountRequest) *ordersvcpb.AssignItemPaidAmountResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*ordersvcpb.AssignItemPaidAmountResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *ordersvcpb.AssignItemPaidAmountRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// ItemAssignService_AssignItemAmount_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'AssignItemAmount'
type ItemAssignService_AssignItemAmount_Call struct {
	*mock.Call
}

// AssignItemAmount is a helper method to define mock.On call
//   - ctx context.Context
//   - req *ordersvcpb.AssignItemPaidAmountRequest
func (_e *ItemAssignService_Expecter) AssignItemAmount(ctx interface{}, req interface{}) *ItemAssignService_AssignItemAmount_Call {
	return &ItemAssignService_AssignItemAmount_Call{Call: _e.mock.On("AssignItemAmount", ctx, req)}
}

func (_c *ItemAssignService_AssignItemAmount_Call) Run(run func(ctx context.Context, req *ordersvcpb.AssignItemPaidAmountRequest)) *ItemAssignService_AssignItemAmount_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*ordersvcpb.AssignItemPaidAmountRequest))
	})
	return _c
}

func (_c *ItemAssignService_AssignItemAmount_Call) Return(_a0 *ordersvcpb.AssignItemPaidAmountResponse, _a1 error) *ItemAssignService_AssignItemAmount_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *ItemAssignService_AssignItemAmount_Call) RunAndReturn(run func(context.Context, *ordersvcpb.AssignItemPaidAmountRequest) (*ordersvcpb.AssignItemPaidAmountResponse, error)) *ItemAssignService_AssignItemAmount_Call {
	_c.Call.Return(run)
	return _c
}

// GetItemAssignedAmount provides a mock function with given fields: ctx, req
func (_m *ItemAssignService) GetItemAssignedAmount(ctx context.Context, req *ordersvcpb.GetAssignedItemPaidAmountRequest) (*ordersvcpb.GetAssignedItemPaidAmountResponse, error) {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for GetItemAssignedAmount")
	}

	var r0 *ordersvcpb.GetAssignedItemPaidAmountResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *ordersvcpb.GetAssignedItemPaidAmountRequest) (*ordersvcpb.GetAssignedItemPaidAmountResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *ordersvcpb.GetAssignedItemPaidAmountRequest) *ordersvcpb.GetAssignedItemPaidAmountResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*ordersvcpb.GetAssignedItemPaidAmountResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *ordersvcpb.GetAssignedItemPaidAmountRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// ItemAssignService_GetItemAssignedAmount_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetItemAssignedAmount'
type ItemAssignService_GetItemAssignedAmount_Call struct {
	*mock.Call
}

// GetItemAssignedAmount is a helper method to define mock.On call
//   - ctx context.Context
//   - req *ordersvcpb.GetAssignedItemPaidAmountRequest
func (_e *ItemAssignService_Expecter) GetItemAssignedAmount(ctx interface{}, req interface{}) *ItemAssignService_GetItemAssignedAmount_Call {
	return &ItemAssignService_GetItemAssignedAmount_Call{Call: _e.mock.On("GetItemAssignedAmount", ctx, req)}
}

func (_c *ItemAssignService_GetItemAssignedAmount_Call) Run(run func(ctx context.Context, req *ordersvcpb.GetAssignedItemPaidAmountRequest)) *ItemAssignService_GetItemAssignedAmount_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*ordersvcpb.GetAssignedItemPaidAmountRequest))
	})
	return _c
}

func (_c *ItemAssignService_GetItemAssignedAmount_Call) Return(_a0 *ordersvcpb.GetAssignedItemPaidAmountResponse, _a1 error) *ItemAssignService_GetItemAssignedAmount_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *ItemAssignService_GetItemAssignedAmount_Call) RunAndReturn(run func(context.Context, *ordersvcpb.GetAssignedItemPaidAmountRequest) (*ordersvcpb.GetAssignedItemPaidAmountResponse, error)) *ItemAssignService_GetItemAssignedAmount_Call {
	_c.Call.Return(run)
	return _c
}

// NewItemAssignService creates a new instance of ItemAssignService. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewItemAssignService(t interface {
	mock.TestingT
	Cleanup(func())
}) *ItemAssignService {
	mock := &ItemAssignService{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
