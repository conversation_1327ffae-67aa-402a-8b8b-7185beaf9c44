// Code generated by mockery v2.53.4. DO NOT EDIT.

package mocks

import (
	context "context"

	model "github.com/MoeGolibrary/moego-svc-order-v2/internal/model"
	mock "github.com/stretchr/testify/mock"
)

// MigrationService is an autogenerated mock type for the MigrationService type
type MigrationService struct {
	mock.Mock
}

type MigrationService_Expecter struct {
	mock *mock.Mock
}

func (_m *MigrationService) EXPECT() *MigrationService_Expecter {
	return &MigrationService_Expecter{mock: &_m.Mock}
}

// MigrateToV4 provides a mock function with given fields: ctx, apptID, orderDetails
func (_m *MigrationService) MigrateToV4(ctx context.Context, apptID int64, orderDetails []*model.OrderDetail) (bool, string, []int64) {
	ret := _m.Called(ctx, apptID, orderDetails)

	if len(ret) == 0 {
		panic("no return value specified for MigrateToV4")
	}

	var r0 bool
	var r1 string
	var r2 []int64
	if rf, ok := ret.Get(0).(func(context.Context, int64, []*model.OrderDetail) (bool, string, []int64)); ok {
		return rf(ctx, apptID, orderDetails)
	}
	if rf, ok := ret.Get(0).(func(context.Context, int64, []*model.OrderDetail) bool); ok {
		r0 = rf(ctx, apptID, orderDetails)
	} else {
		r0 = ret.Get(0).(bool)
	}

	if rf, ok := ret.Get(1).(func(context.Context, int64, []*model.OrderDetail) string); ok {
		r1 = rf(ctx, apptID, orderDetails)
	} else {
		r1 = ret.Get(1).(string)
	}

	if rf, ok := ret.Get(2).(func(context.Context, int64, []*model.OrderDetail) []int64); ok {
		r2 = rf(ctx, apptID, orderDetails)
	} else {
		if ret.Get(2) != nil {
			r2 = ret.Get(2).([]int64)
		}
	}

	return r0, r1, r2
}

// MigrationService_MigrateToV4_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'MigrateToV4'
type MigrationService_MigrateToV4_Call struct {
	*mock.Call
}

// MigrateToV4 is a helper method to define mock.On call
//   - ctx context.Context
//   - apptID int64
//   - orderDetails []*model.OrderDetail
func (_e *MigrationService_Expecter) MigrateToV4(ctx interface{}, apptID interface{}, orderDetails interface{}) *MigrationService_MigrateToV4_Call {
	return &MigrationService_MigrateToV4_Call{Call: _e.mock.On("MigrateToV4", ctx, apptID, orderDetails)}
}

func (_c *MigrationService_MigrateToV4_Call) Run(run func(ctx context.Context, apptID int64, orderDetails []*model.OrderDetail)) *MigrationService_MigrateToV4_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(int64), args[2].([]*model.OrderDetail))
	})
	return _c
}

func (_c *MigrationService_MigrateToV4_Call) Return(_a0 bool, _a1 string, _a2 []int64) *MigrationService_MigrateToV4_Call {
	_c.Call.Return(_a0, _a1, _a2)
	return _c
}

func (_c *MigrationService_MigrateToV4_Call) RunAndReturn(run func(context.Context, int64, []*model.OrderDetail) (bool, string, []int64)) *MigrationService_MigrateToV4_Call {
	_c.Call.Return(run)
	return _c
}

// NewMigrationService creates a new instance of MigrationService. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMigrationService(t interface {
	mock.TestingT
	Cleanup(func())
}) *MigrationService {
	mock := &MigrationService{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
