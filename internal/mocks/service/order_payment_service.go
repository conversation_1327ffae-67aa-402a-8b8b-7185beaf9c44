// Code generated by mockery v2.53.4. DO NOT EDIT.

package mocks

import (
	context "context"

	model "github.com/MoeGolibrary/moego-svc-order-v2/internal/model"
	mock "github.com/stretchr/testify/mock"

	orderpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/order/v1"

	ordersvcpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/order/v2"
)

// OrderPaymentService is an autogenerated mock type for the OrderPaymentService type
type OrderPaymentService struct {
	mock.Mock
}

type OrderPaymentService_Expecter struct {
	mock *mock.Mock
}

func (_m *OrderPaymentService) EXPECT() *OrderPaymentService_Expecter {
	return &OrderPaymentService_Expecter{mock: &_m.Mock}
}

// CombinedPayOrder provides a mock function with given fields: ctx, req
func (_m *OrderPaymentService) CombinedPayOrder(ctx context.Context, req *ordersvcpb.CombinedPayOrderRequest) (int64, []*orderpb.OrderPaymentModel, error) {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for CombinedPayOrder")
	}

	var r0 int64
	var r1 []*orderpb.OrderPaymentModel
	var r2 error
	if rf, ok := ret.Get(0).(func(context.Context, *ordersvcpb.CombinedPayOrderRequest) (int64, []*orderpb.OrderPaymentModel, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *ordersvcpb.CombinedPayOrderRequest) int64); ok {
		r0 = rf(ctx, req)
	} else {
		r0 = ret.Get(0).(int64)
	}

	if rf, ok := ret.Get(1).(func(context.Context, *ordersvcpb.CombinedPayOrderRequest) []*orderpb.OrderPaymentModel); ok {
		r1 = rf(ctx, req)
	} else {
		if ret.Get(1) != nil {
			r1 = ret.Get(1).([]*orderpb.OrderPaymentModel)
		}
	}

	if rf, ok := ret.Get(2).(func(context.Context, *ordersvcpb.CombinedPayOrderRequest) error); ok {
		r2 = rf(ctx, req)
	} else {
		r2 = ret.Error(2)
	}

	return r0, r1, r2
}

// OrderPaymentService_CombinedPayOrder_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CombinedPayOrder'
type OrderPaymentService_CombinedPayOrder_Call struct {
	*mock.Call
}

// CombinedPayOrder is a helper method to define mock.On call
//   - ctx context.Context
//   - req *ordersvcpb.CombinedPayOrderRequest
func (_e *OrderPaymentService_Expecter) CombinedPayOrder(ctx interface{}, req interface{}) *OrderPaymentService_CombinedPayOrder_Call {
	return &OrderPaymentService_CombinedPayOrder_Call{Call: _e.mock.On("CombinedPayOrder", ctx, req)}
}

func (_c *OrderPaymentService_CombinedPayOrder_Call) Run(run func(ctx context.Context, req *ordersvcpb.CombinedPayOrderRequest)) *OrderPaymentService_CombinedPayOrder_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*ordersvcpb.CombinedPayOrderRequest))
	})
	return _c
}

func (_c *OrderPaymentService_CombinedPayOrder_Call) Return(_a0 int64, _a1 []*orderpb.OrderPaymentModel, _a2 error) *OrderPaymentService_CombinedPayOrder_Call {
	_c.Call.Return(_a0, _a1, _a2)
	return _c
}

func (_c *OrderPaymentService_CombinedPayOrder_Call) RunAndReturn(run func(context.Context, *ordersvcpb.CombinedPayOrderRequest) (int64, []*orderpb.OrderPaymentModel, error)) *OrderPaymentService_CombinedPayOrder_Call {
	_c.Call.Return(run)
	return _c
}

// GetOrderGuid provides a mock function with given fields: ctx, req
func (_m *OrderPaymentService) GetOrderGuid(ctx context.Context, req *ordersvcpb.GetOrderGuidRequest) (string, error) {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for GetOrderGuid")
	}

	var r0 string
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *ordersvcpb.GetOrderGuidRequest) (string, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *ordersvcpb.GetOrderGuidRequest) string); ok {
		r0 = rf(ctx, req)
	} else {
		r0 = ret.Get(0).(string)
	}

	if rf, ok := ret.Get(1).(func(context.Context, *ordersvcpb.GetOrderGuidRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// OrderPaymentService_GetOrderGuid_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetOrderGuid'
type OrderPaymentService_GetOrderGuid_Call struct {
	*mock.Call
}

// GetOrderGuid is a helper method to define mock.On call
//   - ctx context.Context
//   - req *ordersvcpb.GetOrderGuidRequest
func (_e *OrderPaymentService_Expecter) GetOrderGuid(ctx interface{}, req interface{}) *OrderPaymentService_GetOrderGuid_Call {
	return &OrderPaymentService_GetOrderGuid_Call{Call: _e.mock.On("GetOrderGuid", ctx, req)}
}

func (_c *OrderPaymentService_GetOrderGuid_Call) Run(run func(ctx context.Context, req *ordersvcpb.GetOrderGuidRequest)) *OrderPaymentService_GetOrderGuid_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*ordersvcpb.GetOrderGuidRequest))
	})
	return _c
}

func (_c *OrderPaymentService_GetOrderGuid_Call) Return(_a0 string, _a1 error) *OrderPaymentService_GetOrderGuid_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *OrderPaymentService_GetOrderGuid_Call) RunAndReturn(run func(context.Context, *ordersvcpb.GetOrderGuidRequest) (string, error)) *OrderPaymentService_GetOrderGuid_Call {
	_c.Call.Return(run)
	return _c
}

// GetOrderIDsByGUID provides a mock function with given fields: ctx, guid
func (_m *OrderPaymentService) GetOrderIDsByGUID(ctx context.Context, guid string) ([]int64, error) {
	ret := _m.Called(ctx, guid)

	if len(ret) == 0 {
		panic("no return value specified for GetOrderIDsByGUID")
	}

	var r0 []int64
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string) ([]int64, error)); ok {
		return rf(ctx, guid)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string) []int64); ok {
		r0 = rf(ctx, guid)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]int64)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = rf(ctx, guid)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// OrderPaymentService_GetOrderIDsByGUID_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetOrderIDsByGUID'
type OrderPaymentService_GetOrderIDsByGUID_Call struct {
	*mock.Call
}

// GetOrderIDsByGUID is a helper method to define mock.On call
//   - ctx context.Context
//   - guid string
func (_e *OrderPaymentService_Expecter) GetOrderIDsByGUID(ctx interface{}, guid interface{}) *OrderPaymentService_GetOrderIDsByGUID_Call {
	return &OrderPaymentService_GetOrderIDsByGUID_Call{Call: _e.mock.On("GetOrderIDsByGUID", ctx, guid)}
}

func (_c *OrderPaymentService_GetOrderIDsByGUID_Call) Run(run func(ctx context.Context, guid string)) *OrderPaymentService_GetOrderIDsByGUID_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string))
	})
	return _c
}

func (_c *OrderPaymentService_GetOrderIDsByGUID_Call) Return(_a0 []int64, _a1 error) *OrderPaymentService_GetOrderIDsByGUID_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *OrderPaymentService_GetOrderIDsByGUID_Call) RunAndReturn(run func(context.Context, string) ([]int64, error)) *OrderPaymentService_GetOrderIDsByGUID_Call {
	_c.Call.Return(run)
	return _c
}

// ListOrdersForCombinedPayment provides a mock function with given fields: ctx, req
func (_m *OrderPaymentService) ListOrdersForCombinedPayment(ctx context.Context, req *ordersvcpb.ListOrdersForCombinedPaymentRequest) ([]*model.Order, error) {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for ListOrdersForCombinedPayment")
	}

	var r0 []*model.Order
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *ordersvcpb.ListOrdersForCombinedPaymentRequest) ([]*model.Order, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *ordersvcpb.ListOrdersForCombinedPaymentRequest) []*model.Order); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*model.Order)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *ordersvcpb.ListOrdersForCombinedPaymentRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// OrderPaymentService_ListOrdersForCombinedPayment_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ListOrdersForCombinedPayment'
type OrderPaymentService_ListOrdersForCombinedPayment_Call struct {
	*mock.Call
}

// ListOrdersForCombinedPayment is a helper method to define mock.On call
//   - ctx context.Context
//   - req *ordersvcpb.ListOrdersForCombinedPaymentRequest
func (_e *OrderPaymentService_Expecter) ListOrdersForCombinedPayment(ctx interface{}, req interface{}) *OrderPaymentService_ListOrdersForCombinedPayment_Call {
	return &OrderPaymentService_ListOrdersForCombinedPayment_Call{Call: _e.mock.On("ListOrdersForCombinedPayment", ctx, req)}
}

func (_c *OrderPaymentService_ListOrdersForCombinedPayment_Call) Run(run func(ctx context.Context, req *ordersvcpb.ListOrdersForCombinedPaymentRequest)) *OrderPaymentService_ListOrdersForCombinedPayment_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*ordersvcpb.ListOrdersForCombinedPaymentRequest))
	})
	return _c
}

func (_c *OrderPaymentService_ListOrdersForCombinedPayment_Call) Return(_a0 []*model.Order, _a1 error) *OrderPaymentService_ListOrdersForCombinedPayment_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *OrderPaymentService_ListOrdersForCombinedPayment_Call) RunAndReturn(run func(context.Context, *ordersvcpb.ListOrdersForCombinedPaymentRequest) ([]*model.Order, error)) *OrderPaymentService_ListOrdersForCombinedPayment_Call {
	_c.Call.Return(run)
	return _c
}

// PayOrder provides a mock function with given fields: ctx, req
func (_m *OrderPaymentService) PayOrder(ctx context.Context, req *ordersvcpb.PayOrderRequest) (*model.OrderPayment, error) {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for PayOrder")
	}

	var r0 *model.OrderPayment
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *ordersvcpb.PayOrderRequest) (*model.OrderPayment, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *ordersvcpb.PayOrderRequest) *model.OrderPayment); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*model.OrderPayment)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *ordersvcpb.PayOrderRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// OrderPaymentService_PayOrder_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'PayOrder'
type OrderPaymentService_PayOrder_Call struct {
	*mock.Call
}

// PayOrder is a helper method to define mock.On call
//   - ctx context.Context
//   - req *ordersvcpb.PayOrderRequest
func (_e *OrderPaymentService_Expecter) PayOrder(ctx interface{}, req interface{}) *OrderPaymentService_PayOrder_Call {
	return &OrderPaymentService_PayOrder_Call{Call: _e.mock.On("PayOrder", ctx, req)}
}

func (_c *OrderPaymentService_PayOrder_Call) Run(run func(ctx context.Context, req *ordersvcpb.PayOrderRequest)) *OrderPaymentService_PayOrder_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*ordersvcpb.PayOrderRequest))
	})
	return _c
}

func (_c *OrderPaymentService_PayOrder_Call) Return(_a0 *model.OrderPayment, _a1 error) *OrderPaymentService_PayOrder_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *OrderPaymentService_PayOrder_Call) RunAndReturn(run func(context.Context, *ordersvcpb.PayOrderRequest) (*model.OrderPayment, error)) *OrderPaymentService_PayOrder_Call {
	_c.Call.Return(run)
	return _c
}

// NewOrderPaymentService creates a new instance of OrderPaymentService. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewOrderPaymentService(t interface {
	mock.TestingT
	Cleanup(func())
}) *OrderPaymentService {
	mock := &OrderPaymentService{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
