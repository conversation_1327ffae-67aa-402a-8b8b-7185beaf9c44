// Code generated by mockery v2.53.4. DO NOT EDIT.

package mocks

import (
	context "context"

	model "github.com/MoeGolibrary/moego-svc-order-v2/internal/model"
	mock "github.com/stretchr/testify/mock"

	orderpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/order/v1"

	ordersvcpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/order/v1"

	v2 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/order/v2"
)

// OrderService is an autogenerated mock type for the OrderService type
type OrderService struct {
	mock.Mock
}

type OrderService_Expecter struct {
	mock *mock.Mock
}

func (_m *OrderService) EXPECT() *OrderService_Expecter {
	return &OrderService_Expecter{mock: &_m.Mock}
}

// AttachDetail provides a mock function with given fields: ctx, order
func (_m *OrderService) AttachDetail(ctx context.Context, order *model.Order) (*model.OrderDetail, error) {
	ret := _m.Called(ctx, order)

	if len(ret) == 0 {
		panic("no return value specified for AttachDetail")
	}

	var r0 *model.OrderDetail
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *model.Order) (*model.OrderDetail, error)); ok {
		return rf(ctx, order)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *model.Order) *model.OrderDetail); ok {
		r0 = rf(ctx, order)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*model.OrderDetail)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *model.Order) error); ok {
		r1 = rf(ctx, order)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// OrderService_AttachDetail_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'AttachDetail'
type OrderService_AttachDetail_Call struct {
	*mock.Call
}

// AttachDetail is a helper method to define mock.On call
//   - ctx context.Context
//   - order *model.Order
func (_e *OrderService_Expecter) AttachDetail(ctx interface{}, order interface{}) *OrderService_AttachDetail_Call {
	return &OrderService_AttachDetail_Call{Call: _e.mock.On("AttachDetail", ctx, order)}
}

func (_c *OrderService_AttachDetail_Call) Run(run func(ctx context.Context, order *model.Order)) *OrderService_AttachDetail_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*model.Order))
	})
	return _c
}

func (_c *OrderService_AttachDetail_Call) Return(_a0 *model.OrderDetail, _a1 error) *OrderService_AttachDetail_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *OrderService_AttachDetail_Call) RunAndReturn(run func(context.Context, *model.Order) (*model.OrderDetail, error)) *OrderService_AttachDetail_Call {
	_c.Call.Return(run)
	return _c
}

// BatchGetOrders provides a mock function with given fields: ctx, orderID
func (_m *OrderService) BatchGetOrders(ctx context.Context, orderID []int64) ([]*model.Order, error) {
	ret := _m.Called(ctx, orderID)

	if len(ret) == 0 {
		panic("no return value specified for BatchGetOrders")
	}

	var r0 []*model.Order
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, []int64) ([]*model.Order, error)); ok {
		return rf(ctx, orderID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, []int64) []*model.Order); ok {
		r0 = rf(ctx, orderID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*model.Order)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, []int64) error); ok {
		r1 = rf(ctx, orderID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// OrderService_BatchGetOrders_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'BatchGetOrders'
type OrderService_BatchGetOrders_Call struct {
	*mock.Call
}

// BatchGetOrders is a helper method to define mock.On call
//   - ctx context.Context
//   - orderID []int64
func (_e *OrderService_Expecter) BatchGetOrders(ctx interface{}, orderID interface{}) *OrderService_BatchGetOrders_Call {
	return &OrderService_BatchGetOrders_Call{Call: _e.mock.On("BatchGetOrders", ctx, orderID)}
}

func (_c *OrderService_BatchGetOrders_Call) Run(run func(ctx context.Context, orderID []int64)) *OrderService_BatchGetOrders_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].([]int64))
	})
	return _c
}

func (_c *OrderService_BatchGetOrders_Call) Return(_a0 []*model.Order, _a1 error) *OrderService_BatchGetOrders_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *OrderService_BatchGetOrders_Call) RunAndReturn(run func(context.Context, []int64) ([]*model.Order, error)) *OrderService_BatchGetOrders_Call {
	_c.Call.Return(run)
	return _c
}

// CancelOrder provides a mock function with given fields: ctx, orderID, cancelReason
func (_m *OrderService) CancelOrder(ctx context.Context, orderID int64, cancelReason string) error {
	ret := _m.Called(ctx, orderID, cancelReason)

	if len(ret) == 0 {
		panic("no return value specified for CancelOrder")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, int64, string) error); ok {
		r0 = rf(ctx, orderID, cancelReason)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// OrderService_CancelOrder_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CancelOrder'
type OrderService_CancelOrder_Call struct {
	*mock.Call
}

// CancelOrder is a helper method to define mock.On call
//   - ctx context.Context
//   - orderID int64
//   - cancelReason string
func (_e *OrderService_Expecter) CancelOrder(ctx interface{}, orderID interface{}, cancelReason interface{}) *OrderService_CancelOrder_Call {
	return &OrderService_CancelOrder_Call{Call: _e.mock.On("CancelOrder", ctx, orderID, cancelReason)}
}

func (_c *OrderService_CancelOrder_Call) Run(run func(ctx context.Context, orderID int64, cancelReason string)) *OrderService_CancelOrder_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(int64), args[2].(string))
	})
	return _c
}

func (_c *OrderService_CancelOrder_Call) Return(_a0 error) *OrderService_CancelOrder_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *OrderService_CancelOrder_Call) RunAndReturn(run func(context.Context, int64, string) error) *OrderService_CancelOrder_Call {
	_c.Call.Return(run)
	return _c
}

// CreateInvoiceID provides a mock function with given fields: ctx, req
func (_m *OrderService) CreateInvoiceID(ctx context.Context, req *ordersvcpb.CreateInvoiceIDRequest) (int64, error) {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for CreateInvoiceID")
	}

	var r0 int64
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *ordersvcpb.CreateInvoiceIDRequest) (int64, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *ordersvcpb.CreateInvoiceIDRequest) int64); ok {
		r0 = rf(ctx, req)
	} else {
		r0 = ret.Get(0).(int64)
	}

	if rf, ok := ret.Get(1).(func(context.Context, *ordersvcpb.CreateInvoiceIDRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// OrderService_CreateInvoiceID_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreateInvoiceID'
type OrderService_CreateInvoiceID_Call struct {
	*mock.Call
}

// CreateInvoiceID is a helper method to define mock.On call
//   - ctx context.Context
//   - req *ordersvcpb.CreateInvoiceIDRequest
func (_e *OrderService_Expecter) CreateInvoiceID(ctx interface{}, req interface{}) *OrderService_CreateInvoiceID_Call {
	return &OrderService_CreateInvoiceID_Call{Call: _e.mock.On("CreateInvoiceID", ctx, req)}
}

func (_c *OrderService_CreateInvoiceID_Call) Run(run func(ctx context.Context, req *ordersvcpb.CreateInvoiceIDRequest)) *OrderService_CreateInvoiceID_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*ordersvcpb.CreateInvoiceIDRequest))
	})
	return _c
}

func (_c *OrderService_CreateInvoiceID_Call) Return(_a0 int64, _a1 error) *OrderService_CreateInvoiceID_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *OrderService_CreateInvoiceID_Call) RunAndReturn(run func(context.Context, *ordersvcpb.CreateInvoiceIDRequest) (int64, error)) *OrderService_CreateInvoiceID_Call {
	_c.Call.Return(run)
	return _c
}

// CreateNoShowOrder provides a mock function with given fields: ctx, req
func (_m *OrderService) CreateNoShowOrder(ctx context.Context, req *ordersvcpb.CreateNoShowOrderRequest) (*model.OrderDetail, error) {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for CreateNoShowOrder")
	}

	var r0 *model.OrderDetail
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *ordersvcpb.CreateNoShowOrderRequest) (*model.OrderDetail, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *ordersvcpb.CreateNoShowOrderRequest) *model.OrderDetail); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*model.OrderDetail)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *ordersvcpb.CreateNoShowOrderRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// OrderService_CreateNoShowOrder_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreateNoShowOrder'
type OrderService_CreateNoShowOrder_Call struct {
	*mock.Call
}

// CreateNoShowOrder is a helper method to define mock.On call
//   - ctx context.Context
//   - req *ordersvcpb.CreateNoShowOrderRequest
func (_e *OrderService_Expecter) CreateNoShowOrder(ctx interface{}, req interface{}) *OrderService_CreateNoShowOrder_Call {
	return &OrderService_CreateNoShowOrder_Call{Call: _e.mock.On("CreateNoShowOrder", ctx, req)}
}

func (_c *OrderService_CreateNoShowOrder_Call) Run(run func(ctx context.Context, req *ordersvcpb.CreateNoShowOrderRequest)) *OrderService_CreateNoShowOrder_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*ordersvcpb.CreateNoShowOrderRequest))
	})
	return _c
}

func (_c *OrderService_CreateNoShowOrder_Call) Return(_a0 *model.OrderDetail, _a1 error) *OrderService_CreateNoShowOrder_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *OrderService_CreateNoShowOrder_Call) RunAndReturn(run func(context.Context, *ordersvcpb.CreateNoShowOrderRequest) (*model.OrderDetail, error)) *OrderService_CreateNoShowOrder_Call {
	_c.Call.Return(run)
	return _c
}

// CreateOrder provides a mock function with given fields: ctx, orderDetail
func (_m *OrderService) CreateOrder(ctx context.Context, orderDetail *model.OrderDetail) (*model.OrderDetail, error) {
	ret := _m.Called(ctx, orderDetail)

	if len(ret) == 0 {
		panic("no return value specified for CreateOrder")
	}

	var r0 *model.OrderDetail
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *model.OrderDetail) (*model.OrderDetail, error)); ok {
		return rf(ctx, orderDetail)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *model.OrderDetail) *model.OrderDetail); ok {
		r0 = rf(ctx, orderDetail)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*model.OrderDetail)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *model.OrderDetail) error); ok {
		r1 = rf(ctx, orderDetail)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// OrderService_CreateOrder_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreateOrder'
type OrderService_CreateOrder_Call struct {
	*mock.Call
}

// CreateOrder is a helper method to define mock.On call
//   - ctx context.Context
//   - orderDetail *model.OrderDetail
func (_e *OrderService_Expecter) CreateOrder(ctx interface{}, orderDetail interface{}) *OrderService_CreateOrder_Call {
	return &OrderService_CreateOrder_Call{Call: _e.mock.On("CreateOrder", ctx, orderDetail)}
}

func (_c *OrderService_CreateOrder_Call) Run(run func(ctx context.Context, orderDetail *model.OrderDetail)) *OrderService_CreateOrder_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*model.OrderDetail))
	})
	return _c
}

func (_c *OrderService_CreateOrder_Call) Return(_a0 *model.OrderDetail, _a1 error) *OrderService_CreateOrder_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *OrderService_CreateOrder_Call) RunAndReturn(run func(context.Context, *model.OrderDetail) (*model.OrderDetail, error)) *OrderService_CreateOrder_Call {
	_c.Call.Return(run)
	return _c
}

// CreateTipOrder provides a mock function with given fields: ctx, req
func (_m *OrderService) CreateTipOrder(ctx context.Context, req *ordersvcpb.CreateTipOrderRequest) (*model.OrderDetail, error) {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for CreateTipOrder")
	}

	var r0 *model.OrderDetail
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *ordersvcpb.CreateTipOrderRequest) (*model.OrderDetail, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *ordersvcpb.CreateTipOrderRequest) *model.OrderDetail); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*model.OrderDetail)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *ordersvcpb.CreateTipOrderRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// OrderService_CreateTipOrder_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreateTipOrder'
type OrderService_CreateTipOrder_Call struct {
	*mock.Call
}

// CreateTipOrder is a helper method to define mock.On call
//   - ctx context.Context
//   - req *ordersvcpb.CreateTipOrderRequest
func (_e *OrderService_Expecter) CreateTipOrder(ctx interface{}, req interface{}) *OrderService_CreateTipOrder_Call {
	return &OrderService_CreateTipOrder_Call{Call: _e.mock.On("CreateTipOrder", ctx, req)}
}

func (_c *OrderService_CreateTipOrder_Call) Run(run func(ctx context.Context, req *ordersvcpb.CreateTipOrderRequest)) *OrderService_CreateTipOrder_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*ordersvcpb.CreateTipOrderRequest))
	})
	return _c
}

func (_c *OrderService_CreateTipOrder_Call) Return(_a0 *model.OrderDetail, _a1 error) *OrderService_CreateTipOrder_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *OrderService_CreateTipOrder_Call) RunAndReturn(run func(context.Context, *ordersvcpb.CreateTipOrderRequest) (*model.OrderDetail, error)) *OrderService_CreateTipOrder_Call {
	_c.Call.Return(run)
	return _c
}

// ExportOrderPaymentDetailList provides a mock function with given fields: ctx, req
func (_m *OrderService) ExportOrderPaymentDetailList(ctx context.Context, req *v2.ExportOrderPaymentDetailListRequest) (int64, error) {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for ExportOrderPaymentDetailList")
	}

	var r0 int64
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *v2.ExportOrderPaymentDetailListRequest) (int64, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *v2.ExportOrderPaymentDetailListRequest) int64); ok {
		r0 = rf(ctx, req)
	} else {
		r0 = ret.Get(0).(int64)
	}

	if rf, ok := ret.Get(1).(func(context.Context, *v2.ExportOrderPaymentDetailListRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// OrderService_ExportOrderPaymentDetailList_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ExportOrderPaymentDetailList'
type OrderService_ExportOrderPaymentDetailList_Call struct {
	*mock.Call
}

// ExportOrderPaymentDetailList is a helper method to define mock.On call
//   - ctx context.Context
//   - req *v2.ExportOrderPaymentDetailListRequest
func (_e *OrderService_Expecter) ExportOrderPaymentDetailList(ctx interface{}, req interface{}) *OrderService_ExportOrderPaymentDetailList_Call {
	return &OrderService_ExportOrderPaymentDetailList_Call{Call: _e.mock.On("ExportOrderPaymentDetailList", ctx, req)}
}

func (_c *OrderService_ExportOrderPaymentDetailList_Call) Run(run func(ctx context.Context, req *v2.ExportOrderPaymentDetailListRequest)) *OrderService_ExportOrderPaymentDetailList_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*v2.ExportOrderPaymentDetailListRequest))
	})
	return _c
}

func (_c *OrderService_ExportOrderPaymentDetailList_Call) Return(_a0 int64, _a1 error) *OrderService_ExportOrderPaymentDetailList_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *OrderService_ExportOrderPaymentDetailList_Call) RunAndReturn(run func(context.Context, *v2.ExportOrderPaymentDetailListRequest) (int64, error)) *OrderService_ExportOrderPaymentDetailList_Call {
	_c.Call.Return(run)
	return _c
}

// GetDetail provides a mock function with given fields: ctx, orderID
func (_m *OrderService) GetDetail(ctx context.Context, orderID int64) (*model.OrderDetail, error) {
	ret := _m.Called(ctx, orderID)

	if len(ret) == 0 {
		panic("no return value specified for GetDetail")
	}

	var r0 *model.OrderDetail
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, int64) (*model.OrderDetail, error)); ok {
		return rf(ctx, orderID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, int64) *model.OrderDetail); ok {
		r0 = rf(ctx, orderID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*model.OrderDetail)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, int64) error); ok {
		r1 = rf(ctx, orderID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// OrderService_GetDetail_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetDetail'
type OrderService_GetDetail_Call struct {
	*mock.Call
}

// GetDetail is a helper method to define mock.On call
//   - ctx context.Context
//   - orderID int64
func (_e *OrderService_Expecter) GetDetail(ctx interface{}, orderID interface{}) *OrderService_GetDetail_Call {
	return &OrderService_GetDetail_Call{Call: _e.mock.On("GetDetail", ctx, orderID)}
}

func (_c *OrderService_GetDetail_Call) Run(run func(ctx context.Context, orderID int64)) *OrderService_GetDetail_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(int64))
	})
	return _c
}

func (_c *OrderService_GetDetail_Call) Return(_a0 *model.OrderDetail, _a1 error) *OrderService_GetDetail_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *OrderService_GetDetail_Call) RunAndReturn(run func(context.Context, int64) (*model.OrderDetail, error)) *OrderService_GetDetail_Call {
	_c.Call.Return(run)
	return _c
}

// GetOrder provides a mock function with given fields: ctx, orderID
func (_m *OrderService) GetOrder(ctx context.Context, orderID int64) (*model.Order, error) {
	ret := _m.Called(ctx, orderID)

	if len(ret) == 0 {
		panic("no return value specified for GetOrder")
	}

	var r0 *model.Order
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, int64) (*model.Order, error)); ok {
		return rf(ctx, orderID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, int64) *model.Order); ok {
		r0 = rf(ctx, orderID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*model.Order)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, int64) error); ok {
		r1 = rf(ctx, orderID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// OrderService_GetOrder_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetOrder'
type OrderService_GetOrder_Call struct {
	*mock.Call
}

// GetOrder is a helper method to define mock.On call
//   - ctx context.Context
//   - orderID int64
func (_e *OrderService_Expecter) GetOrder(ctx interface{}, orderID interface{}) *OrderService_GetOrder_Call {
	return &OrderService_GetOrder_Call{Call: _e.mock.On("GetOrder", ctx, orderID)}
}

func (_c *OrderService_GetOrder_Call) Run(run func(ctx context.Context, orderID int64)) *OrderService_GetOrder_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(int64))
	})
	return _c
}

func (_c *OrderService_GetOrder_Call) Return(_a0 *model.Order, _a1 error) *OrderService_GetOrder_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *OrderService_GetOrder_Call) RunAndReturn(run func(context.Context, int64) (*model.Order, error)) *OrderService_GetOrder_Call {
	_c.Call.Return(run)
	return _c
}

// GetRootOrderBySource provides a mock function with given fields: ctx, sourceType, sourceID
func (_m *OrderService) GetRootOrderBySource(ctx context.Context, sourceType orderpb.OrderSourceType, sourceID int64) (*model.Order, error) {
	ret := _m.Called(ctx, sourceType, sourceID)

	if len(ret) == 0 {
		panic("no return value specified for GetRootOrderBySource")
	}

	var r0 *model.Order
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, orderpb.OrderSourceType, int64) (*model.Order, error)); ok {
		return rf(ctx, sourceType, sourceID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, orderpb.OrderSourceType, int64) *model.Order); ok {
		r0 = rf(ctx, sourceType, sourceID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*model.Order)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, orderpb.OrderSourceType, int64) error); ok {
		r1 = rf(ctx, sourceType, sourceID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// OrderService_GetRootOrderBySource_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetRootOrderBySource'
type OrderService_GetRootOrderBySource_Call struct {
	*mock.Call
}

// GetRootOrderBySource is a helper method to define mock.On call
//   - ctx context.Context
//   - sourceType orderpb.OrderSourceType
//   - sourceID int64
func (_e *OrderService_Expecter) GetRootOrderBySource(ctx interface{}, sourceType interface{}, sourceID interface{}) *OrderService_GetRootOrderBySource_Call {
	return &OrderService_GetRootOrderBySource_Call{Call: _e.mock.On("GetRootOrderBySource", ctx, sourceType, sourceID)}
}

func (_c *OrderService_GetRootOrderBySource_Call) Run(run func(ctx context.Context, sourceType orderpb.OrderSourceType, sourceID int64)) *OrderService_GetRootOrderBySource_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(orderpb.OrderSourceType), args[2].(int64))
	})
	return _c
}

func (_c *OrderService_GetRootOrderBySource_Call) Return(_a0 *model.Order, _a1 error) *OrderService_GetRootOrderBySource_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *OrderService_GetRootOrderBySource_Call) RunAndReturn(run func(context.Context, orderpb.OrderSourceType, int64) (*model.Order, error)) *OrderService_GetRootOrderBySource_Call {
	_c.Call.Return(run)
	return _c
}

// ListByAppointment provides a mock function with given fields: ctx, appointmentID
func (_m *OrderService) ListByAppointment(ctx context.Context, appointmentID int64) ([]*model.Order, error) {
	ret := _m.Called(ctx, appointmentID)

	if len(ret) == 0 {
		panic("no return value specified for ListByAppointment")
	}

	var r0 []*model.Order
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, int64) ([]*model.Order, error)); ok {
		return rf(ctx, appointmentID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, int64) []*model.Order); ok {
		r0 = rf(ctx, appointmentID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*model.Order)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, int64) error); ok {
		r1 = rf(ctx, appointmentID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// OrderService_ListByAppointment_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ListByAppointment'
type OrderService_ListByAppointment_Call struct {
	*mock.Call
}

// ListByAppointment is a helper method to define mock.On call
//   - ctx context.Context
//   - appointmentID int64
func (_e *OrderService_Expecter) ListByAppointment(ctx interface{}, appointmentID interface{}) *OrderService_ListByAppointment_Call {
	return &OrderService_ListByAppointment_Call{Call: _e.mock.On("ListByAppointment", ctx, appointmentID)}
}

func (_c *OrderService_ListByAppointment_Call) Run(run func(ctx context.Context, appointmentID int64)) *OrderService_ListByAppointment_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(int64))
	})
	return _c
}

func (_c *OrderService_ListByAppointment_Call) Return(_a0 []*model.Order, _a1 error) *OrderService_ListByAppointment_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *OrderService_ListByAppointment_Call) RunAndReturn(run func(context.Context, int64) ([]*model.Order, error)) *OrderService_ListByAppointment_Call {
	_c.Call.Return(run)
	return _c
}

// ListDetailByAppointment provides a mock function with given fields: ctx, appointmentID
func (_m *OrderService) ListDetailByAppointment(ctx context.Context, appointmentID int64) ([]*model.OrderDetail, error) {
	ret := _m.Called(ctx, appointmentID)

	if len(ret) == 0 {
		panic("no return value specified for ListDetailByAppointment")
	}

	var r0 []*model.OrderDetail
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, int64) ([]*model.OrderDetail, error)); ok {
		return rf(ctx, appointmentID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, int64) []*model.OrderDetail); ok {
		r0 = rf(ctx, appointmentID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*model.OrderDetail)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, int64) error); ok {
		r1 = rf(ctx, appointmentID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// OrderService_ListDetailByAppointment_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ListDetailByAppointment'
type OrderService_ListDetailByAppointment_Call struct {
	*mock.Call
}

// ListDetailByAppointment is a helper method to define mock.On call
//   - ctx context.Context
//   - appointmentID int64
func (_e *OrderService_Expecter) ListDetailByAppointment(ctx interface{}, appointmentID interface{}) *OrderService_ListDetailByAppointment_Call {
	return &OrderService_ListDetailByAppointment_Call{Call: _e.mock.On("ListDetailByAppointment", ctx, appointmentID)}
}

func (_c *OrderService_ListDetailByAppointment_Call) Run(run func(ctx context.Context, appointmentID int64)) *OrderService_ListDetailByAppointment_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(int64))
	})
	return _c
}

func (_c *OrderService_ListDetailByAppointment_Call) Return(_a0 []*model.OrderDetail, _a1 error) *OrderService_ListDetailByAppointment_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *OrderService_ListDetailByAppointment_Call) RunAndReturn(run func(context.Context, int64) ([]*model.OrderDetail, error)) *OrderService_ListDetailByAppointment_Call {
	_c.Call.Return(run)
	return _c
}

// ListDetailBySource provides a mock function with given fields: ctx, sourceType, sourceID
func (_m *OrderService) ListDetailBySource(ctx context.Context, sourceType orderpb.OrderSourceType, sourceID int64) ([]*model.OrderDetail, error) {
	ret := _m.Called(ctx, sourceType, sourceID)

	if len(ret) == 0 {
		panic("no return value specified for ListDetailBySource")
	}

	var r0 []*model.OrderDetail
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, orderpb.OrderSourceType, int64) ([]*model.OrderDetail, error)); ok {
		return rf(ctx, sourceType, sourceID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, orderpb.OrderSourceType, int64) []*model.OrderDetail); ok {
		r0 = rf(ctx, sourceType, sourceID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*model.OrderDetail)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, orderpb.OrderSourceType, int64) error); ok {
		r1 = rf(ctx, sourceType, sourceID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// OrderService_ListDetailBySource_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ListDetailBySource'
type OrderService_ListDetailBySource_Call struct {
	*mock.Call
}

// ListDetailBySource is a helper method to define mock.On call
//   - ctx context.Context
//   - sourceType orderpb.OrderSourceType
//   - sourceID int64
func (_e *OrderService_Expecter) ListDetailBySource(ctx interface{}, sourceType interface{}, sourceID interface{}) *OrderService_ListDetailBySource_Call {
	return &OrderService_ListDetailBySource_Call{Call: _e.mock.On("ListDetailBySource", ctx, sourceType, sourceID)}
}

func (_c *OrderService_ListDetailBySource_Call) Run(run func(ctx context.Context, sourceType orderpb.OrderSourceType, sourceID int64)) *OrderService_ListDetailBySource_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(orderpb.OrderSourceType), args[2].(int64))
	})
	return _c
}

func (_c *OrderService_ListDetailBySource_Call) Return(_a0 []*model.OrderDetail, _a1 error) *OrderService_ListDetailBySource_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *OrderService_ListDetailBySource_Call) RunAndReturn(run func(context.Context, orderpb.OrderSourceType, int64) ([]*model.OrderDetail, error)) *OrderService_ListDetailBySource_Call {
	_c.Call.Return(run)
	return _c
}

// ListOrderPaymentDetail provides a mock function with given fields: ctx, req
func (_m *OrderService) ListOrderPaymentDetail(ctx context.Context, req *v2.ListOrderPaymentDetailRequest) (*v2.ListOrderPaymentDetailResponse, error) {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for ListOrderPaymentDetail")
	}

	var r0 *v2.ListOrderPaymentDetailResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *v2.ListOrderPaymentDetailRequest) (*v2.ListOrderPaymentDetailResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *v2.ListOrderPaymentDetailRequest) *v2.ListOrderPaymentDetailResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*v2.ListOrderPaymentDetailResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *v2.ListOrderPaymentDetailRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// OrderService_ListOrderPaymentDetail_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ListOrderPaymentDetail'
type OrderService_ListOrderPaymentDetail_Call struct {
	*mock.Call
}

// ListOrderPaymentDetail is a helper method to define mock.On call
//   - ctx context.Context
//   - req *v2.ListOrderPaymentDetailRequest
func (_e *OrderService_Expecter) ListOrderPaymentDetail(ctx interface{}, req interface{}) *OrderService_ListOrderPaymentDetail_Call {
	return &OrderService_ListOrderPaymentDetail_Call{Call: _e.mock.On("ListOrderPaymentDetail", ctx, req)}
}

func (_c *OrderService_ListOrderPaymentDetail_Call) Run(run func(ctx context.Context, req *v2.ListOrderPaymentDetailRequest)) *OrderService_ListOrderPaymentDetail_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*v2.ListOrderPaymentDetailRequest))
	})
	return _c
}

func (_c *OrderService_ListOrderPaymentDetail_Call) Return(_a0 *v2.ListOrderPaymentDetailResponse, _a1 error) *OrderService_ListOrderPaymentDetail_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *OrderService_ListOrderPaymentDetail_Call) RunAndReturn(run func(context.Context, *v2.ListOrderPaymentDetailRequest) (*v2.ListOrderPaymentDetailResponse, error)) *OrderService_ListOrderPaymentDetail_Call {
	_c.Call.Return(run)
	return _c
}

// ListTail provides a mock function with given fields: ctx, orderID
func (_m *OrderService) ListTail(ctx context.Context, orderID int64) ([]*model.Order, error) {
	ret := _m.Called(ctx, orderID)

	if len(ret) == 0 {
		panic("no return value specified for ListTail")
	}

	var r0 []*model.Order
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, int64) ([]*model.Order, error)); ok {
		return rf(ctx, orderID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, int64) []*model.Order); ok {
		r0 = rf(ctx, orderID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*model.Order)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, int64) error); ok {
		r1 = rf(ctx, orderID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// OrderService_ListTail_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ListTail'
type OrderService_ListTail_Call struct {
	*mock.Call
}

// ListTail is a helper method to define mock.On call
//   - ctx context.Context
//   - orderID int64
func (_e *OrderService_Expecter) ListTail(ctx interface{}, orderID interface{}) *OrderService_ListTail_Call {
	return &OrderService_ListTail_Call{Call: _e.mock.On("ListTail", ctx, orderID)}
}

func (_c *OrderService_ListTail_Call) Run(run func(ctx context.Context, orderID int64)) *OrderService_ListTail_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(int64))
	})
	return _c
}

func (_c *OrderService_ListTail_Call) Return(_a0 []*model.Order, _a1 error) *OrderService_ListTail_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *OrderService_ListTail_Call) RunAndReturn(run func(context.Context, int64) ([]*model.Order, error)) *OrderService_ListTail_Call {
	_c.Call.Return(run)
	return _c
}

// ListTailDetail provides a mock function with given fields: ctx, orderID
func (_m *OrderService) ListTailDetail(ctx context.Context, orderID int64) ([]*model.OrderDetail, error) {
	ret := _m.Called(ctx, orderID)

	if len(ret) == 0 {
		panic("no return value specified for ListTailDetail")
	}

	var r0 []*model.OrderDetail
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, int64) ([]*model.OrderDetail, error)); ok {
		return rf(ctx, orderID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, int64) []*model.OrderDetail); ok {
		r0 = rf(ctx, orderID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*model.OrderDetail)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, int64) error); ok {
		r1 = rf(ctx, orderID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// OrderService_ListTailDetail_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ListTailDetail'
type OrderService_ListTailDetail_Call struct {
	*mock.Call
}

// ListTailDetail is a helper method to define mock.On call
//   - ctx context.Context
//   - orderID int64
func (_e *OrderService_Expecter) ListTailDetail(ctx interface{}, orderID interface{}) *OrderService_ListTailDetail_Call {
	return &OrderService_ListTailDetail_Call{Call: _e.mock.On("ListTailDetail", ctx, orderID)}
}

func (_c *OrderService_ListTailDetail_Call) Run(run func(ctx context.Context, orderID int64)) *OrderService_ListTailDetail_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(int64))
	})
	return _c
}

func (_c *OrderService_ListTailDetail_Call) Return(_a0 []*model.OrderDetail, _a1 error) *OrderService_ListTailDetail_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *OrderService_ListTailDetail_Call) RunAndReturn(run func(context.Context, int64) ([]*model.OrderDetail, error)) *OrderService_ListTailDetail_Call {
	_c.Call.Return(run)
	return _c
}

// NewOrderService creates a new instance of OrderService. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewOrderService(t interface {
	mock.TestingT
	Cleanup(func())
}) *OrderService {
	mock := &OrderService{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
