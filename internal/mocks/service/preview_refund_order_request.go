// Code generated by mockery v2.53.4. DO NOT EDIT.

package mocks

import (
	orderpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/order/v1"
	ordersvcpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/order/v1"
	mock "github.com/stretchr/testify/mock"
)

// PreviewRefundOrderRequest is an autogenerated mock type for the PreviewRefundOrderRequest type
type PreviewRefundOrderRequest struct {
	mock.Mock
}

type PreviewRefundOrderRequest_Expecter struct {
	mock *mock.Mock
}

func (_m *PreviewRefundOrderRequest) EXPECT() *PreviewRefundOrderRequest_Expecter {
	return &PreviewRefundOrderRequest_Expecter{mock: &_m.Mock}
}

// GetRefundByItem provides a mock function with no fields
func (_m *PreviewRefundOrderRequest) GetRefundByItem() *ordersvcpb.RefundOrderRequest_RefundByItem {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for GetRefundByItem")
	}

	var r0 *ordersvcpb.RefundOrderRequest_RefundByItem
	if rf, ok := ret.Get(0).(func() *ordersvcpb.RefundOrderRequest_RefundByItem); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*ordersvcpb.RefundOrderRequest_RefundByItem)
		}
	}

	return r0
}

// PreviewRefundOrderRequest_GetRefundByItem_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetRefundByItem'
type PreviewRefundOrderRequest_GetRefundByItem_Call struct {
	*mock.Call
}

// GetRefundByItem is a helper method to define mock.On call
func (_e *PreviewRefundOrderRequest_Expecter) GetRefundByItem() *PreviewRefundOrderRequest_GetRefundByItem_Call {
	return &PreviewRefundOrderRequest_GetRefundByItem_Call{Call: _e.mock.On("GetRefundByItem")}
}

func (_c *PreviewRefundOrderRequest_GetRefundByItem_Call) Run(run func()) *PreviewRefundOrderRequest_GetRefundByItem_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *PreviewRefundOrderRequest_GetRefundByItem_Call) Return(_a0 *ordersvcpb.RefundOrderRequest_RefundByItem) *PreviewRefundOrderRequest_GetRefundByItem_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *PreviewRefundOrderRequest_GetRefundByItem_Call) RunAndReturn(run func() *ordersvcpb.RefundOrderRequest_RefundByItem) *PreviewRefundOrderRequest_GetRefundByItem_Call {
	_c.Call.Return(run)
	return _c
}

// GetRefundByPayment provides a mock function with no fields
func (_m *PreviewRefundOrderRequest) GetRefundByPayment() *ordersvcpb.RefundOrderRequest_RefundByPayment {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for GetRefundByPayment")
	}

	var r0 *ordersvcpb.RefundOrderRequest_RefundByPayment
	if rf, ok := ret.Get(0).(func() *ordersvcpb.RefundOrderRequest_RefundByPayment); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*ordersvcpb.RefundOrderRequest_RefundByPayment)
		}
	}

	return r0
}

// PreviewRefundOrderRequest_GetRefundByPayment_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetRefundByPayment'
type PreviewRefundOrderRequest_GetRefundByPayment_Call struct {
	*mock.Call
}

// GetRefundByPayment is a helper method to define mock.On call
func (_e *PreviewRefundOrderRequest_Expecter) GetRefundByPayment() *PreviewRefundOrderRequest_GetRefundByPayment_Call {
	return &PreviewRefundOrderRequest_GetRefundByPayment_Call{Call: _e.mock.On("GetRefundByPayment")}
}

func (_c *PreviewRefundOrderRequest_GetRefundByPayment_Call) Run(run func()) *PreviewRefundOrderRequest_GetRefundByPayment_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *PreviewRefundOrderRequest_GetRefundByPayment_Call) Return(_a0 *ordersvcpb.RefundOrderRequest_RefundByPayment) *PreviewRefundOrderRequest_GetRefundByPayment_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *PreviewRefundOrderRequest_GetRefundByPayment_Call) RunAndReturn(run func() *ordersvcpb.RefundOrderRequest_RefundByPayment) *PreviewRefundOrderRequest_GetRefundByPayment_Call {
	_c.Call.Return(run)
	return _c
}

// GetRefundMode provides a mock function with no fields
func (_m *PreviewRefundOrderRequest) GetRefundMode() orderpb.RefundMode {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for GetRefundMode")
	}

	var r0 orderpb.RefundMode
	if rf, ok := ret.Get(0).(func() orderpb.RefundMode); ok {
		r0 = rf()
	} else {
		r0 = ret.Get(0).(orderpb.RefundMode)
	}

	return r0
}

// PreviewRefundOrderRequest_GetRefundMode_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetRefundMode'
type PreviewRefundOrderRequest_GetRefundMode_Call struct {
	*mock.Call
}

// GetRefundMode is a helper method to define mock.On call
func (_e *PreviewRefundOrderRequest_Expecter) GetRefundMode() *PreviewRefundOrderRequest_GetRefundMode_Call {
	return &PreviewRefundOrderRequest_GetRefundMode_Call{Call: _e.mock.On("GetRefundMode")}
}

func (_c *PreviewRefundOrderRequest_GetRefundMode_Call) Run(run func()) *PreviewRefundOrderRequest_GetRefundMode_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *PreviewRefundOrderRequest_GetRefundMode_Call) Return(_a0 orderpb.RefundMode) *PreviewRefundOrderRequest_GetRefundMode_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *PreviewRefundOrderRequest_GetRefundMode_Call) RunAndReturn(run func() orderpb.RefundMode) *PreviewRefundOrderRequest_GetRefundMode_Call {
	_c.Call.Return(run)
	return _c
}

// GetSourceOrderPayments provides a mock function with no fields
func (_m *PreviewRefundOrderRequest) GetSourceOrderPayments() []*ordersvcpb.RefundOrderRequest_OrderPayment {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for GetSourceOrderPayments")
	}

	var r0 []*ordersvcpb.RefundOrderRequest_OrderPayment
	if rf, ok := ret.Get(0).(func() []*ordersvcpb.RefundOrderRequest_OrderPayment); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*ordersvcpb.RefundOrderRequest_OrderPayment)
		}
	}

	return r0
}

// PreviewRefundOrderRequest_GetSourceOrderPayments_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetSourceOrderPayments'
type PreviewRefundOrderRequest_GetSourceOrderPayments_Call struct {
	*mock.Call
}

// GetSourceOrderPayments is a helper method to define mock.On call
func (_e *PreviewRefundOrderRequest_Expecter) GetSourceOrderPayments() *PreviewRefundOrderRequest_GetSourceOrderPayments_Call {
	return &PreviewRefundOrderRequest_GetSourceOrderPayments_Call{Call: _e.mock.On("GetSourceOrderPayments")}
}

func (_c *PreviewRefundOrderRequest_GetSourceOrderPayments_Call) Run(run func()) *PreviewRefundOrderRequest_GetSourceOrderPayments_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *PreviewRefundOrderRequest_GetSourceOrderPayments_Call) Return(_a0 []*ordersvcpb.RefundOrderRequest_OrderPayment) *PreviewRefundOrderRequest_GetSourceOrderPayments_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *PreviewRefundOrderRequest_GetSourceOrderPayments_Call) RunAndReturn(run func() []*ordersvcpb.RefundOrderRequest_OrderPayment) *PreviewRefundOrderRequest_GetSourceOrderPayments_Call {
	_c.Call.Return(run)
	return _c
}

// NewPreviewRefundOrderRequest creates a new instance of PreviewRefundOrderRequest. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewPreviewRefundOrderRequest(t interface {
	mock.TestingT
	Cleanup(func())
}) *PreviewRefundOrderRequest {
	mock := &PreviewRefundOrderRequest{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
