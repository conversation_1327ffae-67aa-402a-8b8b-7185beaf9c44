// Code generated by mockery v2.53.4. DO NOT EDIT.

package mocks

import (
	context "context"

	model "github.com/MoeGolibrary/moego-svc-order-v2/internal/model"
	mock "github.com/stretchr/testify/mock"

	ordersvcpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/order/v2"

	paymentpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/payment/v2"

	service "github.com/MoeGolibrary/moego-svc-order-v2/internal/service"

	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/order/v1"
)

// RefundOrderService is an autogenerated mock type for the RefundOrderService type
type RefundOrderService struct {
	mock.Mock
}

type RefundOrderService_Expecter struct {
	mock *mock.Mock
}

func (_m *RefundOrderService) EXPECT() *RefundOrderService_Expecter {
	return &RefundOrderService_Expecter{mock: &_m.Mock}
}

// ExportRefundOrderPaymentDetailList provides a mock function with given fields: ctx, req
func (_m *RefundOrderService) ExportRefundOrderPaymentDetailList(ctx context.Context, req *ordersvcpb.ExportRefundOrderPaymentDetailListRequest) (int64, error) {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for ExportRefundOrderPaymentDetailList")
	}

	var r0 int64
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *ordersvcpb.ExportRefundOrderPaymentDetailListRequest) (int64, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *ordersvcpb.ExportRefundOrderPaymentDetailListRequest) int64); ok {
		r0 = rf(ctx, req)
	} else {
		r0 = ret.Get(0).(int64)
	}

	if rf, ok := ret.Get(1).(func(context.Context, *ordersvcpb.ExportRefundOrderPaymentDetailListRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// RefundOrderService_ExportRefundOrderPaymentDetailList_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ExportRefundOrderPaymentDetailList'
type RefundOrderService_ExportRefundOrderPaymentDetailList_Call struct {
	*mock.Call
}

// ExportRefundOrderPaymentDetailList is a helper method to define mock.On call
//   - ctx context.Context
//   - req *ordersvcpb.ExportRefundOrderPaymentDetailListRequest
func (_e *RefundOrderService_Expecter) ExportRefundOrderPaymentDetailList(ctx interface{}, req interface{}) *RefundOrderService_ExportRefundOrderPaymentDetailList_Call {
	return &RefundOrderService_ExportRefundOrderPaymentDetailList_Call{Call: _e.mock.On("ExportRefundOrderPaymentDetailList", ctx, req)}
}

func (_c *RefundOrderService_ExportRefundOrderPaymentDetailList_Call) Run(run func(ctx context.Context, req *ordersvcpb.ExportRefundOrderPaymentDetailListRequest)) *RefundOrderService_ExportRefundOrderPaymentDetailList_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*ordersvcpb.ExportRefundOrderPaymentDetailListRequest))
	})
	return _c
}

func (_c *RefundOrderService_ExportRefundOrderPaymentDetailList_Call) Return(_a0 int64, _a1 error) *RefundOrderService_ExportRefundOrderPaymentDetailList_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *RefundOrderService_ExportRefundOrderPaymentDetailList_Call) RunAndReturn(run func(context.Context, *ordersvcpb.ExportRefundOrderPaymentDetailListRequest) (int64, error)) *RefundOrderService_ExportRefundOrderPaymentDetailList_Call {
	_c.Call.Return(run)
	return _c
}

// GetDepositDetailForRefund provides a mock function with given fields: ctx, order
func (_m *RefundOrderService) GetDepositDetailForRefund(ctx context.Context, order *model.Order) (*model.DepositDetail, error) {
	ret := _m.Called(ctx, order)

	if len(ret) == 0 {
		panic("no return value specified for GetDepositDetailForRefund")
	}

	var r0 *model.DepositDetail
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *model.Order) (*model.DepositDetail, error)); ok {
		return rf(ctx, order)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *model.Order) *model.DepositDetail); ok {
		r0 = rf(ctx, order)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*model.DepositDetail)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *model.Order) error); ok {
		r1 = rf(ctx, order)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// RefundOrderService_GetDepositDetailForRefund_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetDepositDetailForRefund'
type RefundOrderService_GetDepositDetailForRefund_Call struct {
	*mock.Call
}

// GetDepositDetailForRefund is a helper method to define mock.On call
//   - ctx context.Context
//   - order *model.Order
func (_e *RefundOrderService_Expecter) GetDepositDetailForRefund(ctx interface{}, order interface{}) *RefundOrderService_GetDepositDetailForRefund_Call {
	return &RefundOrderService_GetDepositDetailForRefund_Call{Call: _e.mock.On("GetDepositDetailForRefund", ctx, order)}
}

func (_c *RefundOrderService_GetDepositDetailForRefund_Call) Run(run func(ctx context.Context, order *model.Order)) *RefundOrderService_GetDepositDetailForRefund_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*model.Order))
	})
	return _c
}

func (_c *RefundOrderService_GetDepositDetailForRefund_Call) Return(_a0 *model.DepositDetail, _a1 error) *RefundOrderService_GetDepositDetailForRefund_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *RefundOrderService_GetDepositDetailForRefund_Call) RunAndReturn(run func(context.Context, *model.Order) (*model.DepositDetail, error)) *RefundOrderService_GetDepositDetailForRefund_Call {
	_c.Call.Return(run)
	return _c
}

// GetRefundDetail provides a mock function with given fields: ctx, refundID
func (_m *RefundOrderService) GetRefundDetail(ctx context.Context, refundID int64) (*model.RefundOrderDetail, error) {
	ret := _m.Called(ctx, refundID)

	if len(ret) == 0 {
		panic("no return value specified for GetRefundDetail")
	}

	var r0 *model.RefundOrderDetail
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, int64) (*model.RefundOrderDetail, error)); ok {
		return rf(ctx, refundID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, int64) *model.RefundOrderDetail); ok {
		r0 = rf(ctx, refundID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*model.RefundOrderDetail)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, int64) error); ok {
		r1 = rf(ctx, refundID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// RefundOrderService_GetRefundDetail_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetRefundDetail'
type RefundOrderService_GetRefundDetail_Call struct {
	*mock.Call
}

// GetRefundDetail is a helper method to define mock.On call
//   - ctx context.Context
//   - refundID int64
func (_e *RefundOrderService_Expecter) GetRefundDetail(ctx interface{}, refundID interface{}) *RefundOrderService_GetRefundDetail_Call {
	return &RefundOrderService_GetRefundDetail_Call{Call: _e.mock.On("GetRefundDetail", ctx, refundID)}
}

func (_c *RefundOrderService_GetRefundDetail_Call) Run(run func(ctx context.Context, refundID int64)) *RefundOrderService_GetRefundDetail_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(int64))
	})
	return _c
}

func (_c *RefundOrderService_GetRefundDetail_Call) Return(_a0 *model.RefundOrderDetail, _a1 error) *RefundOrderService_GetRefundDetail_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *RefundOrderService_GetRefundDetail_Call) RunAndReturn(run func(context.Context, int64) (*model.RefundOrderDetail, error)) *RefundOrderService_GetRefundDetail_Call {
	_c.Call.Return(run)
	return _c
}

// ListRefund provides a mock function with given fields: ctx, orderID
func (_m *RefundOrderService) ListRefund(ctx context.Context, orderID int64) ([]*model.RefundOrder, error) {
	ret := _m.Called(ctx, orderID)

	if len(ret) == 0 {
		panic("no return value specified for ListRefund")
	}

	var r0 []*model.RefundOrder
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, int64) ([]*model.RefundOrder, error)); ok {
		return rf(ctx, orderID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, int64) []*model.RefundOrder); ok {
		r0 = rf(ctx, orderID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*model.RefundOrder)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, int64) error); ok {
		r1 = rf(ctx, orderID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// RefundOrderService_ListRefund_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ListRefund'
type RefundOrderService_ListRefund_Call struct {
	*mock.Call
}

// ListRefund is a helper method to define mock.On call
//   - ctx context.Context
//   - orderID int64
func (_e *RefundOrderService_Expecter) ListRefund(ctx interface{}, orderID interface{}) *RefundOrderService_ListRefund_Call {
	return &RefundOrderService_ListRefund_Call{Call: _e.mock.On("ListRefund", ctx, orderID)}
}

func (_c *RefundOrderService_ListRefund_Call) Run(run func(ctx context.Context, orderID int64)) *RefundOrderService_ListRefund_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(int64))
	})
	return _c
}

func (_c *RefundOrderService_ListRefund_Call) Return(_a0 []*model.RefundOrder, _a1 error) *RefundOrderService_ListRefund_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *RefundOrderService_ListRefund_Call) RunAndReturn(run func(context.Context, int64) ([]*model.RefundOrder, error)) *RefundOrderService_ListRefund_Call {
	_c.Call.Return(run)
	return _c
}

// ListRefundDetail provides a mock function with given fields: ctx, orderID
func (_m *RefundOrderService) ListRefundDetail(ctx context.Context, orderID int64) ([]*model.RefundOrderDetail, error) {
	ret := _m.Called(ctx, orderID)

	if len(ret) == 0 {
		panic("no return value specified for ListRefundDetail")
	}

	var r0 []*model.RefundOrderDetail
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, int64) ([]*model.RefundOrderDetail, error)); ok {
		return rf(ctx, orderID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, int64) []*model.RefundOrderDetail); ok {
		r0 = rf(ctx, orderID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*model.RefundOrderDetail)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, int64) error); ok {
		r1 = rf(ctx, orderID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// RefundOrderService_ListRefundDetail_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ListRefundDetail'
type RefundOrderService_ListRefundDetail_Call struct {
	*mock.Call
}

// ListRefundDetail is a helper method to define mock.On call
//   - ctx context.Context
//   - orderID int64
func (_e *RefundOrderService_Expecter) ListRefundDetail(ctx interface{}, orderID interface{}) *RefundOrderService_ListRefundDetail_Call {
	return &RefundOrderService_ListRefundDetail_Call{Call: _e.mock.On("ListRefundDetail", ctx, orderID)}
}

func (_c *RefundOrderService_ListRefundDetail_Call) Run(run func(ctx context.Context, orderID int64)) *RefundOrderService_ListRefundDetail_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(int64))
	})
	return _c
}

func (_c *RefundOrderService_ListRefundDetail_Call) Return(_a0 []*model.RefundOrderDetail, _a1 error) *RefundOrderService_ListRefundDetail_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *RefundOrderService_ListRefundDetail_Call) RunAndReturn(run func(context.Context, int64) ([]*model.RefundOrderDetail, error)) *RefundOrderService_ListRefundDetail_Call {
	_c.Call.Return(run)
	return _c
}

// ListRefundOrderPaymentDetail provides a mock function with given fields: ctx, req
func (_m *RefundOrderService) ListRefundOrderPaymentDetail(ctx context.Context, req *ordersvcpb.ListRefundOrderPaymentDetailRequest) (*ordersvcpb.ListRefundOrderPaymentDetailResponse, error) {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for ListRefundOrderPaymentDetail")
	}

	var r0 *ordersvcpb.ListRefundOrderPaymentDetailResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *ordersvcpb.ListRefundOrderPaymentDetailRequest) (*ordersvcpb.ListRefundOrderPaymentDetailResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *ordersvcpb.ListRefundOrderPaymentDetailRequest) *ordersvcpb.ListRefundOrderPaymentDetailResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*ordersvcpb.ListRefundOrderPaymentDetailResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *ordersvcpb.ListRefundOrderPaymentDetailRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// RefundOrderService_ListRefundOrderPaymentDetail_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ListRefundOrderPaymentDetail'
type RefundOrderService_ListRefundOrderPaymentDetail_Call struct {
	*mock.Call
}

// ListRefundOrderPaymentDetail is a helper method to define mock.On call
//   - ctx context.Context
//   - req *ordersvcpb.ListRefundOrderPaymentDetailRequest
func (_e *RefundOrderService_Expecter) ListRefundOrderPaymentDetail(ctx interface{}, req interface{}) *RefundOrderService_ListRefundOrderPaymentDetail_Call {
	return &RefundOrderService_ListRefundOrderPaymentDetail_Call{Call: _e.mock.On("ListRefundOrderPaymentDetail", ctx, req)}
}

func (_c *RefundOrderService_ListRefundOrderPaymentDetail_Call) Run(run func(ctx context.Context, req *ordersvcpb.ListRefundOrderPaymentDetailRequest)) *RefundOrderService_ListRefundOrderPaymentDetail_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*ordersvcpb.ListRefundOrderPaymentDetailRequest))
	})
	return _c
}

func (_c *RefundOrderService_ListRefundOrderPaymentDetail_Call) Return(_a0 *ordersvcpb.ListRefundOrderPaymentDetailResponse, _a1 error) *RefundOrderService_ListRefundOrderPaymentDetail_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *RefundOrderService_ListRefundOrderPaymentDetail_Call) RunAndReturn(run func(context.Context, *ordersvcpb.ListRefundOrderPaymentDetailRequest) (*ordersvcpb.ListRefundOrderPaymentDetailResponse, error)) *RefundOrderService_ListRefundOrderPaymentDetail_Call {
	_c.Call.Return(run)
	return _c
}

// PreviewRefundOrder provides a mock function with given fields: ctx, orderDetail, req, og
func (_m *RefundOrderService) PreviewRefundOrder(ctx context.Context, orderDetail *model.OrderDetail, req *v1.PreviewRefundOrderRequest, og service.OrderDetailGetter) (*v1.PreviewRefundOrderResponse, error) {
	ret := _m.Called(ctx, orderDetail, req, og)

	if len(ret) == 0 {
		panic("no return value specified for PreviewRefundOrder")
	}

	var r0 *v1.PreviewRefundOrderResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *model.OrderDetail, *v1.PreviewRefundOrderRequest, service.OrderDetailGetter) (*v1.PreviewRefundOrderResponse, error)); ok {
		return rf(ctx, orderDetail, req, og)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *model.OrderDetail, *v1.PreviewRefundOrderRequest, service.OrderDetailGetter) *v1.PreviewRefundOrderResponse); ok {
		r0 = rf(ctx, orderDetail, req, og)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*v1.PreviewRefundOrderResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *model.OrderDetail, *v1.PreviewRefundOrderRequest, service.OrderDetailGetter) error); ok {
		r1 = rf(ctx, orderDetail, req, og)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// RefundOrderService_PreviewRefundOrder_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'PreviewRefundOrder'
type RefundOrderService_PreviewRefundOrder_Call struct {
	*mock.Call
}

// PreviewRefundOrder is a helper method to define mock.On call
//   - ctx context.Context
//   - orderDetail *model.OrderDetail
//   - req *v1.PreviewRefundOrderRequest
//   - og service.OrderDetailGetter
func (_e *RefundOrderService_Expecter) PreviewRefundOrder(ctx interface{}, orderDetail interface{}, req interface{}, og interface{}) *RefundOrderService_PreviewRefundOrder_Call {
	return &RefundOrderService_PreviewRefundOrder_Call{Call: _e.mock.On("PreviewRefundOrder", ctx, orderDetail, req, og)}
}

func (_c *RefundOrderService_PreviewRefundOrder_Call) Run(run func(ctx context.Context, orderDetail *model.OrderDetail, req *v1.PreviewRefundOrderRequest, og service.OrderDetailGetter)) *RefundOrderService_PreviewRefundOrder_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*model.OrderDetail), args[2].(*v1.PreviewRefundOrderRequest), args[3].(service.OrderDetailGetter))
	})
	return _c
}

func (_c *RefundOrderService_PreviewRefundOrder_Call) Return(_a0 *v1.PreviewRefundOrderResponse, _a1 error) *RefundOrderService_PreviewRefundOrder_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *RefundOrderService_PreviewRefundOrder_Call) RunAndReturn(run func(context.Context, *model.OrderDetail, *v1.PreviewRefundOrderRequest, service.OrderDetailGetter) (*v1.PreviewRefundOrderResponse, error)) *RefundOrderService_PreviewRefundOrder_Call {
	_c.Call.Return(run)
	return _c
}

// PreviewRefundOrderPayments provides a mock function with given fields: ctx, req
func (_m *RefundOrderService) PreviewRefundOrderPayments(ctx context.Context, req *v1.PreviewRefundOrderPaymentsRequest) (*v1.PreviewRefundOrderPaymentsResponse, error) {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for PreviewRefundOrderPayments")
	}

	var r0 *v1.PreviewRefundOrderPaymentsResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *v1.PreviewRefundOrderPaymentsRequest) (*v1.PreviewRefundOrderPaymentsResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *v1.PreviewRefundOrderPaymentsRequest) *v1.PreviewRefundOrderPaymentsResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*v1.PreviewRefundOrderPaymentsResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *v1.PreviewRefundOrderPaymentsRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// RefundOrderService_PreviewRefundOrderPayments_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'PreviewRefundOrderPayments'
type RefundOrderService_PreviewRefundOrderPayments_Call struct {
	*mock.Call
}

// PreviewRefundOrderPayments is a helper method to define mock.On call
//   - ctx context.Context
//   - req *v1.PreviewRefundOrderPaymentsRequest
func (_e *RefundOrderService_Expecter) PreviewRefundOrderPayments(ctx interface{}, req interface{}) *RefundOrderService_PreviewRefundOrderPayments_Call {
	return &RefundOrderService_PreviewRefundOrderPayments_Call{Call: _e.mock.On("PreviewRefundOrderPayments", ctx, req)}
}

func (_c *RefundOrderService_PreviewRefundOrderPayments_Call) Run(run func(ctx context.Context, req *v1.PreviewRefundOrderPaymentsRequest)) *RefundOrderService_PreviewRefundOrderPayments_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*v1.PreviewRefundOrderPaymentsRequest))
	})
	return _c
}

func (_c *RefundOrderService_PreviewRefundOrderPayments_Call) Return(_a0 *v1.PreviewRefundOrderPaymentsResponse, _a1 error) *RefundOrderService_PreviewRefundOrderPayments_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *RefundOrderService_PreviewRefundOrderPayments_Call) RunAndReturn(run func(context.Context, *v1.PreviewRefundOrderPaymentsRequest) (*v1.PreviewRefundOrderPaymentsResponse, error)) *RefundOrderService_PreviewRefundOrderPayments_Call {
	_c.Call.Return(run)
	return _c
}

// RefundOrder provides a mock function with given fields: ctx, orderDetail, req, og
func (_m *RefundOrderService) RefundOrder(ctx context.Context, orderDetail *model.OrderDetail, req *v1.RefundOrderRequest, og service.OrderDetailGetter) (*v1.RefundOrderResponse, error) {
	ret := _m.Called(ctx, orderDetail, req, og)

	if len(ret) == 0 {
		panic("no return value specified for RefundOrder")
	}

	var r0 *v1.RefundOrderResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *model.OrderDetail, *v1.RefundOrderRequest, service.OrderDetailGetter) (*v1.RefundOrderResponse, error)); ok {
		return rf(ctx, orderDetail, req, og)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *model.OrderDetail, *v1.RefundOrderRequest, service.OrderDetailGetter) *v1.RefundOrderResponse); ok {
		r0 = rf(ctx, orderDetail, req, og)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*v1.RefundOrderResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *model.OrderDetail, *v1.RefundOrderRequest, service.OrderDetailGetter) error); ok {
		r1 = rf(ctx, orderDetail, req, og)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// RefundOrderService_RefundOrder_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'RefundOrder'
type RefundOrderService_RefundOrder_Call struct {
	*mock.Call
}

// RefundOrder is a helper method to define mock.On call
//   - ctx context.Context
//   - orderDetail *model.OrderDetail
//   - req *v1.RefundOrderRequest
//   - og service.OrderDetailGetter
func (_e *RefundOrderService_Expecter) RefundOrder(ctx interface{}, orderDetail interface{}, req interface{}, og interface{}) *RefundOrderService_RefundOrder_Call {
	return &RefundOrderService_RefundOrder_Call{Call: _e.mock.On("RefundOrder", ctx, orderDetail, req, og)}
}

func (_c *RefundOrderService_RefundOrder_Call) Run(run func(ctx context.Context, orderDetail *model.OrderDetail, req *v1.RefundOrderRequest, og service.OrderDetailGetter)) *RefundOrderService_RefundOrder_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*model.OrderDetail), args[2].(*v1.RefundOrderRequest), args[3].(service.OrderDetailGetter))
	})
	return _c
}

func (_c *RefundOrderService_RefundOrder_Call) Return(_a0 *v1.RefundOrderResponse, _a1 error) *RefundOrderService_RefundOrder_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *RefundOrderService_RefundOrder_Call) RunAndReturn(run func(context.Context, *model.OrderDetail, *v1.RefundOrderRequest, service.OrderDetailGetter) (*v1.RefundOrderResponse, error)) *RefundOrderService_RefundOrder_Call {
	_c.Call.Return(run)
	return _c
}

// SyncRefundOrderPayment provides a mock function with given fields: ctx, refundOrderPaymentID
func (_m *RefundOrderService) SyncRefundOrderPayment(ctx context.Context, refundOrderPaymentID int64) (int64, int64, error) {
	ret := _m.Called(ctx, refundOrderPaymentID)

	if len(ret) == 0 {
		panic("no return value specified for SyncRefundOrderPayment")
	}

	var r0 int64
	var r1 int64
	var r2 error
	if rf, ok := ret.Get(0).(func(context.Context, int64) (int64, int64, error)); ok {
		return rf(ctx, refundOrderPaymentID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, int64) int64); ok {
		r0 = rf(ctx, refundOrderPaymentID)
	} else {
		r0 = ret.Get(0).(int64)
	}

	if rf, ok := ret.Get(1).(func(context.Context, int64) int64); ok {
		r1 = rf(ctx, refundOrderPaymentID)
	} else {
		r1 = ret.Get(1).(int64)
	}

	if rf, ok := ret.Get(2).(func(context.Context, int64) error); ok {
		r2 = rf(ctx, refundOrderPaymentID)
	} else {
		r2 = ret.Error(2)
	}

	return r0, r1, r2
}

// RefundOrderService_SyncRefundOrderPayment_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'SyncRefundOrderPayment'
type RefundOrderService_SyncRefundOrderPayment_Call struct {
	*mock.Call
}

// SyncRefundOrderPayment is a helper method to define mock.On call
//   - ctx context.Context
//   - refundOrderPaymentID int64
func (_e *RefundOrderService_Expecter) SyncRefundOrderPayment(ctx interface{}, refundOrderPaymentID interface{}) *RefundOrderService_SyncRefundOrderPayment_Call {
	return &RefundOrderService_SyncRefundOrderPayment_Call{Call: _e.mock.On("SyncRefundOrderPayment", ctx, refundOrderPaymentID)}
}

func (_c *RefundOrderService_SyncRefundOrderPayment_Call) Run(run func(ctx context.Context, refundOrderPaymentID int64)) *RefundOrderService_SyncRefundOrderPayment_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(int64))
	})
	return _c
}

func (_c *RefundOrderService_SyncRefundOrderPayment_Call) Return(total int64, synced int64, err error) *RefundOrderService_SyncRefundOrderPayment_Call {
	_c.Call.Return(total, synced, err)
	return _c
}

func (_c *RefundOrderService_SyncRefundOrderPayment_Call) RunAndReturn(run func(context.Context, int64) (int64, int64, error)) *RefundOrderService_SyncRefundOrderPayment_Call {
	_c.Call.Return(run)
	return _c
}

// TrySyncRefundTransaction provides a mock function with given fields: ctx, rop, rp
func (_m *RefundOrderService) TrySyncRefundTransaction(ctx context.Context, rop *model.RefundOrderPayment, rp model.RefundPaymentor) error {
	ret := _m.Called(ctx, rop, rp)

	if len(ret) == 0 {
		panic("no return value specified for TrySyncRefundTransaction")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *model.RefundOrderPayment, model.RefundPaymentor) error); ok {
		r0 = rf(ctx, rop, rp)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// RefundOrderService_TrySyncRefundTransaction_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'TrySyncRefundTransaction'
type RefundOrderService_TrySyncRefundTransaction_Call struct {
	*mock.Call
}

// TrySyncRefundTransaction is a helper method to define mock.On call
//   - ctx context.Context
//   - rop *model.RefundOrderPayment
//   - rp model.RefundPaymentor
func (_e *RefundOrderService_Expecter) TrySyncRefundTransaction(ctx interface{}, rop interface{}, rp interface{}) *RefundOrderService_TrySyncRefundTransaction_Call {
	return &RefundOrderService_TrySyncRefundTransaction_Call{Call: _e.mock.On("TrySyncRefundTransaction", ctx, rop, rp)}
}

func (_c *RefundOrderService_TrySyncRefundTransaction_Call) Run(run func(ctx context.Context, rop *model.RefundOrderPayment, rp model.RefundPaymentor)) *RefundOrderService_TrySyncRefundTransaction_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*model.RefundOrderPayment), args[2].(model.RefundPaymentor))
	})
	return _c
}

func (_c *RefundOrderService_TrySyncRefundTransaction_Call) Return(_a0 error) *RefundOrderService_TrySyncRefundTransaction_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *RefundOrderService_TrySyncRefundTransaction_Call) RunAndReturn(run func(context.Context, *model.RefundOrderPayment, model.RefundPaymentor) error) *RefundOrderService_TrySyncRefundTransaction_Call {
	_c.Call.Return(run)
	return _c
}

// TrySyncRefundTransactionByRefundPaymentID provides a mock function with given fields: ctx, ropID, refundModel
func (_m *RefundOrderService) TrySyncRefundTransactionByRefundPaymentID(ctx context.Context, ropID int64, refundModel *paymentpb.RefundModel) error {
	ret := _m.Called(ctx, ropID, refundModel)

	if len(ret) == 0 {
		panic("no return value specified for TrySyncRefundTransactionByRefundPaymentID")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, int64, *paymentpb.RefundModel) error); ok {
		r0 = rf(ctx, ropID, refundModel)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// RefundOrderService_TrySyncRefundTransactionByRefundPaymentID_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'TrySyncRefundTransactionByRefundPaymentID'
type RefundOrderService_TrySyncRefundTransactionByRefundPaymentID_Call struct {
	*mock.Call
}

// TrySyncRefundTransactionByRefundPaymentID is a helper method to define mock.On call
//   - ctx context.Context
//   - ropID int64
//   - refundModel *paymentpb.RefundModel
func (_e *RefundOrderService_Expecter) TrySyncRefundTransactionByRefundPaymentID(ctx interface{}, ropID interface{}, refundModel interface{}) *RefundOrderService_TrySyncRefundTransactionByRefundPaymentID_Call {
	return &RefundOrderService_TrySyncRefundTransactionByRefundPaymentID_Call{Call: _e.mock.On("TrySyncRefundTransactionByRefundPaymentID", ctx, ropID, refundModel)}
}

func (_c *RefundOrderService_TrySyncRefundTransactionByRefundPaymentID_Call) Run(run func(ctx context.Context, ropID int64, refundModel *paymentpb.RefundModel)) *RefundOrderService_TrySyncRefundTransactionByRefundPaymentID_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(int64), args[2].(*paymentpb.RefundModel))
	})
	return _c
}

func (_c *RefundOrderService_TrySyncRefundTransactionByRefundPaymentID_Call) Return(_a0 error) *RefundOrderService_TrySyncRefundTransactionByRefundPaymentID_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *RefundOrderService_TrySyncRefundTransactionByRefundPaymentID_Call) RunAndReturn(run func(context.Context, int64, *paymentpb.RefundModel) error) *RefundOrderService_TrySyncRefundTransactionByRefundPaymentID_Call {
	_c.Call.Return(run)
	return _c
}

// NewRefundOrderService creates a new instance of RefundOrderService. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewRefundOrderService(t interface {
	mock.TestingT
	Cleanup(func())
}) *RefundOrderService {
	mock := &RefundOrderService{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
