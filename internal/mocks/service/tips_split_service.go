// Code generated by mockery v2.53.4. DO NOT EDIT.

package mocks

import (
	context "context"

	model "github.com/MoeGolibrary/moego-svc-order-v2/internal/model"
	mock "github.com/stretchr/testify/mock"

	orderpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/order/v1"

	ordersvcpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/order/v2"

	service "github.com/MoeGolibrary/moego-svc-order-v2/internal/service"
)

// TipsSplitService is an autogenerated mock type for the TipsSplitService type
type TipsSplitService struct {
	mock.Mock
}

type TipsSplitService_Expecter struct {
	mock *mock.Mock
}

func (_m *TipsSplitService) EXPECT() *TipsSplitService_Expecter {
	return &TipsSplitService_Expecter{mock: &_m.<PERSON>}
}

// ComputeEditStaffAndTips provides a mock function with given fields: ctx, req
func (_m *TipsSplitService) ComputeEditStaffAndTips(ctx context.Context, req *ordersvcpb.EditStaffAndTipsSplitRequest) (*service.ComputedResult, error) {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for ComputeEditStaffAndTips")
	}

	var r0 *service.ComputedResult
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *ordersvcpb.EditStaffAndTipsSplitRequest) (*service.ComputedResult, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *ordersvcpb.EditStaffAndTipsSplitRequest) *service.ComputedResult); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*service.ComputedResult)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *ordersvcpb.EditStaffAndTipsSplitRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// TipsSplitService_ComputeEditStaffAndTips_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ComputeEditStaffAndTips'
type TipsSplitService_ComputeEditStaffAndTips_Call struct {
	*mock.Call
}

// ComputeEditStaffAndTips is a helper method to define mock.On call
//   - ctx context.Context
//   - req *ordersvcpb.EditStaffAndTipsSplitRequest
func (_e *TipsSplitService_Expecter) ComputeEditStaffAndTips(ctx interface{}, req interface{}) *TipsSplitService_ComputeEditStaffAndTips_Call {
	return &TipsSplitService_ComputeEditStaffAndTips_Call{Call: _e.mock.On("ComputeEditStaffAndTips", ctx, req)}
}

func (_c *TipsSplitService_ComputeEditStaffAndTips_Call) Run(run func(ctx context.Context, req *ordersvcpb.EditStaffAndTipsSplitRequest)) *TipsSplitService_ComputeEditStaffAndTips_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*ordersvcpb.EditStaffAndTipsSplitRequest))
	})
	return _c
}

func (_c *TipsSplitService_ComputeEditStaffAndTips_Call) Return(_a0 *service.ComputedResult, _a1 error) *TipsSplitService_ComputeEditStaffAndTips_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *TipsSplitService_ComputeEditStaffAndTips_Call) RunAndReturn(run func(context.Context, *ordersvcpb.EditStaffAndTipsSplitRequest) (*service.ComputedResult, error)) *TipsSplitService_ComputeEditStaffAndTips_Call {
	_c.Call.Return(run)
	return _c
}

// DelStatus provides a mock function with given fields: ctx, sourceType, sourceID
func (_m *TipsSplitService) DelStatus(ctx context.Context, sourceType orderpb.OrderSourceType, sourceID int64) error {
	ret := _m.Called(ctx, sourceType, sourceID)

	if len(ret) == 0 {
		panic("no return value specified for DelStatus")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, orderpb.OrderSourceType, int64) error); ok {
		r0 = rf(ctx, sourceType, sourceID)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// TipsSplitService_DelStatus_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DelStatus'
type TipsSplitService_DelStatus_Call struct {
	*mock.Call
}

// DelStatus is a helper method to define mock.On call
//   - ctx context.Context
//   - sourceType orderpb.OrderSourceType
//   - sourceID int64
func (_e *TipsSplitService_Expecter) DelStatus(ctx interface{}, sourceType interface{}, sourceID interface{}) *TipsSplitService_DelStatus_Call {
	return &TipsSplitService_DelStatus_Call{Call: _e.mock.On("DelStatus", ctx, sourceType, sourceID)}
}

func (_c *TipsSplitService_DelStatus_Call) Run(run func(ctx context.Context, sourceType orderpb.OrderSourceType, sourceID int64)) *TipsSplitService_DelStatus_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(orderpb.OrderSourceType), args[2].(int64))
	})
	return _c
}

func (_c *TipsSplitService_DelStatus_Call) Return(_a0 error) *TipsSplitService_DelStatus_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *TipsSplitService_DelStatus_Call) RunAndReturn(run func(context.Context, orderpb.OrderSourceType, int64) error) *TipsSplitService_DelStatus_Call {
	_c.Call.Return(run)
	return _c
}

// EditStaffAndUpsertTipsSplit provides a mock function with given fields: ctx, req
func (_m *TipsSplitService) EditStaffAndUpsertTipsSplit(ctx context.Context, req *ordersvcpb.EditStaffAndTipsSplitRequest) (*model.TipsSplit, error) {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for EditStaffAndUpsertTipsSplit")
	}

	var r0 *model.TipsSplit
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *ordersvcpb.EditStaffAndTipsSplitRequest) (*model.TipsSplit, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *ordersvcpb.EditStaffAndTipsSplitRequest) *model.TipsSplit); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*model.TipsSplit)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *ordersvcpb.EditStaffAndTipsSplitRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// TipsSplitService_EditStaffAndUpsertTipsSplit_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'EditStaffAndUpsertTipsSplit'
type TipsSplitService_EditStaffAndUpsertTipsSplit_Call struct {
	*mock.Call
}

// EditStaffAndUpsertTipsSplit is a helper method to define mock.On call
//   - ctx context.Context
//   - req *ordersvcpb.EditStaffAndTipsSplitRequest
func (_e *TipsSplitService_Expecter) EditStaffAndUpsertTipsSplit(ctx interface{}, req interface{}) *TipsSplitService_EditStaffAndUpsertTipsSplit_Call {
	return &TipsSplitService_EditStaffAndUpsertTipsSplit_Call{Call: _e.mock.On("EditStaffAndUpsertTipsSplit", ctx, req)}
}

func (_c *TipsSplitService_EditStaffAndUpsertTipsSplit_Call) Run(run func(ctx context.Context, req *ordersvcpb.EditStaffAndTipsSplitRequest)) *TipsSplitService_EditStaffAndUpsertTipsSplit_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*ordersvcpb.EditStaffAndTipsSplitRequest))
	})
	return _c
}

func (_c *TipsSplitService_EditStaffAndUpsertTipsSplit_Call) Return(_a0 *model.TipsSplit, _a1 error) *TipsSplitService_EditStaffAndUpsertTipsSplit_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *TipsSplitService_EditStaffAndUpsertTipsSplit_Call) RunAndReturn(run func(context.Context, *ordersvcpb.EditStaffAndTipsSplitRequest) (*model.TipsSplit, error)) *TipsSplitService_EditStaffAndUpsertTipsSplit_Call {
	_c.Call.Return(run)
	return _c
}

// Get provides a mock function with given fields: ctx, sourceID, sourceType
func (_m *TipsSplitService) Get(ctx context.Context, sourceID int64, sourceType orderpb.OrderSourceType) (*model.TipsSplit, error) {
	ret := _m.Called(ctx, sourceID, sourceType)

	if len(ret) == 0 {
		panic("no return value specified for Get")
	}

	var r0 *model.TipsSplit
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, int64, orderpb.OrderSourceType) (*model.TipsSplit, error)); ok {
		return rf(ctx, sourceID, sourceType)
	}
	if rf, ok := ret.Get(0).(func(context.Context, int64, orderpb.OrderSourceType) *model.TipsSplit); ok {
		r0 = rf(ctx, sourceID, sourceType)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*model.TipsSplit)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, int64, orderpb.OrderSourceType) error); ok {
		r1 = rf(ctx, sourceID, sourceType)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// TipsSplitService_Get_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Get'
type TipsSplitService_Get_Call struct {
	*mock.Call
}

// Get is a helper method to define mock.On call
//   - ctx context.Context
//   - sourceID int64
//   - sourceType orderpb.OrderSourceType
func (_e *TipsSplitService_Expecter) Get(ctx interface{}, sourceID interface{}, sourceType interface{}) *TipsSplitService_Get_Call {
	return &TipsSplitService_Get_Call{Call: _e.mock.On("Get", ctx, sourceID, sourceType)}
}

func (_c *TipsSplitService_Get_Call) Run(run func(ctx context.Context, sourceID int64, sourceType orderpb.OrderSourceType)) *TipsSplitService_Get_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(int64), args[2].(orderpb.OrderSourceType))
	})
	return _c
}

func (_c *TipsSplitService_Get_Call) Return(_a0 *model.TipsSplit, _a1 error) *TipsSplitService_Get_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *TipsSplitService_Get_Call) RunAndReturn(run func(context.Context, int64, orderpb.OrderSourceType) (*model.TipsSplit, error)) *TipsSplitService_Get_Call {
	_c.Call.Return(run)
	return _c
}

// GetStatus provides a mock function with given fields: ctx, sourceType, sourceID
func (_m *TipsSplitService) GetStatus(ctx context.Context, sourceType orderpb.OrderSourceType, sourceID int64) (bool, error) {
	ret := _m.Called(ctx, sourceType, sourceID)

	if len(ret) == 0 {
		panic("no return value specified for GetStatus")
	}

	var r0 bool
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, orderpb.OrderSourceType, int64) (bool, error)); ok {
		return rf(ctx, sourceType, sourceID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, orderpb.OrderSourceType, int64) bool); ok {
		r0 = rf(ctx, sourceType, sourceID)
	} else {
		r0 = ret.Get(0).(bool)
	}

	if rf, ok := ret.Get(1).(func(context.Context, orderpb.OrderSourceType, int64) error); ok {
		r1 = rf(ctx, sourceType, sourceID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// TipsSplitService_GetStatus_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetStatus'
type TipsSplitService_GetStatus_Call struct {
	*mock.Call
}

// GetStatus is a helper method to define mock.On call
//   - ctx context.Context
//   - sourceType orderpb.OrderSourceType
//   - sourceID int64
func (_e *TipsSplitService_Expecter) GetStatus(ctx interface{}, sourceType interface{}, sourceID interface{}) *TipsSplitService_GetStatus_Call {
	return &TipsSplitService_GetStatus_Call{Call: _e.mock.On("GetStatus", ctx, sourceType, sourceID)}
}

func (_c *TipsSplitService_GetStatus_Call) Run(run func(ctx context.Context, sourceType orderpb.OrderSourceType, sourceID int64)) *TipsSplitService_GetStatus_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(orderpb.OrderSourceType), args[2].(int64))
	})
	return _c
}

func (_c *TipsSplitService_GetStatus_Call) Return(_a0 bool, _a1 error) *TipsSplitService_GetStatus_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *TipsSplitService_GetStatus_Call) RunAndReturn(run func(context.Context, orderpb.OrderSourceType, int64) (bool, error)) *TipsSplitService_GetStatus_Call {
	_c.Call.Return(run)
	return _c
}

// GetTipsSplitDetails provides a mock function with given fields: ctx, tipsSplitID
func (_m *TipsSplitService) GetTipsSplitDetails(ctx context.Context, tipsSplitID int64) ([]*model.TipsSplitDetail, error) {
	ret := _m.Called(ctx, tipsSplitID)

	if len(ret) == 0 {
		panic("no return value specified for GetTipsSplitDetails")
	}

	var r0 []*model.TipsSplitDetail
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, int64) ([]*model.TipsSplitDetail, error)); ok {
		return rf(ctx, tipsSplitID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, int64) []*model.TipsSplitDetail); ok {
		r0 = rf(ctx, tipsSplitID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*model.TipsSplitDetail)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, int64) error); ok {
		r1 = rf(ctx, tipsSplitID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// TipsSplitService_GetTipsSplitDetails_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetTipsSplitDetails'
type TipsSplitService_GetTipsSplitDetails_Call struct {
	*mock.Call
}

// GetTipsSplitDetails is a helper method to define mock.On call
//   - ctx context.Context
//   - tipsSplitID int64
func (_e *TipsSplitService_Expecter) GetTipsSplitDetails(ctx interface{}, tipsSplitID interface{}) *TipsSplitService_GetTipsSplitDetails_Call {
	return &TipsSplitService_GetTipsSplitDetails_Call{Call: _e.mock.On("GetTipsSplitDetails", ctx, tipsSplitID)}
}

func (_c *TipsSplitService_GetTipsSplitDetails_Call) Run(run func(ctx context.Context, tipsSplitID int64)) *TipsSplitService_GetTipsSplitDetails_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(int64))
	})
	return _c
}

func (_c *TipsSplitService_GetTipsSplitDetails_Call) Return(_a0 []*model.TipsSplitDetail, _a1 error) *TipsSplitService_GetTipsSplitDetails_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *TipsSplitService_GetTipsSplitDetails_Call) RunAndReturn(run func(context.Context, int64) ([]*model.TipsSplitDetail, error)) *TipsSplitService_GetTipsSplitDetails_Call {
	_c.Call.Return(run)
	return _c
}

// GetTipsSplitForLegacy provides a mock function with given fields: ctx, sourceID, sourceType
func (_m *TipsSplitService) GetTipsSplitForLegacy(ctx context.Context, sourceID int64, sourceType orderpb.OrderSourceType) (*model.TipsSplit, error) {
	ret := _m.Called(ctx, sourceID, sourceType)

	if len(ret) == 0 {
		panic("no return value specified for GetTipsSplitForLegacy")
	}

	var r0 *model.TipsSplit
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, int64, orderpb.OrderSourceType) (*model.TipsSplit, error)); ok {
		return rf(ctx, sourceID, sourceType)
	}
	if rf, ok := ret.Get(0).(func(context.Context, int64, orderpb.OrderSourceType) *model.TipsSplit); ok {
		r0 = rf(ctx, sourceID, sourceType)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*model.TipsSplit)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, int64, orderpb.OrderSourceType) error); ok {
		r1 = rf(ctx, sourceID, sourceType)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// TipsSplitService_GetTipsSplitForLegacy_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetTipsSplitForLegacy'
type TipsSplitService_GetTipsSplitForLegacy_Call struct {
	*mock.Call
}

// GetTipsSplitForLegacy is a helper method to define mock.On call
//   - ctx context.Context
//   - sourceID int64
//   - sourceType orderpb.OrderSourceType
func (_e *TipsSplitService_Expecter) GetTipsSplitForLegacy(ctx interface{}, sourceID interface{}, sourceType interface{}) *TipsSplitService_GetTipsSplitForLegacy_Call {
	return &TipsSplitService_GetTipsSplitForLegacy_Call{Call: _e.mock.On("GetTipsSplitForLegacy", ctx, sourceID, sourceType)}
}

func (_c *TipsSplitService_GetTipsSplitForLegacy_Call) Run(run func(ctx context.Context, sourceID int64, sourceType orderpb.OrderSourceType)) *TipsSplitService_GetTipsSplitForLegacy_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(int64), args[2].(orderpb.OrderSourceType))
	})
	return _c
}

func (_c *TipsSplitService_GetTipsSplitForLegacy_Call) Return(_a0 *model.TipsSplit, _a1 error) *TipsSplitService_GetTipsSplitForLegacy_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *TipsSplitService_GetTipsSplitForLegacy_Call) RunAndReturn(run func(context.Context, int64, orderpb.OrderSourceType) (*model.TipsSplit, error)) *TipsSplitService_GetTipsSplitForLegacy_Call {
	_c.Call.Return(run)
	return _c
}

// ListTipsSplitDetailsBySource provides a mock function with given fields: ctx, sourceIDToType
func (_m *TipsSplitService) ListTipsSplitDetailsBySource(ctx context.Context, sourceIDToType map[int64]orderpb.OrderSourceType) ([]*ordersvcpb.ListTipsSplitDetailsBySourceResponse_TipsSplitDetail, error) {
	ret := _m.Called(ctx, sourceIDToType)

	if len(ret) == 0 {
		panic("no return value specified for ListTipsSplitDetailsBySource")
	}

	var r0 []*ordersvcpb.ListTipsSplitDetailsBySourceResponse_TipsSplitDetail
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, map[int64]orderpb.OrderSourceType) ([]*ordersvcpb.ListTipsSplitDetailsBySourceResponse_TipsSplitDetail, error)); ok {
		return rf(ctx, sourceIDToType)
	}
	if rf, ok := ret.Get(0).(func(context.Context, map[int64]orderpb.OrderSourceType) []*ordersvcpb.ListTipsSplitDetailsBySourceResponse_TipsSplitDetail); ok {
		r0 = rf(ctx, sourceIDToType)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*ordersvcpb.ListTipsSplitDetailsBySourceResponse_TipsSplitDetail)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, map[int64]orderpb.OrderSourceType) error); ok {
		r1 = rf(ctx, sourceIDToType)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// TipsSplitService_ListTipsSplitDetailsBySource_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ListTipsSplitDetailsBySource'
type TipsSplitService_ListTipsSplitDetailsBySource_Call struct {
	*mock.Call
}

// ListTipsSplitDetailsBySource is a helper method to define mock.On call
//   - ctx context.Context
//   - sourceIDToType map[int64]orderpb.OrderSourceType
func (_e *TipsSplitService_Expecter) ListTipsSplitDetailsBySource(ctx interface{}, sourceIDToType interface{}) *TipsSplitService_ListTipsSplitDetailsBySource_Call {
	return &TipsSplitService_ListTipsSplitDetailsBySource_Call{Call: _e.mock.On("ListTipsSplitDetailsBySource", ctx, sourceIDToType)}
}

func (_c *TipsSplitService_ListTipsSplitDetailsBySource_Call) Run(run func(ctx context.Context, sourceIDToType map[int64]orderpb.OrderSourceType)) *TipsSplitService_ListTipsSplitDetailsBySource_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(map[int64]orderpb.OrderSourceType))
	})
	return _c
}

func (_c *TipsSplitService_ListTipsSplitDetailsBySource_Call) Return(_a0 []*ordersvcpb.ListTipsSplitDetailsBySourceResponse_TipsSplitDetail, _a1 error) *TipsSplitService_ListTipsSplitDetailsBySource_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *TipsSplitService_ListTipsSplitDetailsBySource_Call) RunAndReturn(run func(context.Context, map[int64]orderpb.OrderSourceType) ([]*ordersvcpb.ListTipsSplitDetailsBySourceResponse_TipsSplitDetail, error)) *TipsSplitService_ListTipsSplitDetailsBySource_Call {
	_c.Call.Return(run)
	return _c
}

// SetStatus provides a mock function with given fields: ctx, sourceType, sourceID
func (_m *TipsSplitService) SetStatus(ctx context.Context, sourceType orderpb.OrderSourceType, sourceID int64) error {
	ret := _m.Called(ctx, sourceType, sourceID)

	if len(ret) == 0 {
		panic("no return value specified for SetStatus")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, orderpb.OrderSourceType, int64) error); ok {
		r0 = rf(ctx, sourceType, sourceID)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// TipsSplitService_SetStatus_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'SetStatus'
type TipsSplitService_SetStatus_Call struct {
	*mock.Call
}

// SetStatus is a helper method to define mock.On call
//   - ctx context.Context
//   - sourceType orderpb.OrderSourceType
//   - sourceID int64
func (_e *TipsSplitService_Expecter) SetStatus(ctx interface{}, sourceType interface{}, sourceID interface{}) *TipsSplitService_SetStatus_Call {
	return &TipsSplitService_SetStatus_Call{Call: _e.mock.On("SetStatus", ctx, sourceType, sourceID)}
}

func (_c *TipsSplitService_SetStatus_Call) Run(run func(ctx context.Context, sourceType orderpb.OrderSourceType, sourceID int64)) *TipsSplitService_SetStatus_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(orderpb.OrderSourceType), args[2].(int64))
	})
	return _c
}

func (_c *TipsSplitService_SetStatus_Call) Return(_a0 error) *TipsSplitService_SetStatus_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *TipsSplitService_SetStatus_Call) RunAndReturn(run func(context.Context, orderpb.OrderSourceType, int64) error) *TipsSplitService_SetStatus_Call {
	_c.Call.Return(run)
	return _c
}

// NewTipsSplitService creates a new instance of TipsSplitService. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewTipsSplitService(t interface {
	mock.TestingT
	Cleanup(func())
}) *TipsSplitService {
	mock := &TipsSplitService{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
