package model

import (
	"fmt"
)

// OrderVersion 是订单模型和逻辑的版本号.
// 每次逻辑或模型上产生老版本不能兼容的变更需要对该版本号进行变更，并附上简要的说明.
type OrderVersion int32

const (
	// OrderVersionLegacy 表示 Order 重构之前产生的老订单.
	OrderVersionLegacy OrderVersion = 0

	// OrderVersionCloseOrder 表示 Order 一期重构支持关单后产生的遵循关单逻辑的订单.
	OrderVersionCloseOrder OrderVersion = 1

	// OrderVersionRefund 表示 Order 二期重构支持 Refund By Item 功能后，遵循新 Refund 逻辑的订单.
	// 同时也表示了三期支持了 Cancel Appt 与 Cancel Order / Refund Order 联动的。
	// 由于二期三期完全兼容，所以没有 Bump 版本号.
	OrderVersionRefund OrderVersion = 2

	// OrderVersionImmutableOrder 表示 Order 四期重构，与 Appt 进行了一定程度的解耦。
	// 该版本开始，移除了 Appt 与 Order 数据双向同步的链路.
	// 不再创建 Appt 时创建 Order，只有在去支付的时候才会创建，同时引入了 Preview 的一整套流程.
	// 目前主要为 BD 0530 War Room 项目适配.
	OrderVersionImmutableOrder OrderVersion = 4
)

// OrderVersionStableLatest 永远指向 *稳定的* & *最新的* 订单版本.
const OrderVersionStableLatest = OrderVersionRefund

func (od OrderVersion) GtEq(other OrderVersion) bool { return od >= other }

func (od OrderVersion) Lt(other OrderVersion) bool { return od < other }

func (od OrderVersion) ToPB() int32 { return int32(od) }

func (od OrderVersion) String() string {
	switch od {
	case OrderVersionLegacy:
		return "Legacy(0)"
	case OrderVersionCloseOrder:
		return "CloseOrder(1)"
	case OrderVersionRefund: // OrderVersionCancelOrder
		return "Refund(2)"
	case OrderVersionImmutableOrder:
		return "ImmutableOrder(4)"
	default:
		return fmt.Sprintf("Unknown(%d)", int(od))
	}
}

type TaxRoundMode int32

const (
	// TaxRoundModeBank 银行家算法
	TaxRoundModeBank TaxRoundMode = iota
	// TaxRoundModeHalfUp 四舍五入
	TaxRoundModeHalfUp
)

type embeddedMethod int32

const (
	embeddedMethodCreditCard embeddedMethod = iota + 1
	embeddedMethodCash
	embeddedMethodCheck
)

func (em embeddedMethod) ID() int64 { return int64(em) }

func (em embeddedMethod) Name() string {
	switch em {
	case embeddedMethodCreditCard:
		return "Credit card"
	case embeddedMethodCash:
		return "Cash"
	case embeddedMethodCheck:
		return "Check"

	default:
		return "Unknown"
	}
}

// OrderItemTypeDeposit
// TODO(Perqin, YunXiang, P2): 老字段在 db 里是小写的 deposit，考虑改为 pb 类型
const (
	OrderItemTypeDeposit = "deposit"
	OrderItemTypeNoShow  = "noshow"
)
