package model

import (
	"time"

	"github.com/shopspring/decimal"

	orderpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/order/v1"
)

type DepositChangeLog struct {
	ID             int64 `gorm:"primaryKey;autoIncrement"`
	DepositOrderID int64
	ChangeType     orderpb.DepositChangeType   `gorm:"serializer:proto_enum"`
	Reason         orderpb.DepositChangeReason `gorm:"serializer:proto_enum"`
	// 对不同的 reason 有不同含义：
	// DEDUCTION：抵扣了本单 deposit 金额的 order id
	// OVERPAYMENT_REVERSAL：退款单 refund order id
	DestOrderID   int64
	ChangedAmount decimal.Decimal
	Balance       decimal.Decimal
	CurrencyCode  string
	PreviousLogID int64
	CompanyID     int64
	BusinessID    int64
	CustomerID    int64
	StaffID       int64
	CreateTime    time.Time `gorm:"autoCreateTime"`
	UpdateTime    time.Time `gorm:"autoUpdateTime"`
}

type DepositDetail struct {
	DepositChangeLogs []*DepositChangeLog
	LatestChangeLog   *DepositChangeLog
	DepositPriceItems []*PriceItem
}

func (d *DepositDetail) IsDeducted() bool {
	for _, cl := range d.DepositChangeLogs {
		if cl.Reason == orderpb.DepositChangeReason_DEDUCTION {
			return true
		}
	}

	return false
}

// GetDeductionDestOrderID 暂时不考虑多条 DEDUCTION 的情况
func (d *DepositDetail) GetDeductionDestOrderID() int64 {
	for _, cl := range d.DepositChangeLogs {
		if cl.Reason == orderpb.DepositChangeReason_DEDUCTION {
			return cl.DestOrderID
		}
	}

	return 0
}
