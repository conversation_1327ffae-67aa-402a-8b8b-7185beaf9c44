package model

import (
	"time"

	"github.com/shopspring/decimal"
	gdecimal "google.golang.org/genproto/googleapis/type/decimal"

	"github.com/MoeGolibrary/go-lib/common/proto/money"
	orderpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/order/v1"
)

type DepositRule struct {
	ID                int64 `gorm:"primaryKey;autoIncrement"`
	Name              string
	Filters           *orderpb.DepositFilters   `gorm:"serializer:proto_json"`
	DepositType       orderpb.DepositAmountType `gorm:"serializer:proto_enum"`
	DepositCurrency   string
	DepositAmount     decimal.Decimal
	DepositPercentage decimal.Decimal
	CompanyID         int64
	BusinessID        int64
	CreateTime        time.Time `gorm:"autoCreateTime"`
	UpdateTime        time.Time `gorm:"autoUpdateTime"`
}

func (r *DepositRule) GetClientGroupFilter() *orderpb.DepositRuleClientGroupFilter {
	for _, filter := range r.Filters.GetFilters() {
		if f := filter.GetClientGroupFilter(); f != nil {
			return f
		}
	}

	return nil
}

func (r *DepositRule) GetServiceFilter() *orderpb.DepositRuleServiceFilter {
	for _, filter := range r.Filters.GetFilters() {
		if f := filter.GetServiceFilter(); f != nil {
			return f
		}
	}

	return nil
}

func (r *DepositRule) GetDateRangeFilter() *orderpb.DepositRuleDateRangeFilter {
	for _, filter := range r.Filters.GetFilters() {
		if f := filter.GetDateRangeFilter(); f != nil {
			return f
		}
	}

	return nil
}

func (r *DepositRule) ToPB() *orderpb.DepositRule {
	ret := &orderpb.DepositRule{
		Id:      r.ID,
		Name:    r.Name,
		Filters: r.Filters,
	}

	switch r.DepositType {
	case orderpb.DepositAmountType_BY_FIXED_AMOUNT:
		ret.DepositConfig = &orderpb.DepositRule_DepositByAmount{
			DepositByAmount: money.FromDecimal(r.DepositAmount, r.DepositCurrency),
		}
	case orderpb.DepositAmountType_BY_PERCENTAGE:
		ret.DepositConfig = &orderpb.DepositRule_DepositByPercentage{
			DepositByPercentage: &gdecimal.Decimal{Value: r.DepositPercentage.String()},
		}
	default:
	}

	return ret
}
