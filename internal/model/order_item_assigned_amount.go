package model

import (
	"database/sql"

	"github.com/shopspring/decimal"

	"github.com/MoeGolibrary/go-lib/common/proto/money"
	orderPb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/order/v1"
)

// OrderItemAssignedAmount 记录订单项的已分配支付金额
type OrderItemAssignedAmount struct {
	ID           int64           `gorm:"primaryKey;column:id"`       // 主键ID
	OrderID      int64           `gorm:"column:order_id;index"`      // 订单ID
	ItemID       int64           `gorm:"column:item_id;index"`       // 订单项ID
	Amount       decimal.Decimal `gorm:"column:amount;type:decimal"` // 已分配金额
	CurrencyCode string          `gorm:"column:currency_code"`       // 货币代码
	BusinessID   int64           `gorm:"column:business_id"`         // 业务ID
	CreatedAt    sql.NullTime    `gorm:"autoCreateTime"`
	UpdatedAt    sql.NullTime    `gorm:"autoUpdateTime"`
}

// TableName 返回数据库表名
func (a *OrderItemAssignedAmount) TableName() string {
	return "order_item_assigned_amounts"
}

// GetAmount 返回已分配金额
func (a *OrderItemAssignedAmount) GetAmount() decimal.Decimal {
	return compatibleWithLegacyZero(a.Amount)
}

func (a *OrderItemAssignedAmount) ToPB() *orderPb.ItemPaidAmountAssignment {
	return &orderPb.ItemPaidAmountAssignment{
		ItemId:             a.ItemID,
		AssignedPaidAmount: money.FromDecimal(a.Amount, a.CurrencyCode),
	}
}
