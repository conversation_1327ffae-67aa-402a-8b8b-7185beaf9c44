package model

import (
	"testing"

	"github.com/shopspring/decimal"
	"github.com/stretchr/testify/suite"

	"github.com/MoeGolibrary/go-lib/common/proto/money"
	orderpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/order/v1"
	ordersvcpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/order/v1"
)

type OrderItemTestSuite struct {
	suite.Suite
}

func (ts *OrderItemTestSuite) TestBuildFullyRefundParam_RefundByQuantity() {
	tests := []struct {
		id                int64
		quantity          int32
		refundedQuantity  int32
		purchasedQuantity int32
		expectedRefundQty int32
	}{
		// Case 1: 全部可退
		{
			id:                1,
			quantity:          5,
			refundedQuantity:  0,
			purchasedQuantity: 0,
			expectedRefundQty: 5,
		},
		// Case 2: 部分可退
		{
			id:                2,
			quantity:          10,
			refundedQuantity:  3,
			purchasedQuantity: 2,
			expectedRefundQty: 5,
		},
		// Case 3: 无可退
		{
			id:                3,
			quantity:          5,
			refundedQuantity:  5,
			purchasedQuantity: 0,
			expectedRefundQty: 0,
		},
	}

	for _, test := range tests {
		orderItem := &OrderItem{
			ID:                test.id,
			Quantity:          test.quantity,
			RefundedQuantity:  test.refundedQuantity,
			PurchasedQuantity: test.purchasedQuantity,
			ItemType:          "product",
		}
		expected := &ordersvcpb.RefundOrderRequest_RefundByItem_RefundItem{
			OrderItemId:    test.id,
			RefundItemMode: orderpb.RefundItemMode_REFUND_ITEM_MODE_BY_QUANTITY,
			RefundItemModeParam: &ordersvcpb.RefundOrderRequest_RefundByItem_RefundItem_RefundQuantity{
				RefundQuantity: int64(test.expectedRefundQty),
			},
		}

		result := orderItem.BuildFullyRefundParam()
		ts.Equal(expected, result)
	}
}

func (ts *OrderItemTestSuite) TestBuildFullyRefundParam_RefundByAmount() {
	tests := []struct {
		id             int64
		quantity       int32
		refundedAmount string
		totalAmount    string
		currencyCode   string
		expectedAmount string
	}{
		// Case 1: 部分退款
		{
			id:             2,
			quantity:       1,
			refundedAmount: "5.00",
			totalAmount:    "15.00",
			currencyCode:   "USD",
			expectedAmount: "10.00",
		},
		// Case 2: 全额退款
		{
			id:             3,
			quantity:       1,
			refundedAmount: "20.00",
			totalAmount:    "20.00",
			currencyCode:   "USD",
			expectedAmount: "0.00",
		},
		// Case 3: 无退款
		{
			id:             4,
			quantity:       1,
			refundedAmount: "0.00",
			totalAmount:    "30.00",
			currencyCode:   "USD",
			expectedAmount: "30.00",
		},
	}

	for _, test := range tests {
		refundedAmount, err := decimal.NewFromString(test.refundedAmount)
		ts.Require().NoError(err)
		totalAmount, err := decimal.NewFromString(test.totalAmount)
		ts.Require().NoError(err)
		expectedAmount, err := decimal.NewFromString(test.expectedAmount)
		ts.Require().NoError(err)

		orderItem := &OrderItem{
			ID:             test.id,
			Quantity:       test.quantity,
			RefundedAmount: refundedAmount,
			TotalAmount:    totalAmount,
			CurrencyCode:   test.currencyCode,
		}
		expected := &ordersvcpb.RefundOrderRequest_RefundByItem_RefundItem{
			OrderItemId:    test.id,
			RefundItemMode: orderpb.RefundItemMode_REFUND_ITEM_MODE_BY_AMOUNT,
			RefundItemModeParam: &ordersvcpb.RefundOrderRequest_RefundByItem_RefundItem_RefundAmount{
				RefundAmount: money.FromDecimal(expectedAmount, test.currencyCode),
			},
		}

		result := orderItem.BuildFullyRefundParam()
		ts.Equal(expected.String(), result.String())
	}
}

func TestOrderItemTestSuite(t *testing.T) {
	suite.Run(t, new(OrderItemTestSuite))
}
