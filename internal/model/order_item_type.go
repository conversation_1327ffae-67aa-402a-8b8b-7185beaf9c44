package model

import (
	orderpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/order/v1"
)

func FormatOrderItemType(itemType orderpb.ItemType) string {
	const unknown = "unknown"

	switch itemType {
	case orderpb.ItemType_ITEM_TYPE_UNSPECIFIED:
		return "unspecified"
	case orderpb.ItemType_ITEM_TYPE_SERVICE:
		return "service"
	case orderpb.ItemType_ITEM_TYPE_PRODUCT:
		return "product"
	case orderpb.ItemType_ITEM_TYPE_PACKAGE:
		return "package"
	case orderpb.ItemType_ITEM_TYPE_NO_SHOW:
		return "noshow"
	case orderpb.ItemType_ITEM_TYPE_SERVICE_CHARGE:
		return "service_charge"
	case orderpb.ItemType_ITEM_TYPE_EVALUATION_SERVICE:
		return "evaluation_service"
	case orderpb.ItemType_ITEM_TYPE_CANCELLATION_FEE:
		return "cancellation_fee"
	case orderpb.ItemType_ITEM_TYPE_MEMBERSHIP_PRODUCT:
		return "membership_product"
	case orderpb.ItemType_ITEM_TYPE_DEPOSIT:
		return "deposit"

	default:
		return unknown
	}
}
