package model

import (
	"github.com/shopspring/decimal"
	googleType "google.golang.org/genproto/googleapis/type/decimal"

	"github.com/MoeGolibrary/go-lib/common/proto/money"
	orderpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/order/v1"
)

const (
	DiscountTypeAmount     = "amount"
	DiscountTypePercentage = "percentage"
	DiscountTypeCredit     = "credit"
)

// copy from com.moego.common.enums.order.LineApplyType
const (
	ApplyTypeAll           = "all"
	ApplyTypeAllNoTip      = "all_no_tip"
	ApplyTypeService       = "service"
	ApplyTypeProduct       = "product"
	ApplyTypePackage       = "package"
	ApplyTypeServiceCharge = "service_charge"
	ApplyTypeItem          = "item"
	ApplyTypeNone          = "none"
)

type OrderLineDiscount struct {
	ID          int64 `gorm:"primaryKey;autoIncrement"`
	BusinessID  int64
	OrderID     int64
	OrderItemID int64

	DiscountCodeID int64
	DiscountType   string
	DiscountAmount decimal.Decimal
	DiscountRate   decimal.Decimal

	ApplyType     string
	ApplyBy       int64
	ApplySequence int32

	IsDeleted  bool
	CreateTime int64 `gorm:"autoCreateTime;serializer:unixtime"`
	UpdateTime int64 `gorm:"autoUpdateTime;serializer:unixtime"`

	// 只在 CreateOrder 的流程中，用于匹配 OrderItem 与 OrderItemDiscount 使用.
	// 不会记录在 LineDiscount 中，从数据库读取的时候也不会获取这个值。
	OrderItemExternalUUID string `gorm:"-"`
}

func (old *OrderLineDiscount) Clone() *OrderLineDiscount {
	cp := *old
	return &cp
}

func (old *OrderLineDiscount) GetDiscountRatePB() *googleType.Decimal {
	return &googleType.Decimal{
		Value: old.DiscountRate.String(),
	}
}

func (old *OrderLineDiscount) GetDiscountAmount() decimal.Decimal {
	if old == nil {
		return decimal.Zero
	}

	return compatibleWithLegacyZero(old.DiscountAmount)
}

func (old *OrderLineDiscount) IsMatchedOrderItem(item *OrderItem) bool {
	if old == nil || item == nil {
		return false
	}

	return old.OrderItemID == item.ID
}

func (old *OrderLineDiscount) ToPB(currencyCode string) *orderpb.OrderLineDiscountModelV1 {
	return &orderpb.OrderLineDiscountModelV1{
		Id:             old.ID,
		BusinessId:     old.BusinessID,
		OrderId:        old.OrderID,
		OrderItemId:    old.OrderItemID,
		ApplyType:      old.ApplyType,
		IsDeleted:      old.IsDeleted,
		DiscountType:   old.DiscountType,
		DiscountAmount: money.FromDecimal(old.DiscountAmount, currencyCode),
		DiscountRate:   old.GetDiscountRatePB(),
		ApplyBy:        old.ApplyBy,
		ApplySequence:  old.ApplySequence,
		CreateTime:     old.CreateTime,
		UpdateTime:     old.UpdateTime,
		DiscountCodeId: old.DiscountCodeID,
	}
}
