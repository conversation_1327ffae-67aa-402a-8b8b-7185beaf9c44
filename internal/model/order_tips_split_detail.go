package model

import (
	"database/sql"
)

type OrderTipsSplitRecord struct {
	ID               int64        `gorm:"primaryKey;autoIncrement"`
	CreateTime       sql.NullTime `gorm:"autoCreateTime"`
	UpdateTime       sql.NullTime `gorm:"autoUpdateTime"`
	OrderID          int64
	SplitMethod      int32
	CustomizedType   int32
	CustomizedConfig string
	BusinessID       int64
	ApplyBy          int64
	IsDeleted        bool
}
