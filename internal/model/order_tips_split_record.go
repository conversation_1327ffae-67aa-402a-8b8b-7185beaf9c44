package model

import (
	"database/sql"

	"github.com/shopspring/decimal"
)

type OrderTipsSplitDetail struct {
	ID               int64        `gorm:"primaryKey;autoIncrement"`
	CreateTime       sql.NullTime `gorm:"autoCreateTime"`
	UpdateTime       sql.NullTime `gorm:"autoUpdateTime"`
	OrderID          int64
	StaffID          int64
	CommissionAmount decimal.Decimal
	CommissionRate   decimal.Decimal
	TotalAmount      decimal.Decimal
	RefundedAmount   decimal.Decimal
	SplitAmount      decimal.Decimal
	SplitRate        decimal.Decimal
	Extra            string
}
