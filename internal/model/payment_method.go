package model

import (
	"fmt"
	"strings"

	paymentpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/payment/v1"
)

type PaymentMethod struct {
	ID     int64              `gorm:"column:payment_method_id"`
	Method string             `gorm:"column:payment_method"`
	Extra  PaymentMethodExtra `gorm:"column:payment_method_extra;serializer:json"`
	Vendor string             `gorm:"column:payment_method_vendor"`
}

func (pm *PaymentMethod) IsCreditCard() bool { return pm.ID == embeddedMethodCreditCard.ID() }

func (pm *PaymentMethod) GetDisplayName() string {
	switch pm.ID {
	case embeddedMethodCreditCard.ID():
		return pm.Extra.buildCreditCardName()

	case embeddedMethodCheck.ID():
		return pm.Extra.buildCheckName()

	default:
		return pm.Method
	}
}

type PaymentMethodExtra struct {
	CardFunding         string              `json:"cardFunding,omitempty"`
	CardType            string              `json:"cardType,omitempty"`
	CardNumber          string              `json:"cardNumber,omitempty"`
	CheckNumber         string              `json:"checkNumber,omitempty"`
	DeviceID            string              `json:"deviceID,omitempty"`
	ExpMonth            int32               `json:"expMonth,omitempty"`
	ExpYear             int32               `json:"expYear,omitempty"`
	Merchant            string              `json:"merchant,omitempty"`
	Signature           string              `json:"signature,omitempty"`
	StripeClientSecret  string              `json:"stripeClientSecret,omitempty"`
	StripeChargeID      string              `json:"stripeChargeID,omitempty"`
	StripeIntentID      string              `json:"stripeIntentID,omitempty"`
	StripePaymentMethod StripePaymentMethod `json:"stripePaymentMethod,omitempty"`
	SquareCheckoutID    string              `json:"squareCheckoutID,omitempty"`
	SquareCustomerID    string              `json:"squareCustomerID,omitempty"`
	SquarePaymentMethod SquarePaymentMethod `json:"squarePaymentMethod,omitempty"`
}

func (pme *PaymentMethodExtra) buildCheckName() string {
	if pme == nil {
		return ""
	}

	if pme.CheckNumber == "" {
		return embeddedMethodCheck.Name()
	}

	return fmt.Sprintf("%s %s", embeddedMethodCheck.Name(), pme.CheckNumber)
}

func (pme *PaymentMethodExtra) buildCreditCardName() string {
	if pme == nil {
		return ""
	}

	builder := strings.Builder{}

	if strings.EqualFold(pme.CardFunding, "debit") {
		builder.WriteString("Debit card")
	} else {
		builder.WriteString(embeddedMethodCreditCard.Name())
	}

	builder.WriteString(" - ")

	if pme.SquarePaymentMethod > 0 {
		builder.WriteString(pme.SquarePaymentMethod.Name())
	} else {
		builder.WriteString(pme.StripePaymentMethod.Name())
	}

	if pme.CardNumber != "" {
		builder.WriteString(pme.CardNumber)
	}

	if pme.CardType != "" {
		builder.WriteString("(" + pme.CardType + ")")
	}

	return builder.String()
}

func (pme *PaymentMethodExtra) ToPB() *paymentpb.PaymentMethodExtra {
	return &paymentpb.PaymentMethodExtra{
		Extra: &paymentpb.PaymentMethodExtra_Legacy_{
			Legacy: &paymentpb.PaymentMethodExtra_Legacy{
				CardFunding:         pme.CardFunding,
				CardType:            pme.CardType,
				CardNumber:          pme.CardNumber,
				CheckNumber:         pme.CheckNumber,
				DeviceId:            pme.DeviceID,
				ExpMonth:            pme.ExpMonth,
				ExpYear:             pme.ExpYear,
				Merchant:            pme.Merchant,
				Signature:           pme.Signature,
				StripeClientSecret:  pme.StripeClientSecret,
				StripeChargeId:      pme.StripeChargeID,
				StripeIntentId:      pme.StripeIntentID,
				StripePaymentMethod: pme.StripePaymentMethod.ToPB(),
				SquareCheckoutId:    pme.SquareCheckoutID,
				SquareCustomerId:    pme.SquareCustomerID,
				SquarePaymentMethod: pme.SquarePaymentMethod.ToPB(),
			},
		},
	}
}
