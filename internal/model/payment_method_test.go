package model

import (
	"testing"

	"github.com/stretchr/testify/suite"
)

type paymentMethodTestSuite struct {
	suite.Suite
}

func TestPaymentMethod(t *testing.T) {
	suite.Run(t, new(paymentMethodTestSuite))
}

func (ts *paymentMethodTestSuite) TestGetDisplayName() {
	for expected, paymentMethod := range map[string]*PaymentMethod{
		"Debit card - card2333(visa)": {
			ID:     embeddedMethodCreditCard.ID(),
			Method: embeddedMethodCreditCard.Name(),
			Extra: PaymentMethodExtra{
				CardFunding:         "debit",
				CardNumber:          "2333",
				CardType:            "visa",
				StripePaymentMethod: stripePaymentMethodCard,
			},
		},

		"Credit card - Square card2333(visa)": {
			ID:     embeddedMethodCreditCard.ID(),
			Method: embeddedMethodCreditCard.Name(),
			Extra: PaymentMethodExtra{
				CardFunding:         "",
				CardNumber:          "2333",
				CardType:            "visa",
				SquarePaymentMethod: squarePaymentMethodCard,
			},
		},
	} {
		ts.Equal(expected, paymentMethod.GetDisplayName())
	}
}
