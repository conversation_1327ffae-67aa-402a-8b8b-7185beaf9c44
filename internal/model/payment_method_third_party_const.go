package model

type SquarePaymentMethod int32

const (
	squarePaymentMethodCard SquarePaymentMethod = iota + 1
	squarePaymentMethodCOF
	squarePaymentMethodTerminal
	squarePaymentMethodReader
	squarePaymentMethodPOS
)

func (spm SquarePaymentMethod) ToPB() int32 { return int32(spm) }

func (spm SquarePaymentMethod) Name() string {
	switch spm {
	case squarePaymentMethodCard:
		return "Square card"

	case squarePaymentMethodCOF:
		return "Square COF"

	case squarePaymentMethodTerminal:
		return "Square terminal"

	case squarePaymentMethodReader:
		return "Square reader"

	case squarePaymentMethodPOS:
		return "Square POS"

	default:
		return "Square Unknown"
	}
}

type StripePaymentMethod int32

const (
	stripePaymentMethodCard StripePaymentMethod = iota + 1
	stripePaymentMethodCOF
	stripePaymentMethodBluetoothReader
	stripePaymentMethodSmartReader
	stripePaymentMethodApplePay
	stripePaymentMethodGooglePay
	stripePaymentMethodTapToPayIPhone
	stripePaymentMethodACH
)

func (spm StripePaymentMethod) ToPB() int32 { return int32(spm) }

func (spm StripePaymentMethod) Name() string {
	switch spm {
	case stripePaymentMethodCard:
		return "card"

	case stripePaymentMethodCOF:
		return "COF"

	case stripePaymentMethodBluetoothReader:
		return "Bluetooth reader"

	case stripePaymentMethodSmartReader:
		return "Smart reader"

	case stripePaymentMethodApplePay:
		return "Apple Pay"

	case stripePaymentMethodGooglePay:
		return "Google Pay"

	case stripePaymentMethodTapToPayIPhone:
		return "Tap To Pay on iPhone"

	case stripePaymentMethodACH:
		return "ACH"

	default:
		return "Unknown Stripe"
	}
}
