package model

import (
	"time"

	"github.com/samber/lo"
	"github.com/shopspring/decimal"
	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/MoeGolibrary/go-lib/common/proto/money"
	orderpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/order/v1"
	promotionpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/promotion/v1"
	ordersvcpb2 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/order/v2"
)

const (
	StoreCreditSourceID     = -1
	OneTimeDiscountSourceID = 0
)

type OrderPromotion struct {
	ID              int64                              `gorm:"primaryKey;autoIncrement"`
	CreateTime      time.Time                          `gorm:"column:create_time"`
	UpdateTime      time.Time                          `gorm:"column:update_time"`
	OrderID         int64                              `gorm:"column:order_id"`
	PromotionID     int64                              `gorm:"column:promotion_id"`
	SourceType      promotionpb.Source_Type            `gorm:"column:source_type;serializer:proto_enum"`
	SourceID        int64                              `gorm:"column:source_id"`
	SourceSubjectID int64                              `gorm:"column:source_subject_id"`
	Name            string                             `gorm:"column:name"`
	DiscountType    orderpb.DiscountType               `gorm:"column:discount_type;serializer:proto_enum"`
	DiscountValue   decimal.Decimal                    `gorm:"column:discount_value"`
	AppliedAmount   decimal.Decimal                    `gorm:"column:applied_amount"`
	Status          orderpb.OrderPromotionModel_Status `gorm:"column:status;serializer:proto_enum"`

	PromotionItems []*OrderPromotionItem `gorm:"-"`
}

func (op *OrderPromotion) IsOneTimeDiscount() bool {
	return op.SourceType == promotionpb.Source_DISCOUNT &&
		op.SourceID == OneTimeDiscountSourceID
}

func (op *OrderPromotion) IsStoreCredit() bool {
	return op.SourceType == promotionpb.Source_DISCOUNT &&
		op.SourceID == StoreCreditSourceID
}

func (op *OrderPromotion) IsMembership() bool {
	return op.SourceType == promotionpb.Source_MEMBERSHIP_DISCOUNT ||
		op.SourceType == promotionpb.Source_MEMBERSHIP_QUANTITY
}

func (op *OrderPromotion) IsDiscount() bool {
	return op.SourceType == promotionpb.Source_DISCOUNT &&
		op.SourceID != OneTimeDiscountSourceID &&
		op.SourceID != StoreCreditSourceID
}

func (op *OrderPromotion) IsPackage() bool {
	return op.SourceType == promotionpb.Source_PACKAGE
}

func (op *OrderPromotion) IsDeduction() bool {
	return op.SourceType == promotionpb.Source_PACKAGE ||
		op.SourceType == promotionpb.Source_MEMBERSHIP_QUANTITY
}

func (op *OrderPromotion) ToPB() *orderpb.OrderPromotionModel {
	if op == nil {
		return nil
	}

	model := &orderpb.OrderPromotionModel{
		Id:            op.ID,
		CreateTime:    timestamppb.New(op.CreateTime),
		UpdateTime:    timestamppb.New(op.UpdateTime),
		OrderId:       op.OrderID,
		PromotionId:   op.PromotionID,
		DiscountType:  op.DiscountType,
		DiscountValue: ToGoogleDecimal(op.DiscountValue),
		AppliedAmount: money.FromDecimal(op.AppliedAmount, ""),
		Status:        op.Status,
		Items: lo.Map(op.PromotionItems, func(it *OrderPromotionItem, _ int) *orderpb.OrderPromotionItem {
			return it.ToPB()
		}),
	}

	switch {
	case op.IsOneTimeDiscount():
		if op.DiscountType == orderpb.DiscountType_FIXED_AMOUNT {
			model.Promotion = &orderpb.OrderPromotionModel_OneTimeDiscount{
				OneTimeDiscount: &orderpb.OneTimeDiscountCode{
					DiscountDetail: &orderpb.OneTimeDiscountCode_DiscountAmount{
						DiscountAmount: money.FromDecimal(op.DiscountValue, ""),
					},
				},
			}
		}

		if op.DiscountType == orderpb.DiscountType_PERCENTAGE {
			model.Promotion = &orderpb.OrderPromotionModel_OneTimeDiscount{
				OneTimeDiscount: &orderpb.OneTimeDiscountCode{
					DiscountDetail: &orderpb.OneTimeDiscountCode_DiscountPercentage{
						DiscountPercentage: ToGoogleDecimal(op.DiscountValue),
					},
				},
			}
		}
	case op.IsStoreCredit():
		model.Promotion = &orderpb.OrderPromotionModel_StoreCredit{
			StoreCredit: &orderpb.OrderPromotionModel_StoreCreditSubject{
				Amount: money.FromDecimal(op.DiscountValue, ""),
			},
		}
	default:
		switch op.SourceType {
		case promotionpb.Source_DISCOUNT:
			model.Promotion = &orderpb.OrderPromotionModel_Discount{
				Discount: &orderpb.OrderPromotionModel_DiscountSubject{
					Id:            op.SourceID,
					SubjectId:     op.SourceSubjectID,
					DiscountCode:  op.Name,
					DiscountType:  op.DiscountType,
					DiscountValue: ToGoogleDecimal(op.DiscountValue),
				},
			}
		case promotionpb.Source_PACKAGE:
			model.Promotion = &orderpb.OrderPromotionModel_Package{
				Package: &orderpb.OrderPromotionModel_PackageSubject{
					Id:          op.SourceID,
					SubjectId:   op.SourceSubjectID,
					PackageName: op.Name,
				},
			}
		case promotionpb.Source_MEMBERSHIP_DISCOUNT, promotionpb.Source_MEMBERSHIP_QUANTITY:
			model.Promotion = &orderpb.OrderPromotionModel_Membership{
				Membership: &orderpb.OrderPromotionModel_MembershipSubject{
					Id:            op.SourceID,
					SubjectId:     op.SourceSubjectID,
					DiscountType:  op.DiscountType,
					DiscountValue: ToGoogleDecimal(op.DiscountValue),
					Name:          op.Name,
				},
			}
		default:
		}
	}

	return model
}

func (op *OrderPromotion) ToCreateReqPromotionPB() *ordersvcpb2.PreviewCreateOrderRequest_Promotion {
	externalUUIDs := lo.Map(op.PromotionItems, func(pi *OrderPromotionItem, _ int) string {
		return pi.ExternalUUID
	})

	switch op.SourceType {
	case promotionpb.Source_DISCOUNT:
		switch op.SourceID {
		case OneTimeDiscountSourceID:
			if op.DiscountType == orderpb.DiscountType_FIXED_AMOUNT {
				return &ordersvcpb2.PreviewCreateOrderRequest_Promotion{
					CartItemExternalUuids: externalUUIDs,
					Promotion: &ordersvcpb2.PreviewCreateOrderRequest_Promotion_OneTimeDiscount{
						OneTimeDiscount: &orderpb.OneTimeDiscountCode{
							DiscountDetail: &orderpb.OneTimeDiscountCode_DiscountAmount{
								DiscountAmount: money.FromDecimal(op.DiscountValue, ""),
							},
						},
					},
				}
			}

			return &ordersvcpb2.PreviewCreateOrderRequest_Promotion{
				CartItemExternalUuids: externalUUIDs,
				Promotion: &ordersvcpb2.PreviewCreateOrderRequest_Promotion_OneTimeDiscount{
					OneTimeDiscount: &orderpb.OneTimeDiscountCode{
						DiscountDetail: &orderpb.OneTimeDiscountCode_DiscountPercentage{
							DiscountPercentage: ToGoogleDecimal(op.DiscountValue),
						},
					},
				},
			}
		case StoreCreditSourceID:
			return &ordersvcpb2.PreviewCreateOrderRequest_Promotion{
				CartItemExternalUuids: externalUUIDs,
				Promotion: &ordersvcpb2.PreviewCreateOrderRequest_Promotion_StoreCredit{
					StoreCredit: money.FromDecimal(op.DiscountValue, ""),
				},
			}
		default:
			return &ordersvcpb2.PreviewCreateOrderRequest_Promotion{
				CartItemExternalUuids: externalUUIDs,
				Promotion: &ordersvcpb2.PreviewCreateOrderRequest_Promotion_CouponSource{
					CouponSource: &promotionpb.Source{
						Type: promotionpb.Source_DISCOUNT,
						Id:   op.SourceID,
						Subject: &promotionpb.Source_Discount{
							Discount: &promotionpb.DiscountSubject{
								Id: op.SourceSubjectID,
							},
						},
					},
				},
			}
		}

	case promotionpb.Source_PACKAGE:
		return &ordersvcpb2.PreviewCreateOrderRequest_Promotion{
			CartItemExternalUuids: externalUUIDs,
			Promotion: &ordersvcpb2.PreviewCreateOrderRequest_Promotion_CouponSource{
				CouponSource: &promotionpb.Source{
					Type: promotionpb.Source_PACKAGE,
					Id:   op.SourceID,
					Subject: &promotionpb.Source_Package{
						Package: &promotionpb.PackageSubject{
							Id: op.SourceSubjectID,
						},
					},
				},
			},
		}

	case promotionpb.Source_MEMBERSHIP_DISCOUNT, promotionpb.Source_MEMBERSHIP_QUANTITY:
		return &ordersvcpb2.PreviewCreateOrderRequest_Promotion{
			CartItemExternalUuids: externalUUIDs,
			Promotion: &ordersvcpb2.PreviewCreateOrderRequest_Promotion_CouponSource{
				CouponSource: &promotionpb.Source{
					Type: op.SourceType,
					Id:   op.SourceID,
					Subject: &promotionpb.Source_Membership{
						Membership: &promotionpb.MembershipSubject{
							Id: op.SourceSubjectID,
						},
					},
				},
			},
		}
	default:
		return nil
	}
}

type OrderPromotionItem struct {
	ID               int64           `gorm:"primaryKey;autoIncrement"`
	CreateTime       time.Time       `gorm:"column:create_time"`
	UpdateTime       time.Time       `gorm:"column:update_time"`
	OrderPromotionID int64           `gorm:"column:order_promotion_id"`
	OrderItemID      int64           `gorm:"column:order_item_id"`
	AppliedAmount    decimal.Decimal `gorm:"column:applied_amount"`
	DeductQuantity   int32           `gorm:"column:deduct_quantity"`

	CartItemType   orderpb.ItemType `gorm:"-"`
	CartItemID     int64            `gorm:"-"`
	ExternalUUID   string           `gorm:"-"`
	UnitPrice      decimal.Decimal  `gorm:"-"`
	Subtotal       decimal.Decimal  `gorm:"-"`
	DiscountCodeID int64            `gorm:"-"`
}

func (opi *OrderPromotionItem) ToPB() *orderpb.OrderPromotionItem {
	if opi == nil {
		return nil
	}

	return &orderpb.OrderPromotionItem{
		Id:               opi.ID,
		CreateTime:       timestamppb.New(opi.CreateTime),
		UpdateTime:       timestamppb.New(opi.UpdateTime),
		OrderPromotionId: opi.OrderPromotionID,
		OrderItemId:      opi.OrderItemID,
		AppliedAmount:    money.FromDecimal(opi.AppliedAmount, ""),
		ExternalUuid:     opi.ExternalUUID,
	}
}

// 转换 CouponUsage 到 OrderPromotion
func ConvertCouponUsageToOrderPromotion(cu *promotionpb.CouponUsage) *OrderPromotion {
	if cu == nil {
		return nil
	}

	discountType := orderpb.DiscountType_DISCOUNT_TYPE_UNSPECIFIED
	discountValue := decimal.Zero

	switch {
	case cu.GetCoupon().GetDiscount().GetDeduction() > 0:
		discountType = orderpb.DiscountType_ITEM_DEDUCTION
		discountValue = decimal.NewFromInt(cu.GetCoupon().GetDiscount().GetDeduction())
	case cu.GetCoupon().GetDiscount().GetFixedAmount() != nil:
		discountType = orderpb.DiscountType_FIXED_AMOUNT
		discountValue = money.ToDecimal(cu.GetCoupon().GetDiscount().GetFixedAmount())
	case cu.GetCoupon().GetDiscount().GetPercentage() != nil:
		discountType = orderpb.DiscountType_PERCENTAGE
		discountValue = ConvertGoogleDecimal(cu.GetCoupon().GetDiscount().GetPercentage())
	}

	return &OrderPromotion{
		SourceType:    cu.GetCoupon().GetSource().GetType(),
		SourceID:      cu.GetCoupon().GetSource().GetId(),
		DiscountType:  discountType,
		DiscountValue: discountValue,
		PromotionItems: lo.Map(cu.GetTargets(), func(
			cat *promotionpb.CouponApplicationTarget, _ int,
		) *OrderPromotionItem {
			return &OrderPromotionItem{
				CartItemType: ConvertTargetType2ItemType(cat.GetTargetType()),
				CartItemID:   cat.GetTargetId(),
			}
		}),
	}
}

// redeem 接口的参数
type RedeemPromotionParams struct {
	OrderID          int64
	CustomerID       int64
	AppointmentID    int64
	OrderTotalAmount decimal.Decimal
	OrderPromotions  []*OrderPromotion
}

func ConvertTargetType2ItemType(targetType promotionpb.TargetType) orderpb.ItemType {
	switch targetType {
	case promotionpb.TargetType_SERVICE:
		return orderpb.ItemType_ITEM_TYPE_SERVICE
	case promotionpb.TargetType_PRODUCT:
		return orderpb.ItemType_ITEM_TYPE_PRODUCT
	case promotionpb.TargetType_SERVICE_CHARGE:
		return orderpb.ItemType_ITEM_TYPE_SERVICE_CHARGE
	default:
		return orderpb.ItemType_ITEM_TYPE_UNSPECIFIED
	}
}

func ConvertItemType2TargetType(itemType orderpb.ItemType) promotionpb.TargetType {
	switch itemType {
	case orderpb.ItemType_ITEM_TYPE_PRODUCT:
		return promotionpb.TargetType_PRODUCT
	case orderpb.ItemType_ITEM_TYPE_SERVICE:
		return promotionpb.TargetType_SERVICE
	case orderpb.ItemType_ITEM_TYPE_SERVICE_CHARGE:
		return promotionpb.TargetType_SERVICE_CHARGE
	default:
		return promotionpb.TargetType_TARGET_TYPE_UNSPECIFIED
	}
}
