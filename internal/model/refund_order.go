package model

import (
	"time"

	"github.com/samber/lo"
	"github.com/shopspring/decimal"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	"github.com/MoeGolibrary/go-lib/common/proto/money"
	orderpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/order/v1"
)

type RefundOrder struct {
	ID      int64 `gorm:"primaryKey;autoIncrement"`
	OrderID int64 // 关联的订单的 ID，不是源单.

	CompanyID  int64
	BusinessID int64
	StaffID    int64 // 发起退款的 Staff ID
	CustomerID int64

	RefundMode          orderpb.RefundMode `gorm:"serializer:proto_enum"`
	RefundReason        string
	OrderStatusSnapshot orderpb.OrderStatus `gorm:"serializer:proto_enum"` // RefundOrder 创建时，订单的状态.

	CurrencyCode              string
	RefundTotalAmount         decimal.Decimal
	RefundItemSubTotal        decimal.Decimal
	RefundDiscountAmount      decimal.Decimal
	RefundDepositAmount       decimal.Decimal
	RefundTipsAmount          decimal.Decimal
	RefundDepositToTipsAmount decimal.Decimal // 注意这里是加了 Refund 前缀来对应 Order 的 DepositToTipsAmount 字段
	RefundConvenienceFee      decimal.Decimal
	RefundTaxAmount           decimal.Decimal

	RefundOrderStatus orderpb.RefundOrderStatus `gorm:"serializer:proto_enum"`
	CreateTime        int64                     `gorm:"type:time;autoCreateTime;serializer:unixtime"`
	RefundTime        int64                     `gorm:"type:time;serializer:unixtime"`
	UpdateTime        int64                     `gorm:"serializer:unixtime"`
}

func (rod *RefundOrder) ToPB() *orderpb.RefundOrderModel {
	return &orderpb.RefundOrderModel{
		Id:                      rod.ID,
		OrderId:                 rod.OrderID,
		CompanyId:               rod.CompanyID,
		BusinessId:              rod.BusinessID,
		CustomerId:              rod.CustomerID,
		StaffId:                 rod.StaffID,
		RefundMode:              rod.RefundMode,
		RefundReason:            rod.RefundReason,
		OrderStatusSnapshot:     rod.OrderStatusSnapshot,
		CurrencyCode:            rod.CurrencyCode,
		RefundTotalAmount:       money.FromDecimal(rod.RefundTotalAmount, rod.CurrencyCode),
		RefundItemSubTotal:      money.FromDecimal(rod.RefundItemSubTotal, rod.CurrencyCode),
		RefundItemDiscountTotal: money.FromDecimal(rod.RefundDiscountAmount, rod.CurrencyCode),
		RefundDeposit:           money.FromDecimal(rod.RefundDepositAmount, rod.CurrencyCode),
		RefundTips:              money.FromDecimal(rod.RefundTipsAmount, rod.CurrencyCode),
		RefundDepositToTips:     money.FromDecimal(rod.RefundDepositToTipsAmount, rod.CurrencyCode),
		RefundConvenienceFee:    money.FromDecimal(rod.RefundConvenienceFee, rod.CurrencyCode),
		RefundTaxFee:            money.FromDecimal(rod.RefundTaxAmount, rod.CurrencyCode),
		RefundOrderStatus:       rod.RefundOrderStatus,
		CreateTime:              rod.CreateTime,
		RefundTime:              rod.RefundTime,
		UpdateTime:              rod.UpdateTime,
	}
}

func (rod *RefundOrder) GetRefundTotalAmount() decimal.Decimal {
	return compatibleWithLegacyZero(rod.RefundTotalAmount)
}

func (rod *RefundOrder) GetRefundItemSubTotal() decimal.Decimal {
	return compatibleWithLegacyZero(rod.RefundItemSubTotal)
}

func (rod *RefundOrder) GetRefundDiscountAmount() decimal.Decimal {
	return compatibleWithLegacyZero(rod.RefundDiscountAmount)
}

func (rod *RefundOrder) GetRefundTipsAmount() decimal.Decimal {
	return compatibleWithLegacyZero(rod.RefundTipsAmount)
}

func (rod *RefundOrder) GetRefundConvenienceFee() decimal.Decimal {
	return compatibleWithLegacyZero(rod.RefundConvenienceFee)
}

func (rod *RefundOrder) GetRefundTaxAmount() decimal.Decimal {
	return compatibleWithLegacyZero(rod.RefundTaxAmount)
}

func (rod *RefundOrder) IsCreatedAfterCompleted() bool {
	if rod == nil {
		return false
	}

	// 先严格判定 Complete，暂时忽略 Remove 状态.
	return rod.OrderStatusSnapshot == orderpb.OrderStatus_COMPLETED
}

func (rod *RefundOrder) MarkCompleted() (bool, error) {
	if rod == nil {
		return false, nil
	}

	switch rod.RefundOrderStatus {
	case orderpb.RefundOrderStatus_REFUND_ORDER_STATUS_CREATED,
		orderpb.RefundOrderStatus_REFUND_ORDER_STATUS_TRANSACTION_CREATED:
		rod.RefundOrderStatus = orderpb.RefundOrderStatus_REFUND_ORDER_STATUS_COMPLETED
		rod.RefundTime = time.Now().Unix()

		return true, nil

	case orderpb.RefundOrderStatus_REFUND_ORDER_STATUS_COMPLETED:
		// 已经更新，不再重复.
		return false, nil

	default:
		return false, status.Error(codes.Internal, "unknown refund order status")
	}
}

type RefundOrderDetail struct {
	RefundOrder         *RefundOrder
	RefundOrderItems    []*RefundOrderItem
	RefundOrderPayments []*RefundOrderPayment
	// 如果本次退款是对未抵扣的 deposit 的退款，则需要更新 change log
	ReversalDepositChangeLog *DepositChangeLog
}

func (rod *RefundOrderDetail) IsCreatedAfterCompleted() bool {
	if rod == nil {
		return false
	}

	return rod.RefundOrder.IsCreatedAfterCompleted()
}

func (rod *RefundOrderDetail) ToPB() *orderpb.RefundOrderDetailModel {
	return &orderpb.RefundOrderDetailModel{
		RefundOrder: rod.RefundOrder.ToPB(),
		RefundOrderItems: lo.Map(
			rod.RefundOrderItems,
			func(item *RefundOrderItem, _ int) *orderpb.RefundOrderItemModel { return item.ToPB() },
		),
		RefundOrderPayments: lo.Map(
			rod.RefundOrderPayments,
			func(item *RefundOrderPayment, _ int) *orderpb.RefundOrderPaymentModel { return item.ToPB() },
		),
	}
}
