package model

import (
	"strings"
	"time"

	"github.com/shopspring/decimal"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	"github.com/MoeGolibrary/go-lib/common/proto/money"
	orderpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/order/v1"
	paymentpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/payment/v1"
)

type RefundOrderPayment struct {
	ID             int64 `gorm:"primaryKey;autoIncrement"`
	OrderID        int64
	OrderPaymentID int64
	RefundOrderID  int64
	// 关联的 RefundPayment 的 ID，在进入 TransactionCreated 状态时写入.
	RefundPaymentID int64

	CompanyID  int64
	BusinessID int64
	// 发起退款的 Staff ID.
	StaffID    int64
	CustomerID int64

	// RefundOrder 创建时，订单的状态.
	// 继承自对应的 RefundOrder.
	RefundPaymentMethod RefundPaymentMethod `gorm:"embedded"`

	CurrencyCode         string
	RefundAmount         decimal.Decimal
	RefundConvenienceFee decimal.Decimal
	Description          string
	// 退款状态.
	RefundStatus orderpb.RefundOrderPaymentStatus `gorm:"serializer:proto_enum"`
	// 结合 RefundPaymentStatus 使用：
	// Cancelled → 取消原因
	// Failed → 失败原因
	RefundStatusReason string

	CreateTime int64 `gorm:"autoCreateTime;serializer:unixtime"`
	RefundTime int64 `gorm:"serializer:unixtime"`
	FailTime   int64 `gorm:"serializer:unixtime"`
	UpdateTime int64 `gorm:"autoUpdateTime;serializer:unixtime"`
}

func (rop *RefundOrderPayment) IsStatusReasonInsufficient() bool {
	return strings.Contains(strings.ToLower(rop.RefundStatusReason), "insufficient")
}

func (rop *RefundOrderPayment) ToPB() *orderpb.RefundOrderPaymentModel {
	return &orderpb.RefundOrderPaymentModel{
		Id:                             rop.ID,
		OrderId:                        rop.OrderID,
		OrderPaymentId:                 rop.OrderPaymentID,
		RefundOrderId:                  rop.RefundOrderID,
		RefundPaymentId:                rop.RefundPaymentID,
		CompanyId:                      rop.CompanyID,
		BusinessId:                     rop.BusinessID,
		CustomerId:                     rop.CustomerID,
		StaffId:                        rop.StaffID,
		RefundPaymentMethodId:          rop.RefundPaymentMethod.ID,
		RefundPaymentMethod:            rop.RefundPaymentMethod.Method,
		RefundPaymentMethodDisplayName: rop.RefundPaymentMethod.GetDisplayName(),
		RefundPaymentMethodExtra:       rop.RefundPaymentMethod.Extra.ToPB(),
		RefundPaymentMethodVendor:      rop.RefundPaymentMethod.Vendor,
		CurrencyCode:                   rop.CurrencyCode,
		RefundTotalAmount:              money.FromDecimal(rop.RefundAmount, rop.CurrencyCode),
		RefundPaymentConvenienceFee:    money.FromDecimal(rop.RefundConvenienceFee, rop.CurrencyCode),
		RefundOrderPaymentStatus:       rop.RefundStatus,
		PaymentStatusReason:            rop.RefundStatusReason,
		CreateTime:                     rop.CreateTime,
		RefundTime:                     rop.RefundTime,
		UpdateTime:                     rop.UpdateTime,
	}
}

func (rop *RefundOrderPayment) GetRefundAmount() decimal.Decimal {
	return compatibleWithLegacyZero(rop.RefundAmount)
}

func (rop *RefundOrderPayment) GetRefundConvenienceFee() decimal.Decimal {
	return compatibleWithLegacyZero(rop.RefundConvenienceFee)
}

type RefundPaymentor interface {
	GetID() int64
	GetRefundStatus() orderpb.RefundOrderPaymentStatus
	GetError() string
	GetRefundOrderPaymentID() int64
}

func (rop *RefundOrderPayment) ApplyRefundPayment(rp RefundPaymentor) (bool, error) {
	if rop == nil || rp == nil {
		return false, nil
	}

	if rop.ID != rp.GetRefundOrderPaymentID() {
		return false, status.Error(codes.InvalidArgument, "refund order payment id not matched")
	}

	rop.RefundPaymentID = rp.GetID()

	switch rp.GetRefundStatus() {
	case orderpb.RefundOrderPaymentStatus_REFUND_ORDER_PAYMENT_STATUS_CREATED:
		// Refund Payment 已创建, Refund Order Payment 推进到 TransactionCreated.
		if rop.RefundStatus != orderpb.RefundOrderPaymentStatus_REFUND_ORDER_PAYMENT_STATUS_CREATED {
			// 尝试 Apply 的 RefundPayment 过时了.
			return false, status.Error(codes.FailedPrecondition, "refund payment outdated")
		}

		if rop.RefundStatus == orderpb.RefundOrderPaymentStatus_REFUND_ORDER_PAYMENT_STATUS_TRANSACTION_CREATED {
			// 已经更新过了, 不重复更新.
			return false, nil
		}

		rop.RefundStatus = orderpb.RefundOrderPaymentStatus_REFUND_ORDER_PAYMENT_STATUS_TRANSACTION_CREATED

		return true, nil

	case orderpb.RefundOrderPaymentStatus_REFUND_ORDER_PAYMENT_STATUS_REFUNDED:
		if rop.RefundStatus != orderpb.RefundOrderPaymentStatus_REFUND_ORDER_PAYMENT_STATUS_CREATED &&
			rop.RefundStatus != orderpb.RefundOrderPaymentStatus_REFUND_ORDER_PAYMENT_STATUS_TRANSACTION_CREATED {
			// 尝试 Apply 的 RefundPayment 过时了.
			return false, status.Error(codes.FailedPrecondition, "refund payment outdated")
		}

		// Refund Payment 已完成退款, Refund Order Payment 也推进到完成.
		if rop.RefundStatus == orderpb.RefundOrderPaymentStatus_REFUND_ORDER_PAYMENT_STATUS_REFUNDED {
			// 已经更新过了, 不重复更新.
			return false, nil
		}

		rop.RefundStatus = orderpb.RefundOrderPaymentStatus_REFUND_ORDER_PAYMENT_STATUS_REFUNDED
		rop.RefundStatusReason = ""
		rop.RefundTime = time.Now().Unix()

		return true, nil

	case orderpb.RefundOrderPaymentStatus_REFUND_ORDER_PAYMENT_STATUS_FAILED:
		if rop.RefundStatus != orderpb.RefundOrderPaymentStatus_REFUND_ORDER_PAYMENT_STATUS_CREATED &&
			rop.RefundStatus != orderpb.RefundOrderPaymentStatus_REFUND_ORDER_PAYMENT_STATUS_TRANSACTION_CREATED {
			// 尝试 Apply 的 RefundPayment 过时了.
			return false, status.Error(codes.FailedPrecondition, "refund payment outdated")
		}

		if rop.RefundStatus == orderpb.RefundOrderPaymentStatus_REFUND_ORDER_PAYMENT_STATUS_FAILED {
			// 已经更新过了, 不重复更新.
			return false, nil
		}

		// Refund Payment 退款失败, Refund Order Payment 推进到失败，并且同步失败原因.
		rop.RefundStatus = orderpb.RefundOrderPaymentStatus_REFUND_ORDER_PAYMENT_STATUS_FAILED
		rop.RefundStatusReason = rp.GetError()
		rop.FailTime = time.Now().Unix()

		return true, nil

	default:
		return false, status.Error(codes.Internal, "unknown refund payment status")
	}
}

type RefundPaymentMethod struct {
	ID     int64                    `gorm:"column:refund_payment_method_id"`
	Method string                   `gorm:"column:refund_payment_method"`
	Extra  RefundPaymentMethodExtra `gorm:"column:refund_payment_method_extra;serializer:json"`
	Vendor string                   `gorm:"column:refund_payment_method_vendor"`

	// 从关联的 OrderPayment 继承过来的数据.
	// TODO(yunxiang): 持久化在 Refund 本身？
	paymentMethodExtra PaymentMethodExtra `gorm:"-"`
}

func (rpm *RefundPaymentMethod) AttachOrderPayment(op *OrderPayment) {
	if rpm == nil || op == nil {
		return
	}

	rpm.ID = op.PaymentMethod.ID
	rpm.Method = op.PaymentMethod.Method
	rpm.paymentMethodExtra = op.PaymentMethod.Extra
}

func (rpm *RefundPaymentMethod) GetDisplayName() string {
	if rpm == nil {
		return ""
	}

	paymentMethod := &PaymentMethod{
		ID:     rpm.ID,
		Method: rpm.Method,
		Extra:  rpm.paymentMethodExtra,
	}

	return paymentMethod.GetDisplayName()
}

type RefundPaymentMethodExtra struct {
	SourcePaymentID string `json:"sourcePaymentID,omitempty"`
	StripeRefundID  string `json:"stripeRefundID,omitempty"`
}

func (rpm RefundPaymentMethodExtra) ToPB() *paymentpb.RefundPaymentMethodExtra {
	return &paymentpb.RefundPaymentMethodExtra{
		Extra: &paymentpb.RefundPaymentMethodExtra_Legacy_{
			Legacy: &paymentpb.RefundPaymentMethodExtra_Legacy{
				StripeRefundId:  rpm.StripeRefundID,
				SourcePaymentId: rpm.SourcePaymentID,
			},
		},
	}
}
