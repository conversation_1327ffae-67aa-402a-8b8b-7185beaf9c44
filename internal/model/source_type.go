package model

import (
	orderpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/order/v1"
)

func FormatSourceType(itemType orderpb.OrderSourceType) string {
	switch itemType {
	case orderpb.OrderSourceType_ORDER_SOURCE_TYPE_UNSPECIFIED:
		return "unspecified"
	case orderpb.OrderSourceType_UNKNOWN:
		return "unknown"
	case orderpb.OrderSourceType_APPOINTMENT:
		return "appointment"
	case orderpb.OrderSourceType_NO_SHOW:
		return "noshow"
	case orderpb.OrderSourceType_PRODUCT:
		return "product"
	case orderpb.OrderSourceType_PACKAGE:
		return "package"
	case orderpb.OrderSourceType_BOOKING_REQUEST:
		return "booking_request"
	case orderpb.OrderSourceType_FULFILLMENT:
		return "fulfillment"

	default:
		return "unknown"
	}
}
