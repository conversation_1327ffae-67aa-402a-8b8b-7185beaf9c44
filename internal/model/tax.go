package model

import (
	"github.com/shopspring/decimal"
	googleType "google.golang.org/genproto/googleapis/type/decimal"
)

// 确保 Tax & RefundTax 完全一致，除了 Tags.
var _ = Tax(RefundTax{})

type Tax struct {
	ID     int64           `gorm:"column:tax_id"`
	Name   string          `gorm:"column:tax_name"`
	Rate   decimal.Decimal `gorm:"column:tax_rate"`
	Amount decimal.Decimal `gorm:"column:tax_amount"`
}

func (tax Tax) GetRatePB() *googleType.Decimal {
	return &googleType.Decimal{
		Value: tax.Rate.String(),
	}
}

func (tax Tax) GetAmount() decimal.Decimal {
	return compatibleWithLegacyZero(tax.Amount)
}

func (tax Tax) Clone() Tax {
	cp := tax
	cp.Rate = tax.Rate.Copy()
	cp.Amount = tax.Amount.Copy()

	return cp
}

type RefundTax struct {
	ID     int64           `gorm:"column:tax_id"`
	Name   string          `gorm:"column:tax_name"`
	Rate   decimal.Decimal `gorm:"column:tax_rate"`
	Amount decimal.Decimal `gorm:"column:refund_tax_amount"`
}

func (tax RefundTax) GetRatePB() *googleType.Decimal {
	return &googleType.Decimal{
		Value: tax.Rate.String(),
	}
}

func (tax RefundTax) GetAmount() decimal.Decimal {
	return compatibleWithLegacyZero(tax.Amount)
}

func (tax RefundTax) Clone() RefundTax {
	cp := tax
	cp.Rate = tax.Rate.Copy()
	cp.Amount = tax.Amount.Copy()

	return cp
}
