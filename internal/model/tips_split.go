package model

import (
	"database/sql"

	"github.com/shopspring/decimal"
	"gorm.io/gorm"

	"github.com/MoeGolibrary/go-lib/common/proto/money"
	orderpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/order/v1"
)

type TipsSplit struct {
	ID                int64 `gorm:"primaryKey;autoIncrement"`
	BusinessID        int64
	CompanyID         int64
	SourceID          int64                                   // 结合 SourceType 存放不同的 ID.
	SourceType        orderpb.OrderSourceType                 `gorm:"serializer:proto_enum"`
	SplitConfig       *orderpb.TipsSplitModel_TipsSplitConfig `gorm:"serializer:proto_json"`
	ApplyBy           int64
	CollectedTips     decimal.Decimal
	CurrencyCode      string
	CollectedOrderIDs []int64      `gorm:"serializer:json;default:[]"`
	CreateTime        sql.NullTime `gorm:"autoCreateTime"`
	UpdateTime        sql.NullTime `gorm:"autoUpdateTime"`
}

func (op *TipsSplit) AfterFind(_ *gorm.DB) error {
	if op == nil {
		return nil
	}

	return nil
}

func (op *TipsSplit) ToPB() *orderpb.TipsSplitModel {
	splitConfig := op.SplitConfig

	// 转换成 PB 的时候，兜底一下老数据的 Business Amount 为 0 的情况.
	if splitConfig != nil && splitConfig.GetBusinessTipAmount() == nil {
		splitConfig = &orderpb.TipsSplitModel_TipsSplitConfig{
			SplitMethod:       op.SplitConfig.GetSplitMethod(),
			StaffConfigs:      op.SplitConfig.GetStaffConfigs(),
			BusinessTipAmount: money.FromDecimal(decimal.Zero, op.CurrencyCode),
		}
	}

	return &orderpb.TipsSplitModel{
		Id:                op.ID,
		BusinessId:        op.BusinessID,
		CompanyId:         op.CompanyID,
		SourceId:          op.SourceID,
		SourceType:        op.SourceType,
		SplitConfig:       splitConfig,
		ApplyBy:           op.ApplyBy,
		CollectedTips:     money.FromDecimal(op.CollectedTips, op.CurrencyCode),
		CollectedOrderIds: op.CollectedOrderIDs,
		CreateTime:        nullTimeToTimestamp(op.CreateTime),
		UpdateTime:        nullTimeToTimestamp(op.UpdateTime),
	}
}

func (op *TipsSplit) ToSplitDetailModels() []*TipsSplitDetail {
	details := make([]*TipsSplitDetail, 0, len(op.SplitConfig.StaffConfigs))

	for _, detail := range op.SplitConfig.StaffConfigs {
		details = append(
			details, &TipsSplitDetail{
				TipsSplitID:  op.ID,
				BusinessID:   op.BusinessID,
				CompanyID:    op.CompanyID,
				StaffID:      detail.StaffId,
				SplitAmount:  money.ToDecimal(detail.Amount),
				CurrencyCode: op.CurrencyCode,
				CreateTime:   op.CreateTime,
				UpdateTime:   op.UpdateTime,
			},
		)
	}

	return details
}

type TipsSplitDetail struct {
	ID           int64 `gorm:"primaryKey;autoIncrement"`
	TipsSplitID  int64
	BusinessID   int64
	CompanyID    int64
	StaffID      int64
	SplitAmount  decimal.Decimal
	CurrencyCode string
	CreateTime   sql.NullTime `gorm:"autoCreateTime"`
	UpdateTime   sql.NullTime `gorm:"autoUpdateTime"`
}

func (op *TipsSplitDetail) ToPB() *orderpb.TipsSplitDetailModel {
	return &orderpb.TipsSplitDetailModel{
		Id:          op.ID,
		TipsSplitId: op.TipsSplitID,
		BusinessId:  op.BusinessID,
		CompanyId:   op.CompanyID,
		StaffId:     op.StaffID,
		SplitAmount: money.FromDecimal(op.SplitAmount, op.CurrencyCode),
		CreateTime:  nullTimeToTimestamp(op.CreateTime),
		UpdateTime:  nullTimeToTimestamp(op.UpdateTime),
	}
}
