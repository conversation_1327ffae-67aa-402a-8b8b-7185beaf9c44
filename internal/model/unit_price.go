package model

import (
	"github.com/shopspring/decimal"
	"gorm.io/gorm"

	"github.com/MoeGolibrary/go-lib/common/proto/money"
	orderpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/order/v1"
)

// PriceItem 是用于支持计算 Price 定义的（典型场景是 Pricing Rule）
// 其计算逻辑比较复杂，会需要展示其计算过程给到用户。
// 因此，定义了 PriceItem 用来固化 Price 的计算过程。
//
// 特别的，这里不限定是用于 UnitPrice 还是 SubTotalAmount，应当以具体使用的场景为准。
type PriceItem struct {
	// 自增主键，无业务意义.
	ID int64 `gorm:"primaryKey;autoIncrement"`
	// 关联的 Order ID.
	OrderID int64
	// 关联的 OrderItem ID.
	OrderItemID int64

	Name         string
	UnitPrice    decimal.Decimal
	CurrencyCode string
	Quantity     int32
	Subtotal     decimal.Decimal
	Operator     orderpb.PriceDetailModel_PriceItem_Operator `gorm:"serializer:proto_enum"`

	// 相关的 Object Type，形式同 OrderItem 中的 type 字段
	ObjectType orderpb.ItemType `gorm:"serializer:proto_enum"`
	// 相关的 Object ID，形式同 OrderItem 中的 object_id 字段
	ObjectID int64
}

func (pi *PriceItem) BeforeCreate(_ *gorm.DB) error {
	if pi == nil {
		return nil
	}

	pi.Subtotal = pi.UnitPrice.Mul(decimal.NewFromInt32(pi.Quantity))

	return nil
}

func (pi *PriceItem) ToPB() *orderpb.PriceDetailModel_PriceItem {
	return &orderpb.PriceDetailModel_PriceItem{
		Name:       pi.Name,
		UnitPrice:  money.FromDecimal(pi.UnitPrice, pi.CurrencyCode),
		Quantity:   pi.Quantity,
		Operator:   pi.Operator,
		SubTotal:   money.FromDecimal(pi.Subtotal, pi.CurrencyCode),
		ObjectType: pi.ObjectType,
		ObjectId:   pi.ObjectID,
	}
}
