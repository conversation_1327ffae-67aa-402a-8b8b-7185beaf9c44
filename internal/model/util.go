package model

import (
	"database/sql"

	"github.com/shopspring/decimal"
	gdecimal "google.golang.org/genproto/googleapis/type/decimal"
	"google.golang.org/protobuf/types/known/timestamppb"
)

func nullTimeToUnix(nt sql.NullTime) int64 {
	if nt.Valid {
		return nt.Time.Unix()
	}

	return 0
}

func nullTimeToTimestamp(t sql.NullTime) *timestamppb.Timestamp {
	if !t.Valid {
		return nil
	}

	return timestamppb.New(t.Time)
}

func compatibleWithLegacyZero(dec decimal.Decimal) decimal.Decimal {
	// 因为历史原因，订单这边有一些表示金额的字段在不存在的时候设置成了 -1.00.
	// 为了计算的时候 **不被** -1.00 反转符号，这里整成 0.
	if dec.LessThanOrEqual(decimal.Zero) {
		return decimal.Zero
	}

	return dec
}

func ConvertGoogleDecimal(g *gdecimal.Decimal) decimal.Decimal {
	if g == nil {
		return decimal.Zero
	}

	d, err := decimal.NewFromString(g.GetValue())
	if err != nil {
		return decimal.Zero
	}

	return d
}

// 转换函数
func ToGoogleDecimal(d decimal.Decimal) *gdecimal.Decimal {
	return &gdecimal.Decimal{
		Value: d.String(), // google.type.Decimal 期望的是 string 表示形式
	}
}
