package appointment

import (
	"context"
	"time"

	"github.com/samber/lo"
	"google.golang.org/genproto/googleapis/type/interval"
	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/MoeGolibrary/go-lib/grpc"
	apptmodpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/appointment/v1"
	apptpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/appointment/v1"
	utilsV2 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/utils/v2"
)

type Client interface {
	ListPetDetailByGroomingID(ctx context.Context, groomingID int64) (*apptpb.GetPetDetailListResponse, error)
	ListAppointmentsForCustomers(
		ctx context.Context,
		customerID int64,
		companyID int64,
		businessID *int64,
		apptStatusList []apptmodpb.AppointmentPaymentStatus,
	) (*apptpb.ListAppointmentsResponse, error)
}

type client struct {
	apptPetDetailCli apptpb.PetDetailServiceClient
	apptCli          apptpb.AppointmentServiceClient
}

// ListAppointmentsForCustomers implements Client.
func (i *client) ListAppointmentsForCustomers(ctx context.Context,
	customerID int64,
	companyID int64,
	businessID *int64,
	apptStatusList []apptmodpb.AppointmentPaymentStatus,
) (*apptpb.ListAppointmentsResponse, error) {
	now := time.Now()
	// 创建时间区间，从一年前
	timeRange := &interval.Interval{
		StartTime: timestamppb.New(now.AddDate(-2, 0, 0)),
	}

	filter := &apptpb.ListAppointmentsRequest_Filter{
		CustomerIds:          []int64{customerID},
		FilterBookingRequest: lo.ToPtr(true),
		Status: []apptmodpb.AppointmentStatus{
			apptmodpb.AppointmentStatus_FINISHED,
			apptmodpb.AppointmentStatus_CANCELED,
		},
		EndTimeRange: timeRange,
	}

	if len(apptStatusList) > 0 {
		filter.PaymentStatuses = apptStatusList
	}

	var pageSize int32 = 1000

	var pageNum int32 = 1

	apptRequst := &apptpb.ListAppointmentsRequest{
		Pagination: &utilsV2.PaginationRequest{
			PageSize: &pageSize,
			PageNum:  &pageNum,
		},
		OrderBys: []*utilsV2.OrderBy{
			{
				FieldName: "appointmentDate",
				Asc:       lo.ToPtr(false),
			},
		},
		CompanyId: companyID,
		Filter:    filter,
	}
	if businessID != nil {
		apptRequst.BusinessIds = []int64{*businessID}
	}

	return i.apptCli.ListAppointments(ctx, apptRequst)
}

func New() Client {
	return &client{
		apptPetDetailCli: grpc.NewClient("moego-svc-appointment:9090", apptpb.NewPetDetailServiceClient),
		apptCli:          grpc.NewClient("moego-svc-appointment:9090", apptpb.NewAppointmentServiceClient),
	}
}

func (i *client) ListPetDetailByGroomingID(
	ctx context.Context,
	groomingID int64,
) (*apptpb.GetPetDetailListResponse, error) {
	return i.apptPetDetailCli.GetPetDetailList(ctx, &apptpb.GetPetDetailListRequest{
		AppointmentIds: []int64{groomingID},
	})
}
