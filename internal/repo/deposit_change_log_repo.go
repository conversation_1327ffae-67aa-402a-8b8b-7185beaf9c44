package repo

import (
	"context"

	"gorm.io/gorm"

	orderpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/order/v1"
	"github.com/MoeGolibrary/moego-svc-order-v2/internal/model"
)

type DepositChangeLogRepo interface {
	Create(ctx context.Context, depositChangeLog *model.DepositChangeLog) error
	ListByDepositOrderID(ctx context.Context, depositOrderID int64) ([]*model.DepositChangeLog, error)
	GetLatestByDepositOrderID(ctx context.Context, depositOrderID int64) (*model.DepositChangeLog, error)
	GetDeductionByDestOrderID(ctx context.Context, destOrderID int64) (*model.DepositChangeLog, error)
}

func NewDepositChangeLogRepoRepo(db *gorm.DB) DepositChangeLogRepo {
	return &depositChangeLogRepo{db: db}
}

type depositChangeLogRepo struct {
	db *gorm.DB
}

func (repo *depositChangeLogRepo) Create(ctx context.Context, depositChangeLog *model.DepositChangeLog) error {
	tx := repo.withContext(ctx).Create(depositChangeLog)
	if tx.Error != nil {
		return parseDBErr(tx.Error)
	}

	return nil
}

func (repo *depositChangeLogRepo) ListByDepositOrderID(
	ctx context.Context, depositOrderID int64,
) ([]*model.DepositChangeLog, error) {
	return listByKeyID[*model.DepositChangeLog](
		repo.withContext(ctx).Order("id desc"), "deposit_order_id", depositOrderID,
	)
}

func (repo *depositChangeLogRepo) GetLatestByDepositOrderID(ctx context.Context, depositOrderID int64) (
	*model.DepositChangeLog, error,
) {
	changeLog := &model.DepositChangeLog{}
	if err := repo.withContext(ctx).Order("create_time desc").
		First(changeLog, "deposit_order_id = ?", depositOrderID).Error; err != nil {
		return nil, parseDBErr(err)
	}

	return changeLog, nil
}

func (repo *depositChangeLogRepo) GetDeductionByDestOrderID(
	ctx context.Context, destOrderID int64,
) (*model.DepositChangeLog, error) {
	changeLog := &model.DepositChangeLog{}
	if err := repo.withContext(ctx).
		First(changeLog, "dest_order_id = ? AND reason = ?", destOrderID, orderpb.DepositChangeReason_DEDUCTION).
		Error; err != nil {
		return nil, parseDBErr(err)
	}

	return changeLog, nil
}

func (repo *depositChangeLogRepo) withContext(ctx context.Context) *gorm.DB {
	const tableName = "public.deposit_change_log"

	return repo.db.WithContext(ctx).Table(tableName)
}
