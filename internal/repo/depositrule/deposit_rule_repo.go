package depositrule

import (
	"context"

	"gorm.io/gorm"

	"github.com/MoeGolibrary/moego-svc-order-v2/internal/model"
)

type Repo interface {
	Create(ctx context.Context, depositRules []*model.DepositRule) error
	Update(ctx context.Context, depositRule *model.DepositRule) error
	Delete(ctx context.Context, ruleID int64) error
	Get(ctx context.Context, ruleID int64) (*model.DepositRule, error)
	ListByBusinessID(ctx context.Context, businessID int64) ([]*model.DepositRule, error)
}

func NewDepositRuleRepo(db *gorm.DB) Repo {
	return &depositRuleRepo{
		db: db,
	}
}

type depositRuleRepo struct {
	db *gorm.DB
}

func (repo *depositRuleRepo) Create(ctx context.Context, depositRules []*model.DepositRule) error {
	err := repo.withContext(ctx).Create(&depositRules).Error
	if err != nil {
		return parseDBErr(err)
	}

	return nil
}

func (repo *depositRuleRepo) Update(ctx context.Context, depositRule *model.DepositRule) error {
	// Nil-able fields must also be updated
	err := repo.withContext(ctx).Model(depositRule).Select("*").Updates(depositRule).Error
	if err != nil {
		return parseDBErr(err)
	}

	return nil
}

func (repo *depositRuleRepo) Delete(ctx context.Context, ruleID int64) error {
	err := repo.withContext(ctx).Delete(&model.DepositRule{}, ruleID).Error
	if err != nil {
		return parseDBErr(err)
	}

	return nil
}

func (repo *depositRuleRepo) Get(ctx context.Context, ruleID int64) (*model.DepositRule, error) {
	var rule model.DepositRule

	err := repo.withContext(ctx).Where("id = ?", ruleID).First(&rule).Error
	if err != nil {
		return nil, parseDBErr(err)
	}

	return &rule, nil
}

func (repo *depositRuleRepo) ListByBusinessID(ctx context.Context, businessID int64) ([]*model.DepositRule, error) {
	var rules []*model.DepositRule

	err := repo.withContext(ctx).Where("business_id = ?", businessID).Find(&rules).Error
	if err != nil {
		return nil, parseDBErr(err)
	}

	return rules, nil
}

func (repo *depositRuleRepo) withContext(ctx context.Context) *gorm.DB {
	const tableName = "public.deposit_rule"

	return repo.db.WithContext(ctx).Table(tableName)
}
