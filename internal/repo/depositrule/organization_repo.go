package depositrule

import (
	"context"

	"github.com/MoeGolibrary/go-lib/grpc"
	organizationpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/organization/v1"
	organizationsvcpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/organization/v1"
)

type OrganizationRepo interface {
	GetCompanyPreference(ctx context.Context, companyID int64) (*organizationpb.CompanyPreferenceSettingModel, error)
	BatchGetLocation(ctx context.Context, locationIDs []int64) ([]*organizationpb.LocationModel, error)
}

func NewBusinessRepo() OrganizationRepo {
	return &organizationRepo{
		companyClient:  grpc.NewClient("moego-svc-organization:9090", organizationsvcpb.NewCompanyServiceClient),
		businessClient: grpc.NewClient("moego-svc-organization:9090", organizationsvcpb.NewBusinessServiceClient),
	}
}

type organizationRepo struct {
	companyClient  organizationsvcpb.CompanyServiceClient
	businessClient organizationsvcpb.BusinessServiceClient
}

func (repo *organizationRepo) GetCompanyPreference(
	ctx context.Context, companyID int64,
) (*organizationpb.CompanyPreferenceSettingModel, error) {
	resp, err := repo.companyClient.GetCompanyPreferenceSetting(ctx, &organizationsvcpb.GetCompanyPreferenceSettingRequest{
		CompanyId: companyID,
	})
	if err != nil {
		return nil, err
	}

	return resp.GetPreferenceSetting(), nil
}

func (repo *organizationRepo) BatchGetLocation(
	ctx context.Context, locationIDs []int64,
) ([]*organizationpb.LocationModel, error) {
	if len(locationIDs) == 0 {
		return nil, nil
	}

	resp, err := repo.businessClient.ListLocations(ctx, &organizationsvcpb.ListLocationsRequest{
		Filter: &organizationsvcpb.ListLocationsRequest_Filter{
			Ids: locationIDs,
		},
	})
	if err != nil {
		return nil, err
	}

	return resp.GetLocations(), nil
}
