package file

import (
	"context"
	"fmt"
	"net/url"

	"github.com/MoeGolibrary/go-lib/grpc"
	filepb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/file/v2"
	filesvcpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/file/v2"
)

type Client interface {
	Create(ctx context.Context, staffID, companyID int64, fileName string) (int64, error)
	Upload(ctx context.Context, fileID int64, content []byte) error
}

type impl struct {
	file filesvcpb.FileServiceClient
}

func New() Client {
	return &impl{
		file: grpc.NewClient("moego-svc-file:9090", filesvcpb.NewFileServiceClient),
	}
}

func (i *impl) Create(ctx context.Context, staffID, companyID int64, fileName string) (int64, error) {
	res, err := i.file.CreateExportFile(ctx, &filesvcpb.CreateExportFileRequest{
		CreatorId: staffID,
		Usage:     "export",
		FileName:  fileName,
		OwnerType: "company",
		OwnerId:   companyID,
		Source: &filesvcpb.CreateExportFileRequest_Tenant{
			Tenant: &filepb.TenantSourceDef{
				CompanyId: companyID,
			},
		},
		Metadata: map[string]string{
			"Content-Disposition": buildContentDisposition(fileName),
		},
	})
	if err != nil {
		return 0, err
	}

	return res.GetFileId(), nil
}

func (i *impl) Upload(ctx context.Context, fileID int64, content []byte) error {
	res, err := i.file.UploadExportFile(ctx, &filesvcpb.UploadExportFileRequest{
		FileId:      fileID,
		FileContent: content,
	})
	if err != nil {
		return err
	}

	if res.GetStatus() != filepb.FileStatus_FILE_STATUS_UPLOADED {
		return fmt.Errorf("upload failed, status: %s", res.GetStatus().String())
	}

	return nil
}

func buildContentDisposition(fileName string) string {
	// 对 fileName 进行 urlEncode，避免文件名有特殊符号导致上传失败
	return fmt.Sprintf("attachment; filename*= UTF-8''%s", url.QueryEscape(fileName))
}
