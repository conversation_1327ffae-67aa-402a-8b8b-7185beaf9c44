package grooming

import (
	"bytes"
	"context"
	"encoding/json"
	"io"
	"net/http"
	"net/url"
	"strconv"

	"github.com/shopspring/decimal"

	"github.com/MoeGolibrary/go-lib/common/proto/money"
	mhttp "github.com/MoeGolibrary/go-lib/http"
	onlinebookingpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/online_booking/v1"
	orderpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/order/v1"
	"github.com/MoeGolibrary/moego-svc-order-v2/internal/model"
	"github.com/MoeGolibrary/moego-svc-order-v2/internal/repo/internal/httphelper"
)

//go:generate mockery --name=Client --keeptree --case underscore --with-expecter --output ../../mocks/grooming

type Client interface {
	ListPetDetailsByGroomingID(
		ctx context.Context, businessID, groomingID int64,
	) ([]*PetDetailDTO, error)
	GetTipsSplitDetails(
		ctx context.Context, orderIDs []int64,
	) (*TipsSplitDetailsDTO, error)
	GetTipsSplitDetailsMap(
		ctx context.Context, orderIDs []int64, currencyCode string,
	) (map[int64]*orderpb.StaffTipConfig, decimal.Decimal, error)
	UpdatePetDetailStaff(ctx context.Context, params []*EditPetDetailStaffCommissionItem) error
	GenerateOrderGUID(ctx context.Context, orderDO *model.Order, requiredCvf bool) (string, error)
	BatchSaveOnlineFeeInvoice(ctx context.Context, param BatchSaveOnlineFeeInvoiceParam) error

	ListOBSetting(ctx context.Context, businessIDs []int64) ([]*OBSetting, error)
	UpdateOBPaymentSetting(ctx context.Context, businessID int64, update ObSettingUpdate) error
	ResetOBGroupPaymentSetting(ctx context.Context, businessID int64) error

	GetInvoiceByGUID(ctx context.Context, guid string) (int64, error)
}

type TipsSplitDetailsDTO struct {
	StaffTipAmountMap map[int64]decimal.Decimal `json:"staffTipAmountMap"`
}

type EditPetDetailStaffCommissionItem struct {
	OrderItemType     string                                       `json:"orderItemType"`
	OrderItemID       int64                                        `json:"orderItemId"`
	PetDetailID       int64                                        `json:"petDetailId"`
	ServiceID         int64                                        `json:"serviceId"`
	PetID             int64                                        `json:"petId"`
	BusinessID        int64                                        `json:"businessId"`
	CompanyID         int64                                        `json:"companyId"`
	StaffID           int64                                        `json:"staffId"`
	OperationItemList []*EditPetDetailStaffCommissionOperationItem `json:"operationItemList"`
}

type EditPetDetailStaffCommissionOperationItem struct {
	StaffID       int64           `json:"staffId"`
	Ratio         decimal.Decimal `json:"ratio"`
	Duration      int64           `json:"duration"`
	OperationName string          `json:"operationName"`
}

type PetDetailDTO struct {
	ID              int64                  `json:"id"`
	GroomingID      int64                  `json:"groomingId"`
	PetID           int64                  `json:"petId"`
	StaffID         int64                  `json:"staffId"`
	ServiceID       int64                  `json:"serviceId"`
	ServiceTime     int64                  `json:"serviceTime"`
	ServicePrice    decimal.Decimal        `json:"servicePrice"`
	ServiceType     int64                  `json:"serviceType"`
	Quantity        int64                  `json:"quantity"`
	ServiceItemType int64                  `json:"serviceItemType"`
	StartStaffID    int64                  `json:"startStaffId"`
	EnableOperation bool                   `json:"enableOperation"`
	WorkMode        int64                  `json:"workMode"`
	OperationList   []*ServiceOperationDTO `json:"operationList"`
	// V4 新增， 创建 Order 后 PetDetail 会 **异步** 关联上 OrderItemID.
	OrderItemID int64 `json:"orderLineItemId"`
	// V4 新增， 创建 Order 时，会传入这个 UUID 关联在 OrderItem 上，该字段非空时唯一.
	ExternalUUID string `json:"externalId"`
}

type TipEnableType int

const (
	TipEnableFalse TipEnableType = iota
	TipEnableTrue
)

// OBSetting is the online booking setting for a business.
// Non-nil fields will be used to update the existing setting. We use pointers here because some enums's zero value is
// also defined.
type OBSetting struct {
	BusinessID int64 `json:"businessId"`
	CompanyID  int64 `json:"companyId"`

	// Default payment options

	// 命名是历史遗留问题
	PaymentType       onlinebookingpb.PaymentType       `json:"enableNoShowFee,omitempty"`
	PrepayType        onlinebookingpb.PrepayType        `json:"prepayType,omitempty"`
	DepositType       onlinebookingpb.PrepayDepositType `json:"depositType,omitempty"`
	DepositPercentage int64                             `json:"depositPercentage,omitempty"`
	DepositAmount     decimal.Decimal                   `json:"depositAmount,omitempty"`
	PreAuthTipEnable  TipEnableType                     `json:"preAuthTipEnable,omitempty"`

	// Customized payment options

	GroupAcceptClient onlinebookingpb.AcceptClientType `json:"groupAcceptClient,omitempty"`
	GroupFilterRule   string                           `json:"groupFilterRule,omitempty"`
	// GroupPaymentType will be nil if customized payment options are not set.
	GroupPaymentType       *onlinebookingpb.PaymentType      `json:"groupPaymentType,omitempty"`
	GroupPrepayType        onlinebookingpb.PrepayType        `json:"groupPrepayType,omitempty"`
	GroupDepositType       onlinebookingpb.PrepayDepositType `json:"groupDepositType,omitempty"`
	GroupDepositPercentage int64                             `json:"groupDepositPercentage,omitempty"`
	GroupDepositAmount     decimal.Decimal                   `json:"groupDepositAmount,omitempty"`
	GroupPreAuthTipEnable  TipEnableType                     `json:"groupPreAuthTipEnable,omitempty"`
}

type ObSettingUpdate struct {
	// 命名是历史遗留问题
	PaymentType           *onlinebookingpb.PaymentType `json:"enableNoShowFee,omitempty"`
	PreAuthTipEnable      *TipEnableType               `json:"preAuthTipEnable,omitempty"`
	GroupPreAuthTipEnable *TipEnableType               `json:"groupPreAuthTipEnable,omitempty"`
}

// get service price
func (g *PetDetailDTO) GetServicePrice() decimal.Decimal {
	return g.ServicePrice
}

// ref: com.moego.server.grooming.dto.GroomingServiceOperationDTO
type ServiceOperationDTO struct {
	ID                int64           `json:"id"`
	BusinessID        int64           `json:"businessId"`
	GroomingID        int64           `json:"groomingId"`
	GroomingServiceID int64           `json:"groomingServiceId"`
	PetID             int64           `json:"petId"`
	OperationName     string          `json:"operationName"`
	Comment           string          `json:"comment"`
	StartTime         int64           `json:"startTime"`
	Price             decimal.Decimal `json:"price"`
	StaffID           int64           `json:"staffId"`
	// 比例，从 0 ~ 1。
	PriceRatio decimal.Decimal `json:"priceRatio"`
	Duration   int64           `json:"duration"`
}

type BatchSaveOnlineFeeInvoiceParam struct {
	BusinessID    int64   `json:"businessId"`
	InvoiceIDList []int64 `json:"invoiceIdList"`
	RequiredCvf   bool    `json:"requiredCvf"`
}

type clientImpl struct {
	cli mhttp.Client
}

// GetTipsSplitDetailsMap implements Client.
func (g *clientImpl) GetTipsSplitDetailsMap(
	ctx context.Context,
	orderIDs []int64,
	currencyCode string,
) (map[int64]*orderpb.StaffTipConfig, decimal.Decimal, error) {
	staffSplitDetailsDTO, err := g.GetTipsSplitDetails(ctx, orderIDs)
	if err != nil {
		return nil, decimal.Zero, err
	}

	staffSplitDetailMap := make(map[int64]*orderpb.StaffTipConfig)
	for staffID, tipAmount := range staffSplitDetailsDTO.StaffTipAmountMap {
		staffSplitDetailMap[staffID] = &orderpb.StaffTipConfig{
			StaffId: staffID,
			Amount:  money.FromDecimal(tipAmount, currencyCode),
		}
	}

	// 老接口里面 staff id = 0 的都是应该分给 business 的.
	businessTipAmount := money.ToDecimal(staffSplitDetailMap[0].GetAmount())
	delete(staffSplitDetailMap, 0)

	return staffSplitDetailMap, businessTipAmount, nil
}

// GetTipsSplitDetails implements Client.
func (g *clientImpl) GetTipsSplitDetails(ctx context.Context, orderIDs []int64) (*TipsSplitDetailsDTO, error) {
	const path = "/service/grooming/invoice/tipsSplitDetails"

	payload, err := json.Marshal(
		orderIDs,
	)
	if err != nil {
		return nil, err
	}

	resp, err := g.cli.Post(ctx, path, nil, bytes.NewReader(payload))
	if err != nil {
		return nil, err
	}

	defer func() { _ = resp.Body.Close() }()

	result, err := httphelper.ParseResponse[TipsSplitDetailsDTO](resp)
	if err != nil {
		return nil, err
	}

	return result, nil
}

// UpdatePetDetailStaff implements Client.
func (g *clientImpl) UpdatePetDetailStaff(
	ctx context.Context,
	params []*EditPetDetailStaffCommissionItem,
) error {
	const path = "/service/grooming/pet-detail/updateStaff"

	payload, err := json.Marshal(
		map[string]any{
			"editPetDetailStaffCommissionItemList": params,
		},
	)
	if err != nil {
		return err
	}

	resp, err := g.cli.Post(ctx, path, nil, bytes.NewReader(payload))
	if err != nil {
		return err
	}

	defer func() { _ = resp.Body.Close() }()

	return nil
}

// ListPetDetailsByGroomingID implements GroomingClient.
func (g *clientImpl) ListPetDetailsByGroomingID(
	ctx context.Context, businessID int64, groomingID int64,
) ([]*PetDetailDTO, error) {
	const path = "/service/grooming/queryAllPetDetail"

	values := url.Values{}
	values.Set("businessId", strconv.FormatInt(businessID, 10))
	values.Set("groomingId", strconv.FormatInt(groomingID, 10))

	resp, err := g.cli.Post(ctx, path, values, nil)
	if err != nil {
		return nil, err
	}

	defer func() { _ = resp.Body.Close() }()

	result, err := httphelper.ParseResponse[[]*PetDetailDTO](resp)
	if err != nil {
		return nil, err
	}

	return *result, nil
}

func (g *clientImpl) GenerateOrderGUID(ctx context.Context, orderDO *model.Order, requiredCvf bool) (string, error) {
	const path = "/service/grooming/invoice/client/url"

	values := url.Values{}
	values.Set("businessId", strconv.FormatInt(orderDO.BusinessID, 10))
	values.Set("invoiceId", strconv.FormatInt(orderDO.ID, 10))
	values.Set("requiredProcessingFee", strconv.FormatBool(requiredCvf))

	resp, err := g.cli.Post(ctx, path, values, nil)

	defer func() { _ = resp.Body.Close() }()

	if err != nil {
		return "", err
	}

	rData, err := io.ReadAll(resp.Body)
	if err != nil {
		return "", err
	}

	return string(rData), nil
}

func (g *clientImpl) ListOBSetting(ctx context.Context, businessIDs []int64) ([]*OBSetting, error) {
	const path = "/service/grooming/bookOnline/listOBSetting"

	payload, err := json.Marshal(businessIDs)
	if err != nil {
		return nil, err
	}

	resp, err := g.cli.Post(ctx, path, nil, bytes.NewReader(payload))
	if err != nil {
		return nil, err
	}

	defer func() { _ = resp.Body.Close() }()

	result, err := httphelper.ParseResponse[[]*OBSetting](resp)
	if err != nil {
		return nil, err
	}

	return *result, nil
}

func (g *clientImpl) BatchSaveOnlineFeeInvoice(ctx context.Context, param BatchSaveOnlineFeeInvoiceParam) error {
	const path = "/service/grooming/invoice/batchSaveOnlineFeeInvoice"

	payload, err := json.Marshal(param)
	if err != nil {
		return err
	}

	resp, err := g.cli.Post(ctx, path, nil, bytes.NewReader(payload))

	defer func() { _ = resp.Body.Close() }()

	if err != nil {
		return err
	}

	return nil
}

func (g *clientImpl) UpdateOBPaymentSetting(ctx context.Context, businessID int64, update ObSettingUpdate) error {
	const path = "/service/grooming/bookOnline/batchUpdateOBSetting"

	type reqBody struct {
		UpdateValue ObSettingUpdate `json:"updateValue"`
		BusinessIDs []int64         `json:"businessIds"`
	}

	payload, err := json.Marshal(
		reqBody{
			UpdateValue: update,
			BusinessIDs: []int64{businessID},
		},
	)
	if err != nil {
		return err
	}

	resp, err := g.cli.Post(ctx, path, nil, bytes.NewReader(payload))
	if err != nil {
		return err
	}

	defer func() { _ = resp.Body.Close() }()

	return nil
}

func (g *clientImpl) ResetOBGroupPaymentSetting(ctx context.Context, businessID int64) error {
	const path = "/service/grooming/bookOnline/deleteOBGroupPaymentSetting"

	values := url.Values{}
	values.Set("businessId", strconv.FormatInt(businessID, 10))

	req, err := g.cli.BuildRequest(http.MethodDelete, path, values, nil)
	if err != nil {
		return err
	}

	resp, err := g.cli.Do(ctx, req)
	if err != nil {
		return err
	}

	defer func() { _ = resp.Body.Close() }()

	return nil
}

func (g *clientImpl) GetInvoiceByGUID(ctx context.Context, guid string) (int64, error) {
	const path = "/service/grooming/invoice/check"

	type RespHelper struct {
		ID int64 `json:"id"`
	}

	param := url.Values{}
	param.Set("guid", guid)

	req, err := g.cli.BuildRequest(http.MethodGet, path, param, nil)
	if err != nil {
		return 0, err
	}

	resp, err := g.cli.Do(ctx, req)
	if err != nil {
		return 0, err
	}

	defer func() { _ = resp.Body.Close() }()

	result, err := httphelper.ParseResponse[RespHelper](resp)
	if err != nil {
		return 0, err
	}

	return result.ID, nil
}

func NewGroomingClient() Client {
	cli := mhttp.NewClient("http://moego-service-grooming:9206")

	return &clientImpl{
		cli: cli,
	}
}
