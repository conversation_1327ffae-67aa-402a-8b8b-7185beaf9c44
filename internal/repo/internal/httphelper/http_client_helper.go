package httphelper

import (
	"encoding/json"
	"fmt"
	"io"
	"net/http"

	"github.com/MoeGolibrary/go-lib/merror"
	errorspb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/errors/v1"
)

type generalHTTPResponse struct {
	Code    int32  `json:"code"`
	Message string `json:"message"`
}

func (res *generalHTTPResponse) IsOK() bool {
	switch errorspb.Code(res.Code) {
	case errorspb.Code_CODE_SUCCESS, errorspb.Code_CODE_UNSPECIFIED:
		return true

	default:
		return false
	}
}

func ParseResponse[T any](resp *http.Response) (*T, error) {
	var gr generalHTTPResponse

	payload, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("read response body failed, err: %w", err)
	}

	// 部分错误会被转换成 Bad Request 返回.
	if resp.StatusCode != http.StatusOK && resp.StatusCode != http.StatusBadRequest {
		return nil, merror.NewBizError(
			errorspb.Code(resp.StatusCode), //nolint:gosec // HTTP 状态码都是三位数.
			string(payload),
		)
	}

	// 部分老服务 server-xxx 的接口在结果正常的时候会直接返回数组，这种情况下无法用 generalHTTPResponse 这个
	// 结构来进行解析，会导致报错。
	// 因此这里特别处理一下，如果识别到 JSON 是一个数组，直接往 result 进行解析。
	isSlice := false

	for _, b := range payload {
		// ignore prefix spaces.
		if b == ' ' {
			continue
		}

		if b == '[' {
			isSlice = true
		}

		break
	}

	if !isSlice {
		if err = json.Unmarshal(payload, &gr); err != nil {
			return nil, fmt.Errorf("unmarshal response body failed, err: %w, body: '%s'", err, payload)
		}

		if !gr.IsOK() {
			return nil, merror.NewBizError(errorspb.Code(gr.Code), gr.Message)
		}
	}

	var result T
	if err := json.Unmarshal(payload, &result); err != nil {
		return nil, fmt.Errorf("unmarshal response body failed, err: %w, body: '%s'", err, payload)
	}

	return &result, nil
}
