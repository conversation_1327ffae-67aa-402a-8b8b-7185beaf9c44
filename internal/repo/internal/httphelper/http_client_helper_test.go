package httphelper

import (
	"bytes"
	"encoding/json"
	"io"
	"net/http"
	"testing"

	"github.com/google/uuid"
	"github.com/stretchr/testify/suite"
	"google.golang.org/grpc/codes"

	errorspb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/errors/v1"
)

type HTTPHelperTestSuite struct {
	suite.Suite
}

func TestHTTPHelper(t *testing.T) {
	suite.Run(t, new(HTTPHelperTestSuite))
}

func (ts *HTTPHelperTestSuite) TestSliceResponse() {
	expected := []string{"element A", "element B"}

	payload, err := json.Marshal(expected)
	ts.Require().NoError(err)

	str, err := ParseResponse[[]string](&http.Response{
		Status:     http.StatusText(http.StatusOK),
		StatusCode: http.StatusOK,
		Body:       io.NopCloser(bytes.NewReader(payload)),
	})

	ts.Require().NoError(err)
	ts.Require().NotNil(str)
	ts.Equal(expected, *str)
}

func (ts *HTTPHelperTestSuite) TestStructResponse() {
	type helper struct {
		UUID string `json:"uuid"`
	}

	expected := helper{UUID: uuid.New().String()}
	payload, err := json.Marshal(expected)
	ts.Require().NoError(err)

	resp, err := ParseResponse[helper](&http.Response{
		Status:     http.StatusText(http.StatusOK),
		StatusCode: http.StatusOK,
		Body:       io.NopCloser(bytes.NewReader(payload)),
	})

	ts.Require().NoError(err)
	ts.Require().NotNil(resp)
	ts.Equal(expected, *resp)
}

func (ts *HTTPHelperTestSuite) TestErrorResponse() {
	expected := generalHTTPResponse{
		Code:    int32(codes.NotFound),
		Message: codes.NotFound.String(),
	}

	payload, err := json.Marshal(expected)
	ts.Require().NoError(err)

	str, err := ParseResponse[string](&http.Response{
		Status:     http.StatusText(http.StatusOK),
		StatusCode: http.StatusOK,
		Body:       io.NopCloser(bytes.NewReader(payload)),
	})

	ts.Require().Error(err)
	ts.Require().Nil(str)

	type bizErrCoder interface {
		ErrCode() errorspb.Code
		ErrMsg() string
	}

	bizErr, ok := err.(bizErrCoder)
	ts.Require().True(ok)
	ts.EqualValues(expected.Code, bizErr.ErrCode())
	ts.EqualValues(expected.Message, bizErr.ErrMsg())
}

func (ts *HTTPHelperTestSuite) TestNonOKHTTPStatus() {
	expected := generalHTTPResponse{
		Code:    int32(errorspb.Code_CODE_REFUND_NOT_FOUND),
		Message: errorspb.Code_CODE_REFUND_NOT_FOUND.String(),
	}

	str, err := ParseResponse[string](&http.Response{
		Status:     http.StatusText(http.StatusOK),
		StatusCode: int(expected.Code),
		Body:       io.NopCloser(bytes.NewBufferString(expected.Message)),
	})

	ts.Require().Error(err)
	ts.Require().Nil(str)

	type bizErrCoder interface {
		ErrCode() errorspb.Code
		ErrMsg() string
	}

	bizErr, ok := err.(bizErrCoder)
	ts.Require().True(ok)
	ts.EqualValues(expected.Code, bizErr.ErrCode())
	ts.EqualValues(expected.Message, bizErr.ErrMsg())
}
