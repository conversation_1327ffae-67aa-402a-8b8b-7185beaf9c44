package repohelper

import orderpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/order/v1"

// SourceType 定义的是订单里面老的 SourceType.
// 因为历史原因，老的 sourceType 在 DB 里面写入的数据与 PB 的不符合.
// 绝大部分都是大小写不匹配，特殊的还有 NoShow， PB 里面是 NO_SHOW， 老的写入的是 noshow。
type SourceType string

const (
	SourceTypeAppointment    SourceType = "appointment"
	SourceTypeNoShow         SourceType = "noshow"
	SourceTypeBookingRequest SourceType = "booking_request"
)

// convert 将老的 order sourceType 转换为新的 sourceType.
func ConvertToSourceType(ost orderpb.OrderSourceType) SourceType {
	switch ost {
	case orderpb.OrderSourceType_APPOINTMENT:
		return SourceTypeAppointment
	case orderpb.OrderSourceType_NO_SHOW:
		return SourceTypeNoShow
	case orderpb.OrderSourceType_BOOKING_REQUEST:
		return SourceTypeBookingRequest
	default:
		return ""
	}
}
