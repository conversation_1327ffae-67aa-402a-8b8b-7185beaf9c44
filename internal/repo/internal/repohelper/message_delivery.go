package repohelper

import (
	"google.golang.org/protobuf/proto"

	eventbuspb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/event_bus/v1"
	orderpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/order/v1"
)

type MessageDelivery[T proto.Message] struct {
	ID              int64                `gorm:"primaryKey,autoIncrement"`
	MessageType     eventbuspb.EventType `gorm:"serializer:proto_enum"`
	ReferenceID     string
	Payload         T                           `gorm:"serializer:proto_json"`
	DeliveryStatus  orderpb.EventDeliveryStatus `gorm:"column:status;serializer:proto_enum"`
	RetryCount      int64
	LastAttemptTime int64 `gorm:"serializer:unixtime"`
	CreateTime      int64 `gorm:"autoCreateTime;serializer:unixtime"`
	UpdateTime      int64 `gorm:"autoUpdateTime;serializer:unixtime"`
}
