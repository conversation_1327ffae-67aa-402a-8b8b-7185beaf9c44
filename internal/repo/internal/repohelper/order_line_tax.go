package repohelper

import (
	"database/sql"

	"github.com/MoeGolibrary/moego-svc-order-v2/internal/model"
)

// OrderItemTaxHelper 用于兼容老版本的订单，从 OrderLineTax 表中获取数据与 OrderLineItem 进行映射.
type OrderItemTaxHelper struct {
	ID          int64 `gorm:"primaryKey"`
	OrderID     int64
	OrderItemID int64
	Tax         model.Tax `gorm:"embedded"`
	IsDeleted   bool

	BusinessID int64
	ApplyType  string
	ApplyBy    int64
	CreateTime sql.NullTime `gorm:"autoCreateTime"`
	UpdateTime sql.NullTime `gorm:"autoUpdateTime"`
}
