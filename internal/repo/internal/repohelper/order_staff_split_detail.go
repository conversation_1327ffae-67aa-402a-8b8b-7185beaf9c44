package repohelper

import (
	"strings"

	"github.com/shopspring/decimal"

	"github.com/MoeGolibrary/moego-svc-order-v2/internal/model"
)

// StaffSplitDetailHelper 用于从 OrderStaffSplitDetail 表中获取 Staff 和 OrderItem 的关联关系.
type StaffSplitDetailHelper struct {
	ID          int64 `gorm:"primaryKey"`
	OrderID     int64
	ReferenceID int64 `gorm:"column:order_item_id"`
	StaffID     int64
	BusinessID  int64
	PetID       int64
	// ItemType 目前只有 service 和 product 两种.
	ItemType string `gorm:"column:type"`
	// ItemObjectID 依据不同的 ItemType 存储不同的 ID.
	//  - ItemType 是 service 时，这里是 serviceID.
	//  - ItemType 是 product 时，这里是 productID.
	ItemObjectID int64           `gorm:"column:object_id"`
	SplitRate    decimal.Decimal `gorm:"column:split_rate"`
}

func (ssd *StaffSplitDetailHelper) IsMatchedOrderItem(item *model.OrderItem) bool {
	if ssd == nil || item == nil {
		return false
	}

	switch strings.ToLower(ssd.ItemType) {
	case "service":
		return ssd.PetID == item.PetID &&
			ssd.ItemObjectID == item.ObjectID

	case "product":
		return ssd.ReferenceID == item.ID

	default:
		return false
	}
}
