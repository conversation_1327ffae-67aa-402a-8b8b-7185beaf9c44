package repohelper

import (
	orderpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/order/v1"
)

type PaymentStatus int8

const (
	PaymentStatusFailed PaymentStatus = iota - 1
	PaymentStatusCreated
	PaymentStatusProcessing
	PaymentStatusPaid
	PaymentStatusCompleted
)

func (ps PaymentStatus) ToOrderPaymentStatus() orderpb.OrderPaymentStatus {
	switch ps {
	case PaymentStatusFailed:
		return orderpb.OrderPaymentStatus_ORDER_PAYMENT_STATUS_FAILED

	case PaymentStatusCreated:
		return orderpb.OrderPaymentStatus_ORDER_PAYMENT_STATUS_CREATED

	case PaymentStatusProcessing:
		return orderpb.OrderPaymentStatus_ORDER_PAYMENT_STATUS_TRANSACTION_CREATED

	case PaymentStatusPaid:
		return orderpb.OrderPaymentStatus_ORDER_PAYMENT_STATUS_PAID

	case PaymentStatusCompleted:
		return orderpb.OrderPaymentStatus_ORDER_PAYMENT_STATUS_PAID

	default:
		return orderpb.OrderPaymentStatus_ORDER_PAYMENT_STATUS_UNSPECIFIED
	}
}
