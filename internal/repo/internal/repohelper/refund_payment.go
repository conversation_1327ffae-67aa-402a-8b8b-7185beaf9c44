package repohelper

import (
	"github.com/shopspring/decimal"

	orderpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/order/v1"
	"github.com/MoeGolibrary/moego-svc-order-v2/internal/model"
)

type RefundPaymentModel struct {
	ID              int64 `gorm:"primaryKey"`
	Module          string
	InvoiceID       int64
	RefundID        int64
	CustomerID      int64
	StaffID         int64
	Method          string
	Amount          decimal.Decimal
	Status          RefundStatus
	CreateTime      int64
	UpdateTime      int64
	StripeRefundID  string
	OriginPaymentID int64
	MethodID        int64
	BusinessID      int64
	Reason          string
	Error           string
	SourcePaymentID string
	GroomingID      int64
	CompanyID       int64
	BookingFee      decimal.Decimal
}

func (rp *RefundPaymentModel) ToRefundOrderPayment() *model.RefundOrderPayment {
	rop := &model.RefundOrderPayment{
		ID:              rp.ID,
		OrderID:         rp.InvoiceID,
		OrderPaymentID:  rp.OriginPaymentID,
		RefundOrderID:   0,     // 无该字段
		RefundPaymentID: rp.ID, // RefundPayment 就是本身.
		CompanyID:       rp.CompanyID,
		BusinessID:      rp.BusinessID,
		StaffID:         rp.StaffID,
		CustomerID:      rp.CustomerID,
		RefundPaymentMethod: model.RefundPaymentMethod{
			ID:     rp.MethodID,
			Method: rp.Method,
			Extra: model.RefundPaymentMethodExtra{
				SourcePaymentID: rp.SourcePaymentID,
				StripeRefundID:  rp.StripeRefundID,
			},
			Vendor: "", // 无该字段
		},
		CurrencyCode:         "", // 无该字段
		RefundAmount:         rp.Amount.Add(rp.BookingFee),
		RefundConvenienceFee: decimal.Decimal{},
		Description:          rp.Reason,
		RefundStatus:         rp.Status.ToOrderPaymentStatus(),
		RefundStatusReason:   "", // Fill below
		CreateTime:           rp.CreateTime,
		RefundTime:           0, // Fill below
		FailTime:             0,
		UpdateTime:           rp.UpdateTime,
	}

	switch rop.RefundStatus {
	case orderpb.RefundOrderPaymentStatus_REFUND_ORDER_PAYMENT_STATUS_FAILED:
		rop.RefundStatusReason = rp.Error

		return rop

	case orderpb.RefundOrderPaymentStatus_REFUND_ORDER_PAYMENT_STATUS_REFUNDED:
		// Refund 无 Refund Time 记录，用 Update Time 来顶替一下
		rop.RefundTime = rp.UpdateTime

		return rop

	default:
		return rop
	}
}
