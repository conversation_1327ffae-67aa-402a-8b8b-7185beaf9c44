package repohelper

import (
	orderpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/order/v1"
)

type RefundStatus int8

const (
	RefundStatusInit    = RefundStatus(-2)
	RefundStatusCreated = RefundStatus(0)
	RefundStatusFailed  = RefundStatus(-1)
)

func (rs RefundStatus) ToOrderPaymentStatus() orderpb.RefundOrderPaymentStatus {
	switch rs {
	case RefundStatusInit:
		// Init 表示的是等待分账ing，用户不应该看到这个状态。
		// 这里先认为其与 Created 状态等价。
		return orderpb.RefundOrderPaymentStatus_REFUND_ORDER_PAYMENT_STATUS_CREATED

	case RefundStatusCreated:
		// 当前 RefundPayment 创建就表示已经退完了。
		return orderpb.RefundOrderPaymentStatus_REFUND_ORDER_PAYMENT_STATUS_REFUNDED

	case RefundStatusFailed:
		return orderpb.RefundOrderPaymentStatus_REFUND_ORDER_PAYMENT_STATUS_FAILED

	default:
		return orderpb.RefundOrderPaymentStatus_REFUND_ORDER_PAYMENT_STATUS_UNSPECIFIED
	}
}
