package serializer

import (
	"gorm.io/gorm/schema"

	orderpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/order/v1"
)

func init() {
	schema.RegisterSerializer(
		"proto_enum_legacy_order_payment_status", &ProtoEnumSerializer{
			CustomMapping: map[string]string{
				"":             orderpb.OrderModel_PAYMENT_STATUS_UNSPECIFIED.String(),
				"UNPAID":       orderpb.OrderModel_UNPAID.String(),
				"FULLY PAID":   orderpb.OrderModel_PAID.String(),
				"PAID":         orderpb.OrderModel_PAID.String(),
				"PARTIAL PAID": orderpb.OrderModel_PARTIAL_PAID.String(),
				"PARTIAL_PAID": orderpb.OrderModel_PARTIAL_PAID.String(),
			},
		},
	)

	schema.RegisterSerializer(
		"proto_enum_legacy_order_type", &ProtoEnumSerializer{
			CustomMapping: map[string]string{
				"":       orderpb.OrderModel_ORIGIN.String(),
				"ORIGIN": orderpb.OrderModel_ORIGIN.String(),
				"EXTRA":  orderpb.OrderModel_EXTRA.String(),
			},
		},
	)

	schema.RegisterSerializer(
		"proto_enum_legacy_order_item_type", &ProtoEnumSerializer{
			CustomMapping: map[string]string{
				"":                   orderpb.ItemType_ITEM_TYPE_UNSPECIFIED.String(),
				"PRODUCT":            orderpb.ItemType_ITEM_TYPE_PRODUCT.String(),
				"PACKAGE":            orderpb.ItemType_ITEM_TYPE_PACKAGE.String(),
				"EVALUATION_SERVICE": orderpb.ItemType_ITEM_TYPE_EVALUATION_SERVICE.String(),
				"SERVICE_CHARGE":     orderpb.ItemType_ITEM_TYPE_SERVICE_CHARGE.String(),
				"membership_product": "",
				"CANCELLATION_FEE":   orderpb.ItemType_ITEM_TYPE_CANCELLATION_FEE.String(),
				"SERVICE":            orderpb.ItemType_ITEM_TYPE_SERVICE.String(),
				"NOSHOW":             orderpb.ItemType_ITEM_TYPE_NO_SHOW.String(),
				"NO_SHOW":            orderpb.ItemType_ITEM_TYPE_NO_SHOW.String(),
			},
		})
}
