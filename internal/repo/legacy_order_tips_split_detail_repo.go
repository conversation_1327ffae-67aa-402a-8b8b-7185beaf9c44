package repo

import (
	"context"

	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"gorm.io/gorm"

	"github.com/MoeGolibrary/moego-svc-order-v2/internal/model"
)

type LegacyOrderTipsSplitDetailRepo interface {
	ListByOrderID(ctx context.Context, orderID int64) ([]*model.OrderTipsSplitDetail, error)
	ListByOrderIDs(ctx context.Context, orderIDs []int64) ([]*model.OrderTipsSplitDetail, error)
}

type legacyTipsSplitRepoImpl struct {
	db *gorm.DB
}

// ListByOrderID implements LegacyOrderTipsSplitDetailRepo.
func (l *legacyTipsSplitRepoImpl) ListByOrderID(
	ctx context.Context, orderID int64,
) ([]*model.OrderTipsSplitDetail, error) {
	var tipsSplitDetails []*model.OrderTipsSplitDetail
	if err := l.withContext(ctx).Where("order_id = ? ", orderID).Find(&tipsSplitDetails).Error; err != nil {
		return nil, status.Errorf(codes.Internal, "failed to list tips split details by order id: %v", err)
	}

	return tipsSplitDetails, nil
}

// ListByOrderIDs implements LegacyOrderTipsSplitDetailRepo.
func (l *legacyTipsSplitRepoImpl) ListByOrderIDs(
	ctx context.Context, orderIDs []int64,
) ([]*model.OrderTipsSplitDetail, error) {
	var tipsSplitDetails []*model.OrderTipsSplitDetail
	if err := l.withContext(ctx).Where("order_id IN ? ", orderIDs).Find(&tipsSplitDetails).Error; err != nil {
		return nil, status.Errorf(codes.Internal, "failed to list tips split details by order ids: %v", err)
	}

	return tipsSplitDetails, nil
}

func NewLegacyOrderTipsSplitDetailRepo(db *gorm.DB) LegacyOrderTipsSplitDetailRepo {
	return &legacyTipsSplitRepoImpl{
		db: db,
	}
}

func (l *legacyTipsSplitRepoImpl) withContext(ctx context.Context) *gorm.DB {
	const tableName = "order_tips_split_detail"
	return l.db.WithContext(ctx).Table(tableName)
}
