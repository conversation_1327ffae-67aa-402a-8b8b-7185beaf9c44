package repo

import (
	"context"
	"strings"
	"time"

	"github.com/samber/lo"
	"gorm.io/gorm"

	paymentpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/payment/v1"
	orderv2svcpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/order/v2"
	"github.com/MoeGolibrary/moego-svc-order-v2/internal/model"
	"github.com/MoeGolibrary/moego-svc-order-v2/internal/repo/internal/repohelper"
)

// PaymentDB 对于老订单而言， Payment DB 中的 Payment 就是 Order Payment.
// 因此这里实现一套读的逻辑用来适配老订单.
// 后续无老订单之后移除.
type PaymentDB gorm.DB

type LegacyPaymentRepo interface {
	BatchGet(ctx context.Context, ids []int64) ([]*model.OrderPayment, error)
	ListByOrderID(ctx context.Context, orderID int64) ([]*model.OrderPayment, error)
	CountByConditions(ctx context.Context, companyID int64, conditions *ListLegacyPaymentConditions) (int64, error)
	// ListRawByConditions keeps the payment.order_payment_id field.
	ListRawByConditions(
		ctx context.Context, companyID int64, conditions *ListLegacyPaymentConditions,
		offset *int, limit *int, orderBys []*orderv2svcpb.ListOrderPaymentDetailRequest_OrderBy,
	) ([]RawOrderPayment, error)
	ListByIDsAndConditions(
		ctx context.Context, ids []int64, method paymentpb.PaymentMethod, vendor string,
	) ([]*model.OrderPayment, error)
}

type RawOrderPayment struct {
	*model.OrderPayment
	// 当前 legacy payment 关联的 order payment 的 id，可能为 0
	OrderPaymentID int64
}

type ListLegacyPaymentConditions struct {
	CustomerID int64
	StartTime  time.Time // Inclusive
	EndTime    time.Time // Exclusive
	// The IDs of legacy payment's order_payment_id
	OrderPaymentIDs []int64
}

type legacyPaymentRepo struct {
	db *gorm.DB
}

func NewLegacyPaymentRepo(db *PaymentDB) LegacyPaymentRepo {
	return &legacyPaymentRepo{db: (*gorm.DB)(db)}
}

func (repo *legacyPaymentRepo) BatchGet(ctx context.Context, ids []int64) ([]*model.OrderPayment, error) {
	payments, err := listByConditions[*repohelper.PaymentModel](
		repo.withContext(ctx), map[string][]any{"id IN (?)": {ids}},
	)
	if err != nil {
		return nil, err
	}

	return lo.Map(
		payments,
		func(p *repohelper.PaymentModel, _ int) *model.OrderPayment { return p.ToOrderPayment() },
	), nil
}

func (repo *legacyPaymentRepo) ListByOrderID(ctx context.Context, orderID int64) ([]*model.OrderPayment, error) {
	payments, err := listByKeyID[*repohelper.PaymentModel](repo.withContext(ctx), "invoice_id", orderID)
	if err != nil {
		return nil, err
	}

	return lo.Map(
		payments,
		func(p *repohelper.PaymentModel, _ int) *model.OrderPayment { return p.ToOrderPayment() },
	), nil
}

func (repo *legacyPaymentRepo) CountByConditions(
	ctx context.Context, companyID int64, conditions *ListLegacyPaymentConditions,
) (int64, error) {
	var count int64

	if err := repo.withConditions(ctx, companyID, conditions).Count(&count).Error; err != nil {
		return 0, parseDBErr(err)
	}

	return count, nil
}

var orderColumnMap = map[orderv2svcpb.ListOrderPaymentDetailRequest_OrderableField]string{
	orderv2svcpb.ListOrderPaymentDetailRequest_CREATE_TIME: "create_time",
}

func (repo *legacyPaymentRepo) ListRawByConditions(
	ctx context.Context, companyID int64, conditions *ListLegacyPaymentConditions,
	offset *int, limit *int, orderBys []*orderv2svcpb.ListOrderPaymentDetailRequest_OrderBy,
) ([]RawOrderPayment, error) {
	if len(orderBys) == 0 {
		orderBys = []*orderv2svcpb.ListOrderPaymentDetailRequest_OrderBy{
			{
				Field: orderv2svcpb.ListOrderPaymentDetailRequest_CREATE_TIME,
				Desc:  true,
			},
		}
	}

	var payments []*repohelper.PaymentModel

	orderBy := strings.Join(lo.Map(orderBys, func(o *orderv2svcpb.ListOrderPaymentDetailRequest_OrderBy, _ int) string {
		ret := orderColumnMap[o.GetField()]
		if o.GetDesc() {
			ret += " DESC"
		}

		return ret
	}), ", ")

	tx := repo.withConditions(ctx, companyID, conditions).Order(orderBy)

	if offset != nil && limit != nil {
		tx = tx.Offset(*offset).Limit(*limit).Find(&payments)
	} else {
		var batch []*repohelper.PaymentModel

		tx = tx.FindInBatches(
			&batch, defaultSelectBatchSize,
			func(_ *gorm.DB, _ int) error {
				payments = append(payments, batch...)
				return nil
			},
		)
	}

	if err := tx.Error; err != nil {
		return nil, parseDBErr(err)
	}

	return lo.Map(
		payments,
		func(p *repohelper.PaymentModel, _ int) RawOrderPayment {
			return RawOrderPayment{OrderPayment: p.ToOrderPayment(), OrderPaymentID: p.OrderPaymentID}
		},
	), nil
}

func (repo *legacyPaymentRepo) ListByIDsAndConditions(
	ctx context.Context, ids []int64, method paymentpb.PaymentMethod, vendor string,
) ([]*model.OrderPayment, error) {
	conditions := map[string][]any{
		"id IN (?)": {ids},
	}
	if method != paymentpb.PaymentMethod_PAYMENT_METHOD_UNSPECIFIED {
		conditions["method_id"] = []any{int(method)}
		// 筛选 vendor 的时候必须是 credit card 支付的退款
		if method == paymentpb.PaymentMethod_CREDIT_CARD && vendor != "" {
			conditions["vendor"] = []any{vendor}
		}
	}

	payments, err := listByConditions[*repohelper.PaymentModel](repo.withContext(ctx), conditions)
	if err != nil {
		return nil, err
	}

	return lo.Map(
		payments,
		func(p *repohelper.PaymentModel, _ int) *model.OrderPayment { return p.ToOrderPayment() },
	), nil
}

func (repo *legacyPaymentRepo) withConditions(
	ctx context.Context, companyID int64, conditions *ListLegacyPaymentConditions,
) *gorm.DB {
	tx := repo.withContext(ctx).Where("company_id = ?", companyID)

	if conditions != nil {
		if conditions.CustomerID != 0 {
			tx = tx.Where("customer_id = ?", conditions.CustomerID)
		}

		if !conditions.StartTime.Equal(time.Unix(0, 0).UTC()) {
			tx = tx.Where("create_time >= ?", conditions.StartTime.Unix())
		}

		if !conditions.EndTime.Equal(time.Unix(0, 0).UTC()) {
			tx = tx.Where("create_time < ?", conditions.EndTime.Unix())
		}

		if len(conditions.OrderPaymentIDs) > 0 {
			tx = tx.Where("order_payment_id IN (?)", conditions.OrderPaymentIDs)
		}
	}

	return tx
}

func (repo *legacyPaymentRepo) withContext(ctx context.Context) *gorm.DB {
	const tableName = "payment"
	return repo.db.WithContext(ctx).Table(tableName)
}
