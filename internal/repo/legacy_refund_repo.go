package repo

import (
	"context"
	"time"

	"github.com/samber/lo"
	"gorm.io/gorm"

	orderpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/order/v1"
	"github.com/MoeGolibrary/moego-svc-order-v2/internal/model"
	"github.com/MoeGolibrary/moego-svc-order-v2/internal/repo/internal/repohelper"
)

type LegacyRefundRepo interface {
	BatchGet(ctx context.Context, ids []int64) ([]*model.RefundOrderPayment, error)
	ListByOrderID(ctx context.Context, orderID int64) ([]*model.RefundOrderPayment, error)
	ListByPaymentIDs(ctx context.Context, paymentIDs []int64) ([]*model.RefundOrderPayment, error)
	ListByConditions(
		ctx context.Context, companyID int64, conditions *ListRefundConditions,
		// 除外 RefundOrderPaymentID 不为零的数据，即仅筛选 legacy order 创建的 refund。增加这个字段是以显式表明查询条件
		excludeNonZeroROPID bool,
	) ([]*model.RefundOrderPayment, error)
}

type legacyRefundRepo struct {
	db *gorm.DB
}

func NewLegacyRefundRepo(db *PaymentDB) LegacyRefundRepo {
	return &legacyRefundRepo{db: (*gorm.DB)(db)}
}

func (repo *legacyRefundRepo) BatchGet(ctx context.Context, ids []int64) ([]*model.RefundOrderPayment, error) {
	payments, err := listByConditions[*repohelper.RefundPaymentModel](
		repo.withContext(ctx), map[string][]any{"id IN (?)": {ids}},
	)
	if err != nil {
		return nil, err
	}

	return lo.Map(
		payments,
		func(p *repohelper.RefundPaymentModel, _ int) *model.RefundOrderPayment {
			return p.ToRefundOrderPayment()
		},
	), nil
}

func (repo *legacyRefundRepo) ListByOrderID(ctx context.Context, orderID int64) ([]*model.RefundOrderPayment, error) {
	payments, err := listByKeyID[*repohelper.RefundPaymentModel](repo.withContext(ctx), "invoice_id", orderID)
	if err != nil {
		return nil, err
	}

	return lo.Map(
		payments,
		func(p *repohelper.RefundPaymentModel, _ int) *model.RefundOrderPayment {
			return p.ToRefundOrderPayment()
		},
	), nil
}

func (repo *legacyRefundRepo) ListByPaymentIDs(
	ctx context.Context, paymentIDs []int64,
) ([]*model.RefundOrderPayment, error) {
	payments, err := listByConditions[*repohelper.RefundPaymentModel](
		repo.withContext(ctx), map[string][]any{"origin_payment_id IN (?)": {paymentIDs}},
	)
	if err != nil {
		return nil, err
	}

	return lo.Map(
		payments,
		func(p *repohelper.RefundPaymentModel, _ int) *model.RefundOrderPayment {
			return p.ToRefundOrderPayment()
		},
	), nil
}

func (repo *legacyRefundRepo) ListByConditions(
	ctx context.Context, companyID int64, conditions *ListRefundConditions, excludeNonZeroROPID bool,
) ([]*model.RefundOrderPayment, error) {
	condMap := map[string][]any{
		"company_id = ?": {companyID},
	}

	if conditions != nil {
		if conditions.BusinessID != 0 {
			condMap["business_id = ?"] = []any{conditions.BusinessID}
		}

		if conditions.CustomerID != 0 {
			condMap["customer_id = ?"] = []any{conditions.CustomerID}
		}

		if !conditions.StartTime.Equal(time.Unix(0, 0).UTC()) {
			condMap["create_time >= ?"] = []any{conditions.StartTime.Unix()}
		}

		if !conditions.EndTime.Equal(time.Unix(0, 0).UTC()) {
			condMap["create_time < ?"] = []any{conditions.EndTime.Unix()}
		}

		if len(conditions.Statuses) > 0 {
			// 兼容查询：根据 RefundStatus.ToOrderPaymentStatus 的映射逻辑来筛选
			condMap["status in (?)"] = lo.FilterMap(
				conditions.Statuses,
				func(s orderpb.RefundOrderPaymentStatus, _ int) (any, bool) {
					switch s {
					case orderpb.RefundOrderPaymentStatus_REFUND_ORDER_PAYMENT_STATUS_CREATED:
						return repohelper.RefundStatusInit, true
					case orderpb.RefundOrderPaymentStatus_REFUND_ORDER_PAYMENT_STATUS_REFUNDED:
						return repohelper.RefundStatusCreated, true
					case orderpb.RefundOrderPaymentStatus_REFUND_ORDER_PAYMENT_STATUS_FAILED:
						return repohelper.RefundStatusFailed, true
					default:
						return 0, false
					}
				},
			)
		}
	}

	if excludeNonZeroROPID {
		condMap["refund_order_payment_id = ?"] = []any{0}
	}

	payments, err := listByConditions[*repohelper.RefundPaymentModel](repo.withContext(ctx), condMap)
	if err != nil {
		return nil, err
	}

	return lo.Map(
		payments,
		func(p *repohelper.RefundPaymentModel, _ int) *model.RefundOrderPayment {
			return p.ToRefundOrderPayment()
		},
	), nil
}

func (repo *legacyRefundRepo) withContext(ctx context.Context) *gorm.DB {
	const tableName = "refund"
	return repo.db.WithContext(ctx).Table(tableName)
}
