package message

import (
	"bytes"
	"context"
	"encoding/json"

	"github.com/shopspring/decimal"

	mhttp "github.com/MoeGolibrary/go-lib/http"
)

type Client interface {
	NotifyPaymentRefunded(ctx context.Context, params *NotifyPaymentRefundedParams) error
}

type NotifyPaymentRefundedParams struct {
	BusinessID     int64
	StaffID        int64 // 需要收到消息的 staff ID
	CustomerID     int64
	OrderID        int64
	RefundedAmount decimal.Decimal // 本次 refund payment 退款成功的金额
}

type messageClient struct {
	cli mhttp.Client
}

func NewMessageClient() Client {
	return &messageClient{mhttp.NewClient("http://moego-service-message:9205")}
}

type sendInvoiceRefundedRequest struct {
	Title                    string                                `json:"title,omitempty"`
	Type                     string                                `json:"type,omitempty"`
	WebPushDto               *sendInvoiceRefundedRequestWebPushDto `json:"webPushDto,omitempty"`
	MobilePushTitle          string                                `json:"mobilePushTitle,omitempty"`
	MobilePushBody           string                                `json:"mobilePushBody,omitempty"`
	Body                     string                                `json:"body,omitempty"`
	IsSendMobilePush         *bool                                 `json:"isSendMobilePush,omitempty"`
	BusinessID               int64                                 `json:"businessId,omitempty"`
	AccountID                int64                                 `json:"accountId,omitempty"`
	TokenStaffID             int64                                 `json:"tokenStaffId,omitempty"`
	StaffIDList              []int64                               `json:"staffIdList,omitempty"`
	IsNotifyBusinessAllStaff *bool                                 `json:"isNotifyBusinessAllStaff,omitempty"`
	IsAppointmentRelated     *bool                                 `json:"isAppointmentRelated,omitempty"`
}

type sendInvoiceRefundedRequestWebPushDto struct {
	InvoiceID         int64           `json:"invoiceId,omitempty"`
	CustomerID        int64           `json:"customerId,omitempty"`
	CustomerFirstName string          `json:"customerFirstName,omitempty"`
	CustomerLastName  string          `json:"customerLastName,omitempty"`
	RefundedAmount    decimal.Decimal `json:"refundedAmount,omitempty"`
}

func (c *messageClient) NotifyPaymentRefunded(ctx context.Context, params *NotifyPaymentRefundedParams) error {
	const path = "/service/message/notification/sendPaymentRefundedNotification"

	// 不能传 TokenStaffID，否则会被过滤（“自己不能给自己发通知”）
	req := &sendInvoiceRefundedRequest{
		BusinessID:      params.BusinessID,
		StaffIDList:     []int64{params.StaffID},
		Title:           "Refund completed",
		MobilePushTitle: "Refund completed",
		// 不设置 body，message 那边会构造
		WebPushDto: &sendInvoiceRefundedRequestWebPushDto{
			CustomerID:     params.CustomerID,
			InvoiceID:      params.OrderID,
			RefundedAmount: params.RefundedAmount,
		},
	}

	payload, err := json.Marshal(req)
	if err != nil {
		return err
	}

	resp, err := c.cli.Post(ctx, path, nil, bytes.NewReader(payload))
	if err != nil {
		return err
	}

	defer func() { _ = resp.Body.Close() }()

	// The API returns a boolean indicating success/failure
	// We don't need to parse the response body for this notification API
	// as long as the HTTP request was successful
	return nil
}
