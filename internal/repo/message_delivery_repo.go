package repo

import (
	"context"
	"strconv"
	"strings"

	"gorm.io/gorm/clause"

	"github.com/MoeGolibrary/go-lib/gorm"
	eventbuspb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/event_bus/v1"
	orderpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/order/v1"
	"github.com/MoeGolibrary/moego-svc-order-v2/internal/model"
	"github.com/MoeGolibrary/moego-svc-order-v2/internal/repo/internal/repohelper"
)

type MessageDeliveryRepo interface {
	CreateRefunded(ctx context.Context, rod *model.RefundOrder) error
	CreateCanceled(ctx context.Context, od *model.Order) error
	CreateCreated(ctx context.Context, od *model.Order) error
	CreateCompleted(ctx context.Context, od *model.Order) error
}

type messageDeliveryRepo struct {
	db *gorm.DB
}

func NewMessageDeliveryRepo(db *gorm.DB) MessageDeliveryRepo {
	return &messageDeliveryRepo{db: db}
}

func (repo *messageDeliveryRepo) CreateRefunded(ctx context.Context, rod *model.RefundOrder) error {
	md := &repohelper.MessageDelivery[*orderpb.RefundOrderModel]{
		MessageType:    eventbuspb.EventType_REFUND_ORDER_COMPLETED,
		ReferenceID:    strconv.FormatInt(rod.ID, 10),
		Payload:        rod.ToPB(),
		DeliveryStatus: orderpb.EventDeliveryStatus_PENDING,
	}

	if err := repo.withContext(ctx).Save(md).Error; err != nil {
		return parseDBErr(err)
	}

	return nil
}

func (repo *messageDeliveryRepo) CreateCanceled(ctx context.Context, od *model.Order) error {
	return repo.createOrderEvent(ctx, od, eventbuspb.EventType_ORDER_CANCELED)
}

func (repo *messageDeliveryRepo) CreateCreated(ctx context.Context, od *model.Order) error {
	return repo.createOrderEvent(ctx, od, eventbuspb.EventType_ORDER_CREATED)
}

func (repo *messageDeliveryRepo) CreateCompleted(ctx context.Context, od *model.Order) error {
	/*
		历史原因, 这里并不是发送的 OrderModelV1, 这里为了兼容, 只能先转换.
		{
			"id": "*********",
			"orderType": "ORIGIN",
			"status": "2",
			"sourceType": "APPOINTMENT",
			"paymentStatus": "PAID",
			"fulfillmentStatus": "INIT",
			"completeTime": "1749551246",
			"sourceId": "22240889",
			"paidAmount": 3.0,
			"totalAmount": 3.0,
			"companyId": "100661",
			"businessId": "100770",
			"orderVersion": 4
		}
	*/
	completedEvent := &orderpb.OrderModelHistoryView{
		Id:            od.ID,
		OrderType:     od.OrderType,
		Status:        strconv.FormatInt(int64(od.Status), 10),
		SourceType:    orderpb.OrderSourceType(orderpb.OrderSourceType_value[strings.ToUpper(od.SourceType)]),
		PaymentStatus: od.PaymentStatus,
		FulfillmentStatus: orderpb.OrderModel_FulfillmentStatus(
			orderpb.OrderModel_FulfillmentStatus_value[strings.ToUpper(od.FulfillmentStatus)],
		),
		CompleteTime: 0,
		SourceId:     od.SourceID,
		PaidAmount:   od.PaidAmount.InexactFloat64(),
		TotalAmount:  od.TotalAmount.InexactFloat64(),
		CompanyId:    od.CompanyID,
		BusinessId:   od.BusinessID,
		OrderVersion: int32(od.OrderVersion),
	}

	md := &repohelper.MessageDelivery[*orderpb.OrderModelHistoryView]{
		MessageType:    eventbuspb.EventType_ORDER_COMPLETED,
		ReferenceID:    strconv.FormatInt(od.ID, 10),
		Payload:        completedEvent,
		DeliveryStatus: orderpb.EventDeliveryStatus_PENDING,
	}

	if err := repo.withContext(ctx).Clauses(
		clause.OnConflict{DoNothing: true}, // 目前在老 order 服务中也会创建消息，这里先忽略冲突的情况.
	).Save(md).Error; err != nil {
		return parseDBErr(err)
	}

	return nil
}

func (repo *messageDeliveryRepo) createOrderEvent(
	ctx context.Context, od *model.Order, eventType eventbuspb.EventType,
) error {
	md := &repohelper.MessageDelivery[*orderpb.OrderModelV1]{
		MessageType:    eventType,
		ReferenceID:    strconv.FormatInt(od.ID, 10),
		Payload:        od.ToPB(),
		DeliveryStatus: orderpb.EventDeliveryStatus_PENDING,
	}

	if err := repo.withContext(ctx).Clauses(
		clause.OnConflict{DoNothing: true}, // 目前在老 order 服务中也会创建消息，这里先忽略冲突的情况.
	).Save(md).Error; err != nil {
		return parseDBErr(err)
	}

	return nil
}

func (repo *messageDeliveryRepo) withContext(ctx context.Context) *gorm.DB {
	const tableName = "public.message_delivery"

	return repo.db.WithContext(ctx).Table(tableName)
}
