package repo

import (
	"context"
	"database/sql"

	"github.com/samber/lo"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"

	"github.com/MoeGolibrary/moego-svc-order-v2/internal/model"
)

type MigrationRepo interface {
	MigrateToV4(ctx context.Context, orders []*model.Order) error
}

type migrationRepo struct {
	db *gorm.DB
}

func newMigrationRepo(db *gorm.DB) MigrationRepo {
	return &migrationRepo{db: db}
}

func (repo *migrationRepo) MigrateToV4(ctx context.Context, orders []*model.Order) error {
	type helper struct {
		ID         int64 `gorm:"primaryKey;autoIncrement"`
		OrderID    int64
		CompanyID  int64
		BusinessID int64
		CustomerID int64
		GUID       string `gorm:"column:guid"`
		SourceType string
		SourceID   int64
		CreateBy   int64
		UpdateBy   int64
		CreateTime sql.NullTime `gorm:"autoCreateTime"`
		UpdateTime sql.NullTime `gorm:"autoUpdateTime"`
	}

	helpers := lo.Map(orders, func(it *model.Order, _ int) helper {
		return helper{
			OrderID:    it.ID,
			CompanyID:  it.CompanyID,
			BusinessID: it.BusinessID,
			CustomerID: it.CustomerID,
			GUID:       it.GUID,
			SourceType: it.SourceType,
			SourceID:   it.SourceID,
			CreateBy:   it.CreateBy,
			UpdateBy:   it.UpdateBy,
		}
	})

	if err := repo.db.WithContext(ctx).Table("public.migration_record").
		CreateInBatches(helpers, defaultInsertBatchSize).Error; err != nil {
		return parseDBErr(err)
	}

	orderIDs := lo.Map(orders, func(it *model.Order, _ int) int64 { return it.ID })
	if err := repo.db.WithContext(ctx).Table("public.order").
		Where("id IN (?)", orderIDs).
		Updates(map[string]any{
			"update_time":  clause.Expr{SQL: "NOW()"},
			"business_id":  clause.Expr{SQL: "-business_id"},
			"company_id":   clause.Expr{SQL: "-company_id"},
			"customer_id":  clause.Expr{SQL: "-customer_id"},
			"create_by":    clause.Expr{SQL: "-create_by"},
			"update_by":    clause.Expr{SQL: "-update_by"},
			"order_ref_id": clause.Expr{SQL: "-order_ref_id"},
			"source_id":    clause.Expr{SQL: "-source_id"},
			"guid":         clause.Expr{SQL: "CONCAT(guid, '_migrated_v4')"},
		}).Error; err != nil {
		return parseDBErr(err)
	}

	return nil
}
