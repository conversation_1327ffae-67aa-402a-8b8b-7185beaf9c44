package repo

import (
	"context"

	"gorm.io/gorm"

	"github.com/MoeGolibrary/moego-svc-order-v2/internal/model"
)

// OrderItemAssignedAmountRepo 定义了订单项分配金额的仓储接口
type OrderItemAssignedAmountRepo interface {
	// BatchGetByOrderID 根据订单ID批量获取订单项的分配金额信息
	BatchGetByOrderID(ctx context.Context, orderID int64) ([]*model.OrderItemAssignedAmount, error)

	// BatchInsert 批量插入订单项分配金额记录
	BatchInsert(ctx context.Context, amounts []*model.OrderItemAssignedAmount) error
}

type orderItemAssignedAmountRepo struct {
	db *gorm.DB
}

// NewOrderItemAssignedAmountRepo 创建订单项分配金额仓储实例
func NewOrderItemAssignedAmountRepo(db *gorm.DB) OrderItemAssignedAmountRepo {
	return &orderItemAssignedAmountRepo{db: db}
}

func (repo *orderItemAssignedAmountRepo) BatchGetByOrderID(ctx context.Context,
	orderID int64,
) ([]*model.OrderItemAssignedAmount, error) {
	var amounts []*model.OrderItemAssignedAmount

	amounts, err := listByKeyID[*model.OrderItemAssignedAmount](repo.withContext(ctx), "order_id", orderID)
	if err != nil {
		return nil, err
	}

	return amounts, nil
}

func (repo *orderItemAssignedAmountRepo) BatchInsert(ctx context.Context,
	amounts []*model.OrderItemAssignedAmount,
) error {
	if len(amounts) == 0 {
		return nil
	}

	if err := repo.withContext(ctx).CreateInBatches(amounts, defaultInsertBatchSize).Error; err != nil {
		return parseDBErr(err)
	}

	return nil
}

func (repo *orderItemAssignedAmountRepo) withContext(ctx context.Context) *gorm.DB {
	const tableName = "public.order_item_assigned_amount"
	return repo.db.WithContext(ctx).Table(tableName)
}
