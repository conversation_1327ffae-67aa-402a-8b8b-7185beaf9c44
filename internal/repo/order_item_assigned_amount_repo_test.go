package repo

import (
	"context"
	"regexp"
	"testing"
	"time"

	sqlmock "github.com/DATA-DOG/go-sqlmock"
	"github.com/stretchr/testify/suite"

	"github.com/MoeGolibrary/go-lib/gorm"
)

type OrderItemAssignedAmountRepoTestSuite struct {
	suite.Suite
	repo OrderItemAssignedAmountRepo

	mockDB  *gorm.DB
	sqlMock sqlmock.Sqlmock
	ctx     context.Context
}

func TestOrderItemAssignedAmountRepo(t *testing.T) {
	suite.Run(t, new(OrderItemAssignedAmountRepoTestSuite))
}

func (ts *OrderItemAssignedAmountRepoTestSuite) SetupTest() {
	ts.mockDB, ts.sqlMock = gorm.MockPostgres(ts.T())
	ts.repo = NewOrderItemAssignedAmountRepo(ts.mockDB)
}

func (ts *OrderItemAssignedAmountRepoTestSuite) TestBatchGetByOrderID() {
	timestamp := time.Now()

	ts.sqlMock.ExpectQuery(regexp.QuoteMeta(`SELECT * FROM "public"."order_item_assigned_amount"  WHERE order_id = $1 ORDER BY "order_item_assigned_amount"."id" LIMIT $2`)).
		WithArgs(1001, 50).
		WillReturnRows(
			sqlmock.NewRows([]string{
				"id", "order_id", "item_id", "amount", "currency_code",
				"created_at", "updated_at",
			}).
				AddRow(1, 1001, 1, mustParseDecimal("100"), "USD", timestamp, timestamp).
				AddRow(2, 1001, 2, mustParseDecimal("50"), "USD", timestamp, timestamp),
		)

	testCases := []struct {
		name        string
		orderID     int64
		expectedLen int
		expectError bool
	}{
		{
			name:        "成功获取订单的分配金额记录",
			orderID:     1001,
			expectedLen: 2,
			expectError: false,
		},
	}

	for _, tc := range testCases {
		ts.Run(tc.name, func() {
			results, err := ts.repo.BatchGetByOrderID(ts.ctx, tc.orderID)

			if tc.expectError {
				ts.Error(err)
			} else {
				ts.NoError(err)
				ts.Len(results, tc.expectedLen)

				if tc.expectedLen > 0 {
					// 验证返回数据的正确性
					ts.Equal(tc.orderID, results[0].OrderID)
					ts.Equal("USD", results[0].CurrencyCode)
					ts.Equal(mustParseDecimal("100"), results[0].Amount)
				}
			}
		})
	}

	// 验证所有的数据库操作都已执行
	ts.NoError(ts.sqlMock.ExpectationsWereMet())
}

// test batch get by order id with no data
func (ts *OrderItemAssignedAmountRepoTestSuite) TestBatchGetByOrderIDWithNoData() {
	ts.sqlMock.ExpectQuery(regexp.QuoteMeta(`SELECT * FROM "public"."order_item_assigned_amount" WHERE order_id = $1 ORDER BY "order_item_assigned_amount"."id" LIMIT $2`)).
		WithArgs(1001, 50).
		WillReturnRows(sqlmock.NewRows([]string{}))

	results, err := ts.repo.BatchGetByOrderID(ts.ctx, 1001)
	ts.NoError(err)
	ts.Len(results, 0)
}
