package repo

import (
	"context"

	"gorm.io/gorm"

	"github.com/MoeGolibrary/moego-svc-order-v2/internal/model"
)

type orderItemPriceDetailRepo struct {
	db *gorm.DB
}

func newOrderItemPriceDetailRepo(db *gorm.DB) *orderItemPriceDetailRepo {
	return &orderItemPriceDetailRepo{db: db}
}

func (repo *orderItemPriceDetailRepo) BatchCreate(ctx context.Context, priceItems []*model.PriceItem) error {
	if err := repo.withContext(ctx).CreateInBatches(priceItems, defaultInsertBatchSize).Error; err != nil {
		return parseDBErr(err)
	}

	return nil
}

func (repo *orderItemPriceDetailRepo) ListByOrderID(ctx context.Context, orderID int64) (
	[]*model.PriceItem, error,
) {
	return listByKeyID[*model.PriceItem](repo.withContext(ctx), "order_id", orderID)
}

func (repo *orderItemPriceDetailRepo) withContext(ctx context.Context) *gorm.DB {
	const tableName = "public.order_item_price_detail"

	return repo.db.WithContext(ctx).Table(tableName)
}
