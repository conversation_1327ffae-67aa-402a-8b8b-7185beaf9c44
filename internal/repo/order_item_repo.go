package repo

import (
	"context"
	"time"

	"github.com/samber/lo"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"gorm.io/gorm"

	"github.com/MoeGolibrary/moego-svc-order-v2/internal/model"
	"github.com/MoeGolibrary/moego-svc-order-v2/internal/repo/internal/repohelper"
)

//go:generate mockery --name=OrderItemRepo --keeptree --case underscore --with-expecter --output ../mocks
type OrderItemRepo interface {
	BatchCreate(ctx context.Context, items []*model.OrderItem) error

	Get(ctx context.Context, id int64) (*model.OrderItem, error)
	BatchGet(ctx context.Context, ids []int64) ([]*model.OrderItem, error)
	ListByOrder(ctx context.Context, orderID int64) ([]*model.OrderItem, error)
	ListItemsByOrderIDs(
		ctx context.Context,
		orderIDs []int64,
	) ([]*model.OrderItem, error)

	UpdateRefund(ctx context.Context, refundItem *model.RefundOrderItem) (int64, error)

	// Staff 后续属于 Fulfillment 的内容，修改的时候可以不加锁
	UpdateStaffID(ctx context.Context, id, staffID int64) (int64, error)
}

type orderItemRepo struct {
	db *gorm.DB

	taxRepo         *orderLineTaxRepo
	priceDetailRepo *orderItemPriceDetailRepo
}

func NewOrderItemRepo(db *gorm.DB) OrderItemRepo {
	return &orderItemRepo{
		db:              db,
		taxRepo:         newOrderLineTaxRepo(db),
		priceDetailRepo: newOrderItemPriceDetailRepo(db),
	}
}

// ListItemsByOrderIDs implements OrderItemRepo.
func (repo *orderItemRepo) ListItemsByOrderIDs(
	ctx context.Context,
	orderIDs []int64,
) ([]*model.OrderItem, error) {
	orderItems, err := listByConditions[*model.OrderItem](
		repo.withContext(ctx),
		map[string][]any{"order_id IN (?)": {orderIDs}},
	)
	if err != nil {
		return nil, err
	}

	if len(orderItems) == 0 {
		return nil, nil
	}

	return orderItems, nil
}

// UpdateStaffID implements OrderItemRepo.
func (repo *orderItemRepo) UpdateStaffID(ctx context.Context, id, staffID int64) (int64, error) {
	tx := repo.withContext(ctx).Where("id =?", id).Updates(
		map[string]interface{}{
			"staff_id":    staffID,
			"update_time": toTimestamp(time.Now()),
		},
	)
	if tx.Error != nil {
		return 0, parseDBErr(tx.Error)
	}

	return tx.RowsAffected, nil
}

func (repo *orderItemRepo) BatchCreate(ctx context.Context, items []*model.OrderItem) error {
	if len(items) == 0 {
		return nil
	}

	itemPriceDetails := make([]*model.PriceItem, 0, len(items))
	for _, it := range items {
		itemPriceDetails = append(itemPriceDetails, it.SubTotalItems...)
	}

	if err := repo.withContext(ctx).CreateInBatches(items, defaultInsertBatchSize).Error; err != nil {
		return parseDBErr(err)
	}

	// OrderItemID & OrderID 已经通过 OrderItem.AfterCreate 钩子更新到 price item 中了.

	if err := repo.priceDetailRepo.BatchCreate(ctx, itemPriceDetails); err != nil {
		return parseDBErr(err)
	}

	taxedItems := lo.Filter(
		items, func(it *model.OrderItem, _ int) bool {
			return it.Tax.ID > 0
		},
	)

	taxes := lo.Map(
		taxedItems, func(it *model.OrderItem, _ int) *repohelper.OrderItemTaxHelper {
			return &repohelper.OrderItemTaxHelper{
				ID:          0,
				OrderID:     it.OrderID,
				OrderItemID: it.ID,
				Tax:         it.Tax,
				IsDeleted:   false,
				BusinessID:  it.BusinessID,
				ApplyType:   "item", // 固定值
				ApplyBy:     0,      // 固定值
			}
		},
	)

	return repo.taxRepo.batchCreate(ctx, taxes)
}

func (repo *orderItemRepo) Get(ctx context.Context, id int64) (*model.OrderItem, error) {
	item, err := getByID[*model.OrderItem](repo.withContext(ctx), id)
	if err != nil {
		return nil, err
	}

	if err := repo.attachExtraData(ctx, item.OrderID, item); err != nil {
		return nil, err
	}

	return item, nil
}

func (repo *orderItemRepo) BatchGet(ctx context.Context, ids []int64) ([]*model.OrderItem, error) {
	orderItems, err := listByConditions[*model.OrderItem](
		repo.withContext(ctx),
		map[string][]any{"id IN (?)": {ids}},
	)
	if err != nil {
		return nil, err
	}

	if len(orderItems) == 0 {
		return nil, nil
	}

	orderID := orderItems[0].OrderID

	if err := repo.attachExtraData(ctx, orderID, orderItems...); err != nil {
		return nil, err
	}

	return orderItems, nil
}

func (repo *orderItemRepo) ListByOrder(ctx context.Context, orderID int64) ([]*model.OrderItem, error) {
	orderItems, err := listByKeyID[*model.OrderItem](repo.withContext(ctx), "order_id", orderID)
	if err != nil {
		return nil, err
	}

	if err := repo.attachExtraData(ctx, orderID, orderItems...); err != nil {
		return nil, err
	}

	return orderItems, nil
}

func (repo *orderItemRepo) attachExtraData(ctx context.Context, orderID int64, orderItems ...*model.OrderItem) error {
	for _, attach := range []func(ctx context.Context, orderID int64, items ...*model.OrderItem) error{
		repo.attachPriceDetail,
		repo.attachTax,
	} {
		if err := attach(ctx, orderID, orderItems...); err != nil {
			return err
		}
	}

	if attachErr := repo.attachTax(ctx, orderID, orderItems...); attachErr != nil {
		return attachErr
	}

	return nil
}

func (repo *orderItemRepo) attachPriceDetail(ctx context.Context, orderID int64, items ...*model.OrderItem) error {
	priceDetails, err := repo.priceDetailRepo.ListByOrderID(ctx, orderID)
	if err != nil {
		return err
	}

	itemIDToPriceDetails := lo.GroupBy(
		priceDetails,
		func(pd *model.PriceItem) int64 { return pd.OrderItemID },
	)

	for _, it := range items {
		it.SubTotalItems = itemIDToPriceDetails[it.ID]
	}

	return nil
}

func (repo *orderItemRepo) attachTax(ctx context.Context, orderID int64, items ...*model.OrderItem) error {
	itemsWithoutTax := lo.Filter(
		items,
		func(it *model.OrderItem, _ int) bool { return it.Tax.ID == 0 },
	)

	switch len(itemsWithoutTax) {
	case 0:
		// 没有需要补充的 Item.
		return nil

	case 1:
		// 只有一个需要补的 Item.
		item := itemsWithoutTax[0]

		// 补充 Tax 信息.
		taxLines, err := repo.taxRepo.listByOrderItemID(ctx, item.ID)
		if err != nil {
			return err
		}

		if tax, ok := lo.Find(
			taxLines,
			func(it *repohelper.OrderItemTaxHelper) bool { return !it.IsDeleted },
		); ok {
			item.Tax = tax.Tax
		}

		return nil

	default:
		// 有多个需要补的 Item.
		taxLines, err := repo.taxRepo.listByOrderID(ctx, orderID)
		if err != nil {
			return err
		}

		itemIDToTaxLines := lo.GroupBy(
			lo.Filter(
				taxLines,
				func(it *repohelper.OrderItemTaxHelper, _ int) bool { return !it.IsDeleted },
			),
			func(it *repohelper.OrderItemTaxHelper) int64 { return it.OrderItemID },
		)

		for _, it := range itemsWithoutTax {
			tax, ok := itemIDToTaxLines[it.ID]
			if !ok || len(tax) == 0 {
				continue
			}

			// 目前一个 Item 只会有一条有效的 Tax.
			it.SetTax(tax[0].Tax)
		}

		return nil
	}
}

func (repo *orderItemRepo) UpdateRefund(ctx context.Context, refundItem *model.RefundOrderItem) (int64, error) {
	tx := repo.withContext(ctx).Where("id = ?", refundItem.OrderItemID).
		Where("total_amount >= ?", gorm.Expr("refunded_amount + ?", refundItem.RefundTotalAmount)).
		Where("tax_amount >= ?", gorm.Expr("refunded_tax_amount + ?", refundItem.RefundTax.Amount)).
		Where("discount_amount >= ?", gorm.Expr("refunded_discount_amount + ?", refundItem.RefundDiscountAmount)).
		Where("quantity >= ?", gorm.Expr("purchased_quantity + refunded_quantity + ?", refundItem.RefundQuantity)).
		Updates(
			map[string]interface{}{
				"refunded_amount":          gorm.Expr("refunded_amount + ?", refundItem.RefundTotalAmount),
				"refunded_tax_amount":      gorm.Expr("refunded_tax_amount + ?", refundItem.RefundTax.Amount),
				"refunded_discount_amount": gorm.Expr("refunded_discount_amount + ?", refundItem.RefundDiscountAmount),
				"refunded_quantity":        gorm.Expr("refunded_quantity + ?", refundItem.RefundQuantity),
				"refunded_deposit_amount":  gorm.Expr("refunded_deposit_amount + ?", refundItem.RefundDepositAmount),
				"update_time":              toTimestamp(time.Now()),
			},
		)

	if tx.Error != nil {
		return 0, parseDBErr(tx.Error)
	}

	if tx.RowsAffected == 0 {
		return 0, status.Error(codes.FailedPrecondition, "cannot update order item for refund")
	}

	return tx.RowsAffected, nil
}

func (repo *orderItemRepo) withContext(ctx context.Context) *gorm.DB {
	const tableName = "public.order_line_item"

	return repo.db.WithContext(ctx).Table(tableName)
}
