package repo

import (
	"context"

	"gorm.io/gorm"

	"github.com/MoeGolibrary/moego-svc-order-v2/internal/model"
)

type OrderLineDiscountRepo interface {
	BatchCreate(ctx context.Context, orderLineDiscounts []*model.OrderLineDiscount) error

	ListByOrderID(ctx context.Context, orderID int64) ([]*model.OrderLineDiscount, error)
}

type orderLineDiscountRepo struct {
	db *gorm.DB
}

func NewOrderLineDiscountRepo(db *gorm.DB) OrderLineDiscountRepo {
	return &orderLineDiscountRepo{db: db}
}

func (repo *orderLineDiscountRepo) ListByOrderID(ctx context.Context, orderID int64) (
	[]*model.OrderLineDiscount, error,
) {
	return listByKeyID[*model.OrderLineDiscount](
		repo.withContext(ctx), "order_id", orderID,
	)
}

func (repo *orderLineDiscountRepo) BatchCreate(
	ctx context.Context,
	orderLineDiscounts []*model.OrderLineDiscount,
) error {
	if len(orderLineDiscounts) == 0 {
		return nil
	}

	if err := repo.withContext(ctx).CreateInBatches(orderLineDiscounts, defaultInsertBatchSize).Error; err != nil {
		return parseDBErr(err)
	}

	return nil
}

func (repo *orderLineDiscountRepo) withContext(ctx context.Context) *gorm.DB {
	const tableName = "public.order_line_discount"
	return repo.db.WithContext(ctx).Table(tableName)
}
