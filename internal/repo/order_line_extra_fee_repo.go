package repo

import (
	"context"

	"gorm.io/gorm"

	"github.com/MoeGolibrary/moego-svc-order-v2/internal/model"
	"github.com/MoeGolibrary/moego-svc-order-v2/internal/repo/internal/repohelper"
)

type orderLineExtraFeeRepo struct {
	db *gorm.DB
}

func newOrderLineExtraFeeRepo(db *gorm.DB) *orderLineExtraFeeRepo {
	return &orderLineExtraFeeRepo{db: db}
}

func (repo *orderLineExtraFeeRepo) RefundConvenienceFee(ctx context.Context, rod *model.RefundOrder) error {
	ole := &repohelper.OrderLineExtraFee{}
	ole.ApplyRefundOrder(rod)

	if err := repo.withContext(ctx).Create(&ole).Error; err != nil {
		return parseDBErr(err)
	}

	return nil
}

func (repo *orderLineExtraFeeRepo) PaymentConvenienceFee(ctx context.Context, op *model.OrderPayment) error {
	ole := &repohelper.OrderLineExtraFee{}
	ole.ApplyPaymentOrder(op)

	if err := repo.withContext(ctx).Create(&ole).Error; err != nil {
		return parseDBErr(err)
	}

	return nil
}

func (repo *orderLineExtraFeeRepo) withContext(ctx context.Context) *gorm.DB {
	const tableName = "public.order_line_extra_fee"

	return repo.db.WithContext(ctx).Table(tableName)
}
