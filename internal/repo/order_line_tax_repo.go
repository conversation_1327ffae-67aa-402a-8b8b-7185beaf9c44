package repo

import (
	"context"

	"gorm.io/gorm"

	"github.com/MoeGolibrary/moego-svc-order-v2/internal/repo/internal/repohelper"
)

type orderLineTaxRepo struct {
	db *gorm.DB
}

func newOrderLineTaxRepo(db *gorm.DB) *orderLineTaxRepo {
	return &orderLineTaxRepo{db: db}
}

func (repo *orderLineTaxRepo) batchCreate(ctx context.Context, taxes []*repohelper.OrderItemTaxHelper) error {
	if err := repo.withContext(ctx).CreateInBatches(taxes, defaultInsertBatchSize).Error; err != nil {
		return parseDBErr(err)
	}

	return nil
}

func (repo *orderLineTaxRepo) listByOrderID(ctx context.Context, orderID int64) (
	[]*repohelper.OrderItemTaxHelper, error,
) {
	return listByKeyID[*repohelper.OrderItemTaxHelper](repo.withContext(ctx), "order_id", orderID)
}

func (repo *orderLineTaxRepo) listByOrderItemID(
	ctx context.Context, orderItemID int64,
) ([]*repohelper.OrderItemTaxHelper, error) {
	return listByKeyID[*repohelper.OrderItemTaxHelper](repo.withContext(ctx), "order_item_id", orderItemID)
}

func (repo *orderLineTaxRepo) withContext(ctx context.Context) *gorm.DB {
	const tableName = "public.order_line_tax"
	return repo.db.WithContext(ctx).Table(tableName)
}
