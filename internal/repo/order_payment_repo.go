package repo

import (
	"context"
	"fmt"
	"time"

	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"

	orderpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/order/v1"
	"github.com/MoeGolibrary/moego-svc-order-v2/internal/model"
)

type OrderPaymentRepo interface {
	Get(ctx context.Context, id int64) (*model.OrderPayment, error)
	GetForUpdate(ctx context.Context, id int64) (*model.OrderPayment, error)
	BatchGet(ctx context.Context, ids []int64) ([]*model.OrderPayment, error)

	ListByOrderID(ctx context.Context, orderID int64) ([]*model.OrderPayment, error)

	UpdateRefund(ctx context.Context, refundOrderPayment *model.RefundOrderPayment) (int64, error)
	UpdateTransactionCreated(ctx context.Context, op *model.OrderPayment) (int64, error)
	UpdatePaymentStatus(ctx context.Context, id int64, targetStatus orderpb.OrderPaymentStatus,
		paymentMethod string) (int64, error)
	UpdateForPaid(ctx context.Context, op *model.OrderPayment) error
	CancelOrderPayment(ctx context.Context, orderPayment *model.OrderPayment) (int64, error)

	BatchCreate(ctx context.Context, orderPayments []*model.OrderPayment) error
	BatchUpdateTransactionCreated(ctx context.Context, orderPayments []*model.OrderPayment) (int64, error)

	Save(ctx context.Context, op *model.OrderPayment) error

	Create(ctx context.Context, op *model.OrderPayment) error
}

type orderPaymentRepo struct {
	db *gorm.DB
}

func NewOrderPaymentRepo(db *gorm.DB) OrderPaymentRepo {
	return &orderPaymentRepo{db: db}
}

func (repo *orderPaymentRepo) Get(ctx context.Context, id int64) (*model.OrderPayment, error) {
	return getByID[*model.OrderPayment](repo.withContext(ctx), id)
}

func (repo *orderPaymentRepo) GetForUpdate(ctx context.Context, id int64) (*model.OrderPayment, error) {
	return getByID[*model.OrderPayment](
		repo.withContext(ctx).Clauses(
			clause.Locking{
				Strength: clause.LockingStrengthUpdate,
			},
		), id,
	)
}

func (repo *orderPaymentRepo) BatchGet(ctx context.Context, ids []int64) ([]*model.OrderPayment, error) {
	return listByConditions[*model.OrderPayment](
		repo.withContext(ctx),
		map[string][]any{"id IN (?)": {ids}},
	)
}

func (repo *orderPaymentRepo) ListByOrderID(ctx context.Context, orderID int64) ([]*model.OrderPayment, error) {
	return listByKeyID[*model.OrderPayment](repo.withContext(ctx), "order_id", orderID)
}

func (repo *orderPaymentRepo) UpdateRefund(ctx context.Context, refundOrderPayment *model.RefundOrderPayment) (
	int64, error,
) {
	tx := repo.withContext(ctx).Where("id = ?", refundOrderPayment.OrderPaymentID).
		Where("total_amount >= ?", gorm.Expr("refunded_amount + ?", refundOrderPayment.RefundAmount)).
		Where(
			"convenience_fee >= ?", gorm.Expr(
				"refunded_convenience_fee + ?", refundOrderPayment.RefundConvenienceFee,
			),
		).
		Updates(
			map[string]any{
				"refunded_amount": gorm.Expr("refunded_amount + ?", refundOrderPayment.RefundAmount),
				"refunded_convenience_fee": gorm.Expr(
					"refunded_convenience_fee + ?", refundOrderPayment.RefundConvenienceFee,
				),
				"update_time": toTimestamp(time.Now()),
			},
		)

	if tx.Error != nil {
		return 0, parseDBErr(tx.Error)
	}

	if tx.RowsAffected == 0 {
		return 0, status.Error(codes.FailedPrecondition, "cannot update order payment for refund")
	}

	return tx.RowsAffected, tx.Error
}

func (repo *orderPaymentRepo) UpdateTransactionCreated(ctx context.Context, op *model.OrderPayment) (int64, error) {
	tx := repo.withContext(ctx).Where("id = ?", op.ID).
		Where("payment_status = ?", orderpb.OrderPaymentStatus_ORDER_PAYMENT_STATUS_CREATED.String()).
		Updates(
			map[string]any{
				"payment_status": orderpb.OrderPaymentStatus_ORDER_PAYMENT_STATUS_TRANSACTION_CREATED.String(),
				"payment_id":     op.PaymentID,
				"update_time":    toTimestamp(time.Now()),
			},
		)

	if tx.Error != nil {
		return 0, parseDBErr(tx.Error)
	}

	if tx.RowsAffected == 0 {
		return 0, status.Error(codes.FailedPrecondition, "cannot update order payment for transaction created")
	}

	return tx.RowsAffected, tx.Error
}

func (repo *orderPaymentRepo) UpdatePaymentStatus(
	ctx context.Context, id int64,
	targetStatus orderpb.OrderPaymentStatus,
	paymentMethod string,
) (int64, error) {
	tx := repo.withContext(ctx).Where("id = ?", id).
		Updates(
			map[string]any{
				"payment_status": targetStatus.String(),
				"payment_method": paymentMethod,
				"update_time":    toTimestamp(time.Now()),
			},
		)

	if tx.Error != nil {
		return 0, parseDBErr(tx.Error)
	}

	if tx.RowsAffected == 0 {
		return 0, status.Errorf(
			codes.FailedPrecondition, "cannot update order payment, payment status: %s",
			targetStatus.String(),
		)
	}

	return tx.RowsAffected, tx.Error
}

// UpdateForPaid 在 OrderPayment 支付后更新相关的字段。
// See also: OrderPayment.AttachPaidPayment.
func (repo *orderPaymentRepo) UpdateForPaid(ctx context.Context, op *model.OrderPayment) error {
	if err := repo.withContext(ctx).
		Where("id = ?", op.ID).
		Updates(map[string]any{
			"payment_method_vendor":     op.PaymentMethod.Vendor,
			"paid_by":                   op.PaidBy,
			"pay_time":                  op.PayTime,
			"processing_fee":            op.ProcessingFee,
			"convenience_fee":           op.ConvenienceFee,
			"payment_tips_after_create": op.PaymentTipsAfterCreate,
			"payment_tips":              op.PaymentTips,
			"amount":                    op.Amount,
			"total_amount":              op.TotalAmount,
			"payment_status":            op.PaymentStatus.String(),
			"update_time":               toTimestamp(time.Now()),
		}).
		Error; err != nil {
		return parseDBErr(err)
	}

	return nil
}

func (repo *orderPaymentRepo) CancelOrderPayment(ctx context.Context, orderPayment *model.OrderPayment) (int64, error) {
	tx := repo.withContext(ctx).Where("id = ?", orderPayment.ID).
		Where(
			"payment_status IN ?", []string{
				orderpb.OrderPaymentStatus_ORDER_PAYMENT_STATUS_CREATED.String(),
				orderpb.OrderPaymentStatus_ORDER_PAYMENT_STATUS_TRANSACTION_CREATED.String(),
			},
		).
		Updates(
			map[string]any{
				"payment_status": orderpb.OrderPaymentStatus_ORDER_PAYMENT_STATUS_CANCELED,
				"reason":         orderPayment.PaymentStatusReason,
				"cancel_time":    toTimestamp(time.Now()),
				"update_time":    toTimestamp(time.Now()),
			},
		)

	if tx.Error != nil {
		return 0, parseDBErr(tx.Error)
	}

	if tx.RowsAffected == 0 {
		return 0, status.Error(codes.FailedPrecondition, "cannot cancel order payment")
	}

	return tx.RowsAffected, tx.Error
}

func (repo *orderPaymentRepo) BatchCreate(ctx context.Context, orderPayments []*model.OrderPayment) error {
	if len(orderPayments) == 0 {
		return nil
	}

	if err := repo.withContext(ctx).CreateInBatches(orderPayments, defaultInsertBatchSize).Error; err != nil {
		return parseDBErr(err)
	}

	return nil
}

func (repo *orderPaymentRepo) BatchUpdateTransactionCreated(ctx context.Context, ops []*model.OrderPayment,
) (int64, error) {
	if len(ops) == 0 {
		return 0, nil
	}
	// UPDATE order_payments
	// SET
	// 	payment_status = 'ORDER_PAYMENT_STATUS_CREATED',
	// 	update_time = CASE id
	// 		WHEN 101 THEN now()
	// 		WHEN 102 THEN now()
	// 		WHEN 103 THEN now()
	// 	END,
	// 	payment_id = CASE id
	// 		WHEN 101 THEN 1
	// 		WHEN 102 THEN 2
	// 		WHEN 103 THEN 3
	// 	END
	// WHERE id IN (101, 102, 103)
	// AND payment_status = 'ORDER_PAYMENT_STATUS_CREATED';

	ids := make([]int64, len(ops))
	updateCases := "CASE id"
	paymentCases := "CASE id"

	for i, op := range ops {
		ids[i] = op.ID

		// update_time CASE 构建
		updateCases += fmt.Sprintf(" WHEN %d THEN now()", op.ID)

		// payment_id CASE 构建
		paymentCases += fmt.Sprintf(" WHEN %d THEN %d", op.ID, op.PaymentID)
	}

	updateCases += " END"
	paymentCases += " END"

	tx := repo.withContext(ctx).
		Where("id IN (?)", ids).
		Where("payment_status = ?", orderpb.OrderPaymentStatus_ORDER_PAYMENT_STATUS_CREATED.String()).
		Updates(map[string]any{
			"payment_status": orderpb.OrderPaymentStatus_ORDER_PAYMENT_STATUS_TRANSACTION_CREATED.String(),
			"update_time":    gorm.Expr(updateCases),
			"payment_id":     gorm.Expr(paymentCases),
		})

	if tx.Error != nil {
		return 0, parseDBErr(tx.Error)
	}

	if tx.RowsAffected == 0 {
		return 0, status.Error(codes.FailedPrecondition, "cannot update order payments for transaction created")
	}

	return tx.RowsAffected, nil
}

func (repo *orderPaymentRepo) Save(ctx context.Context, op *model.OrderPayment) error {
	if err := repo.withContext(ctx).Omit("update_time").Save(op).Error; err != nil {
		return parseDBErr(err)
	}

	return nil
}

func (repo *orderPaymentRepo) Create(ctx context.Context, op *model.OrderPayment) error {
	if err := repo.withContext(ctx).Create(op).Error; err != nil {
		return parseDBErr(err)
	}

	return nil
}

func (repo *orderPaymentRepo) withContext(ctx context.Context) *gorm.DB {
	const tableName = "public.order_payment"

	return repo.db.WithContext(ctx).Table(tableName)
}
