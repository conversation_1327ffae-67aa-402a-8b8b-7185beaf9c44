package repo

import (
	"context"

	"gorm.io/gorm"

	"github.com/MoeGolibrary/moego-svc-order-v2/internal/model"
)

type OrderPromotionItemRepo interface {
	ListByPromotionIDs(ctx context.Context, promotionIDList []int64) ([]*model.OrderPromotionItem, error)
	BatchCreate(ctx context.Context, orderPromotions []*model.OrderPromotionItem) error
}

func NewOrderPromotionItemRepo(db *gorm.DB) OrderPromotionItemRepo {
	return &orderPromotionItemRepo{db: db}
}

type orderPromotionItemRepo struct {
	db *gorm.DB
}

func (repo *orderPromotionItemRepo) withContext(ctx context.Context) *gorm.DB {
	const tableName = "public.order_promotion_item"

	return repo.db.WithContext(ctx).Table(tableName)
}

func (repo *orderPromotionItemRepo) ListByPromotionIDs(
	ctx context.Context,
	promotionIDList []int64,
) ([]*model.OrderPromotionItem, error) {
	if len(promotionIDList) == 0 {
		return nil, nil
	}

	return listByConditions[*model.OrderPromotionItem](
		repo.withContext(ctx),
		map[string][]any{"order_promotion_id IN (?)": {promotionIDList}},
	)
}

func (repo *orderPromotionItemRepo) BatchCreate(
	ctx context.Context,
	orderPromotions []*model.OrderPromotionItem,
) error {
	return repo.withContext(ctx).
		Omit("create_time", "update_time").
		CreateInBatches(orderPromotions, defaultInsertBatchSize).
		Error
}
