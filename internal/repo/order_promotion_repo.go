package repo

import (
	"context"
	"time"

	"gorm.io/gorm"

	orderpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/order/v1"
	"github.com/MoeGolibrary/moego-svc-order-v2/internal/model"
)

type OrderPromotionRepo interface {
	ListByStatus(
		ctx context.Context,
		statusList []orderpb.OrderPromotionModel_Status,
		offset time.Duration,
	) ([]*model.OrderPromotion, error)
	ListByOrderID(ctx context.Context, orderID int64) ([]*model.OrderPromotion, error)
	BatchCreate(ctx context.Context, orderPromotions []*model.OrderPromotion) error
	BatchUpdateToApplied(ctx context.Context, orderPromotionIDList []int64,
		src []orderpb.OrderPromotionModel_Status) error
}

func NewOrderPromotionRepo(db *gorm.DB) OrderPromotionRepo {
	return &orderPromotionRepo{db: db}
}

type orderPromotionRepo struct {
	db *gorm.DB
}

func (repo *orderPromotionRepo) withContext(ctx context.Context) *gorm.DB {
	const tableName = "public.order_promotion"

	return repo.db.WithContext(ctx).Table(tableName)
}

func (repo *orderPromotionRepo) ListByStatus(
	ctx context.Context,
	statusList []orderpb.OrderPromotionModel_Status,
	offset time.Duration,
) ([]*model.OrderPromotion, error) {
	if len(statusList) == 0 {
		return nil, nil
	}

	return listByConditions[*model.OrderPromotion](
		repo.withContext(ctx),
		map[string][]any{
			"status IN (?)":    {statusList},
			"create_time <= ?": {time.Now().Add(-offset)},
		},
	)
}

func (repo *orderPromotionRepo) ListByOrderID(
	ctx context.Context,
	orderID int64,
) ([]*model.OrderPromotion, error) {
	return listByConditions[*model.OrderPromotion](
		repo.withContext(ctx),
		map[string][]any{
			"order_id = ?": {orderID},
		},
	)
}

func (repo *orderPromotionRepo) BatchCreate(
	ctx context.Context, orderPromotions []*model.OrderPromotion,
) error {
	return repo.withContext(ctx).
		Omit("create_time", "update_time").
		CreateInBatches(orderPromotions, defaultInsertBatchSize).
		Error
}

func (repo *orderPromotionRepo) BatchUpdateToApplied(
	ctx context.Context,
	orderPromotionIDList []int64,
	src []orderpb.OrderPromotionModel_Status,
) error {
	result := repo.withContext(ctx).
		Model(&model.OrderPromotion{}).
		Where("id IN ?", orderPromotionIDList).
		Where("status IN ?", src).
		Update("status", orderpb.OrderPromotionModel_APPLIED)
	if result.Error != nil {
		return result.Error
	}

	return nil
}
