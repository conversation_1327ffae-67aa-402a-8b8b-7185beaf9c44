package repo

import (
	"context"
	"database/sql"
	"regexp"
	"testing"
	"time"

	sqlmock "github.com/DATA-DOG/go-sqlmock"
	"github.com/stretchr/testify/suite"

	"github.com/MoeGolibrary/go-lib/gorm"
	orderpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/order/v1"
	"github.com/MoeGolibrary/moego-svc-order-v2/internal/model"
)

type OrderTestSuite struct {
	suite.Suite

	mockDB  *gorm.DB
	sqlMock sqlmock.Sqlmock

	repo OrderRepo
}

func TestOrderRepo(t *testing.T) {
	suite.Run(t, new(OrderTestSuite))
}

func (ts *OrderTestSuite) SetupSuite() {
	ts.mockDB, ts.sqlMock = gorm.MockPostgres(ts.T())
	ts.repo = NewOrderRepo(ts.mockDB)
}

func (ts *OrderTestSuite) TestGet() {
	timestamp, err := time.Parse("2006-01-02 15:04:05.999999 -07:00", "2024-10-08 03:56:57.028000 +00:00")
	ts.Require().NoError(err)

	// 为了验证 ORM 构造的数据，金额不是按实际规则构建的。
	expectedOrder := &model.Order{
		ID:                *********,
		OrderVersion:      9,
		CompanyID:         12,
		BusinessID:        106531,
		CustomerID:        13231081,
		CreateBy:          170639,
		UpdateBy:          170639,
		GUID:              "0a31fce1a34546ceb7f40f0dd0fe3b7b",
		Title:             "Full service - small",
		Description:       "Demo Profile",
		Status:            orderpb.OrderStatus_COMPLETED,
		FulfillmentStatus: "INIT",
		PaymentStatus:     orderpb.OrderModel_UNPAID,
		OrderType:         orderpb.OrderModel_ORIGIN,
		OrderRefID:        23333,
		ExtraChargeReason: "Extra charge reason",
		SourceType:        "appointment",
		SourceID:          11204339,
		Version:           1,
		TaxRoundMode:      model.TaxRoundModeHalfUp,
		CurrencyCode:      "USD",
		LineItemTypes:     17,
		TipsAmount:        mustParseDecimal("1.11"),
		TaxAmount:         mustParseDecimal("2.22"),
		DiscountAmount:    mustParseDecimal("3.33"),
		ConvenienceFee:    mustParseDecimal("4.44"),
		SubTotalAmount:    mustParseDecimal("5.55"),
		TipsBasedAmount:   mustParseDecimal("6.66"),
		TotalAmount:       mustParseDecimal("7.77"),
		PaidAmount:        mustParseDecimal("8.88"),
		RemainAmount:      mustParseDecimal("9.99"),
		RefundedAmount:    mustParseDecimal("10.11"),
		CompleteTime:      sql.NullTime{Time: time.Time{}, Valid: false},
		CreateTime:        sql.NullTime{Time: timestamp, Valid: true},
		UpdateTime:        sql.NullTime{Time: timestamp, Valid: true},
	}

	ts.sqlMock.ExpectQuery(regexp.QuoteMeta(`SELECT * FROM "public"."order" WHERE id = $1 LIMIT $2`)).WithArgs(
		*********, 1,
	).WillReturnRows(
		sqlmock.NewRows(
			[]string{
				"id", "business_id", "status", "payment_status", "guid", "source_type", "source_id", "line_item_types",
				"version", "customer_id", "tips_amount", "tax_amount", "discount_amount", "extra_fee_amount",
				"sub_total_amount", "tips_based_amount", "total_amount", "paid_amount", "remain_amount",
				"refunded_amount", "title", "create_by", "update_by",
				"create_time", "update_time", "description", "fulfillment_status", "complete_time", "order_type",
				"order_ref_id", "extra_charge_reason", "order_version", "currency_code", "company_id", "tax_round_mod",
			},
		).AddRow(
			// unpaid / origin 是特地改成小写的，为了测试兼容性.
			*********, 106531, 2, "unpaid", "0a31fce1a34546ceb7f40f0dd0fe3b7b", "appointment", 11204339, 17,
			1, 13231081, 1.11, 2.22, 3.33, 4.44,
			5.55, 6.66, 7.77, 8.88, 9.99,
			10.11, "Full service - small", 170639, 170639,
			timestamp, timestamp, "Demo Profile", "INIT", nil, "origin",
			23333, "Extra charge reason", 9, "USD", 12, 1,
		),
	)

	order, err := ts.repo.Get(context.Background(), *********)
	ts.Require().NoError(err)
	ts.Require().NotNil(order)
	ts.Equal(expectedOrder, order)

	ts.NotEqual(expectedOrder.TotalAmount.String(), order.SubTotalAmount.String())
}

func (ts *OrderTestSuite) TestListExtraOrder() {
	const originOrderID = 23333

	firstBatch := sqlmock.NewRows([]string{"id"})
	secondBatch := sqlmock.NewRows([]string{"id"})
	thirdBatch := sqlmock.NewRows([]string{"id"})

	for i := 0; i < defaultSelectBatchSize; i++ {
		firstBatch = firstBatch.AddRow(i + 1)
		secondBatch = secondBatch.AddRow(defaultSelectBatchSize + i + 1)
	}

	// 这里只是为了验证 ORM 构造的 SQL，并且模型本身能支持 FindInBatch 方法.
	ts.sqlMock.ExpectQuery(regexp.QuoteMeta(`SELECT * FROM "public"."order" WHERE order_ref_id = $1 ORDER BY "order"."id" LIMIT $2`)).
		WithArgs(originOrderID, defaultSelectBatchSize).
		WillReturnRows(firstBatch)
	ts.sqlMock.ExpectQuery(regexp.QuoteMeta(`SELECT * FROM "public"."order" WHERE order_ref_id = $1 AND "order"."id" > $2 ORDER BY "order"."id" LIMIT $3`)).
		WithArgs(originOrderID, defaultSelectBatchSize, defaultSelectBatchSize).
		WillReturnRows(secondBatch)
	ts.sqlMock.ExpectQuery(regexp.QuoteMeta(`SELECT * FROM "public"."order" WHERE order_ref_id = $1 AND "order"."id" > $2 ORDER BY "order"."id" LIMIT $3`)).
		WithArgs(originOrderID, 2*defaultSelectBatchSize, defaultSelectBatchSize).
		WillReturnRows(thirdBatch)

	orders, err := ts.repo.ListTailOrder(context.Background(), originOrderID)
	ts.Require().NoError(err)
	ts.Require().Len(orders, 2*defaultSelectBatchSize)

	for idx, od := range orders {
		ts.EqualValues(idx+1, od.ID)
	}
}

func (ts *OrderTestSuite) TestLegacyOrderPaymentStatusCompatibility() {
	for status, expectedStatus := range map[string]orderpb.OrderModel_PaymentStatus{
		"fully paid":   orderpb.OrderModel_PAID,
		"unpaid":       orderpb.OrderModel_UNPAID,
		"PAID":         orderpb.OrderModel_PAID,
		"partial paid": orderpb.OrderModel_PARTIAL_PAID,
		"PARTIAL_PAID": orderpb.OrderModel_PARTIAL_PAID,
		"UNPAID":       orderpb.OrderModel_UNPAID,
	} {
		ts.sqlMock.ExpectQuery(regexp.QuoteMeta(`SELECT * FROM "public"."order" WHERE id = $1 LIMIT $2`)).WithArgs(
			2333, 1,
		).WillReturnRows(sqlmock.NewRows([]string{"payment_status"}).AddRow(status))

		order, err := ts.repo.Get(context.Background(), 2333)
		ts.Require().NoError(err)
		ts.Require().NotNil(order)
		ts.Equal(expectedStatus, order.PaymentStatus)
	}
}

func (ts *OrderTestSuite) TestLegacyOrderTypeCompatibility() {
	for orderType, expectedOrderType := range map[string]orderpb.OrderModel_OrderType{
		"":       orderpb.OrderModel_ORIGIN,
		"extra":  orderpb.OrderModel_EXTRA,
		"EXTRA":  orderpb.OrderModel_EXTRA,
		"origin": orderpb.OrderModel_ORIGIN,
		"ORIGIN": orderpb.OrderModel_ORIGIN,
	} {
		ts.sqlMock.ExpectQuery(regexp.QuoteMeta(`SELECT * FROM "public"."order" WHERE id = $1 LIMIT $2`)).WithArgs(
			2333, 1,
		).WillReturnRows(sqlmock.NewRows([]string{"order_type"}).AddRow(orderType))

		order, err := ts.repo.Get(context.Background(), 2333)
		ts.Require().NoError(err)
		ts.Require().NotNil(order)
		ts.Equal(expectedOrderType, order.OrderType)
	}
}
