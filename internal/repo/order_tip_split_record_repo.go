package repo

import (
	"context"

	"gorm.io/gorm"

	"github.com/MoeGolibrary/moego-svc-order-v2/internal/model"
)

type orderTipSplitRecordRepo struct {
	db *gorm.DB
}

func newOrderTipSplitRecordRepo(db *gorm.DB) *orderTipSplitRecordRepo {
	return &orderTipSplitRecordRepo{db: db}
}

func (repo *orderTipSplitRecordRepo) Create(ctx context.Context, record *model.OrderTipsSplitRecord) (int64, error) {
	result := repo.withContext(ctx).Create(record)
	return result.RowsAffected, result.Error
}

// find the only one record by order_id
func (repo *orderTipSplitRecordRepo) ListByOrderIDs(
	ctx context.Context,
	businessID int64,
	orderIDs []int64,
) ([]*model.OrderTipsSplitRecord, error) {
	records, err := listByConditions[*model.OrderTipsSplitRecord](
		repo.withContext(ctx), map[string][]any{
			"business_id = ? and is_deleted = false and order_id in ?": {businessID, orderIDs},
		},
	)

	if len(records) == 0 {
		return nil, err
	}

	return records, nil
}

// delete by primary id
func (repo *orderTipSplitRecordRepo) DeleteByID(ctx context.Context, id int64) (int64, error) {
	result := repo.withContext(ctx).Where("id = ?", id).
		Updates(
			map[string]any{
				"is_deleted": true,
			},
		)

	return result.RowsAffected, result.Error
}

func (repo *orderTipSplitRecordRepo) withContext(ctx context.Context) *gorm.DB {
	const tableName = "public.order_tip_split_record"

	return repo.db.WithContext(ctx).Table(tableName)
}
