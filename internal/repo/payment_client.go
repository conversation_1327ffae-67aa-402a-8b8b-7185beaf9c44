package repo

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"strconv"

	"github.com/samber/lo"
	"github.com/shopspring/decimal"
	"go.uber.org/zap"

	"github.com/MoeGolibrary/go-lib/common/proto/money"
	"github.com/MoeGolibrary/go-lib/grpc"
	mhttp "github.com/MoeGolibrary/go-lib/http"
	"github.com/MoeGolibrary/go-lib/merror"
	"github.com/MoeGolibrary/go-lib/zlog"
	errorspb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/errors/v1"
	orderpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/order/v1"
	paymentpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/payment/v1"
	paymentv2pb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/payment/v2"
	paymentsvcpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/payment/v2"
	"github.com/MoeGolibrary/moego-svc-order-v2/internal/model"
	"github.com/MoeGolibrary/moego-svc-order-v2/internal/repo/internal/httphelper"
)

//go:generate mockery --name=PaymentClient  --case underscore --with-expecter --output ../mocks
type PaymentClient interface {
	GetRefundPayment(ctx context.Context, rop *model.RefundOrderPayment) (*RefundPaymentBrief, error)

	CreateRefundPayment(
		ctx context.Context, paymentID int64, reason string, rop *model.RefundOrderPayment,
	) (*RefundPaymentBrief, error)
	// 调用 svc-payment 创建支付单据
	CreatePayment(ctx context.Context, op *model.OrderPayment) (*CreatePaymentResult, error)
	// 调用 svc-payment 取消支付
	CancelPayment(ctx context.Context, paymentID int64) error

	// 以下是走server-payment的接口，后续预期需要消除掉

	// GetRefundableChannel 获取订单的退款渠道.
	// 用于支持 OrderVersion < 2 的老订单的退款.
	GetRefundableChannel(
		ctx context.Context, businessID, orderID int64, amount decimal.Decimal,
	) ([]*RefundableChannel, error)
	// CreateRefundByOrder 针对订单批量发起退款.
	// 用于支持 OrderVersion < 2 的老订单的退款.
	CreateRefundByOrder(
		ctx context.Context, orderID int64, reason string, refundAmount decimal.Decimal,
		refundableChannels []*RefundableChannel,
	) error

	// CreateCombinedPayment 创建批量创建合单支付单据
	CreateCombinedPayment(
		ctx context.Context, orderPayments []*model.OrderPayment,
	) (int64, []*paymentv2pb.PaymentModel, error)
	// ListPreAuthRecords 获取preauth记录
	ListPreAuthRecords(ctx context.Context, companyID int64, groomingIDs []int64) ([]*PreAuthDTO, error)

	GetPaymentSetting(ctx context.Context, businessID int64) (*PaymentSetting, error)
	GetConvenienceFee(
		ctx context.Context, businessID int64, amount decimal.Decimal, pm paymentpb.StripePaymentMethod,
	) (decimal.Decimal, error)

	CaptureByInvoiceID(ctx context.Context, businessID, invoiceID int64) error
}

type PreAuthDTO struct {
	PreAuthID            int64           `json:"preAuthId"`
	PaymentID            int64           `json:"paymentId"`
	TicketID             int64           `json:"ticketId"`
	PreAuthAmount        decimal.Decimal `json:"preAuthAmount"`
	CustomerID           int64           `json:"customerId"`
	PreAuthTime          int64           `json:"preAuthTime"`
	PreAuthStatus        int64           `json:"preAuthStatus"`
	PreAuthPaymentMethod string          `json:"preAuthPaymentMethod"`
	PreAuthFailedMessage string          `json:"preAuthFailedMessage"`
	InBSPD               bool            `json:"inBSPD"`
	IsCapture            bool            `json:"isCapture"`
	PreAuthFinishTime    int64           `json:"preAuthFinishTime"`

	Method      string `json:"method"`
	CardType    string `json:"cardType"`
	CardNumber  string `json:"cardNumber"`
	ExpMonth    string `json:"expMonth"`
	ExpYear     string `json:"expYear"`
	CardFunding string `json:"cardFunding"`
}

type CreatePaymentResult struct {
	// ID of the created payment
	PaymentID int64
	// Transaction id of the created payment (bulk payment)
	TransactionID int64
}

type paymentClient struct {
	cli     mhttp.Client
	payment paymentsvcpb.PaymentServiceClient
	refund  paymentsvcpb.RefundServiceClient
}

func NewPaymentClient() PaymentClient {
	return &paymentClient{
		cli:     mhttp.NewClient("http://moego-service-payment:9204"),
		payment: grpc.NewClient("moego-svc-payment:9090", paymentsvcpb.NewPaymentServiceClient),
		refund:  grpc.NewClient("moego-svc-payment:9090", paymentsvcpb.NewRefundServiceClient),
	}
}

// ListPreAuthRecords implements PaymentClient.
func (p *paymentClient) ListPreAuthRecords(ctx context.Context,
	companyID int64,
	groomingIDs []int64,
) ([]*PreAuthDTO, error) {
	if len(groomingIDs) == 0 {
		return nil, nil
	}

	const path = "/service/payment/preauth/listPreAuthRecords"

	payload, err := json.Marshal(
		map[string]any{
			"companyId": companyID,
			"ticketIds": groomingIDs,
		},
	)
	if err != nil {
		return nil, err
	}

	resp, err := p.cli.Post(ctx, path, nil, bytes.NewReader(payload))
	if err != nil {
		return nil, err
	}

	defer func() { _ = resp.Body.Close() }()

	result, err := httphelper.ParseResponse[[]*PreAuthDTO](resp)

	return *result, err
}

var _ model.RefundPaymentor = (*RefundPaymentBrief)(nil)

type RefundPaymentBrief struct {
	ID                   int64                                `json:"id"`
	BusinessID           int64                                `json:"businessId"`
	RefundStatus         paymentv2pb.RefundModel_RefundStatus `json:"status"`
	RefundOrderPaymentID int64                                `json:"refundOrderPaymentId"`
	StripeRefundID       string                               `json:"stripeRefundId"`
	Error                string                               `json:"error"`
}

func (rpb RefundPaymentBrief) GetID() int64 { return rpb.ID }

func (rpb RefundPaymentBrief) GetRefundStatus() orderpb.RefundOrderPaymentStatus {
	switch rpb.RefundStatus {
	case paymentv2pb.RefundModel_CREATED, paymentv2pb.RefundModel_SUBMITTED:
		// 对于order来说，不关心payment侧是否有发起过第三方调用，直接转成 TransactionCreated
		return orderpb.RefundOrderPaymentStatus_REFUND_ORDER_PAYMENT_STATUS_TRANSACTION_CREATED
	case paymentv2pb.RefundModel_SUCCEEDED:
		return orderpb.RefundOrderPaymentStatus_REFUND_ORDER_PAYMENT_STATUS_REFUNDED
	case paymentv2pb.RefundModel_FAILED:
		return orderpb.RefundOrderPaymentStatus_REFUND_ORDER_PAYMENT_STATUS_FAILED
	default:
		return orderpb.RefundOrderPaymentStatus_REFUND_ORDER_PAYMENT_STATUS_UNSPECIFIED
	}
}

func (rpb RefundPaymentBrief) GetError() string { return rpb.Error }

func (rpb RefundPaymentBrief) GetRefundOrderPaymentID() int64 { return rpb.RefundOrderPaymentID }

func (p *paymentClient) CreateRefundPayment(
	ctx context.Context,
	paymentID int64,
	// 之前 RefundPayment 实际承担了 RefundOrder 的职责，里面包含了 Refund Reason.
	// 这里为了让老的数据模型字段保持一致，先把 Reason 带过来，后续移除.
	reason string,
	rop *model.RefundOrderPayment,
) (*RefundPaymentBrief, error) {
	resp, err := p.refund.RefundPayment(
		ctx, &paymentsvcpb.RefundPaymentRequest{
			PaymentId:    paymentID,
			Amount:       money.FromDecimal(rop.GetRefundAmount(), rop.CurrencyCode),
			ExternalType: paymentv2pb.ExternalType_REFUND_ORDER_PAYMENT,
			ExternalId:   strconv.FormatInt(rop.ID, 10),
			Reason:       reason,
		},
	)
	if err != nil {
		return nil, err
	}

	return RefundPaymentBriefFromPB(resp.GetRefund()), nil
}

func (p *paymentClient) GetRefundPayment(ctx context.Context, rop *model.RefundOrderPayment) (
	*RefundPaymentBrief, error,
) {
	resp, err := p.refund.GetRefund(
		ctx, &paymentsvcpb.GetRefundRequest{
			Id: rop.RefundPaymentID,
		},
	)
	if err != nil {
		if bizError, ok := merror.ConvertRPCError(err); ok {
			return nil, bizError
		}

		return nil, err
	}

	return RefundPaymentBriefFromPB(resp.GetRefund()), nil
}

func (p *paymentClient) CreatePayment(ctx context.Context, op *model.OrderPayment) (
	*CreatePaymentResult, error,
) {
	resp, err := p.payment.CreatePayment(
		ctx, &paymentsvcpb.CreatePaymentRequest{
			ExternalType: paymentv2pb.ExternalType_ORDER_PAYMENT,
			ExternalId:   strconv.FormatInt(op.ID, 10),
			Payer: &paymentv2pb.User{
				EntityType: paymentv2pb.EntityType_CUSTOMER,
				EntityId:   op.CustomerID,
			},
			Payee: &paymentv2pb.User{
				EntityType: paymentv2pb.EntityType_BUSINESS,
				EntityId:   op.BusinessID,
			},
			Amount:      money.FromDecimal(op.TotalAmount, op.CurrencyCode),
			PaymentType: paymentv2pb.PaymentModel_STANDARD,
		},
	)
	if err != nil {
		return nil, err
	}

	return &CreatePaymentResult{
		PaymentID:     resp.GetPayment().GetId(),
		TransactionID: resp.GetPayment().GetTransactionId(),
	}, nil
}

func (p *paymentClient) CancelPayment(ctx context.Context, paymentID int64) error {
	_, err := p.payment.CancelPayment(
		ctx, &paymentsvcpb.CancelPaymentRequest{
			Id: paymentID,
		},
	)
	if err != nil {
		return err
	}

	return nil
}

type RefundableChannel struct {
	PaymentMethod   string          `json:"paymentMethod"`
	PaymentID       int64           `json:"paymentId"`
	CanRefundAmount decimal.Decimal `json:"canRefundAmount"`
}

func (p *paymentClient) GetRefundableChannel(
	ctx context.Context, businessID, orderID int64, amount decimal.Decimal,
) ([]*RefundableChannel, error) {
	const path = "/service/payment/refund/check"

	payload, err := json.Marshal(
		map[string]any{
			"businessId":    businessID,
			"invoiceId":     orderID,
			"changedAmount": amount.String(),
		},
	)
	if err != nil {
		return nil, err
	}

	resp, err := p.cli.Post(ctx, path, nil, bytes.NewReader(payload))
	if err != nil {
		return nil, err
	}

	defer func() { _ = resp.Body.Close() }()

	type getRefundableChannelHelper struct {
		RefundAmount  decimal.Decimal      `json:"refundAmount"`
		IsCombination bool                 `json:"isCombination"`
		OrderID       int64                `json:"invoiceId"`
		Channels      []*RefundableChannel `json:"channelList"`
	}

	res, err := httphelper.ParseResponse[getRefundableChannelHelper](resp)
	if err != nil {
		return nil, err
	}

	return res.Channels, nil
}

func (p *paymentClient) CreateRefundByOrder(
	ctx context.Context,
	orderID int64,
	reason string,
	refundAmount decimal.Decimal,
	refundableChannels []*RefundableChannel,
) error {
	const path = "/service/payment/invoice/refund/submit"

	payload, err := json.Marshal(
		map[string]any{
			"invoiceId":    orderID,
			"refunds":      refundableChannels,
			"refundReason": reason,
			"refundAmount": refundAmount.String(),
		},
	)
	if err != nil {
		return err
	}

	// 这个接口正常的时候也没有返回任何 Body.
	resp, err := p.cli.Post(ctx, path, nil, bytes.NewReader(payload))
	if err != nil {
		return err
	}

	defer func() { _ = resp.Body.Close() }()

	if resp.StatusCode != http.StatusOK {
		msg, _ := io.ReadAll(resp.Body)
		return merror.NewBizError(errorspb.Code(resp.StatusCode), string(msg)) //nolint:gosec // StatusCode is int32
	}

	return nil
}

// CreateCombinedPayment 创建批量创建合单支付单据
func (p *paymentClient) CreateCombinedPayment(
	ctx context.Context, orderPayments []*model.OrderPayment,
) (int64, []*paymentv2pb.PaymentModel, error) {
	combinedItems := lo.Map(orderPayments, func(
		op *model.OrderPayment, _ int,
	) *paymentsvcpb.CreateCombinedPaymentRequest_CombinedItem {
		item := &paymentsvcpb.CreateCombinedPaymentRequest_CombinedItem{
			ExternalType: paymentv2pb.ExternalType_ORDER,
			ExternalId:   strconv.FormatInt(op.OrderID, 10),
			Payer: &paymentv2pb.User{
				EntityType: paymentv2pb.EntityType_CUSTOMER,
				EntityId:   op.CustomerID,
			},
			Payee: &paymentv2pb.User{
				EntityType: paymentv2pb.EntityType_BUSINESS,
				EntityId:   op.BusinessID,
			},
			Amount: money.FromDecimal(op.TotalAmount, op.CurrencyCode),
			Extra: &paymentsvcpb.CreateCombinedPaymentRequest_CombinedItem_Extra{
				InvoiceId: op.OrderID,
				CompanyId: op.CompanyID,
				StaffId:   op.StaffID,
				Module:    op.Module,
				ModuleId:  op.ModuleID,
			},
		}
		// 有 order payment id 的应该使用 order payment
		if op.ID > 0 {
			item.ExternalType = paymentv2pb.ExternalType_ORDER_PAYMENT
			item.ExternalId = strconv.FormatInt(op.ID, 10)
		}

		return item
	})

	resp, err := p.payment.CreateCombinedPayment(ctx, &paymentsvcpb.CreateCombinedPaymentRequest{
		CombinedItems: combinedItems,
		PaymentType:   paymentv2pb.PaymentModel_STANDARD,
	})
	if err != nil {
		return 0, nil, err
	}

	return resp.GetTransactionId(), resp.GetPaymentModels(), nil
}

func RefundPaymentBriefFromPB(refundModel *paymentv2pb.RefundModel) *RefundPaymentBrief {
	if refundModel == nil {
		return nil
	}

	id, err := strconv.ParseInt(refundModel.GetExternalId(), 10, 64)
	if err != nil {
		zlog.Error(context.Background(), "parse refun mode external id error",
			zap.String("externalID", refundModel.GetExternalId()), zap.Error(err))
	}

	return &RefundPaymentBrief{
		ID:                   refundModel.GetId(),
		BusinessID:           refundModel.GetPayee().GetEntityId(),
		RefundStatus:         refundModel.GetStatus(),
		StripeRefundID:       refundModel.GetChannelRefundId(),
		RefundOrderPaymentID: id,
		Error:                "",
	}
}

type ProcessingFeePayBy int

const ProcessingFeePayByBusiness = 0

const ProcessingFeePayByClient = 1

type PaymentSetting struct {
	ProcessingFeePayBy ProcessingFeePayBy `json:"processingFeePayBy"`
}

func (p *paymentClient) GetPaymentSetting(ctx context.Context, businessID int64) (*PaymentSetting, error) {
	const path = "/service/payment/setting/info"

	params := url.Values{}
	params.Set("businessId", strconv.FormatInt(businessID, 10))

	resp, err := p.cli.Get(ctx, path, params)
	if err != nil {
		return nil, err
	}

	defer func() { _ = resp.Body.Close() }()

	res, err := httphelper.ParseResponse[PaymentSetting](resp)
	if err != nil {
		return nil, err
	}

	return res, nil
}

func (p *paymentClient) GetConvenienceFee(
	ctx context.Context, businessID int64, amount decimal.Decimal, pm paymentpb.StripePaymentMethod,
) (decimal.Decimal, error) {
	const path = "/service/payment/getConvenienceFee"

	params := url.Values{}
	params.Set("businessId", strconv.FormatInt(businessID, 10))
	params.Set("amount", amount.String())
	params.Set("stripePaymentMethod", strconv.Itoa(int(pm)))

	resp, err := p.cli.Get(ctx, path, params)
	if err != nil {
		return decimal.Zero, err
	}

	defer func() { _ = resp.Body.Close() }()

	if resp.StatusCode != http.StatusOK {
		return decimal.Zero, fmt.Errorf("HTTP status code error, code: %d", resp.StatusCode)
	}

	payload, err := io.ReadAll(resp.Body)
	if err != nil {
		return decimal.Zero, fmt.Errorf("read response body failed, err: %w", err)
	}

	var fee decimal.Decimal
	if err := json.Unmarshal(payload, &fee); err != nil {
		return decimal.Zero, fmt.Errorf("unmarshal fee failed, err: %w", err)
	}

	return fee, nil
}

func (p *paymentClient) CaptureByInvoiceID(ctx context.Context, businessID, invoiceID int64) error {
	const path = "/service/payment/preauth/captureByInvoiceId"

	params := url.Values{}
	params.Set("businessId", strconv.FormatInt(businessID, 10))
	params.Set("invoiceId", strconv.FormatInt(invoiceID, 10))

	resp, err := p.cli.Post(ctx, path, params, nil)
	if err != nil {
		return err
	}

	defer func() { _ = resp.Body.Close() }()

	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("HTTP status code error, code: %d", resp.StatusCode)
	}

	return nil
}
