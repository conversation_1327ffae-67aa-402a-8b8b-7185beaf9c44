package repo

import (
	"context"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/shopspring/decimal"
	"github.com/stretchr/testify/suite"

	mhttp "github.com/MoeGolibrary/go-lib/http"
)

type PaymentHTTPClientTestSuite struct {
	suite.Suite

	svr     *httptest.Server
	cli     PaymentClient
	handler http.HandlerFunc
}

func TestPaymentHTTPClient(t *testing.T) { suite.Run(t, new(PaymentHTTPClientTestSuite)) }

func (ts *PaymentHTTPClientTestSuite) TestCreateRefundByOrder() {
	type helper struct {
		InvoiceID    int64                `json:"invoiceId"`
		RefundReason string               `json:"refundReason"`
		Refunds      []*RefundableChannel `json:"refunds"`
		RefundAmount string               `json:"refundAmount"`
	}

	called := false
	expectedRefundAmount := decimal.NewFromInt(10)
	expected := &helper{
		InvoiceID:    110374584,
		RefundReason: "",
		Refunds: []*RefundableChannel{
			{
				PaymentMethod:   "Credit card - cof4242(visa)",
				PaymentID:       6134056,
				CanRefundAmount: decimal.NewFromFloat(15.84),
			},
		},
		RefundAmount: expectedRefundAmount.String(),
	}

	ts.handler = func(w http.ResponseWriter, r *http.Request) {
		called = true

		var req *helper

		ts.Require().NoError(json.NewDecoder(r.Body).Decode(&req))
		ts.Equal(expected, req)

		w.WriteHeader(http.StatusOK)
	}

	err := ts.cli.CreateRefundByOrder(
		context.Background(),
		expected.InvoiceID,
		expected.RefundReason,
		expectedRefundAmount,
		expected.Refunds,
	)
	ts.NoError(err)
	ts.True(called)
}

func (ts *PaymentHTTPClientTestSuite) TestGetRefundableChannel() {
	called := false
	expected := []*RefundableChannel{
		{
			PaymentMethod:   "Credit card - cof4242(visa)",
			PaymentID:       6134056,
			CanRefundAmount: decimal.NewFromFloat(15.84),
		},
	}

	ts.handler = func(w http.ResponseWriter, r *http.Request) {
		called = true

		ts.Equal(http.MethodPost, r.Method)
		ts.NoError(r.ParseForm())

		_, err := w.Write(
			[]byte(`{"channelList":[{"paymentMethod":"Credit card - cof4242(visa)","paymentId":6134056,"canRefundAmount":15.84}],"invoiceId":110374584,"isCombination":false,"refundAmount":10.00}`),
		)

		ts.Require().NoError(err)
	}

	resp, err := ts.cli.GetRefundableChannel(
		context.Background(), 110447, 110374584, decimal.NewFromFloat(10.00),
	)
	ts.NoError(err)
	ts.Equal(expected, resp)
	ts.True(called)
}

func (ts *PaymentHTTPClientTestSuite) SetupSuite() {
	ts.svr = httptest.NewServer(
		http.HandlerFunc(
			func(w http.ResponseWriter, r *http.Request) {
				if ts.handler == nil {
					w.WriteHeader(http.StatusNotImplemented)
					return
				}

				ts.handler.ServeHTTP(w, r)
			},
		),
	)

	ts.cli = NewPaymentClient()
	ts.cli.(*paymentClient).cli = mhttp.NewClient(ts.svr.URL)
}

func (ts *PaymentHTTPClientTestSuite) TearDownSuite() {
	ts.svr.Close()
}

func (ts *PaymentHTTPClientTestSuite) SetupTest() {
	ts.handler = nil
}
