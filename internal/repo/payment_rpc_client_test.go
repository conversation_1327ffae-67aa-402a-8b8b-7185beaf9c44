package repo

import (
	"context"
	"net"
	"testing"

	"github.com/shopspring/decimal"
	"github.com/stretchr/testify/suite"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	"github.com/MoeGolibrary/go-lib/common/proto/money"
	"github.com/MoeGolibrary/go-lib/grpc"
	paymentpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/payment/v2"
	paymentsvcpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/payment/v2"
	"github.com/MoeGolibrary/moego-svc-order-v2/internal/model"
)

type PaymentRPCClientTestSuite struct {
	suite.Suite
	cli                PaymentClient
	server             *grpc.Server
	mockPaymentService *MockPaymentService
	mockRefundService  *MockRefundService
}

func TestPaymentRPCClient(t *testing.T) {
	suite.Run(t, new(PaymentRPCClientTestSuite))
}

func (ts *PaymentRPCClientTestSuite) SetupSuite() {
	var lc net.ListenConfig

	listener, err := lc.Listen(context.Background(), "tcp", ":0")
	ts.Require().NoError(err)

	ts.server = grpc.NewServer()
	ts.mockPaymentService = &MockPaymentService{}
	ts.mockRefundService = &MockRefundService{}
	paymentsvcpb.RegisterPaymentServiceServer(ts.server, ts.mockPaymentService)
	paymentsvcpb.RegisterRefundServiceServer(ts.server, ts.mockRefundService)

	go func() { ts.NoError(ts.server.Serve(listener)) }()

	ts.cli = NewPaymentClient()
	ts.cli.(*paymentClient).refund = grpc.NewClient(listener.Addr().String(), paymentsvcpb.NewRefundServiceClient)
	ts.cli.(*paymentClient).payment = grpc.NewClient(listener.Addr().String(), paymentsvcpb.NewPaymentServiceClient)
}

func (ts *PaymentRPCClientTestSuite) TearDownSuite() {
	ts.server.Stop()
}

func (ts *PaymentRPCClientTestSuite) SetupTest() {
	ts.mockPaymentService.createPaymentHandler = nil
	ts.mockPaymentService.cancelPaymentHandler = nil
	ts.mockRefundService.refundHandler = nil
	ts.mockRefundService.getRefundHandler = nil
}

func (ts *PaymentRPCClientTestSuite) TestRefund() {
	called := false
	ts.mockRefundService.refundHandler = func(
		_ context.Context, _ *paymentsvcpb.RefundPaymentRequest,
	) (*paymentsvcpb.RefundPaymentResponse, error) {
		called = true

		return &paymentsvcpb.RefundPaymentResponse{
			Refund: &paymentpb.RefundModel{
				PaymentId: 1,
			},
		}, nil
	}

	paymentID := int64(12345)
	rop := &model.RefundOrderPayment{
		ID:           1,
		RefundAmount: decimal.RequireFromString("100.00"),
		CurrencyCode: "USD",
	}

	resp, err := ts.cli.CreateRefundPayment(context.Background(), paymentID, "reason", rop)
	ts.NoError(err)
	ts.Require().True(called)
	ts.NotNil(resp)
}

func (ts *PaymentRPCClientTestSuite) TestGetRefund() {
	called := false
	ts.mockRefundService.getRefundHandler = func(
		_ context.Context, _ *paymentsvcpb.GetRefundRequest,
	) (*paymentsvcpb.GetRefundResponse, error) {
		called = true

		return &paymentsvcpb.GetRefundResponse{
			Refund: &paymentpb.RefundModel{
				Id: 1,
			},
		}, nil
	}
	resp, err := ts.cli.GetRefundPayment(context.Background(), &model.RefundOrderPayment{
		RefundPaymentID: 1,
	})
	ts.NoError(err)
	ts.Require().True(called)
	ts.NotNil(resp)
	ts.Equal(int64(1), resp.ID)
}

func (ts *PaymentRPCClientTestSuite) TestCreatePayment() {
	expected := &paymentpb.PaymentModel{
		Id:           12345,
		ExternalType: paymentpb.ExternalType_ORDER_PAYMENT,
		ExternalId:   "order_12345",
		Payer: &paymentpb.User{
			EntityType: paymentpb.EntityType_CUSTOMER,
			EntityId:   67890,
		},
		Payee: &paymentpb.User{
			EntityType: paymentpb.EntityType_BUSINESS,
			EntityId:   54321,
		},
		Amount:      money.FromDecimal(decimal.RequireFromString("100.00"), "USD"),
		PaymentType: paymentpb.PaymentModel_STANDARD,
	}

	called := false

	ts.mockPaymentService.createPaymentHandler = func(
		_ context.Context, _ *paymentsvcpb.CreatePaymentRequest,
	) (*paymentsvcpb.CreatePaymentResponse, error) {
		called = true

		return &paymentsvcpb.CreatePaymentResponse{
			Payment: expected,
		}, nil
	}

	op := &model.OrderPayment{
		ID:           12345,
		CustomerID:   67890,
		BusinessID:   54321,
		TotalAmount:  decimal.RequireFromString("100.00"),
		CurrencyCode: "USD",
	}

	resp, err := ts.cli.CreatePayment(context.Background(), op)
	ts.NoError(err)
	ts.Require().True(called)

	ts.Equal(expected.Id, resp.PaymentID)
}

func (ts *PaymentRPCClientTestSuite) TestCancelPayment() {
	paymentID := int64(12345)

	called := false

	ts.mockPaymentService.cancelPaymentHandler = func(
		_ context.Context, _ *paymentsvcpb.CancelPaymentRequest,
	) (*paymentsvcpb.CancelPaymentResponse, error) {
		called = true
		return &paymentsvcpb.CancelPaymentResponse{Msg: "success"}, nil
	}

	err := ts.cli.CancelPayment(context.Background(), paymentID)
	ts.NoError(err)
	ts.Require().True(called)
}

func (ts *PaymentRPCClientTestSuite) TestCancelPayment_Failed() {
	paymentID := int64(12345)
	expectedErr := status.Error(codes.Internal, "method CancelPayment not implemented")

	called := false

	ts.mockPaymentService.cancelPaymentHandler = func(
		_ context.Context, _ *paymentsvcpb.CancelPaymentRequest,
	) (*paymentsvcpb.CancelPaymentResponse, error) {
		called = true
		return nil, expectedErr
	}

	err := ts.cli.CancelPayment(context.Background(), paymentID)
	ts.Error(err)
	ts.Require().True(called)
	ts.Equal(expectedErr.Error(), err.Error())
}

type MockPaymentService struct {
	paymentsvcpb.UnimplementedPaymentServiceServer

	createPaymentHandler func(ctx context.Context, req *paymentsvcpb.CreatePaymentRequest) (
		*paymentsvcpb.CreatePaymentResponse, error,
	)
	cancelPaymentHandler func(
		ctx context.Context, req *paymentsvcpb.CancelPaymentRequest,
	) (*paymentsvcpb.CancelPaymentResponse, error)
}

type MockRefundService struct {
	paymentsvcpb.UnimplementedRefundServiceServer
	refundHandler func(
		ctx context.Context, req *paymentsvcpb.RefundPaymentRequest,
	) (*paymentsvcpb.RefundPaymentResponse, error)
	getRefundHandler func(
		ctx context.Context, req *paymentsvcpb.GetRefundRequest,
	) (*paymentsvcpb.GetRefundResponse, error)
}

func (m *MockRefundService) RefundPayment(
	ctx context.Context, req *paymentsvcpb.RefundPaymentRequest,
) (*paymentsvcpb.RefundPaymentResponse, error) {
	if m.refundHandler != nil {
		return m.refundHandler(ctx, req)
	}

	return nil, status.Error(codes.Unimplemented, "method Refund not implemented")
}

func (m *MockRefundService) GetRefund(
	ctx context.Context, req *paymentsvcpb.GetRefundRequest,
) (*paymentsvcpb.GetRefundResponse, error) {
	if m.getRefundHandler != nil {
		return m.getRefundHandler(ctx, req)
	}

	return nil, status.Error(codes.Unimplemented, "method GetRefund not implemented")
}

func (m *MockPaymentService) CreatePayment(
	ctx context.Context, req *paymentsvcpb.CreatePaymentRequest,
) (*paymentsvcpb.CreatePaymentResponse, error) {
	if m.createPaymentHandler != nil {
		return m.createPaymentHandler(ctx, req)
	}

	return nil, status.Error(codes.Unimplemented, "method CreatePayment not implemented")
}

func (m *MockPaymentService) CancelPayment(
	ctx context.Context, req *paymentsvcpb.CancelPaymentRequest,
) (*paymentsvcpb.CancelPaymentResponse, error) {
	if m.cancelPaymentHandler != nil {
		return m.cancelPaymentHandler(ctx, req)
	}

	return nil, status.Error(codes.Unimplemented, "method CancelPayment not implemented")
}
