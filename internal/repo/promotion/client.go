package promotion

import (
	"context"

	"github.com/shopspring/decimal"

	"github.com/MoeGolibrary/go-lib/common/proto/money"
	"github.com/MoeGolibrary/go-lib/grpc"
	promotionpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/promotion/v1"
	promotionsvcpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/promotion/v1"
)

type Client interface {
	RecommendCoupons(
		ctx context.Context, customerID int64,
		targetItems []*promotionpb.CouponApplicationTarget,
	) (*promotionsvcpb.RecommendCouponsResponse, error)
	PreviewCoupons(
		ctx context.Context, customerID int64,
		couponUsages []*promotionpb.CouponUsage,
	) (*promotionsvcpb.PreviewCouponsResponse, error)
	RedeemCoupons(
		ctx context.Context,
		orderID int64,
		customerID int64,
		appointmentID int64,
		orderSales decimal.Decimal,
		redeems []*promotionpb.CouponRedeem,
	) (*promotionsvcpb.RedeemCouponsResponse, error)
}

type client struct {
	promotionCli promotionsvcpb.PromotionServiceClient
}

func New() Client {
	return &client{
		promotionCli: grpc.NewClient(
			"moego-svc-promotion:9090", promotionsvcpb.NewPromotionServiceClient,
		),
	}
}

func (cli *client) RecommendCoupons(
	ctx context.Context, customerID int64, targetItems []*promotionpb.CouponApplicationTarget,
) (*promotionsvcpb.RecommendCouponsResponse, error) {
	req := &promotionsvcpb.RecommendCouponsRequest{
		CustomerId: customerID,
		Targets:    targetItems,
	}

	return cli.promotionCli.RecommendCoupons(ctx, req)
}

func (cli *client) PreviewCoupons(
	ctx context.Context, customerID int64,
	couponUsages []*promotionpb.CouponUsage,
) (*promotionsvcpb.PreviewCouponsResponse, error) {
	req := &promotionsvcpb.PreviewCouponsRequest{
		CouponUsages: couponUsages,
		CustomerId:   customerID,
	}

	return cli.promotionCli.PreviewCoupons(ctx, req)
}

func (cli *client) RedeemCoupons(
	ctx context.Context,
	orderID int64,
	customerID int64,
	appointmentID int64,
	orderSales decimal.Decimal,
	redeems []*promotionpb.CouponRedeem,
) (*promotionsvcpb.RedeemCouponsResponse, error) {
	req := &promotionsvcpb.RedeemCouponsRequest{
		OrderId:    orderID,
		Redeems:    redeems,
		CustomerId: customerID,
		RedeemScene: &promotionsvcpb.RedeemCouponsRequest_AppointmentId{
			AppointmentId: appointmentID,
		},
		OrderSales: money.FromDecimal(orderSales, ""),
	}

	return cli.promotionCli.RedeemCoupons(ctx, req)
}
