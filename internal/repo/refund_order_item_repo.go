package repo

import (
	"context"

	"gorm.io/gorm"

	"github.com/MoeGolibrary/moego-svc-order-v2/internal/model"
)

type RefundOrderItemRepo interface {
	ListByOrderID(ctx context.Context, orderID int64) ([]*model.RefundOrderItem, error)
	ListByRefundOrderID(ctx context.Context, refundOrderID int64) ([]*model.RefundOrderItem, error)

	BatchCreate(ctx context.Context, refundOrderItems []*model.RefundOrderItem) error
}

func NewRefundOrderItemRepo(db *gorm.DB) RefundOrderItemRepo {
	return &refundOrderItemRepo{db: db}
}

type refundOrderItemRepo struct {
	db *gorm.DB
}

func (repo *refundOrderItemRepo) ListByRefundOrderID(
	ctx context.Context, refundOrderID int64,
) ([]*model.RefundOrderItem, error) {
	allRefundOrders, err := listByKeyID[*model.RefundOrderItem](
		repo.withContext(ctx),
		"refund_order_id", refundOrderID,
	)
	if err != nil {
		return nil, err
	}

	return allRefundOrders, nil
}

func (repo *refundOrderItemRepo) ListByOrderID(
	ctx context.Context, refundOrderID int64,
) ([]*model.RefundOrderItem, error) {
	allRefundOrders, err := listByKeyID[*model.RefundOrderItem](
		repo.withContext(ctx),
		"order_id", refundOrderID,
	)
	if err != nil {
		return nil, err
	}

	return allRefundOrders, nil
}

func (repo *refundOrderItemRepo) BatchCreate(ctx context.Context, refundOrderItems []*model.RefundOrderItem) error {
	if len(refundOrderItems) == 0 {
		return nil
	}

	if err := repo.withContext(ctx).CreateInBatches(refundOrderItems, defaultInsertBatchSize).Error; err != nil {
		return parseDBErr(err)
	}

	return nil
}

func (repo *refundOrderItemRepo) withContext(ctx context.Context) *gorm.DB {
	const tableName = "refund_order_item"
	return repo.db.WithContext(ctx).Table(tableName)
}
