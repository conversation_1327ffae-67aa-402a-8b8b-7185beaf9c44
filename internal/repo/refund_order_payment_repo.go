package repo

import (
	"context"
	"time"

	"github.com/samber/lo"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"

	orderpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/order/v1"
	paymentpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/payment/v1"
	"github.com/MoeGolibrary/moego-svc-order-v2/internal/model"
)

type RefundOrderPaymentRepo interface {
	Get(ctx context.Context, id int64) (*model.RefundOrderPayment, error)
	BatchGet(ctx context.Context, ids []int64) ([]*model.RefundOrderPayment, error)

	ListByOrderID(ctx context.Context, orderID int64) ([]*model.RefundOrderPayment, error)
	ListByRefundOrderID(ctx context.Context, refundOrderID int64) ([]*model.RefundOrderPayment, error)
	ListByOrderPaymentIDs(ctx context.Context, opIDs []int64) ([]*model.RefundOrderPayment, error)
	ListByStatusAndMaxUpdateTime(
		ctx context.Context, refundStatus orderpb.RefundOrderPaymentStatus, updateTime int64,
	) ([]*model.RefundOrderPayment, error)
	ListByConditions(
		ctx context.Context, companyID int64, conditions *ListRefundConditions,
	) ([]*model.RefundOrderPayment, error)

	BatchCreate(ctx context.Context, refundOrderPayments []*model.RefundOrderPayment) error
	GetForUpdate(ctx context.Context, id int64) (*model.RefundOrderPayment, error)

	UpdateTrxCreated(ctx context.Context, refundOrderPayment *model.RefundOrderPayment) error
	UpdateRefunded(ctx context.Context, refundOrderPayment *model.RefundOrderPayment) error
	UpdateFailed(ctx context.Context, refundOrderPayment *model.RefundOrderPayment) error

	UpdateReason(ctx context.Context, id int64, reason string, refundStatus orderpb.RefundOrderPaymentStatus) error
}

func NewRefundOrderPaymentRepo(db *gorm.DB) RefundOrderPaymentRepo {
	return &refundOrderPaymentRepo{db: db}
}

type ListRefundConditions struct {
	// IDs of RefundOrderPayment
	IDs           []int64
	BusinessID    int64
	CustomerID    int64
	PaymentMethod paymentpb.PaymentMethod
	Vendor        string    // Applicable only if PaymentMethodID is 1 (credit card)
	StartTime     time.Time // Inclusive
	EndTime       time.Time // Exclusive
	Statuses      []orderpb.RefundOrderPaymentStatus
}

type refundOrderPaymentRepo struct {
	db *gorm.DB
}

func (repo *refundOrderPaymentRepo) Get(ctx context.Context, refundOrderPaymentID int64) (
	*model.RefundOrderPayment, error,
) {
	return getByID[*model.RefundOrderPayment](repo.withContext(ctx), refundOrderPaymentID)
}

func (repo *refundOrderPaymentRepo) BatchGet(ctx context.Context, ids []int64) ([]*model.RefundOrderPayment, error) {
	return listByConditions[*model.RefundOrderPayment](repo.withContext(ctx), map[string][]any{"id IN (?)": {ids}})
}

func (repo *refundOrderPaymentRepo) ListByRefundOrderID(ctx context.Context, refundOrderID int64) (
	[]*model.RefundOrderPayment, error,
) {
	return listByKeyID[*model.RefundOrderPayment](
		repo.withContext(ctx), "refund_order_id", refundOrderID,
	)
}

func (repo *refundOrderPaymentRepo) ListByOrderPaymentIDs(
	ctx context.Context, opIDs []int64,
) ([]*model.RefundOrderPayment, error) {
	return listByConditions[*model.RefundOrderPayment](
		repo.withContext(ctx), map[string][]any{"order_payment_id IN (?)": {opIDs}},
	)
}

func (repo *refundOrderPaymentRepo) ListByOrderID(ctx context.Context, orderID int64) (
	[]*model.RefundOrderPayment, error,
) {
	return listByKeyID[*model.RefundOrderPayment](
		repo.withContext(ctx), "order_id", orderID,
	)
}

func (repo *refundOrderPaymentRepo) BatchCreate(
	ctx context.Context, refundOrderPayments []*model.RefundOrderPayment,
) error {
	if len(refundOrderPayments) == 0 {
		return nil
	}

	if err := repo.withContext(ctx).Create(refundOrderPayments).Error; err != nil {
		return parseDBErr(err)
	}

	return nil
}

func (repo *refundOrderPaymentRepo) GetForUpdate(ctx context.Context, id int64) (*model.RefundOrderPayment, error) {
	return getByID[*model.RefundOrderPayment](
		repo.withContext(ctx).Clauses(
			clause.Locking{
				Strength: clause.LockingStrengthUpdate,
			},
		), id,
	)
}

func (repo *refundOrderPaymentRepo) UpdateTrxCreated(
	ctx context.Context, rop *model.RefundOrderPayment,
) error {
	// 只允许从 CREATED 流向 TRANSACTION_CREATED.
	// 并且只更新状态、RefundPaymentID 和 UpdateTime.
	tx := repo.withContext(ctx).
		Where(
			&model.RefundOrderPayment{
				ID:           rop.ID,
				RefundStatus: orderpb.RefundOrderPaymentStatus_REFUND_ORDER_PAYMENT_STATUS_CREATED,
			},
		).
		Updates(
			map[string]any{
				"refund_status":     orderpb.RefundOrderPaymentStatus_REFUND_ORDER_PAYMENT_STATUS_TRANSACTION_CREATED.String(),
				"refund_payment_id": rop.RefundPaymentID,
				"update_time":       toTimestamp(time.Now()),
			},
		)

	if err := tx.Error; err != nil {
		return parseDBErr(err)
	}

	if tx.RowsAffected == 0 {
		return status.Error(codes.FailedPrecondition, "cannot update refund order payment to TRX_CREATED")
	}

	return nil
}

func (repo *refundOrderPaymentRepo) UpdateRefunded(
	ctx context.Context, rop *model.RefundOrderPayment,
) error {
	// 只允许从 CREATED / TRANSACTION_CREATED 流向 REFUNDED.
	// 并且只更新 RefundPaymentID、状态、RefundTime 和 UpdateTime.
	tx := repo.withContext(ctx).
		Where(
			"id = ? AND (refund_status = ? OR refund_status = ?)",
			rop.ID,
			orderpb.RefundOrderPaymentStatus_REFUND_ORDER_PAYMENT_STATUS_CREATED.String(),
			orderpb.RefundOrderPaymentStatus_REFUND_ORDER_PAYMENT_STATUS_TRANSACTION_CREATED.String(),
		).
		Updates(
			map[string]any{
				"refund_payment_id":    rop.RefundPaymentID,
				"refund_status":        orderpb.RefundOrderPaymentStatus_REFUND_ORDER_PAYMENT_STATUS_REFUNDED.String(),
				"refund_status_reason": "",
				"refund_time":          toTimestamp(time.Unix(rop.RefundTime, 0)),
				"update_time":          toTimestamp(time.Now()),
			},
		)

	if err := tx.Error; err != nil {
		return parseDBErr(err)
	}

	if tx.RowsAffected == 0 {
		return status.Error(codes.FailedPrecondition, "cannot update refund order payment to REFUNDED")
	}

	return nil
}

func (repo *refundOrderPaymentRepo) UpdateFailed(
	ctx context.Context, rop *model.RefundOrderPayment,
) error {
	// 只允许从 CREATED / TRANSACTION_CREATED 流向 FAILED.
	// 并且只更新状态、RefundStatusReason、FailTime 和 UpdateTime.
	tx := repo.withContext(ctx).
		Where(
			"id = ? AND (refund_status = ? OR refund_status = ?)",
			rop.ID,
			orderpb.RefundOrderPaymentStatus_REFUND_ORDER_PAYMENT_STATUS_CREATED.String(),
			orderpb.RefundOrderPaymentStatus_REFUND_ORDER_PAYMENT_STATUS_TRANSACTION_CREATED.String(),
		).
		Updates(
			map[string]any{
				"refund_status":        orderpb.RefundOrderPaymentStatus_REFUND_ORDER_PAYMENT_STATUS_FAILED.String(),
				"refund_status_reason": rop.RefundStatusReason,
				"fail_time":            toTimestamp(time.Unix(rop.FailTime, 0)),
				"update_time":          toTimestamp(time.Unix(rop.UpdateTime, 0)),
			},
		)

	if err := tx.Error; err != nil {
		return parseDBErr(err)
	}

	if tx.RowsAffected == 0 {
		return status.Error(codes.FailedPrecondition, "cannot update refund order payment to REFUNDED")
	}

	return nil
}

func (repo *refundOrderPaymentRepo) UpdateReason(
	ctx context.Context, id int64, reason string, refundStatus orderpb.RefundOrderPaymentStatus,
) error {
	tx := repo.withContext(ctx).
		Where("id = ? AND refund_status = ?", id, refundStatus.String()).
		Updates(
			map[string]any{
				"refund_status_reason": reason,
				"update_time":          toTimestamp(time.Now()),
			},
		)

	if err := tx.Error; err != nil {
		return parseDBErr(err)
	}

	if tx.RowsAffected == 0 {
		return status.Error(codes.FailedPrecondition, "cannot update refund order payment to REFUNDED")
	}

	return nil
}

func (repo *refundOrderPaymentRepo) ListByStatusAndMaxUpdateTime(
	ctx context.Context, refundStatus orderpb.RefundOrderPaymentStatus, updateTime int64,
) ([]*model.RefundOrderPayment, error) {
	return listByConditions[*model.RefundOrderPayment](
		repo.withContext(ctx),
		map[string][]any{
			"refund_status = ? AND update_time <= to_timestamp(?)": {refundStatus.String(), updateTime},
		},
	)
}

func (repo *refundOrderPaymentRepo) ListByConditions(
	ctx context.Context, companyID int64, conditions *ListRefundConditions,
) ([]*model.RefundOrderPayment, error) {
	condMap := map[string][]any{
		"company_id = ?": {companyID},
	}

	if conditions != nil {
		if len(conditions.IDs) > 0 {
			condMap["id in (?)"] = []any{conditions.IDs}
		}

		if conditions.BusinessID != 0 {
			condMap["business_id = ?"] = []any{conditions.BusinessID}
		}

		if conditions.CustomerID != 0 {
			condMap["customer_id = ?"] = []any{conditions.CustomerID}
		}

		if conditions.PaymentMethod != paymentpb.PaymentMethod_PAYMENT_METHOD_UNSPECIFIED {
			condMap["refund_payment_method_id = ?"] = []any{int(conditions.PaymentMethod)}
			if conditions.PaymentMethod == paymentpb.PaymentMethod_CREDIT_CARD && conditions.Vendor != "" {
				condMap["refund_payment_method_vendor = ?"] = []any{conditions.Vendor}
			}
		}

		if !conditions.StartTime.Equal(time.Unix(0, 0).UTC()) {
			condMap["create_time >= to_timestamp(?)"] = []any{conditions.StartTime.Unix()}
		}

		if !conditions.EndTime.Equal(time.Unix(0, 0).UTC()) {
			condMap["create_time < to_timestamp(?)"] = []any{conditions.EndTime.Unix()}
		}

		if len(conditions.Statuses) > 0 {
			condMap["refund_status in (?)"] = lo.Map(
				conditions.Statuses,
				func(s orderpb.RefundOrderPaymentStatus, _ int) any { return s.String() },
			)
		}
	}

	return listByConditions[*model.RefundOrderPayment](repo.withContext(ctx), condMap)
}

func (repo *refundOrderPaymentRepo) withContext(ctx context.Context) *gorm.DB {
	return repo.db.WithContext(ctx).Table("public.refund_order_payment")
}
