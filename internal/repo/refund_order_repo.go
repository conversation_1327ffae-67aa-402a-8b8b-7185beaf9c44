package repo

import (
	"context"
	"time"

	"github.com/samber/lo"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"

	orderpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/order/v1"
	"github.com/MoeGolibrary/moego-svc-order-v2/internal/model"
)

type RefundOrderRepo interface {
	Get(ctx context.Context, id int64) (*model.RefundOrder, error)
	GetForUpdate(ctx context.Context, id int64) (*model.RefundOrder, error)
	ListByOrderID(ctx context.Context, orderID int64) ([]*model.RefundOrder, error)

	GetDetail(ctx context.Context, id int64) (*model.RefundOrderDetail, error)
	ListDetailByOrderID(ctx context.Context, orderID int64) ([]*model.RefundOrderDetail, error)

	Create(ctx context.Context, refundOrder *model.RefundOrder) error
	UpdateCompleted(ctx context.Context, refundOrder *model.RefundOrder) error
}

func NewRefundOrderRepo(db *gorm.DB) RefundOrderRepo {
	return &refundOrderRepo{
		db:                     db,
		refundItemRepo:         NewRefundOrderItemRepo(db),
		refundOrderPaymentRepo: NewRefundOrderPaymentRepo(db),
	}
}

type refundOrderRepo struct {
	db *gorm.DB

	refundItemRepo         RefundOrderItemRepo
	refundOrderPaymentRepo RefundOrderPaymentRepo
}

func (repo *refundOrderRepo) Get(ctx context.Context, id int64) (*model.RefundOrder, error) {
	return getByID[*model.RefundOrder](repo.withContext(ctx), id)
}

func (repo *refundOrderRepo) GetForUpdate(ctx context.Context, id int64) (*model.RefundOrder, error) {
	return getByID[*model.RefundOrder](
		repo.withContext(ctx).Clauses(
			clause.Locking{
				Strength: clause.LockingStrengthUpdate,
			},
		), id,
	)
}

func (repo *refundOrderRepo) ListByOrderID(ctx context.Context, orderID int64) ([]*model.RefundOrder, error) {
	allRefundOrders, err := listByKeyID[*model.RefundOrder](repo.withContext(ctx), "order_id", orderID)
	if err != nil {
		return nil, err
	}

	return allRefundOrders, nil
}

func (repo *refundOrderRepo) GetDetail(ctx context.Context, id int64) (*model.RefundOrderDetail, error) {
	refundOrder, err := repo.Get(ctx, id)
	if err != nil {
		return nil, err
	}

	refundOrderItems, err := repo.refundItemRepo.ListByRefundOrderID(ctx, id)
	if err != nil {
		return nil, err
	}

	refundPayment, err := repo.refundOrderPaymentRepo.ListByRefundOrderID(ctx, id)
	if err != nil {
		return nil, err
	}

	return &model.RefundOrderDetail{
		RefundOrder:         refundOrder,
		RefundOrderItems:    refundOrderItems,
		RefundOrderPayments: refundPayment,
	}, nil
}

func (repo *refundOrderRepo) ListDetailByOrderID(ctx context.Context, orderID int64) (
	[]*model.RefundOrderDetail, error,
) {
	refundOrders, err := repo.ListByOrderID(ctx, orderID)
	if err != nil {
		return nil, err
	}

	refundOrderItems, err := repo.refundItemRepo.ListByOrderID(ctx, orderID)
	if err != nil {
		return nil, err
	}

	refundOrderToItems := lo.GroupBy(
		refundOrderItems,
		func(item *model.RefundOrderItem) int64 { return item.RefundOrderID },
	)

	refundOrderPayments, err := repo.refundOrderPaymentRepo.ListByOrderID(ctx, orderID)
	if err != nil {
		return nil, err
	}

	refundOrderToPayments := lo.GroupBy(
		refundOrderPayments,
		func(payment *model.RefundOrderPayment) int64 { return payment.RefundOrderID },
	)

	refundOrderDetails := lo.Map(
		refundOrders,
		func(ro *model.RefundOrder, _ int) *model.RefundOrderDetail {
			return &model.RefundOrderDetail{
				RefundOrder:         ro,
				RefundOrderItems:    refundOrderToItems[ro.ID],
				RefundOrderPayments: refundOrderToPayments[ro.ID],
			}
		},
	)

	return refundOrderDetails, nil
}

func (repo *refundOrderRepo) Create(ctx context.Context, refundOrder *model.RefundOrder) error {
	if err := repo.withContext(ctx).Create(refundOrder).Error; err != nil {
		return parseDBErr(err)
	}

	return nil
}

func (repo *refundOrderRepo) UpdateCompleted(ctx context.Context, rod *model.RefundOrder) error {
	// 只允许从 CREATED / TRANSACTION_CREATED 流向 REFUNDED.
	// 并且只更新状态、RefundTime 和 UpdateTime.
	tx := repo.withContext(ctx).
		Where(
			"id = ? AND (refund_order_status = ? OR refund_order_status = ?)",
			rod.ID,
			orderpb.RefundOrderStatus_REFUND_ORDER_STATUS_CREATED.String(),
			orderpb.RefundOrderStatus_REFUND_ORDER_STATUS_TRANSACTION_CREATED.String(),
		).
		Updates(
			map[string]any{
				"refund_order_status": orderpb.RefundOrderStatus_REFUND_ORDER_STATUS_COMPLETED.String(),
				"refund_time":         toTimestamp(time.Unix(rod.RefundTime, 0)),
				"update_time":         toTimestamp(time.Now()),
			},
		)

	if err := tx.Error; err != nil {
		return parseDBErr(err)
	}

	if tx.RowsAffected == 0 {
		return status.Error(codes.FailedPrecondition, "cannot update refund order to REFUNDED")
	}

	return nil
}

func (repo *refundOrderRepo) withContext(ctx context.Context) *gorm.DB {
	const tableName = "public.refund_order"

	return repo.db.WithContext(ctx).Table(tableName)
}
