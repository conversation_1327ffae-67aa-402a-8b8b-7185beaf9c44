package repo

import (
	"context"
	"regexp"
	"testing"
	"time"

	sqlmock "github.com/DATA-DOG/go-sqlmock"
	"github.com/stretchr/testify/suite"

	"github.com/MoeGolibrary/go-lib/gorm"
	orderpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/order/v1"
	"github.com/MoeGolibrary/moego-svc-order-v2/internal/model"
)

type RefundOrderTestSuite struct {
	suite.Suite

	mockDB  *gorm.DB
	sqlMock sqlmock.Sqlmock

	repo RefundOrderRepo
}

func TestRefundOrderRepo(t *testing.T) {
	suite.Run(t, new(RefundOrderTestSuite))
}

func (ts *RefundOrderTestSuite) SetupSuite() {
	ts.mockDB, ts.sqlMock = gorm.MockPostgres(ts.T())
	ts.repo = NewRefundOrderRepo(ts.mockDB)
}

func (ts *RefundOrderTestSuite) TestUpdateCompleted() {
	refundTime := time.Unix(time.Now().Unix(), 0).UTC()
	timestamp := refundTime.Format("2006-01-02 15:04:05")

	rod := &model.RefundOrder{
		ID:         23333,
		RefundTime: refundTime.Unix(),
	}

	ts.sqlMock.ExpectBegin()
	ts.sqlMock.ExpectExec(
		regexp.QuoteMeta(
			"UPDATE \"public\".\"refund_order\" "+
				"SET \"refund_order_status\"=$1,\"refund_time\"=$2,\"update_time\"=$3 "+
				"WHERE id = $4 AND (refund_order_status = $5 OR refund_order_status = $6)",
		),
	).WithArgs(
		orderpb.RefundOrderStatus_REFUND_ORDER_STATUS_COMPLETED.String(),
		timestamp,
		timestamp,
		rod.ID,
		orderpb.RefundOrderStatus_REFUND_ORDER_STATUS_CREATED.String(),
		orderpb.RefundOrderStatus_REFUND_ORDER_STATUS_TRANSACTION_CREATED.String(),
	).WillReturnResult(sqlmock.NewResult(1, 1))
	ts.sqlMock.ExpectCommit()

	ts.NoError(ts.repo.UpdateCompleted(context.Background(), rod))
}

func (ts *RefundOrderTestSuite) TestListByOrderID() {
	const orderID int64 = 23333

	firstBatch := sqlmock.NewRows([]string{"id", "order_id"})
	secondBatch := sqlmock.NewRows([]string{"id", "order_id"})
	thirdBatch := sqlmock.NewRows([]string{"id", "order_id"})

	for i := 0; i < defaultSelectBatchSize; i++ {
		firstBatch = firstBatch.AddRow(i+1, orderID)
		secondBatch = secondBatch.AddRow(defaultSelectBatchSize+i+1, orderID)
	}

	ts.sqlMock.ExpectQuery(regexp.QuoteMeta(`SELECT * FROM "public"."refund_order" WHERE order_id = $1 ORDER BY "refund_order"."id" LIMIT $2`)).
		WithArgs(orderID, defaultSelectBatchSize).
		WillReturnRows(firstBatch)
	ts.sqlMock.ExpectQuery(regexp.QuoteMeta(`SELECT * FROM "public"."refund_order" WHERE order_id = $1 AND "refund_order"."id" > $2 ORDER BY "refund_order"."id" LIMIT $3`)).
		WithArgs(orderID, defaultSelectBatchSize, defaultSelectBatchSize).
		WillReturnRows(secondBatch)
	ts.sqlMock.ExpectQuery(regexp.QuoteMeta(`SELECT * FROM "public"."refund_order" WHERE order_id = $1 AND "refund_order"."id" > $2 ORDER BY "refund_order"."id" LIMIT $3`)).
		WithArgs(orderID, 2*defaultSelectBatchSize, defaultSelectBatchSize).
		WillReturnRows(thirdBatch)

	// 调用 ListByOrderID 方法
	refundOrders, err := ts.repo.(*refundOrderRepo).ListByOrderID(context.Background(), orderID)

	// 验证查询是否成功，无错误
	ts.Require().NoError(err)
	ts.Require().Len(refundOrders, 2*defaultSelectBatchSize)

	// 验证返回数据的正确性
	for i, rod := range refundOrders {
		ts.EqualValues(i+1, rod.ID)
		ts.Equal(orderID, rod.OrderID)
	}
}
