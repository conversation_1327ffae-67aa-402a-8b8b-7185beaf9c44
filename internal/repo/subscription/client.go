package subscription

import (
	"context"

	"github.com/samber/lo"

	"github.com/MoeGolibrary/go-lib/grpc"
	subscriptionapipb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/api/subscription/v1"
	subscriptionpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/subscription/v1"
	subscriptionsvcpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/subscription/v1"
)

type Client interface {
	// amount 单位是 分
	RedeemStoreCredit(ctx context.Context, amount int64, orderID int64, customerID int64) error
}

type client struct {
	subscriptionCli subscriptionsvcpb.SubscriptionServiceClient
}

func New() Client {
	return &client{
		subscriptionCli: grpc.NewClient(
			"moego-svc-subscription:9090", subscriptionsvcpb.NewSubscriptionServiceClient,
		),
	}
}

func (cli *client) RedeemStoreCredit(ctx context.Context, amount, orderID, customerID int64) error {
	user := &subscriptionpb.User{
		Id:   customerID,
		Type: subscriptionpb.User_CUSTOMER,
	}
	note := "promotion redeem"
	// 这个参数很变态，没有事先定死在 UpdateEntitlements 的入参
	// 但是 data 团队 需要解析这个字段来做分析，按指定格式hhh
	p := &subscriptionapipb.UpdateCreditParams{
		User:      user,
		Credit:    -amount,
		Note:      &note,
		Type:      subscriptionpb.UpdateCredit_TYPE_PAYMENT,
		InvoiceId: &orderID,
	}

	_, err := cli.subscriptionCli.UpdateEntitlements(ctx, &subscriptionsvcpb.UpdateEntitlementsRequest{
		User: user,
		Features: []*subscriptionpb.Feature{
			{
				Key: subscriptionpb.Feature_CREDIT_CREDIT_POINT,
				Setting: &subscriptionpb.Feature_Setting{
					Setting: &subscriptionpb.Feature_Setting_Count{
						Count: &subscriptionpb.Count{
							TotalAmount: -amount, // 这里是扣减，变成负数
						},
					},
				},
			},
		},
		Related: lo.ToPtr(p.String()),
	})

	return err
}
