package repo

import (
	"context"

	"go.uber.org/zap"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"gorm.io/gorm"

	"github.com/MoeGolibrary/go-lib/zlog"
	"github.com/MoeGolibrary/moego-svc-order-v2/internal/model"
)

type TipsSplitDetailRepo interface {
	Upsert(ctx context.Context, tipsSplitID int64, tipsSplitDetail []*model.TipsSplitDetail) error
	ListByIDs(ctx context.Context, tipsSplitID ...int64) ([]*model.TipsSplitDetail, error)
}

func NewTipsSplitDetailRepo(db *gorm.DB) TipsSplitDetailRepo {
	return &tipsSplitDetailRepo{
		db: db,
	}
}

type tipsSplitDetailRepo struct {
	db *gorm.DB
}

// Upsert implements TipsSplitDetailRepo.
func (repo *tipsSplitDetailRepo) Upsert(
	ctx context.Context,
	tipsSplitID int64,
	tipsSplitDetail []*model.TipsSplitDetail,
) error {
	if tipsSplitID <= 0 {
		return status.Error(codes.InvalidArgument, "tipsSplitID is not normal")
	}

	if len(tipsSplitDetail) == 0 {
		return status.Error(codes.InvalidArgument, "tipsSplitDetail is nil")
	}

	existingDetails, err := repo.ListByIDs(ctx, tipsSplitID)
	if err != nil {
		return err
	}

	existingMap := make(map[int64]*model.TipsSplitDetail)
	for _, detail := range existingDetails {
		existingMap[detail.StaffID] = detail
	}
	// Update existing details and identify new ones
	for _, newDetail := range tipsSplitDetail {
		if existingDetail, found := existingMap[newDetail.StaffID]; found {
			// Update existing detail
			existingDetail.SplitAmount = newDetail.SplitAmount
			if err := repo.withContext(ctx).Save(existingDetail).Error; err != nil {
				return status.Error(codes.Internal, err.Error())
			}

			delete(existingMap, newDetail.StaffID) // Remove from map to identify deletions
		} else {
			// Insert new detail
			if err := repo.withContext(ctx).Create(newDetail).Error; err != nil {
				return status.Error(codes.Internal, err.Error())
			}
		}
	}

	// Delete details not found in the new list
	for _, detailToDelete := range existingMap {
		if err := repo.withContext(ctx).Delete(detailToDelete).Error; err != nil {
			return status.Error(codes.Internal, err.Error())
		}
	}

	return nil
}

// ListByIDs implements TipsSplitRepo.
func (repo *tipsSplitDetailRepo) ListByIDs(
	ctx context.Context, tipsSplitIDs ...int64,
) ([]*model.TipsSplitDetail, error) {
	if len(tipsSplitIDs) == 0 {
		return nil, nil
	}

	tipsSplitDetails, err := listByConditions[*model.TipsSplitDetail](
		repo.withContext(ctx), map[string][]any{
			"tips_split_id IN ?": {tipsSplitIDs},
		},
	)
	if err != nil {
		zlog.Debug(ctx, "query tips split detail error", zap.Int64s("tips_split_id", tipsSplitIDs))
		return nil, err
	}

	return tipsSplitDetails, nil
}

func (repo *tipsSplitDetailRepo) withContext(ctx context.Context) *gorm.DB {
	const tableName = "public.tips_split_detail"

	return repo.db.WithContext(ctx).Table(tableName)
}
