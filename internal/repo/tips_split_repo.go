package repo

import (
	"context"

	"go.uber.org/zap"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	"github.com/MoeGolibrary/go-lib/gorm"
	"github.com/MoeGolibrary/go-lib/zlog"
	orderpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/order/v1"
	"github.com/MoeGolibrary/moego-svc-order-v2/internal/model"
)

type TipsSplitRepo interface {
	Create(ctx context.Context, tipsSplit *model.TipsSplit) error
	Update(ctx context.Context, tipsSplit *model.TipsSplit) (int64, error)
	Get(ctx context.Context, id int64) (*model.TipsSplit, error)
	GetBySourceIDAndType(
		ctx context.Context, sourceID int64, sourceType orderpb.OrderSourceType,
	) (*model.TipsSplit, error)
	DeleteLegacyTipSplitRecords(ctx context.Context, id int64) error
	ListLegacyTipSplitRecordsByOrderIDs(
		ctx context.Context,
		businessID int64,
		orderIDs []int64,
	) ([]*model.OrderTipsSplitRecord, error)

	// ListBySources 提供给老 Report 批量查询分配.
	ListBySources(ctx context.Context, sourceIDToType map[int64]orderpb.OrderSourceType) ([]*model.TipsSplit, error)
}

type tipsSplitRepo struct {
	db                *gorm.DB
	legacySplitRecord *orderTipSplitRecordRepo
}

// ListLegacyTipSplitRecordsByOrderIDs implements TipsSplitRepo.
func (repo *tipsSplitRepo) ListLegacyTipSplitRecordsByOrderIDs(
	ctx context.Context,
	businessID int64,
	orderIDs []int64,
) ([]*model.OrderTipsSplitRecord, error) {
	return repo.legacySplitRecord.ListByOrderIDs(ctx, businessID, orderIDs)
}

func NewTipsSplitRepo(db *gorm.DB) TipsSplitRepo {
	return &tipsSplitRepo{
		db:                db,
		legacySplitRecord: newOrderTipSplitRecordRepo(db),
	}
}

func (repo *tipsSplitRepo) DeleteLegacyTipSplitRecords(ctx context.Context, id int64) error {
	tipsSplit, err := repo.Get(ctx, id)
	if err != nil {
		return err
	}

	records, err := repo.legacySplitRecord.ListByOrderIDs(ctx, tipsSplit.BusinessID, tipsSplit.CollectedOrderIDs)
	if err != nil {
		return err
	}
	// delete each records
	for _, record := range records {
		zlog.Debug(ctx, "delete legacy tip split record", zap.Any("record", record))

		if _, err := repo.legacySplitRecord.DeleteByID(ctx, record.ID); err != nil {
			zlog.Error(
				ctx, "delete legacy tip split record failed",
				zap.Error(err),
				zap.Int64("id", record.ID),
			)

			return err
		}
	}

	return nil
}

// Update implements TipsSplitRepo
func (repo *tipsSplitRepo) Update(ctx context.Context, tipsSplit *model.TipsSplit) (int64, error) {
	tx := repo.withContext(ctx).Updates(tipsSplit)
	if tx.Error != nil {
		return 0, parseDBErr(tx.Error)
	}

	return tx.RowsAffected, nil
}

// Create implements TipsSplitRepo.
func (repo *tipsSplitRepo) Create(ctx context.Context, tipsSplit *model.TipsSplit) error {
	tx := repo.withContext(ctx).Create(tipsSplit)
	if tx.Error != nil {
		return parseDBErr(tx.Error)
	}

	return nil
}

// Get implements TipsSplitRepo.
func (repo *tipsSplitRepo) Get(ctx context.Context, id int64) (*model.TipsSplit, error) {
	return getByID[*model.TipsSplit](repo.withContext(ctx), id)
}

// GetBySourceIDAndType implements TipsSplitRepo.
func (repo *tipsSplitRepo) GetBySourceIDAndType(
	ctx context.Context, sourceID int64, sourceType orderpb.OrderSourceType,
) (*model.TipsSplit, error) {
	if sourceID <= 0 {
		return nil, status.Error(codes.InvalidArgument, "sourceID is not normal")
	}

	if sourceType != orderpb.OrderSourceType_APPOINTMENT {
		return nil, status.Error(codes.InvalidArgument, "not supported source type")
	}

	tipsSplits, err := listByConditions[*model.TipsSplit](
		repo.withContext(ctx), map[string][]any{
			"source_id = ? and source_type = ?": {sourceID, sourceType.String()},
		},
	)
	if err != nil {
		return nil, err
	}
	// check empty or return the first one
	if len(tipsSplits) == 0 {
		zlog.Debug(ctx, "tips split not found", zap.Int64("source_id", sourceID))
		return nil, status.Error(codes.NotFound, "tips split not found")
	}

	return tipsSplits[0], nil
}

func (repo *tipsSplitRepo) ListBySources(
	ctx context.Context, sourceIDToType map[int64]orderpb.OrderSourceType,
) ([]*model.TipsSplit, error) {
	if len(sourceIDToType) == 0 {
		return nil, nil
	}

	sources := make([][]any, 0, len(sourceIDToType))
	for id, typ := range sourceIDToType {
		sources = append(sources, []any{id, typ.String()})
	}

	return listByConditions[*model.TipsSplit](
		repo.withContext(ctx).Where(
			gorm.MultiIn{
				Columns: []string{"source_id", "source_type"},
				Values:  sources,
			},
		), map[string][]any{},
	)
}

func (repo *tipsSplitRepo) withContext(ctx context.Context) *gorm.DB {
	const tableName = "public.tips_split"

	return repo.db.WithContext(ctx).Table(tableName)
}
