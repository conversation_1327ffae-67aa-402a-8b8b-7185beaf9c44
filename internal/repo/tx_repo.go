package repo

import (
	"gorm.io/gorm"
)

type TXRepo interface {
	Tx(func(OrderTX) error) error
}

type OrderTX interface {
	Order() OrderRepo
	OrderItem() OrderItemRepo
	OrderLineDiscount() OrderLineDiscountRepo
	OrderPayment() OrderPaymentRepo
	RefundOrder() RefundOrderRepo
	RefundItem() RefundOrderItemRepo
	RefundPayment() RefundOrderPaymentRepo
	MessageDeliveryRepo() MessageDeliveryRepo
	DepositChangeLog() DepositChangeLogRepo
	OrderPromotion() OrderPromotionRepo
	OrderPromotionItem() OrderPromotionItemRepo

	MigrationRepo() MigrationRepo
}

type txRepo struct {
	db *gorm.DB
}

func NewTXRepo(db *gorm.DB) TXRepo {
	return &txRepo{db: db}
}

type orderTX struct {
	order              OrderRepo
	orderItem          OrderItemRepo
	orderLineDiscount  OrderLineDiscountRepo
	orderPayment       OrderPaymentRepo
	refundOrder        RefundOrderRepo
	refundItem         RefundOrderItemRepo
	refundPayment      RefundOrderPaymentRepo
	messageDelivery    MessageDeliveryRepo
	depositChangeLog   DepositChangeLogRepo
	orderPromotion     OrderPromotionRepo
	orderPromotionItem OrderPromotionItemRepo
	migrationRepo      MigrationRepo
}

func (repo *txRepo) Tx(fn func(tx OrderTX) error) error {
	return repo.db.Transaction(
		func(tx *gorm.DB) error {
			return fn(newOrderTx(tx))
		},
	)
}

func newOrderTx(tx *gorm.DB) OrderTX {
	return &orderTX{
		order:              NewOrderRepo(tx),
		orderItem:          NewOrderItemRepo(tx),
		orderLineDiscount:  NewOrderLineDiscountRepo(tx),
		orderPayment:       NewOrderPaymentRepo(tx),
		refundOrder:        NewRefundOrderRepo(tx),
		refundItem:         NewRefundOrderItemRepo(tx),
		refundPayment:      NewRefundOrderPaymentRepo(tx),
		messageDelivery:    NewMessageDeliveryRepo(tx),
		depositChangeLog:   NewDepositChangeLogRepoRepo(tx),
		orderPromotion:     NewOrderPromotionRepo(tx),
		orderPromotionItem: NewOrderPromotionItemRepo(tx),
		migrationRepo:      newMigrationRepo(tx),
	}
}

func (tx *orderTX) Order() OrderRepo {
	return tx.order
}

func (tx *orderTX) OrderItem() OrderItemRepo { return tx.orderItem }

func (tx *orderTX) OrderLineDiscount() OrderLineDiscountRepo { return tx.orderLineDiscount }

func (tx *orderTX) OrderPayment() OrderPaymentRepo { return tx.orderPayment }

func (tx *orderTX) RefundOrder() RefundOrderRepo { return tx.refundOrder }

func (tx *orderTX) RefundItem() RefundOrderItemRepo { return tx.refundItem }

func (tx *orderTX) RefundPayment() RefundOrderPaymentRepo { return tx.refundPayment }

func (tx *orderTX) MessageDeliveryRepo() MessageDeliveryRepo { return tx.messageDelivery }

func (tx *orderTX) DepositChangeLog() DepositChangeLogRepo { return tx.depositChangeLog }

func (tx *orderTX) OrderPromotion() OrderPromotionRepo { return tx.orderPromotion }

func (tx *orderTX) OrderPromotionItem() OrderPromotionItemRepo { return tx.orderPromotionItem }

func (tx *orderTX) MigrationRepo() MigrationRepo { return tx.migrationRepo }
