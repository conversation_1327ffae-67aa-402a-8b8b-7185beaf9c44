package repo

import "gorm.io/gorm"

type TxTipsSplitRepo interface {
	Tx(func(TipsSplitTx) error) error
}

type TipsSplitTx interface {
	TipsSplit() TipsSplitRepo
	TipsSplitDetailRepo() TipsSplitDetailRepo
	OrderItemRepo() OrderItemRepo
}

type txTipsSplitRepo struct {
	db *gorm.DB
}

type tipsSplitTx struct {
	tipsSplit       TipsSplitRepo
	tipsSplitDetail TipsSplitDetailRepo
	orderItemRepo   OrderItemRepo
}

// Tx implements TxTipsSplitRepo.
func (t *txTipsSplitRepo) Tx(fn func(TipsSplitTx) error) error {
	return t.db.Transaction(
		func(tx *gorm.DB) error {
			return fn(newTipsSplitTx(tx))
		},
	)
}

func newTipsSplitTx(tx *gorm.DB) TipsSplitTx {
	return &tipsSplitTx{
		tipsSplit:       NewTipsSplitRepo(tx),
		tipsSplitDetail: NewTipsSplitDetailRepo(tx),
		orderItemRepo:   NewOrderItemRepo(tx),
	}
}

func (t *tipsSplitTx) TipsSplit() TipsSplitRepo {
	return t.tipsSplit
}

func (t *tipsSplitTx) TipsSplitDetailRepo() TipsSplitDetailRepo {
	return t.tipsSplitDetail
}

func (t *tipsSplitTx) OrderItemRepo() OrderItemRepo {
	return t.orderItemRepo
}

func NewTxTipsSplitRepo(db *gorm.DB) TxTipsSplitRepo {
	return &txTipsSplitRepo{
		db: db,
	}
}
