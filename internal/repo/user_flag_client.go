package repo

import (
	"context"
	"strconv"

	growthbook "github.com/growthbook/growthbook-golang"

	"github.com/MoeGolibrary/go-lib/redis"
)

type UserFlagClient interface {
	SwitchedToNewInvoice(ctx context.Context, businessID int64) (bool, error)
	ImmutableInvoice(ctx context.Context, companyID int64) (bool, error)
}

type userFlagClient struct {
	cli redis.UniversalClient
	gb  *growthbook.Client
}

func NewUserFlagClient(cli redis.UniversalClient, gb *growthbook.Client) UserFlagClient {
	return &userFlagClient{cli: cli, gb: gb}
}

func (uf *userFlagClient) SwitchedToNewInvoice(ctx context.Context, businessID int64) (bool, error) {
	const key = "WHITE_LIST:order_reinvent"

	return uf.cli.SIsMember(ctx, key, businessID).Result()
}

func (uf *userFlagClient) ImmutableInvoice(ctx context.Context, companyID int64) (bool, error) {
	const feature = "new_order_flow"

	if err := uf.gb.EnsureLoaded(ctx); err != nil {
		return false, err
	}

	attr := growthbook.Attributes{
		"company": strconv.FormatInt(companyID, 10),
	}

	child, err := uf.gb.WithAttributes(attr)
	if err != nil {
		return false, err
	}

	res := child.EvalFeature(ctx, feature)

	return res.On, nil
}
