package repo

import (
	"errors"
	"fmt"
	"time"

	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"gorm.io/gorm"

	orderpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/order/v1"
	"github.com/MoeGolibrary/moego-svc-order-v2/internal/repo/internal/repohelper"
)

func IsNotFound(err error) bool {
	if errors.Is(err, gorm.ErrRecordNotFound) {
		return true
	}

	if code, ok := status.FromError(err); ok {
		return code.Code() == codes.NotFound
	}

	return false
}

func parseDBErr(err error) error {
	if err == nil {
		return nil
	}

	if errors.Is(err, gorm.ErrRecordNotFound) {
		return status.Error(codes.NotFound, err.Error())
	}

	return status.Error(codes.Internal, err.<PERSON>rror())
}

func getByID[T any](tx *gorm.DB, id int64) (T, error) {
	var t T

	if err := tx.Take(&t, "id = ?", id).Error; err != nil {
		return t, parseDBErr(err)
	}

	return t, nil
}

func listByKeyID[T any](tx *gorm.DB, keyName string, keyValue int64) ([]T, error) {
	return listByConditions[T](
		tx, map[string][]any{
			fmt.Sprintf("%s = ?", keyName): {keyValue},
		},
	)
}

func listByConditions[T any](tx *gorm.DB, whereCondition map[string][]any) ([]T, error) {
	var batch, allRecords []T

	for query, args := range whereCondition {
		tx.Where(query, args...)
	}

	if err := tx.FindInBatches(
		&batch, defaultSelectBatchSize,
		func(_ *gorm.DB, _ int) error {
			allRecords = append(allRecords, batch...)
			return nil
		},
	).Error; err != nil {
		return nil, parseDBErr(err)
	}

	return allRecords, nil
}

func toTimestamp(t time.Time) string {
	return t.UTC().Format("2006-01-02 15:04:05")
}

func withSourceTypeScope(sourceType orderpb.OrderSourceType) func(db *gorm.DB) *gorm.DB {
	switch sourceType {
	case orderpb.OrderSourceType_APPOINTMENT, orderpb.OrderSourceType_NO_SHOW:
		return func(db *gorm.DB) *gorm.DB {
			return db.Where("source_type IN (?)", []repohelper.SourceType{
				repohelper.ConvertToSourceType(orderpb.OrderSourceType_APPOINTMENT),
				repohelper.ConvertToSourceType(orderpb.OrderSourceType_NO_SHOW),
			})
		}
	case orderpb.OrderSourceType_BOOKING_REQUEST:
		return func(db *gorm.DB) *gorm.DB {
			return db.Where("source_type = ?", repohelper.ConvertToSourceType(orderpb.OrderSourceType_BOOKING_REQUEST))
		}
	default:
		return func(db *gorm.DB) *gorm.DB {
			return db
		}
	}
}
