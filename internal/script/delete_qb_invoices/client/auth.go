package client

import (
	"encoding/json"
	"fmt"
	"net/http"
	"net/url"
	"strings"
	"sync"
	"time"

	"go.uber.org/zap"

	"github.com/MoeGolibrary/go-lib/zlog"
)

const (
	tokenEndpoint = "https://oauth.platform.intuit.com/oauth2/v1/tokens/bearer"
)

// TokenResponse OAuth token 响应
type TokenResponse struct {
	AccessToken  string `json:"access_token"`
	TokenType    string `json:"token_type"`
	ExpiresIn    int    `json:"expires_in"`
	RefreshToken string `json:"refresh_token"`
	CreatedAt    time.Time
}

// TokenManager 管理 OAuth token 的生命周期
type TokenManager struct {
	clientID     string
	clientSecret string
	currentToken *TokenResponse
	mu           sync.RWMutex
}

// NewTokenManager 创建新的 token 管理器
func NewTokenManager(clientID, clientSecret string) *TokenManager {
	return &TokenManager{
		clientID:     clientID,
		clientSecret: clientSecret,
	}
}

// GetValidToken 获取有效的 access token
func (tm *TokenManager) GetValidToken(refreshToken string) (string, error) {
	tm.mu.RLock()
	currentToken := tm.currentToken
	tm.mu.RUnlock()

	// 检查当前 token 是否存在且有效
	if currentToken != nil {
		// 提前 5 分钟刷新 token
		if time.Since(currentToken.CreatedAt) < time.Duration(currentToken.ExpiresIn-300)*time.Second {
			return currentToken.AccessToken, nil
		}
	}

	// 需要刷新 token
	return tm.refreshToken(refreshToken)
}

// refreshToken 刷新 access token
func (tm *TokenManager) refreshToken(refreshToken string) (string, error) {
	tm.mu.Lock()
	defer tm.mu.Unlock()

	// 再次检查，防止并发刷新
	if tm.currentToken != nil {
		if time.Since(tm.currentToken.CreatedAt) < time.Duration(tm.currentToken.ExpiresIn-300)*time.Second {
			return tm.currentToken.AccessToken, nil
		}
	}

	// 准备刷新 token 的请求
	data := url.Values{}
	data.Set("grant_type", "refresh_token")
	data.Set("refresh_token", refreshToken)

	req, err := http.NewRequest(http.MethodPost, tokenEndpoint, strings.NewReader(data.Encode()))
	if err != nil {
		return "", fmt.Errorf("create refresh token request failed: %w", err)
	}

	// 设置认证头
	req.SetBasicAuth(tm.clientID, tm.clientSecret)
	req.Header.Set("Content-Type", "application/x-www-form-urlencoded")

	// 发送请求
	resp, err := http.DefaultClient.Do(req)
	if err != nil {
		return "", fmt.Errorf("refresh token request failed: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return "", fmt.Errorf("refresh token failed with status: %d", resp.StatusCode)
	}

	// 解析响应
	var tokenResp TokenResponse
	if err := json.NewDecoder(resp.Body).Decode(&tokenResp); err != nil {
		return "", fmt.Errorf("decode token response failed: %w", err)
	}

	// 记录创建时间
	tokenResp.CreatedAt = time.Now()

	// 更新当前 token
	tm.currentToken = &tokenResp

	zlog.Default().Info(
		"successfully refreshed access token",
		zap.Int("expires_in", tokenResp.ExpiresIn),
		zap.Time("created_at", tokenResp.CreatedAt),
	)

	return tokenResp.AccessToken, nil
}
