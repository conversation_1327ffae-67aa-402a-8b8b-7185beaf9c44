package client

import (
	"bytes"
	"encoding/json"
	"fmt"
	"net/http"
	"strings"
	"time"

	"go.uber.org/zap"

	"github.com/MoeGolibrary/go-lib/zlog"
)

type QBClient struct {
	httpClient   *http.Client
	baseURL      string
	refreshToken string
	realmID      string
	rateLimiter  *RateLimiter
	tokenManager *TokenManager
}

// Invoice QB发票结构体
type Invoice struct {
	Id        string `json:"Id"`
	SyncToken string `json:"SyncToken"`
	LinkedTxn []struct {
		TxnId   string `json:"TxnId"`
		TxnType string `json:"TxnType"`
	} `json:"LinkedTxn"`
}

// Payment QB付款结构体
type Payment struct {
	Id        string `json:"Id"`
	SyncToken string `json:"SyncToken"`
}

// BatchResponse QB批量查询响应
type BatchResponse struct {
	BatchItemResponse []struct {
		Fault *struct {
			Error []struct {
				Message string `json:"Message"`
				Detail  string `json:"Detail"`
			} `json:"Error"`
		} `json:"Fault,omitempty"`
		QueryResponse *struct {
			Invoice       []Invoice `json:"Invoice,omitempty"`
			Payment       []Payment `json:"Payment,omitempty"`
			MaxResults    int       `json:"maxResults"`
			StartPosition int       `json:"startPosition"`
			TotalCount    int       `json:"totalCount"`
		} `json:"QueryResponse,omitempty"`
	} `json:"BatchItemResponse"`
}

// BatchRequest QB批量查询请求
type BatchRequest struct {
	BatchItemRequest []any `json:"BatchItemRequest"`
}

// QueryItem QB查询项
type QueryItem struct {
	bId   string `json:"bId"`
	Query string `json:"Query"`
}

// InvoiceOperationItem QB发票操作项
type InvoiceOperationItem struct {
	bId       string      `json:"bId"`
	Operation string      `json:"operation"`
	Invoice   interface{} `json:"Invoice"`
}

// PaymentOperationItem QB付款操作项
type PaymentOperationItem struct {
	bId       string      `json:"bId"`
	Operation string      `json:"operation"`
	Payment   interface{} `json:"Payment"`
}

// InvoiceInfo 包含发票的 SyncToken 和关联的支付信息
type InvoiceInfo struct {
	SyncToken string
	Payments  []PaymentRef
}

// PaymentRef 包含支付记录的引用信息
type PaymentRef struct {
	ID   string
	Type string
}

type QBClientOption func(*QBClient)

// WithRefreshToken 设置刷新令牌
func WithRefreshToken(token string) QBClientOption {
	return func(c *QBClient) {
		c.refreshToken = token
	}
}

// WithRealmID 设置 QB RealmID
func WithRealmID(realmID string) QBClientOption {
	return func(c *QBClient) {
		c.realmID = realmID
	}
}

// WithTokenManager 设置 token 管理器
func WithTokenManager(tm *TokenManager) QBClientOption {
	return func(c *QBClient) {
		c.tokenManager = tm
	}
}

// NewQBClient 创建新的 QB 客户端
func NewQBClient(opts ...QBClientOption) *QBClient {
	client := &QBClient{
		httpClient: &http.Client{
			Timeout: 30 * time.Second,
		},
		baseURL:     "https://quickbooks.api.intuit.com/v3/company",
		rateLimiter: NewRateLimiter(),
	}

	for _, opt := range opts {
		opt(client)
	}

	return client
}

// do 执行 HTTP 请求，自动添加 QB 认证头
func (c *QBClient) do(req *http.Request) (*http.Response, error) {
	// 获取有效的 access token
	accessToken, err := c.tokenManager.GetValidToken(c.refreshToken)
	if err != nil {
		return nil, fmt.Errorf("get valid token failed: %w", err)
	}

	// 等待速率限制
	c.rateLimiter.Wait()

	// 添加 QB 认证头
	req.Header.Set("Authorization", "Bearer "+accessToken)
	req.Header.Set("Accept", "application/json")
	req.Header.Set("Content-Type", "application/json")

	// 记录请求头
	headers := make(map[string]string)

	for k, v := range req.Header {
		if k == "Authorization" {
			headers[k] = "Bearer ****" // 隐藏实际的 token
		} else {
			headers[k] = strings.Join(v, ", ")
		}
	}

	resp, err := c.httpClient.Do(req)
	if err != nil {
		return nil, err
	}

	// 记录响应头
	respHeaders := make(map[string]string)
	for k, v := range resp.Header {
		respHeaders[k] = strings.Join(v, ", ")
	}

	zlog.Default().Debug(
		"response headers",
		zap.Any("headers", respHeaders),
	)

	// 处理速率限制响应
	if c.rateLimiter.HandleResponse(resp) {
		// 关闭之前的响应
		resp.Body.Close()
		// 重新发送请求
		return c.do(req)
	}

	// 如果是认证错误，尝试刷新 token 并重试
	if resp.StatusCode == http.StatusUnauthorized {
		// 关闭之前的响应
		resp.Body.Close()
		// 强制刷新 token
		c.tokenManager.currentToken = nil
		// 重新发送请求
		return c.do(req)
	}

	return resp, nil
}

// GetEndpoint 获取完整的 API 端点
func (c *QBClient) GetEndpoint(path string) string {
	return c.baseURL + "/" + c.realmID + path
}

// GetInvoicesSyncToken 获取发票的 SyncToken 和关联的支付信息
func (c *QBClient) GetInvoicesSyncToken(invoiceIDs []string) (map[string]InvoiceInfo, error) {
	if len(invoiceIDs) == 0 {
		return nil, nil
	}

	// 构建批量查询请求
	batchReq := BatchRequest{
		BatchItemRequest: make([]any, len(invoiceIDs)),
	}

	for i, id := range invoiceIDs {
		batchReq.BatchItemRequest[i] = QueryItem{
			bId:   fmt.Sprintf("get_invoice_%d", i),
			Query: fmt.Sprintf("select Id, SyncToken, LinkedTxn from Invoice where Id = '%s'", id),
		}
	}

	// 序列化请求体
	body, err := json.Marshal(batchReq)
	if err != nil {
		return nil, fmt.Errorf("marshal batch request failed: %w", err)
	}

	// 创建请求
	req, err := http.NewRequest(http.MethodPost, c.GetEndpoint("/batch"), bytes.NewReader(body))
	if err != nil {
		return nil, fmt.Errorf("create request failed: %w", err)
	}

	// 发送请求
	resp, err := c.do(req)
	if err != nil {
		return nil, fmt.Errorf("do request failed: %w", err)
	}
	defer resp.Body.Close()

	// 解析响应
	var batchResp BatchResponse
	if err := json.NewDecoder(resp.Body).Decode(&batchResp); err != nil {
		return nil, fmt.Errorf("decode response failed: %w", err)
	}

	// 提取发票信息
	result := make(map[string]InvoiceInfo)

	for _, item := range batchResp.BatchItemResponse {
		if item.Fault != nil {
			zlog.Default().Warn(
				"get invoice failed",
				zap.Any("error", item.Fault.Error),
			)

			continue
		}

		if item.QueryResponse != nil && len(item.QueryResponse.Invoice) > 0 {
			invoice := item.QueryResponse.Invoice[0]
			info := InvoiceInfo{
				SyncToken: invoice.SyncToken,
				Payments:  make([]PaymentRef, 0),
			}

			// 提取关联的支付信息
			for _, txn := range invoice.LinkedTxn {
				if txn.TxnType == "Payment" {
					info.Payments = append(
						info.Payments, PaymentRef{
							ID:   txn.TxnId,
							Type: txn.TxnType,
						},
					)

					zlog.Default().Info(
						"found linked payment",
						zap.String("invoice_id", invoice.Id),
						zap.String("payment_id", txn.TxnId),
					)
				}
			}

			result[invoice.Id] = info
			zlog.Default().Info(
				"got invoice info",
				zap.String("invoice_id", invoice.Id),
				zap.String("sync_token", info.SyncToken),
				zap.Int("payment_count", len(info.Payments)),
			)
		}
	}

	return result, nil
}

// GetInvoicePayments 获取发票关联的付款记录
func (c *QBClient) GetInvoicePayments(invoiceID string) (map[string]string, error) {
	// 1. 先获取 Invoice 详情
	batchReq := BatchRequest{
		BatchItemRequest: []any{
			QueryItem{
				bId:   "get_invoice",
				Query: fmt.Sprintf("select * from Invoice where Id = '%s'", invoiceID),
			},
		},
	}

	// 序列化请求体
	body, err := json.Marshal(batchReq)
	if err != nil {
		return nil, fmt.Errorf("marshal batch request failed: %w", err)
	}

	// 创建请求
	req, err := http.NewRequest(http.MethodPost, c.GetEndpoint("/batch"), bytes.NewReader(body))
	if err != nil {
		return nil, fmt.Errorf("create request failed: %w", err)
	}

	// 记录查询详情
	zlog.Default().Info(
		"querying invoice details",
		zap.String("invoice_id", invoiceID),
		zap.String("query", fmt.Sprintf("select * from Invoice where Id = '%s'", invoiceID)),
	)

	// 发送请求
	resp, err := c.do(req)
	if err != nil {
		return nil, fmt.Errorf("do request failed: %w", err)
	}
	defer resp.Body.Close()

	// 解析响应
	var batchResp BatchResponse
	if err := json.NewDecoder(resp.Body).Decode(&batchResp); err != nil {
		return nil, fmt.Errorf("decode response failed: %w", err)
	}

	// 2. 从 Invoice 中获取关联的 Payment IDs
	var paymentIDs []string

	if len(batchResp.BatchItemResponse) > 0 && batchResp.BatchItemResponse[0].QueryResponse != nil {
		for _, invoice := range batchResp.BatchItemResponse[0].QueryResponse.Invoice {
			for _, txn := range invoice.LinkedTxn {
				if txn.TxnType == "Payment" {
					paymentIDs = append(paymentIDs, txn.TxnId)
					zlog.Default().Info(
						"found linked payment",
						zap.String("invoice_id", invoiceID),
						zap.String("payment_id", txn.TxnId),
					)
				}
			}
		}
	}

	if len(paymentIDs) == 0 {
		zlog.Default().Info(
			"no payments found for invoice",
			zap.String("invoice_id", invoiceID),
		)

		return nil, nil
	}

	// 3. 查询这些 Payment 的 SyncToken
	batchReq = BatchRequest{
		BatchItemRequest: make([]any, len(paymentIDs)),
	}

	for i, paymentID := range paymentIDs {
		batchReq.BatchItemRequest[i] = QueryItem{
			bId:   fmt.Sprintf("get_payment_%d", i),
			Query: fmt.Sprintf("select Id, SyncToken from Payment where Id = '%s'", paymentID),
		}
	}

	// 序列化请求体
	body, err = json.Marshal(batchReq)
	if err != nil {
		return nil, fmt.Errorf("marshal batch request failed: %w", err)
	}

	// 创建请求
	req, err = http.NewRequest(http.MethodPost, c.GetEndpoint("/batch"), bytes.NewReader(body))
	if err != nil {
		return nil, fmt.Errorf("create request failed: %w", err)
	}

	// 发送请求
	resp, err = c.do(req)
	if err != nil {
		return nil, fmt.Errorf("do request failed: %w", err)
	}
	defer resp.Body.Close()

	// 解析响应
	var paymentResp BatchResponse
	if err := json.NewDecoder(resp.Body).Decode(&paymentResp); err != nil {
		return nil, fmt.Errorf("decode response failed: %w", err)
	}

	// 提取 Payment 信息
	result := make(map[string]string)

	for _, item := range paymentResp.BatchItemResponse {
		if item.Fault != nil {
			zlog.Default().Warn(
				"get payment failed",
				zap.String("invoice_id", invoiceID),
				zap.Any("error", item.Fault.Error),
			)

			continue
		}

		if item.QueryResponse != nil && len(item.QueryResponse.Invoice) > 0 {
			payment := item.QueryResponse.Invoice[0] // 这里实际返回的是 Payment，但结构相同
			result[payment.Id] = payment.SyncToken
			zlog.Default().Info(
				"got payment sync token",
				zap.String("invoice_id", invoiceID),
				zap.String("payment_id", payment.Id),
				zap.String("sync_token", payment.SyncToken),
			)
		}
	}

	return result, nil
}

// GetPaymentsSyncToken 批量获取付款记录的 SyncToken
func (c *QBClient) GetPaymentsSyncToken(paymentIDs []string) (map[string]string, error) {
	if len(paymentIDs) == 0 {
		return nil, nil
	}

	// 构建批量查询请求
	batchReq := BatchRequest{
		BatchItemRequest: make([]any, len(paymentIDs)),
	}

	for i, id := range paymentIDs {
		batchReq.BatchItemRequest[i] = QueryItem{
			bId:   fmt.Sprintf("get_payment_%d", i),
			Query: fmt.Sprintf("select Id, SyncToken from Payment where Id = '%s'", id),
		}
	}

	// 序列化请求体
	body, err := json.Marshal(batchReq)
	if err != nil {
		return nil, fmt.Errorf("marshal batch request failed: %w", err)
	}

	// 创建请求
	req, err := http.NewRequest(http.MethodPost, c.GetEndpoint("/batch"), bytes.NewReader(body))
	if err != nil {
		return nil, fmt.Errorf("create request failed: %w", err)
	}

	// 发送请求
	resp, err := c.do(req)
	if err != nil {
		return nil, fmt.Errorf("do request failed: %w", err)
	}
	defer resp.Body.Close()

	// 解析响应
	var batchResp BatchResponse
	if err := json.NewDecoder(resp.Body).Decode(&batchResp); err != nil {
		return nil, fmt.Errorf("decode response failed: %w", err)
	}

	// 提取 Payment 信息
	result := make(map[string]string)

	for _, item := range batchResp.BatchItemResponse {
		if item.Fault != nil {
			zlog.Default().Warn(
				"get payment failed",
				zap.Any("error", item.Fault.Error),
			)

			continue
		}

		if item.QueryResponse != nil && len(item.QueryResponse.Payment) > 0 {
			payment := item.QueryResponse.Payment[0]
			result[payment.Id] = payment.SyncToken
			zlog.Default().Info(
				"got payment sync token",
				zap.String("payment_id", payment.Id),
				zap.String("sync_token", payment.SyncToken),
			)
		}
	}

	return result, nil
}

// DeleteInvoicesOptions 删除发票的选项
type DeleteInvoicesOptions struct {
	DryRun bool // 是否为演练模式，默认为 true
}

// DeleteInvoices 删除发票
func (c *QBClient) DeleteInvoices(invoiceIDs []string, opts DeleteInvoicesOptions) error {
	if opts.DryRun {
		zlog.Default().Info("running in dry run mode - no actual deletions will be performed")
	}

	// 1. 获取发票信息（包含 SyncToken 和关联的 Payment）
	syncTokens, err := c.GetInvoicesSyncToken(invoiceIDs)
	if err != nil {
		return fmt.Errorf("get invoice sync tokens failed: %w", err)
	}

	if len(syncTokens) == 0 {
		zlog.Default().Error("no valid invoices found")
		return nil
	}

	zlog.Default().Info(
		"got initial invoice sync tokens",
		zap.Int("count", len(syncTokens)),
	)

	// 2. 获取所有 payment 的 sync token
	allPaymentSyncTokens := make(map[string]string)

	var paymentIDs []string

	for _, info := range syncTokens {
		for _, payment := range info.Payments {
			paymentIDs = append(paymentIDs, payment.ID)
		}
	}

	const batchSize = 30
	for i := 0; i < len(paymentIDs); i += batchSize {
		end := i + batchSize
		if end > len(paymentIDs) {
			end = len(paymentIDs)
		}

		batch := paymentIDs[i:end]

		tokens, err := c.GetPaymentsSyncToken(batch)
		if err != nil {
			return fmt.Errorf("get payment sync token failed: %w", err)
		}

		for id, token := range tokens {
			allPaymentSyncTokens[id] = token
			zlog.Default().Info(
				"payment details",
				zap.String("payment_id", id),
				zap.String("payment_sync_token", token),
			)
		}
	}

	if !opts.DryRun {
		// 3. 创建删除请求（包含 payment 和 invoice）
		const batchSize = 30 // QuickBooks API 的批量操作限制

		var allOperations []any

		// 3.1 添加删除 payment 的操作
		for paymentID, syncToken := range allPaymentSyncTokens {
			deletePayment := Payment{
				Id:        paymentID,
				SyncToken: syncToken,
			}
			allOperations = append(
				allOperations, PaymentOperationItem{
					bId:       fmt.Sprintf("delete_payment_%s", paymentID),
					Operation: "delete",
					Payment:   deletePayment,
				},
			)
		}

		// 3.2 添加删除 invoice 的操作
		for id, info := range syncTokens {
			deleteInvoice := Invoice{
				Id:        id,
				SyncToken: info.SyncToken,
			}
			allOperations = append(
				allOperations, InvoiceOperationItem{
					bId:       fmt.Sprintf("delete_invoice_%s", id),
					Operation: "delete",
					Invoice:   deleteInvoice,
				},
			)
		}

		// 3.3 分批处理所有操作
		for i := 0; i < len(allOperations); i += batchSize {
			end := i + batchSize
			if end > len(allOperations) {
				end = len(allOperations)
			}

			batchReq := BatchRequest{
				BatchItemRequest: allOperations[i:end],
			}

			// 序列化请求体
			body, err := json.Marshal(batchReq)
			if err != nil {
				return fmt.Errorf("marshal delete batch request failed: %w", err)
			}

			// 创建请求
			req, err := http.NewRequest(http.MethodPost, c.GetEndpoint("/batch"), bytes.NewReader(body))
			if err != nil {
				return fmt.Errorf("create delete request failed: %w", err)
			}

			// 发送请求
			resp, err := c.do(req)
			if err != nil {
				return fmt.Errorf("do delete request failed: %w", err)
			}
			defer resp.Body.Close()

			// 解析响应
			var batchResp BatchResponse
			if err := json.NewDecoder(resp.Body).Decode(&batchResp); err != nil {
				return fmt.Errorf("decode delete response failed: %w", err)
			}

			// 检查响应中的错误
			for _, item := range batchResp.BatchItemResponse {
				if item.Fault != nil {
					zlog.Default().Error(
						"delete operation failed",
						zap.Any("error", item.Fault.Error),
					)

					return fmt.Errorf("delete operation failed: %v", item.Fault.Error[0].Message)
				}
			}

			zlog.Default().Info(
				"successfully processed batch",
				zap.Int("batch_start", i),
				zap.Int("batch_end", end),
				zap.Int("batch_size", end-i),
			)
		}

		zlog.Default().Info(
			"successfully deleted all items",
			zap.Int("payment_count", len(allPaymentSyncTokens)),
			zap.Int("invoice_count", len(syncTokens)),
		)
	} else {
		// Dry run 模式下只记录将要执行的操作
		zlog.Default().Info("[DRY RUN] would perform the following operations:")

		if len(allPaymentSyncTokens) > 0 {
			zlog.Default().Info(
				"[DRY RUN] would delete payments",
				zap.Int("payment_count", len(allPaymentSyncTokens)),
			)
		}

		zlog.Default().Info(
			"[DRY RUN] would delete invoices",
			zap.Int("invoice_count", len(syncTokens)),
		)
	}

	return nil
}
