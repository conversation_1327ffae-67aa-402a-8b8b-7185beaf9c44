package client

import (
	"context"
	"net/http"
	"time"

	"go.uber.org/zap"
	"golang.org/x/time/rate"

	"github.com/MoeGolibrary/go-lib/zlog"
)

const (
	// QB API 默认限制
	defaultRPS        = 10.0             // 每秒请求数
	defaultBurst      = 3                // 突发请求数
	rateLimitWaitTime = 60 * time.Second // 429 错误等待时间
)

// RateLimiter QB API 速率限制器
type RateLimiter struct {
	limiter *rate.Limiter
}

// NewRateLimiter 创建新的速率限制器
func NewRateLimiter() *RateLimiter {
	return &RateLimiter{
		limiter: rate.NewLimiter(rate.Limit(defaultRPS), defaultBurst),
	}
}

// Wait 等待令牌
func (r *RateLimiter) Wait() {
	r.limiter.Wait(context.Background())
}

// HandleResponse 处理响应中的速率限制信息
func (r *RateLimiter) HandleResponse(resp *http.Response) bool {
	// 检查是否达到速率限制
	if resp.StatusCode == http.StatusTooManyRequests {
		zlog.Default().Warn(
			"rate limit exceeded, waiting for recovery",
			zap.Duration("wait_time", rateLimitWaitTime),
		)
		time.Sleep(rateLimitWaitTime)

		return true
	}

	return false
}
