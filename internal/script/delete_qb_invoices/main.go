package main

import (
	"bufio"
	"flag"
	"os"
	"sync"
	"time"

	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"

	"github.com/MoeGolibrary/go-lib/zlog"

	"github.com/MoeGolibrary/moego-svc-order-v2/internal/script/delete_qb_invoices/client"
)

func main() {
	var (
		clientID     string
		clientSecret string
		refreshToken string
		realm        string
		orderFile    string
		dryRun       bool
	)

	flag.StringVar(&clientID, "client-id", "", "QB client ID")
	flag.StringVar(&clientSecret, "client-secret", "", "QB client secret")
	flag.StringVar(&refreshToken, "refresh-token", "", "QB refresh token")
	flag.StringVar(&realm, "realm", "", "QB realm ID")
	flag.StringVar(&orderFile, "order-file", "", "orders csv file path")
	flag.BoolVar(&dryRun, "dry-run", true, "dry run mode, no actual deletions will be performed")
	flag.Parse()

	if clientID == "" || clientSecret == "" || refreshToken == "" || realm == "" || orderFile == "" {
		flag.Usage()
		os.Exit(1)
	}

	// 初始化日志
	zlog.InitLogger(zlog.NewConfig(zlog.WithLevel(zapcore.DebugLevel)))

	// 初始化 token manager
	tokenManager := client.NewTokenManager(clientID, clientSecret)

	// 初始化 QB client
	qbClient := client.NewQBClient(
		client.WithRefreshToken(refreshToken),
		client.WithRealmID(realm),
		client.WithTokenManager(tokenManager),
	)

	// 解析发票 ID
	qbIDs, err := loadQBInvoiceIDs(orderFile)
	if err != nil {
		zlog.Default().Fatal("load QB invoice ids failed", zap.Error(err))
	}

	// 创建删除选项
	opts := client.DeleteInvoicesOptions{
		DryRun: dryRun,
	}

	ch := make(chan []string)

	wg := sync.WaitGroup{}
	for i := 0; i < 5; i++ {
		wg.Add(1)

		go func() {
			defer wg.Done()

			for batch := range ch {
				for {
					zlog.Default().Info("deleting invoices", zap.Int("count", len(batch)), zap.Strings("ids", batch))

					err := qbClient.DeleteInvoices(batch, opts)
					if err != nil {
						zlog.Default().Error("delete invoices failed", zap.Error(err))

						time.Sleep(time.Second)

						continue
					}

					zlog.Default().Info("successfully deleted invoices", zap.Int("count", len(batch)))

					break
				}
			}
		}()
	}

	// 删除发票
	const batchSize = 30

	for i := 0; i < len(qbIDs); i += batchSize {
		end := i + batchSize
		if end > len(qbIDs) {
			end = len(qbIDs)
		}

		batch := qbIDs[i:end]
		ch <- batch
	}

	close(ch)
	wg.Wait()

	zlog.Default().Info("completed invoice deletion process")
}

func loadQBInvoiceIDs(filepath string) ([]string, error) {
	file, err := os.Open(filepath)
	if err != nil {
		return nil, err
	}
	defer file.Close()

	var orders []string

	scanner := bufio.NewScanner(file)
	for scanner.Scan() {
		orders = append(orders, scanner.Text())
	}

	if err := scanner.Err(); err != nil {
		return nil, err
	}

	return orders, nil
}
