package service

import (
	"context"

	"github.com/samber/lo"

	"github.com/MoeGolibrary/go-lib/common/proto/money"
	"github.com/MoeGolibrary/go-lib/merror"
	errorspb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/errors/v1"
	itempb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/order/v1"
	ordersvcpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/temp_order/v1"
	"github.com/MoeGolibrary/moego-svc-order-v2/internal/model"
	"github.com/MoeGolibrary/moego-svc-order-v2/internal/repo"
)

// ItemAssignServiceServer 实现了 ItemAssignServiceServer 接口
type ItemAssignService interface {
	AssignItemAmount(
		ctx context.Context,
		req *ordersvcpb.AssignItemPaidAmountRequest,
	) (*ordersvcpb.AssignItemPaidAmountResponse, error)
	GetItemAssignedAmount(
		ctx context.Context,
		req *ordersvcpb.GetAssignedItemPaidAmountRequest,
	) (*ordersvcpb.GetAssignedItemPaidAmountResponse, error)
}

type itemAssignService struct {
	orderItemAssignedAmountRepo repo.OrderItemAssignedAmountRepo
}

func NewItemAssignService(orderItemAssignedAmountRepo repo.OrderItemAssignedAmountRepo) ItemAssignService {
	return &itemAssignService{orderItemAssignedAmountRepo: orderItemAssignedAmountRepo}
}

// Assign 方法用于分配item assigned amount
func (s *itemAssignService) AssignItemAmount(
	ctx context.Context,
	req *ordersvcpb.AssignItemPaidAmountRequest,
) (*ordersvcpb.AssignItemPaidAmountResponse, error) {
	// 这里是处理分配请求的逻辑
	// step1: get existing assigned amount from repo
	existingAssignedAmount, err := s.orderItemAssignedAmountRepo.BatchGetByOrderID(ctx, req.GetOrderId())
	if err != nil {
		return nil, err
	}

	if len(existingAssignedAmount) > 0 {
		return nil, merror.NewBizError(errorspb.Code_CODE_ASSIGN_ITEM_AMOUNT_ONCE_ONLY_ERROR,
			"can only assign item amount once")
	}
	// step2: transform the request to model
	assignedAmounts := lo.Map(req.GetItems(),
		func(item *itempb.ItemPaidAmountAssignment, _ int) *model.OrderItemAssignedAmount {
			return &model.OrderItemAssignedAmount{
				OrderID:      req.GetOrderId(),
				ItemID:       item.GetItemId(),
				Amount:       money.ToDecimal(item.GetAssignedPaidAmount()),
				CurrencyCode: item.GetAssignedPaidAmount().GetCurrencyCode(),
				BusinessID:   req.GetBusinessId(),
			}
		})

	// step3: insert the new assigned amount into repo
	err = s.orderItemAssignedAmountRepo.BatchInsert(ctx, assignedAmounts)
	if err != nil {
		return nil, err
	}
	// 返回响应
	return &ordersvcpb.AssignItemPaidAmountResponse{
		// 填充响应数据
		Success: true,
	}, nil
}

// Get 方法用于获取item assigned amount
func (s *itemAssignService) GetItemAssignedAmount(
	ctx context.Context,
	req *ordersvcpb.GetAssignedItemPaidAmountRequest,
) (*ordersvcpb.GetAssignedItemPaidAmountResponse, error) {
	// 这里是处理获取请求的逻辑
	assignedAmounts, err := s.orderItemAssignedAmountRepo.BatchGetByOrderID(ctx, req.GetOrderId())
	if err != nil {
		return nil, err
	}
	// check businessid
	if len(assignedAmounts) > 0 {
		if assignedAmounts[0].BusinessID != req.GetBusinessId() {
			return nil,
				merror.NewBizError(errorspb.Code_CODE_ASSIGN_ITEM_AMOUNT_DATA_PERMISSION_ERROR,
					"business id not match")
		}
	}
	// transfer to response
	assignedAmountsResp := lo.Map(assignedAmounts,
		func(item *model.OrderItemAssignedAmount, _ int) *itempb.ItemPaidAmountAssignment {
			return item.ToPB()
		})

	// 返回响应
	return &ordersvcpb.GetAssignedItemPaidAmountResponse{
		// 填充响应数据
		OrderId: req.GetOrderId(),
		Items:   assignedAmountsResp,
	}, nil
}
