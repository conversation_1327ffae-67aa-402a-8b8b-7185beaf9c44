// internal/service/assign_item_service_test.go
package service

import (
	"context"
	"testing"

	"github.com/shopspring/decimal"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"

	"github.com/MoeGolibrary/go-lib/common/proto/money"
	"github.com/MoeGolibrary/go-lib/merror"
	errorspb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/errors/v1"
	itempb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/order/v1"
	ordersvcpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/temp_order/v1"
	"github.com/MoeGolibrary/moego-svc-order-v2/internal/model"
)

// MockRepo 是一个模拟的仓库，用于测试
type MockRepo struct {
	mock.Mock
}

func (m *MockRepo) BatchGetByOrderID(ctx context.Context, orderID int64) ([]*model.OrderItemAssignedAmount, error) {
	args := m.Called(ctx, orderID)
	return args.Get(0).([]*model.OrderItemAssignedAmount), args.Error(1)
}

func (m *MockRepo) BatchInsert(ctx context.Context, amounts []*model.OrderItemAssignedAmount) error {
	args := m.Called(ctx, amounts)
	return args.Error(0)
}

func TestAssignItemAmount(t *testing.T) {
	mockRepo := new(MockRepo)
	service := &itemAssignService{orderItemAssignedAmountRepo: mockRepo}

	// 测试用例：成功分配金额-u
	t.Run(
		"成功分配金额", func(t *testing.T) {
			req := &ordersvcpb.AssignItemPaidAmountRequest{
				OrderId:    123,
				BusinessId: 1001,
				Items: []*itempb.ItemPaidAmountAssignment{
					{ItemId: 1231, AssignedPaidAmount: money.FromDecimal(decimal.NewFromInt(100), "USD")},
				},
			}

			mockRepo.On("BatchGetByOrderID", mock.Anything, int64(123)).
				Return([]*model.OrderItemAssignedAmount{}, nil)
			mockRepo.On("BatchInsert", mock.Anything, mock.Anything).
				Return(nil)

			resp, err := service.AssignItemAmount(context.Background(), req)

			assert.NoError(t, err)
			assert.True(t, resp.Success)
			mockRepo.AssertExpectations(t)
		},
	)
}

func TestAssignItemAmountFailed(t *testing.T) {
	mockRepo := new(MockRepo)
	service := &itemAssignService{orderItemAssignedAmountRepo: mockRepo}
	// 测试用例：已分配金额
	t.Run(
		"已分配金额", func(t *testing.T) {
			req := &ordersvcpb.AssignItemPaidAmountRequest{
				OrderId:    123,
				BusinessId: 1001,
				Items: []*itempb.ItemPaidAmountAssignment{
					{ItemId: 1231, AssignedPaidAmount: money.FromDecimal(decimal.NewFromInt(100), "USD")},
				},
			}
			mockRes := []*model.OrderItemAssignedAmount{
				{
					OrderID:      123,
					ItemID:       1231,
					CurrencyCode: "USD",
				},
			}
			mockRepo.On("BatchGetByOrderID", mock.Anything, int64(123)).Return(mockRes, nil)

			resp, err := service.AssignItemAmount(context.Background(), req)
			assert.Error(t, err)
			assert.Nil(t, resp)
			assert.Equal(
				t, merror.NewBizError(errorspb.Code_CODE_ASSIGN_ITEM_AMOUNT_ONCE_ONLY_ERROR, "can only assign item amount once"), err,
			)
			mockRepo.AssertExpectations(t)
		},
	)
}

// TestGetAssignedItemPaidAmount 测试获取已分配金额
func TestGetAssignedItemPaidAmountSuccess(t *testing.T) {
	mockRepo := new(MockRepo)
	service := &itemAssignService{orderItemAssignedAmountRepo: mockRepo}
	// test case: 获取已分配金额
	t.Run(
		"获取已分配金额", func(t *testing.T) {
			req := &ordersvcpb.GetAssignedItemPaidAmountRequest{
				OrderId:    123,
				BusinessId: 1001,
			}
			mockRepo.On("BatchGetByOrderID", mock.Anything, int64(123)).
				Return(
					[]*model.OrderItemAssignedAmount{
						{
							OrderID:      123,
							ItemID:       1231,
							CurrencyCode: "USD",
							BusinessID:   req.BusinessId,
							Amount:       decimal.NewFromInt(100),
						},
					}, nil,
				)

			resp, err := service.GetItemAssignedAmount(context.Background(), req)
			assert.NoError(t, err)

			assert.Equal(t, resp.Items[0].AssignedPaidAmount, money.FromDecimal(decimal.NewFromInt(100), "USD"))
			mockRepo.AssertExpectations(t)
		},
	)
}

func TestGetAssignedItemPaidAmountFailed(t *testing.T) {
	mockRepo := new(MockRepo)
	service := &itemAssignService{orderItemAssignedAmountRepo: mockRepo}
	// test case: 获取已分配金额
	t.Run(
		"获取已分配金额", func(t *testing.T) {
			req := &ordersvcpb.GetAssignedItemPaidAmountRequest{
				OrderId:    123,
				BusinessId: 1001,
			}
			mockRes := []*model.OrderItemAssignedAmount{
				{
					OrderID:      123,
					ItemID:       1231,
					CurrencyCode: "USD",
					BusinessID:   req.BusinessId + 1,
					Amount:       decimal.NewFromInt(100),
				},
			}
			mockRepo.On("BatchGetByOrderID", mock.Anything, int64(123)).
				Return(mockRes, nil)

			resp, err := service.GetItemAssignedAmount(context.Background(), req)
			assert.Error(t, err)
			assert.Nil(t, resp)
			assert.Equal(t, merror.NewBizError(errorspb.Code_CODE_ASSIGN_ITEM_AMOUNT_DATA_PERMISSION_ERROR, "business id not match"), err)
			mockRepo.AssertExpectations(t)
		},
	)
}
