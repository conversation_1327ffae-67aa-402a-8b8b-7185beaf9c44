package service

import (
	"context"
	"strings"

	"github.com/google/uuid"
	"github.com/samber/lo"
	"github.com/shopspring/decimal"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	"github.com/MoeGolibrary/go-lib/common/proto/money"
	orderpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/order/v1"
	ordersvcpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/order/v1"
	"github.com/MoeGolibrary/moego-svc-order-v2/internal/model"
	"github.com/MoeGolibrary/moego-svc-order-v2/internal/repo"
)

type DepositOrderService interface {
	CreateDepositOrder(ctx context.Context, req *ordersvcpb.CreateDepositOrderRequest) (*model.OrderDetail, error)
	UpdateDepositOrderSource(ctx context.Context, req *ordersvcpb.UpdateDepositOrderSourceRequest) error
	GetDeductedDepositOrderID(ctx context.Context, destOrderID int64) (int64, error)
	GetDepositDetail(ctx context.Context, order *model.Order) (*model.DepositDetail, error)
	GetDepositSummary(
		ctx context.Context,
		order *model.Order,
	) (*ordersvcpb.GetDepositDetailResponse, error)
}

func NewDepositOrderService(
	depositChangeLogRepo repo.DepositChangeLogRepo,
	orderRepo repo.OrderRepo,
	orderItemRepo repo.OrderItemRepo,
	txRepo repo.TXRepo,
) DepositOrderService {
	return &depositOrderService{
		depositChangeLogRepo: depositChangeLogRepo,
		orderRepo:            orderRepo,
		orderItemRepo:        orderItemRepo,
		txRepo:               txRepo,
	}
}

type depositOrderService struct {
	depositChangeLogRepo repo.DepositChangeLogRepo
	orderRepo            repo.OrderRepo
	orderItemRepo        repo.OrderItemRepo
	txRepo               repo.TXRepo
}

// CreateDepositOrder creates a new deposit order.
// Only compatible with Appointment now.
func (svc *depositOrderService) CreateDepositOrder(ctx context.Context, req *ordersvcpb.CreateDepositOrderRequest) (
	*model.OrderDetail, error,
) {
	// 目前只支持了 Appointment & OB.
	if req.GetSourceType() != orderpb.OrderSourceType_APPOINTMENT &&
		req.GetSourceType() != orderpb.OrderSourceType_BOOKING_REQUEST {
		return nil, status.Errorf(codes.InvalidArgument, "unsupported source type: %s", req.GetSourceType())
	}

	subTotalItems, err := svc.validateAndBuildSubTotalItems(req)
	if err != nil {
		return nil, err
	}

	order := &model.Order{
		OrderVersion: model.OrderVersionImmutableOrder,
		CompanyID:    req.GetCompanyId(),
		BusinessID:   req.GetBusinessId(),
		CustomerID:   req.GetCustomerId(),
		CreateBy:     req.GetStaffId(),
		UpdateBy:     req.GetStaffId(),
		SourceType:   strings.ToLower(req.GetSourceType().String()),
		SourceID:     req.GetSourceId(),
		CurrencyCode: req.GetDepositAmount().GetCurrencyCode(),

		OrderRefID: 0, // Fill below.
		// 用于 Pay online, 原本是在查询 GUID 的时候才生成，这里调整为创建的时候直接生成.
		GUID: strings.ReplaceAll(uuid.New().String(), "-", ""),

		OrderType:     orderpb.OrderModel_DEPOSIT,
		Status:        orderpb.OrderStatus_CREATED,
		PaymentStatus: orderpb.OrderModel_UNPAID,
	}

	orderItems := []*model.OrderItem{
		{
			BusinessID:        req.GetBusinessId(),
			ItemType:          model.OrderItemTypeDeposit,
			Name:              "Deposit",
			Description:       req.GetDepositDescription(),
			UnitPrice:         money.ToDecimal(req.GetDepositAmount()),
			Quantity:          1,
			PurchasedQuantity: 0,
			CurrencyCode:      req.GetDepositAmount().GetCurrencyCode(),
			Tax: model.Tax{
				Amount: decimal.Zero,
			},
			TipsAmount:             decimal.Zero,
			DiscountAmount:         decimal.Zero,
			SubTotalAmount:         decimal.Zero,
			TotalAmount:            decimal.Zero,
			RefundedQuantity:       0,
			RefundedAmount:         decimal.Zero,
			RefundedTaxAmount:      decimal.Zero,
			RefundedDiscountAmount: decimal.Zero,
			IsDeleted:              false,
			SubTotalItems:          subTotalItems,
		},
	}

	newDepositOrder := svc.buildOrder(order, orderItems)

	// 一个 Appointment 只能有一个有效的（status != REMOVED） Deposit Order
	txErr := svc.txRepo.Tx(
		func(tx repo.OrderTX) error {
			sourceType := req.GetSourceType()
			if sourceType == orderpb.OrderSourceType_APPOINTMENT || sourceType == orderpb.OrderSourceType_NO_SHOW {
				sourceType = orderpb.OrderSourceType_APPOINTMENT
			}

			orders, err := tx.Order().ListBySourceForUpdate(ctx, req.GetSourceId(), sourceType)
			if err != nil {
				return err
			}

			for _, od := range orders {
				if od.IsDeposit() && !od.IsCanceled() {
					return status.Errorf(codes.AlreadyExists, "can only take deposit one time for the same appointment")
				}
			}

			// 如果非第一笔订单，需要填入 OrderRefID.
			if len(orders) > 0 {
				od := orders[0]

				newDepositOrder.Order.OrderRefID = od.ID
				if od.OrderRefID > 0 {
					// 如果第一个单不是第一个创建的单，关联到同一个 OrderRefID.
					newDepositOrder.Order.OrderRefID = od.OrderRefID
				}
			}

			if err := tx.Order().Create(ctx, newDepositOrder.Order); err != nil {
				return err
			}

			// Fill orderID.
			for _, it := range newDepositOrder.OrderItems {
				it.OrderID = newDepositOrder.GetID()
			}

			return tx.OrderItem().BatchCreate(ctx, newDepositOrder.OrderItems)
		},
	)
	if txErr != nil {
		return nil, txErr
	}

	return newDepositOrder, nil
}

func (svc *depositOrderService) GetDepositSummary(
	ctx context.Context, depositOrder *model.Order,
) (*ordersvcpb.GetDepositDetailResponse, error) {
	changeLogs, err := svc.depositChangeLogRepo.ListByDepositOrderID(ctx, depositOrder.ID)
	if err != nil {
		return nil, err
	}

	collected := decimal.Zero
	reversed := decimal.Zero
	deducted := decimal.Zero

	for _, log := range changeLogs {
		switch log.ChangeType {
		case orderpb.DepositChangeType_INCREASE:
			collected = collected.Add(log.ChangedAmount)
		case orderpb.DepositChangeType_DECREASE:
			switch log.Reason {
			case orderpb.DepositChangeReason_DEDUCTION:
				deducted = deducted.Add(log.ChangedAmount)
			case orderpb.DepositChangeReason_OVERPAYMENT_REVERSAL:
				reversed = reversed.Add(log.ChangedAmount)
			default:
			}
		default:
		}
	}

	return &ordersvcpb.GetDepositDetailResponse{
		CollectedAmount: money.FromDecimal(collected, depositOrder.CurrencyCode),
		ReversedAmount:  money.FromDecimal(reversed, depositOrder.CurrencyCode),
		DeductedAmount:  money.FromDecimal(deducted, depositOrder.CurrencyCode),
		// 计算逻辑 Balance = collected - reversed - deducted
		Balance: money.FromDecimal(collected.Sub(reversed).Sub(deducted), depositOrder.CurrencyCode),
	}, nil
}

func (svc *depositOrderService) GetDepositDetail(ctx context.Context,
	order *model.Order,
) (*model.DepositDetail, error) {
	if order.OrderType != orderpb.OrderModel_DEPOSIT {
		return nil, nil
	}

	changeLogs, err := svc.depositChangeLogRepo.ListByDepositOrderID(ctx, order.ID)
	if err != nil {
		return nil, err
	}

	var latestChangeLog *model.DepositChangeLog
	if len(changeLogs) > 0 {
		latestChangeLog = changeLogs[0]
	}

	orderItems, err := svc.orderItemRepo.ListByOrder(ctx, order.ID)
	if err != nil {
		return nil, err
	}

	return &model.DepositDetail{
		DepositChangeLogs: changeLogs,
		LatestChangeLog:   latestChangeLog,
		DepositPriceItems: orderItems[0].SubTotalItems,
	}, nil
}

func (svc *depositOrderService) UpdateDepositOrderSource(
	ctx context.Context, req *ordersvcpb.UpdateDepositOrderSourceRequest,
) error {
	return svc.orderRepo.UpdateSourceForType(
		ctx, orderpb.OrderModel_DEPOSIT,
		req.GetOldSourceType(), req.GetOldSourceId(), req.GetNewSourceType(), req.GetNewSourceId(),
	)
}

func (svc *depositOrderService) GetDeductedDepositOrderID(ctx context.Context, destOrderID int64) (int64, error) {
	changeLog, err := svc.depositChangeLogRepo.GetDeductionByDestOrderID(ctx, destOrderID)
	if err != nil {
		return 0, err
	}

	return changeLog.DepositOrderID, nil
}

func (svc *depositOrderService) validateAndBuildSubTotalItems(
	req *ordersvcpb.CreateDepositOrderRequest,
) ([]*model.PriceItem, error) {
	// 向后兼容
	if len(req.GetDepositPriceDetail().GetPriceItems()) == 0 {
		return nil, nil
	}

	subTotalItems := make([]*model.PriceItem, 0, len(req.GetDepositPriceDetail().GetPriceItems()))
	subTotal := decimal.Zero

	for _, item := range req.GetDepositPriceDetail().GetPriceItems() {
		priceItem := &model.PriceItem{
			Name:         item.GetName(),
			UnitPrice:    money.ToDecimal(item.GetUnitPrice()),
			CurrencyCode: item.GetUnitPrice().GetCurrencyCode(),
			Quantity:     item.GetQuantity(),
			Subtotal:     money.ToDecimal(item.GetSubTotal()),
			Operator:     item.GetOperator(),
			ObjectType:   item.GetObjectType(),
			ObjectID:     item.GetObjectId(),
		}
		subTotalItems = append(subTotalItems, priceItem)

		if priceItem.Operator == orderpb.PriceDetailModel_PriceItem_ADD {
			subTotal = subTotal.Add(priceItem.UnitPrice.Mul(decimal.NewFromInt32(priceItem.Quantity)))
		} else {
			subTotal = subTotal.Sub(priceItem.UnitPrice.Mul(decimal.NewFromInt32(priceItem.Quantity)))
		}
	}

	if !subTotal.Equal(money.ToDecimal(req.GetDepositAmount())) {
		return nil, status.Error(codes.FailedPrecondition, "subtotal amount does not match deposit amount")
	}

	type groupKey struct {
		ObjectType orderpb.ItemType
		ObjectID   int64
	}

	// 按照 object type 和 object id 合并 price items，并且把 quantity 归整为 1
	groupedSubTotalItems := lo.GroupBy(
		subTotalItems,
		func(item *model.PriceItem) groupKey {
			return groupKey{item.ObjectType, item.ObjectID}
		},
	)
	reducedSubTotalItems := lo.MapToSlice(
		groupedSubTotalItems,
		func(key groupKey, items []*model.PriceItem) *model.PriceItem {
			st := lo.Reduce(
				items,
				func(agg decimal.Decimal, item *model.PriceItem, _ int) decimal.Decimal {
					if item.Operator == orderpb.PriceDetailModel_PriceItem_ADD {
						return agg.Add(item.UnitPrice.Mul(decimal.NewFromInt32(item.Quantity)))
					}

					return agg.Sub(item.UnitPrice.Mul(decimal.NewFromInt32(item.Quantity)))
				},
				decimal.Zero,
			)
			op := orderpb.PriceDetailModel_PriceItem_ADD

			if st.IsNegative() {
				st = st.Neg()
				op = orderpb.PriceDetailModel_PriceItem_SUBTRACT
			}

			return &model.PriceItem{
				Name:         items[0].Name,
				UnitPrice:    st,
				CurrencyCode: items[0].CurrencyCode,
				Quantity:     1,
				Subtotal:     st,
				Operator:     op,
				ObjectType:   key.ObjectType,
				ObjectID:     key.ObjectID,
			}
		},
	)

	return reducedSubTotalItems, nil
}

func (svc *depositOrderService) buildOrder(order *model.Order, items []*model.OrderItem) *model.OrderDetail {
	subTotal := decimal.Zero

	for _, it := range items {
		// TODO(yunxiang): 抽一套完整版的计算工具，需要同时处理之前 RefundHelper 的部分.
		it.SubTotalAmount = it.GetUnitPrice().Mul(decimal.NewFromInt32(it.Quantity - it.PurchasedQuantity))
		it.TotalAmount = it.GetSubTotalAmount()

		subTotal = subTotal.Add(it.SubTotalAmount)
	}

	// TODO(yunxiang): 计算一下 itemTypes 的 bitmap
	order.LineItemTypes = 1 << (int(orderpb.ItemType_ITEM_TYPE_DEPOSIT) - 1)
	order.TipsAmount = decimal.Zero
	order.TaxAmount = decimal.Zero
	order.DiscountAmount = decimal.Zero
	order.ConvenienceFee = decimal.Zero
	order.SubTotalAmount = subTotal
	order.TipsBasedAmount = decimal.Zero
	order.TotalAmount = subTotal
	order.PaidAmount = decimal.Zero
	order.RemainAmount = subTotal
	order.RefundedAmount = decimal.Zero

	return &model.OrderDetail{
		Order:               order,
		OrderItems:          items,
		OrderDiscount:       nil,
		OrderPayments:       nil,
		RefundOrderPayments: nil,
	}
}
