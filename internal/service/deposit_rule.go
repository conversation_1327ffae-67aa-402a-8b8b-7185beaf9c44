// NOTE:
// Deposit Rules 应当是上层业务逻辑，只是排期和人力等原因暂时放在 order-v2 服务里。不要过多耦合 order-v2 里的业务，以后可能要拿出去的。

package service

import (
	"context"
	"encoding/json"
	"slices"
	"strconv"
	"time"

	"github.com/samber/lo"
	"github.com/shopspring/decimal"
	"go.uber.org/zap"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	"github.com/MoeGolibrary/go-lib/ants"
	"github.com/MoeGolibrary/go-lib/common/proto/money"
	"github.com/MoeGolibrary/go-lib/zlog"
	offeringpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/offering/v1"
	onlinebookingpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/online_booking/v1"
	orderpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/order/v1"
	organizationpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/organization/v1"
	paymentpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/payment/v1"
	ordersvcpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/order/v2"
	"github.com/MoeGolibrary/moego-svc-order-v2/internal/core"
	"github.com/MoeGolibrary/moego-svc-order-v2/internal/model"
	"github.com/MoeGolibrary/moego-svc-order-v2/internal/repo"
	"github.com/MoeGolibrary/moego-svc-order-v2/internal/repo/depositrule"
	"github.com/MoeGolibrary/moego-svc-order-v2/internal/repo/grooming"
)

type DepositRuleService interface {
	CreateDepositRule(
		ctx context.Context, companyID, businessID int64, rule *orderpb.CreateDepositRuleDef,
	) (*model.DepositRule, error)
	UpdateDepositRule(ctx context.Context, ruleID int64, rule *orderpb.UpdateDepositRuleDef) (*model.DepositRule, error)
	DeleteDepositRule(ctx context.Context, ruleID int64) error
	ListDepositRules(ctx context.Context, businessID int64) ([]*model.DepositRule, error)
	PreviewDepositOrder(
		ctx context.Context, req *ordersvcpb.PreviewDepositOrderRequest,
	) (*model.OrderDetail, []*ordersvcpb.PreviewDepositOrderResponse_PriceItemByRule, error)

	MigrateToDepositRules(ctx context.Context, businessIDs []int64) ([]DepositRuleMigrationResult, error)
}

func NewDepositRulesService(
	depositRuleRepo depositrule.Repo,
	organizationRepo depositrule.OrganizationRepo,
	customerRepo depositrule.CustomerRepo,
	paymentClient repo.PaymentClient,
	groomingClient grooming.Client,
) DepositRuleService {
	return &depositRulesService{
		depositRuleRepo:  depositRuleRepo,
		organizationRepo: organizationRepo,
		customerRepo:     customerRepo,
		paymentClient:    paymentClient,
		groomingClient:   groomingClient,
	}
}

type depositRulesService struct {
	depositRuleRepo  depositrule.Repo
	organizationRepo depositrule.OrganizationRepo
	customerRepo     depositrule.CustomerRepo
	paymentClient    repo.PaymentClient
	groomingClient   grooming.Client
}

func (d *depositRulesService) CreateDepositRule(
	ctx context.Context, companyID, businessID int64, def *orderpb.CreateDepositRuleDef,
) (*model.DepositRule, error) {
	rule := &model.DepositRule{
		Name:       def.GetName(),
		Filters:    &orderpb.DepositFilters{Filters: def.GetFilters()},
		CompanyID:  companyID,
		BusinessID: businessID,
	}
	switch def.GetDepositConfig().(type) {
	case *orderpb.CreateDepositRuleDef_DepositByAmount:
		rule.DepositType = orderpb.DepositAmountType_BY_FIXED_AMOUNT
		rule.DepositAmount = money.ToDecimal(def.GetDepositByAmount())
		rule.DepositCurrency = def.GetDepositByAmount().GetCurrencyCode()
	case *orderpb.CreateDepositRuleDef_DepositByPercentage:
		rule.DepositType = orderpb.DepositAmountType_BY_PERCENTAGE

		p, err := decimal.NewFromString(def.GetDepositByPercentage().GetValue())
		if err != nil {
			return nil, status.Errorf(codes.InvalidArgument, "parse deposit percentage error: %v", err)
		}

		rule.DepositPercentage = p
	default:
		return nil, status.Errorf(codes.InvalidArgument, "invalid deposit config type")
	}

	if err := d.depositRuleRepo.Create(ctx, []*model.DepositRule{rule}); err != nil {
		return nil, err
	}

	return rule, nil
}

func (d *depositRulesService) UpdateDepositRule(
	ctx context.Context, ruleID int64, def *orderpb.UpdateDepositRuleDef,
) (*model.DepositRule, error) {
	rule, err := d.depositRuleRepo.Get(ctx, ruleID)
	if err != nil {
		return nil, err
	}

	rule.Name = def.GetName()
	rule.Filters = &orderpb.DepositFilters{Filters: def.GetFilters()}

	switch def.GetDepositConfig().(type) {
	case *orderpb.UpdateDepositRuleDef_DepositByAmount:
		rule.DepositType = orderpb.DepositAmountType_BY_FIXED_AMOUNT
		rule.DepositAmount = money.ToDecimal(def.GetDepositByAmount())
		rule.DepositCurrency = def.GetDepositByAmount().GetCurrencyCode()
	case *orderpb.UpdateDepositRuleDef_DepositByPercentage:
		rule.DepositType = orderpb.DepositAmountType_BY_PERCENTAGE

		p, err := decimal.NewFromString(def.GetDepositByPercentage().GetValue())
		if err != nil {
			return nil, status.Errorf(codes.InvalidArgument, "parse deposit percentage error: %v", err)
		}

		rule.DepositPercentage = p
	default:
		return nil, status.Errorf(codes.InvalidArgument, "invalid deposit config type")
	}

	if err := d.depositRuleRepo.Update(ctx, rule); err != nil {
		return nil, err
	}

	return rule, nil
}

func (d *depositRulesService) DeleteDepositRule(ctx context.Context, ruleID int64) error {
	return d.depositRuleRepo.Delete(ctx, ruleID)
}

func (d *depositRulesService) ListDepositRules(ctx context.Context, businessID int64) ([]*model.DepositRule, error) {
	rules, err := d.depositRuleRepo.ListByBusinessID(ctx, businessID)
	if err != nil {
		return nil, err
	}

	d.sortDepositRules(rules)

	return rules, nil
}

func (d *depositRulesService) PreviewDepositOrder(
	ctx context.Context, req *ordersvcpb.PreviewDepositOrderRequest,
) (*model.OrderDetail, []*ordersvcpb.PreviewDepositOrderResponse_PriceItemByRule, error) {
	pref, err := d.organizationRepo.GetCompanyPreference(ctx, req.GetCompanyId())
	if err != nil {
		return nil, nil, err
	}

	rules, err := d.depositRuleRepo.ListByBusinessID(ctx, req.GetBusinessId())
	if err != nil {
		return nil, nil, err
	}

	d.sortDepositRules(rules)

	loc := timeZonePBToLocation(pref.GetTimeZone())

	candidates, err := d.buildFilterCandidates(req, loc)
	if err != nil {
		return nil, nil, err
	}

	// 先简单做，只算总金额，后续 by item 记录
	totalDepositAmount := decimal.Zero

	// 这里的 items 只是落库到 order_item_price_detail 表，order_item 实际上还是只有一行的
	var depositPriceItems []depositPriceItemDetail

	for _, candidate := range candidates {
		_, items, matched := d.matchDepositRule(ctx, candidate, rules)
		if !matched {
			continue
		}

		for _, item := range items {
			totalDepositAmount = totalDepositAmount.Add(item.DepositAmount)
			depositPriceItems = append(depositPriceItems, item)
		}
	}

	if totalDepositAmount.IsZero() {
		return nil, nil, nil
	}

	convenienceFee := decimal.Zero

	if req.GetIncludeConvenienceFee() {
		fee, err := d.calculateProcessingFee(ctx, req.GetBusinessId(), totalDepositAmount)
		if err != nil {
			return nil, nil, err
		}

		convenienceFee = fee
	}

	totalAmount := totalDepositAmount.Add(convenienceFee)

	// 其实前端关注的应该只有 TotalAmount，所以有些拿不到的字段就不填充了
	order := &model.Order{
		OrderVersion: model.OrderVersionRefund,
		CompanyID:    req.GetCompanyId(),
		BusinessID:   req.GetBusinessId(),
		CustomerID:   req.GetCustomerId(),
		CurrencyCode: pref.GetCurrencyCode(),

		OrderRefID: 0,

		SubTotalAmount: totalDepositAmount,
		TotalAmount:    totalAmount,
		RemainAmount:   totalAmount,
		ConvenienceFee: convenienceFee,

		OrderType:     orderpb.OrderModel_DEPOSIT,
		Status:        orderpb.OrderStatus_CREATED,
		PaymentStatus: orderpb.OrderModel_UNPAID,
		LineItemTypes: 1 << (int(orderpb.ItemType_ITEM_TYPE_DEPOSIT) - 1),
	}

	// TODO(Perqin, P1): 530 版本先按照单 item 实现，暂不实现针对 item 的多条 item 收取
	orderItems := []*model.OrderItem{
		{
			BusinessID:     req.GetBusinessId(),
			ItemType:       model.OrderItemTypeDeposit,
			Name:           "Deposit",
			Description:    "Deposit by rules",
			UnitPrice:      totalDepositAmount,
			Quantity:       1,
			CurrencyCode:   pref.GetCurrencyCode(),
			SubTotalAmount: totalDepositAmount,
			TotalAmount:    totalDepositAmount,
		},
	}

	orderDetailPreview := &model.OrderDetail{
		Order:      order,
		OrderItems: orderItems,
	}
	orderDepositItems := lo.Map(
		depositPriceItems,
		func(item depositPriceItemDetail, _ int) *ordersvcpb.PreviewDepositOrderResponse_PriceItemByRule {
			return &ordersvcpb.PreviewDepositOrderResponse_PriceItemByRule{
				PriceItem: &orderpb.PriceDetailModel_PriceItem{
					Name:       item.Item.GetService().GetName(),
					Operator:   orderpb.PriceDetailModel_PriceItem_ADD,
					Quantity:   1,
					UnitPrice:  money.FromDecimal(item.DepositAmount, pref.GetCurrencyCode()),
					SubTotal:   money.FromDecimal(item.DepositAmount, pref.GetCurrencyCode()),
					ObjectType: orderpb.ItemType_ITEM_TYPE_SERVICE,
					ObjectId:   item.Item.GetService().GetId(),
				},
				RuleId:       item.RuleID,
				ExternalUuid: item.Item.ExternalUuid,
			}
		},
	)

	return orderDetailPreview, orderDepositItems, nil
}

// buildFilterCandidates merges the add-on items to their associated service items.
func (d *depositRulesService) buildFilterCandidates(
	req *ordersvcpb.PreviewDepositOrderRequest, loc *time.Location,
) ([]*FilterCandidate, error) {
	var appointmentStartDate time.Time

	if req.GetAppointmentStartDate() != "" {
		date, err := time.ParseInLocation(time.DateOnly, req.GetAppointmentStartDate(), loc)
		if err != nil {
			return nil, err
		}

		appointmentStartDate = date
	}

	type petServiceKey struct {
		PetID     int64
		ServiceID int64
	}

	candidates := make([]*FilterCandidate, 0, len(req.GetServicePricingDetails()))
	psToCandidates := make(map[petServiceKey]*FilterCandidate)

	// 非 Addon 都可以独立作为 Candidate.
	nonAddonDetails := lo.Filter(
		req.GetServicePricingDetails(),
		func(it *ordersvcpb.PreviewDepositOrderRequest_ServicePricingDetail, _ int) bool {
			return it.GetService().GetType() != offeringpb.ServiceType_ADDON
		},
	)

	for _, detail := range nonAddonDetails {
		key := petServiceKey{
			PetID:     detail.GetPetId(),
			ServiceID: detail.GetService().GetId(),
		}
		cad := &FilterCandidate{
			CompanyID:            req.GetCompanyId(),
			BusinessID:           req.GetBusinessId(),
			CustomerID:           req.GetCustomerId(),
			AppointmentStartDate: appointmentStartDate,
			Item:                 detail,
		}

		candidates = append(candidates, cad)
		psToCandidates[key] = cad
	}

	// Addon 在当前版本能独立存在，也可以挂在某个 <pet, service> 上。
	// 独立存在的时候，应当按照独立 Candidate 处理.
	addonDetails := lo.Filter(
		req.GetServicePricingDetails(),
		func(it *ordersvcpb.PreviewDepositOrderRequest_ServicePricingDetail, _ int) bool {
			return it.GetService().GetType() == offeringpb.ServiceType_ADDON
		},
	)

	for _, detail := range addonDetails {
		key := petServiceKey{
			PetID:     detail.GetPetId(),
			ServiceID: detail.GetAssociatedServiceId(),
		}

		// 匹配到了 Service.
		cad, ok := psToCandidates[key]
		if ok {
			cad.AddonItems = append(cad.AddonItems, detail)
			continue
		}

		// 独立的 Addon.
		cad = &FilterCandidate{
			CompanyID:            req.GetCompanyId(),
			BusinessID:           req.GetBusinessId(),
			CustomerID:           req.GetCustomerId(),
			AppointmentStartDate: appointmentStartDate,
			Item:                 detail,
		}

		candidates = append(candidates, cad)
		psToCandidates[key] = cad
	}

	return candidates, nil
}

func (d *depositRulesService) sortDepositRules(rules []*model.DepositRule) {
	slices.SortFunc(
		rules,
		func(a, b *model.DepositRule) int {
			af := a.GetDateRangeFilter()
			bf := b.GetDateRangeFilter()

			// No expiration goes first
			if af == nil && bf != nil {
				return -1
			}

			if af != nil && bf == nil {
				return 1
			}

			// Sort by start date
			if af != nil && bf != nil {
				ast := af.GetRange().GetStartTime().AsTime()
				aet := af.GetRange().GetEndTime().AsTime()
				bst := bf.GetRange().GetStartTime().AsTime()
				bet := bf.GetRange().GetEndTime().AsTime()

				// 先比较 end time，保证过期的在后面
				if !aet.Equal(bet) {
					// 倒序
					return bet.Compare(aet)
				}

				if !ast.Equal(bst) {
					return bst.Compare(ast)
				}
			}

			// Both no expiration, or same expiration range
			return b.CreateTime.Compare(a.CreateTime)
		},
	)
}

type depositPriceItemDetail struct {
	Item          *ordersvcpb.PreviewDepositOrderRequest_ServicePricingDetail
	DepositAmount decimal.Decimal
	RuleID        int64
}

// matchDepositRule matches the best deposit rule for the given item. The "best" means that when multiple rules match,
// it chooses the one with the highest price.
func (d *depositRulesService) matchDepositRule(
	ctx context.Context, candidate *FilterCandidate, rules []*model.DepositRule,
) (*model.DepositRule, []depositPriceItemDetail, bool) {
	// Deposit rules only apply to grooming, boarding, and daycare services.
	if candidate.Item.GetService().GetServiceItemType() != offeringpb.ServiceItemType_GROOMING &&
		candidate.Item.GetService().GetServiceItemType() != offeringpb.ServiceItemType_BOARDING &&
		candidate.Item.GetService().GetServiceItemType() != offeringpb.ServiceItemType_DAYCARE {
		return nil, nil, false
	}

	// 只有关联到 service 上的 addon 的金额会被一并计算
	if candidate.Item.GetService().GetType() == offeringpb.ServiceType_ADDON {
		return nil, nil, false
	}

	var matchedRule *model.DepositRule

	var matchedItems []depositPriceItemDetail

	matchedDepositAmount := decimal.Zero

	for _, rule := range rules {
		matched := true

		for _, filter := range rule.Filters.GetFilters() {
			if !d.getFilterMatcher(filter).Match(ctx, candidate) {
				matched = false
				break
			}
		}

		if !matched {
			continue
		}

		amount, items := d.calculateDepositAmount(candidate, rule)
		if amount.GreaterThan(matchedDepositAmount) {
			matchedRule = rule
			matchedItems = items
			matchedDepositAmount = amount
		}
	}

	return matchedRule, matchedItems, matchedRule != nil
}

func (d *depositRulesService) getFilterMatcher(filter *orderpb.DepositFilter) DepositRuleMatcher {
	switch filter.GetFilter().(type) {
	case *orderpb.DepositFilter_ClientGroupFilter:
		return &clientGroupMatcher{
			filter:       filter.GetClientGroupFilter(),
			customerRepo: d.customerRepo,
		}
	case *orderpb.DepositFilter_ServiceFilter:
		return &serviceMatcher{filter: filter.GetServiceFilter()}
	case *orderpb.DepositFilter_DateRangeFilter:
		return &dateRangeMatcher{filter: filter.GetDateRangeFilter()}
	default:
		return &nothingMatcher{}
	}
}

func (d *depositRulesService) calculateDepositAmount(
	candidate *FilterCandidate, rule *model.DepositRule,
) (decimal.Decimal, []depositPriceItemDetail) {
	var items []depositPriceItemDetail

	switch rule.DepositType {
	case orderpb.DepositAmountType_BY_FIXED_AMOUNT:
		// By fixed amount 的时候不考虑 addon
		items = append(items, depositPriceItemDetail{
			Item:          candidate.Item,
			DepositAmount: rule.DepositAmount,
			RuleID:        rule.ID,
		})

		return rule.DepositAmount, items
	case orderpb.DepositAmountType_BY_PERCENTAGE:
		ratio := core.RateForCal(rule.DepositPercentage)
		amount := decimal.Zero

		// Service
		depositAmount := core.RoundPreTaxAmount(money.ToDecimal(candidate.Item.GetTotalPrice()).Mul(ratio))
		amount = amount.Add(depositAmount)
		items = append(items, depositPriceItemDetail{
			Item:          candidate.Item,
			DepositAmount: depositAmount,
			RuleID:        rule.ID,
		})

		// Add-ons
		for _, addon := range candidate.AddonItems {
			depositAmount := core.RoundPreTaxAmount(money.ToDecimal(addon.GetTotalPrice()).Mul(ratio))
			amount = amount.Add(depositAmount)
			items = append(items, depositPriceItemDetail{
				Item:          addon,
				DepositAmount: depositAmount,
				RuleID:        rule.ID,
			})
		}

		return amount, items

	default:
		return decimal.Zero, nil
	}
}

func (d *depositRulesService) calculateProcessingFee(
	ctx context.Context, businessID int64, amount decimal.Decimal,
) (decimal.Decimal, error) {
	if amount.IsZero() {
		return decimal.Zero, nil
	}

	paymentSetting, err := d.paymentClient.GetPaymentSetting(ctx, businessID)
	if err != nil {
		return decimal.Zero, err
	}

	if paymentSetting.ProcessingFeePayBy != repo.ProcessingFeePayByClient {
		return decimal.Zero, nil
	}

	fee, err := d.paymentClient.GetConvenienceFee(
		ctx, businessID, amount, paymentpb.StripePaymentMethod_STRIPE_PAYMENT_METHOD_CARD,
	)
	if err != nil {
		return decimal.Zero, err
	}

	return fee, nil
}

func timeZonePBToLocation(tz *organizationpb.TimeZone) *time.Location {
	return time.FixedZone(tz.GetName(), int(tz.GetSeconds()))
}

type DepositRuleMigrationResult struct {
	BusinessID int64
	Error      error
}

// MigrateToDepositRules migrates the old OB payment settings to deposit rules. Note that the returned results are not
// guaranteed to be in the same order as the input business IDs.
func (d *depositRulesService) MigrateToDepositRules(
	ctx context.Context, businessIDs []int64,
) ([]DepositRuleMigrationResult, error) {
	obSettings, err := d.groomingClient.ListOBSetting(ctx, businessIDs)
	if err != nil {
		return nil, err
	}

	results := make(chan DepositRuleMigrationResult)

	for _, obSetting := range obSettings {
		err := ants.Go(ctx, time.Minute, func(ctx context.Context) {
			err := d.migrateBusinessToDepositRules(ctx, obSetting)
			results <- DepositRuleMigrationResult{BusinessID: obSetting.BusinessID, Error: err}
		})
		// Goroutine not submitted
		if err != nil {
			results <- DepositRuleMigrationResult{BusinessID: obSetting.BusinessID, Error: err}
		}
	}

	ret := make([]DepositRuleMigrationResult, 0, len(businessIDs))
	for i := 0; i < len(businessIDs); i++ {
		ret = append(ret, <-results)
	}

	close(results)

	return ret, nil
}

func (d *depositRulesService) convertGroupSettingToClientGroupFilter(
	acceptClient onlinebookingpb.AcceptClientType, rule string,
) *orderpb.DepositRuleClientGroupFilter {
	return &orderpb.DepositRuleClientGroupFilter{
		NewVisitors: acceptClient == onlinebookingpb.AcceptClientType_ACCEPT_CLIENT_TYPE_NEW ||
			acceptClient == onlinebookingpb.AcceptClientType_ACCEPT_CLIENT_TYPE_BOTH,
		ExistingCustomers: acceptClient == onlinebookingpb.AcceptClientType_ACCEPT_CLIENT_TYPE_EXISTING ||
			acceptClient == onlinebookingpb.AcceptClientType_ACCEPT_CLIENT_TYPE_BOTH,
		ExistingCustomersFilterJson: rule,
	}
}

func (d *depositRulesService) migrateBusinessToDepositRules(ctx context.Context, obSetting *grooming.OBSetting) error {
	pref, err := d.organizationRepo.GetCompanyPreference(ctx, obSetting.CompanyID)
	if err != nil {
		return err
	}

	var rules []*model.DepositRule

	var update grooming.ObSettingUpdate

	var updatePaymentSetting bool

	var resetGroupPaymentSetting bool

	if obSetting.PaymentType == onlinebookingpb.PaymentType_PAYMENT_TYPE_PREPAY {
		depositType, depositAmount, depositPercentage := d.convertDepositConfig(
			obSetting.PrepayType, obSetting.DepositType,
			obSetting.DepositPercentage, obSetting.DepositAmount,
		)
		rules = append(rules, &model.DepositRule{
			Name:              "Default deposit rule",
			Filters:           &orderpb.DepositFilters{},
			DepositType:       depositType,
			DepositCurrency:   pref.GetCurrencyCode(),
			DepositAmount:     depositAmount,
			DepositPercentage: depositPercentage,
			CompanyID:         obSetting.CompanyID,
			BusinessID:        obSetting.BusinessID,
		})

		updatePaymentSetting = true
		update.PaymentType = lo.ToPtr(onlinebookingpb.PaymentType_PAYMENT_TYPE_DISABLE)
	}

	if obSetting.PaymentType == onlinebookingpb.PaymentType_PAYMENT_TYPE_PRE_AUTH &&
		obSetting.PreAuthTipEnable == grooming.TipEnableTrue {
		updatePaymentSetting = true
		update.PreAuthTipEnable = lo.ToPtr(grooming.TipEnableFalse)
	}

	if obSetting.GroupPaymentType != nil &&
		*obSetting.GroupPaymentType == onlinebookingpb.PaymentType_PAYMENT_TYPE_PREPAY {
		depositType, depositAmount, depositPercentage := d.convertDepositConfig(
			obSetting.GroupPrepayType, obSetting.GroupDepositType,
			obSetting.GroupDepositPercentage, obSetting.GroupDepositAmount,
		)
		rules = append(rules, &model.DepositRule{
			Name: "Customized deposit rule by clients",
			Filters: &orderpb.DepositFilters{
				Filters: []*orderpb.DepositFilter{
					{
						Filter: &orderpb.DepositFilter_ClientGroupFilter{
							ClientGroupFilter: d.convertGroupSettingToClientGroupFilter(
								obSetting.GroupAcceptClient, obSetting.GroupFilterRule,
							),
						},
					},
				},
			},
			DepositType:       depositType,
			DepositCurrency:   pref.GetCurrencyCode(),
			DepositAmount:     depositAmount,
			DepositPercentage: depositPercentage,
			CompanyID:         obSetting.CompanyID,
			BusinessID:        obSetting.BusinessID,
		})

		resetGroupPaymentSetting = true
	}

	if obSetting.GroupPaymentType != nil &&
		*obSetting.GroupPaymentType == onlinebookingpb.PaymentType_PAYMENT_TYPE_PRE_AUTH &&
		obSetting.GroupPreAuthTipEnable == grooming.TipEnableTrue {
		updatePaymentSetting = true
		update.GroupPreAuthTipEnable = lo.ToPtr(grooming.TipEnableFalse)
	}

	if len(rules) > 0 {
		if err := d.depositRuleRepo.Create(ctx, rules); err != nil {
			return err
		}
	}

	if updatePaymentSetting {
		if err := d.groomingClient.UpdateOBPaymentSetting(ctx, obSetting.BusinessID, update); err != nil {
			return err
		}
	}

	if resetGroupPaymentSetting {
		if err := d.groomingClient.ResetOBGroupPaymentSetting(ctx, obSetting.BusinessID); err != nil {
			return err
		}
	}

	return nil
}

func (d *depositRulesService) convertDepositConfig(
	prepayType onlinebookingpb.PrepayType,
	prepayDepositType onlinebookingpb.PrepayDepositType,
	depositPercentage int64,
	depositAmount decimal.Decimal,
) (t orderpb.DepositAmountType, amount, percentage decimal.Decimal) {
	switch prepayType {
	case onlinebookingpb.PrepayType_PREPAY_TYPE_FULL_AMOUNT:
		const fullPercentage = 100
		return orderpb.DepositAmountType_BY_PERCENTAGE, decimal.Zero, decimal.NewFromInt(fullPercentage)
	case onlinebookingpb.PrepayType_PREPAY_TYPE_DEPOSIT:
		switch prepayDepositType {
		case onlinebookingpb.PrepayDepositType_PREPAY_DEPOSIT_TYPE_FIXED_AMOUNT:
			return orderpb.DepositAmountType_BY_FIXED_AMOUNT, depositAmount, decimal.Zero
		case onlinebookingpb.PrepayDepositType_PREPAY_DEPOSIT_TYPE_PERCENTAGE:
			return orderpb.DepositAmountType_BY_PERCENTAGE, decimal.Zero, decimal.NewFromInt(depositPercentage)
		}
	}

	return orderpb.DepositAmountType_DEPOSIT_AMOUNT_TYPE_UNSPECIFIED, decimal.Zero, decimal.Zero
}

//// Begin: filter matchers

type FilterCandidate struct {
	CompanyID  int64
	BusinessID int64
	// 0 stands for a new visitor
	CustomerID           int64
	AppointmentStartDate time.Time
	Item                 *ordersvcpb.PreviewDepositOrderRequest_ServicePricingDetail
	AddonItems           []*ordersvcpb.PreviewDepositOrderRequest_ServicePricingDetail
}

// GetTotalPrice returns the sum of the item itself price and the associated addons.
func (c *FilterCandidate) GetTotalPrice() decimal.Decimal {
	price := money.ToDecimal(c.Item.GetTotalPrice())
	for _, a := range c.AddonItems {
		price = price.Add(money.ToDecimal(a.GetTotalPrice()))
	}

	return price
}

// DepositRuleMatcher is an interface for matching deposit rules.
type DepositRuleMatcher interface {
	// Match returns true if this matcher matches the given item.
	Match(ctx context.Context, candidate *FilterCandidate) bool
}

// clientGroupMatcher matches against a ClientGroupFilter.
type clientGroupMatcher struct {
	customerRepo depositrule.CustomerRepo
	filter       *orderpb.DepositRuleClientGroupFilter
}

func (m *clientGroupMatcher) Match(ctx context.Context, candidate *FilterCandidate) bool {
	if m.filter.GetNewVisitors() && candidate.CustomerID == 0 {
		return true
	}

	if m.filter.GetExistingCustomers() && candidate.CustomerID != 0 {
		j := m.filter.GetExistingCustomersFilterJson()

		var filter depositrule.SmartListFilter
		// 不合法的 json 认定为无匹配；要指定匹配所有 customers 不能传空字符串，必须传空 JSON 对象 `{}`
		if err := json.Unmarshal([]byte(j), &filter); err != nil {
			return false
		}
		// Fast path: if the filter is empty, we can skip the API call
		if filter.IsEmpty() {
			return true
		}

		//nolint:gocritic // See below
		// listParams := depositrule.SmartListParams{
		//	Filter: depositrule.SmartListFilter{
		//		Type: "TYPE_AND",
		//		Filters: []depositrule.SmartListFilter{
		//			{
		//				Property: "client_id",
		//				Value:    strconv.FormatInt(candidate.CustomerID, 10),
		//				Operator: "OPERATOR_EQUAL",
		//			},
		//			filter,
		//		},
		//	},
		//}

		// TODO(Perqin, P2): customer 服务的接口有问题，上面的层数过多会报错，这里只能先拍平。带来的问题是，如果商家指定的 filter 不是
		//  AND 模式的话，client_id 的匹配条件就会不符合预期。
		if filter.Type != "TYPE_AND" {
			zlog.Warn(ctx,
				"client group filter must have type TYPE_AND but not, force false match",
				zap.String("type", filter.Type))

			return false
		}

		listParams := depositrule.SmartListParams{Filter: filter}
		listParams.Filter.Filters = append(listParams.Filter.Filters, depositrule.SmartListFilter{
			Property: "client_id",
			Value:    strconv.FormatInt(candidate.CustomerID, 10),
			Operator: "OPERATOR_EQUAL",
		})

		count, err := m.customerRepo.SmartListCountByFilter(ctx, &depositrule.SmartListByFilterRequest{
			CompanyID:        candidate.CompanyID,
			BusinessID:       candidate.BusinessID,
			ClientListParams: listParams,
		})
		if err != nil {
			return false
		}

		// 因为限定了 customer id 所以理论上只能是 0 或 1 个
		return count == 1
	}

	return false
}

// serviceMatcher matches nothing.
type serviceMatcher struct {
	filter *orderpb.DepositRuleServiceFilter
}

func (m *serviceMatcher) Match(_ context.Context, candidate *FilterCandidate) bool {
	for _, sbt := range m.filter.GetServicesByType() {
		if sbt.GetIsAll() {
			if sbt.GetServiceType() == candidate.Item.GetService().GetServiceItemType() {
				return true
			}

			continue
		}

		for _, serviceID := range sbt.GetServiceIds() {
			if serviceID == candidate.Item.GetService().GetId() {
				return true
			}
		}
	}

	return false
}

// dateRangeMatcher matches nothing.
type dateRangeMatcher struct {
	filter *orderpb.DepositRuleDateRangeFilter
}

func (m *dateRangeMatcher) Match(_ context.Context, candidate *FilterCandidate) bool {
	// Evaluation 等不支持的 service 类型，传过来的时间可能是空的，认为不匹配。
	if candidate.AppointmentStartDate.IsZero() {
		return false
	}

	st := m.filter.GetRange().GetStartTime().AsTime()
	et := m.filter.GetRange().GetEndTime().AsTime()

	return !candidate.AppointmentStartDate.Before(st) && candidate.AppointmentStartDate.Before(et)
}

// nothingMatcher matches nothing.
type nothingMatcher struct{}

func (m *nothingMatcher) Match(_ context.Context, _ *FilterCandidate) bool {
	return false
}

//// End: filter matchers
