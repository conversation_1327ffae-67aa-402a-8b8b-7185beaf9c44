package service

import (
	"context"
	"testing"

	"github.com/samber/lo"
	"github.com/shopspring/decimal"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"

	"github.com/MoeGolibrary/go-lib/common/proto/money"
	offeringpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/offering/v1"
	orderpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/order/v1"
	organizationpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/organization/v1"
	ordersvcpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/order/v2"
	depositrulemocks "github.com/MoeGolibrary/moego-svc-order-v2/internal/mocks/repo/depositrule"
	"github.com/MoeGolibrary/moego-svc-order-v2/internal/model"
)

func TestDepositRulesService_PreviewDepositOrder_AddonsIncluded(t *testing.T) {
	ctx := context.Background()
	depositRuleRepo := depositrulemocks.NewRepo(t)
	orgRepo := depositrulemocks.NewOrganizationRepo(t)
	srv := NewDepositRulesService(depositRuleRepo, orgRepo, nil, nil, nil)

	depositRuleRepo.EXPECT().ListByBusinessID(mock.Anything, int64(201)).
		Return(
			[]*model.DepositRule{
				{
					Name:              "All 80%",
					DepositCurrency:   "USD",
					DepositType:       orderpb.DepositAmountType_BY_PERCENTAGE,
					DepositPercentage: decimal.NewFromInt(80),
					Filters:           &orderpb.DepositFilters{},
				},
				{
					Name:            "All 70",
					DepositCurrency: "USD",
					DepositType:     orderpb.DepositAmountType_BY_FIXED_AMOUNT,
					DepositAmount:   decimal.NewFromInt(70),
					Filters:         &orderpb.DepositFilters{},
				},
			}, nil)
	orgRepo.EXPECT().GetCompanyPreference(mock.Anything, int64(101)).
		Return(&organizationpb.CompanyPreferenceSettingModel{
			CurrencyCode: "USD",
			TimeZone: &organizationpb.TimeZone{
				Name:    "America/Los_Angeles",
				Seconds: -3600 * 7,
			},
		}, nil)

	od, priceItems, err := srv.PreviewDepositOrder(ctx, &ordersvcpb.PreviewDepositOrderRequest{
		CompanyId:            101,
		BusinessId:           201,
		CustomerId:           lo.ToPtr(int64(301)),
		AppointmentStartDate: "2025-06-20",
		ServicePricingDetails: []*ordersvcpb.PreviewDepositOrderRequest_ServicePricingDetail{
			{
				PetId: 401,
				Service: &offeringpb.CustomizedServiceView{
					Id:              1011,
					ServiceItemType: offeringpb.ServiceItemType_GROOMING,
					Type:            offeringpb.ServiceType_ADDON,
				},
				TotalPrice:          money.FromDecimal(decimal.NewFromInt(30), "USD"),
				AssociatedServiceId: 1001,
			},
			{
				PetId: 401,
				Service: &offeringpb.CustomizedServiceView{
					Id:              1001,
					ServiceItemType: offeringpb.ServiceItemType_GROOMING,
					Type:            offeringpb.ServiceType_SERVICE,
				},
				TotalPrice: money.FromDecimal(decimal.NewFromInt(100), "USD"),
			},
			// 不同 pet 同一个 service
			{
				PetId: 402,
				Service: &offeringpb.CustomizedServiceView{
					Id:              1001,
					ServiceItemType: offeringpb.ServiceItemType_GROOMING,
					Type:            offeringpb.ServiceType_SERVICE,
				},
				TotalPrice: money.FromDecimal(decimal.NewFromInt(50), "USD"),
			},
			// 没有关联到 service 的 addon
			{
				PetId: 401,
				Service: &offeringpb.CustomizedServiceView{
					Id:              1012,
					ServiceItemType: offeringpb.ServiceItemType_GROOMING,
					Type:            offeringpb.ServiceType_ADDON,
				},
				TotalPrice:          money.FromDecimal(decimal.NewFromInt(20), "USD"),
				AssociatedServiceId: 0,
			},
		},
		IncludeConvenienceFee: false,
	})
	assert.NoError(t, err)
	assert.NotNil(t, priceItems)
	// 计算公式：(100 + 30) * 0.8 + 70
	expectedAmount := decimal.NewFromFloat(174.00)
	assert.True(t, expectedAmount.Equal(od.Order.TotalAmount), "Expected %s but got %s", expectedAmount.String(), od.Order.TotalAmount.String())
}
