package export

import (
	"bytes"
	"context"
	"encoding/csv"
	"fmt"

	excelize "github.com/xuri/excelize/v2"
	"go.uber.org/zap"

	"github.com/MoeGolibrary/go-lib/zlog"
)

// Tabler helps iterate over a table. It is the implementation's responsibility to ensure that when row and column
// indices are within the range, the functions like ColumnConfig always behave properly.
type Tabler interface {
	RowCount() int
	ColumnCount() int
	ColumnConfig(col int) ColumnConfig
	CellText(row, col int) string
}

type ColumnConfig struct {
	Key         string
	Name        string
	ColumnWidth float64
}

const (
	FileTypeXlsx = "xlsx"
	FileTypeCsv  = "csv"
)

// BuildTableExportContent builds the binary file content for specified file type filled with the table data from the
// tabler.
func BuildTableExportContent(ctx context.Context, tabler Tabler, fileType string) ([]byte, error) {
	f := excelize.NewFile()

	defer func() {
		if err := f.Close(); err != nil {
			zlog.Error(ctx, "close export xlsx file error", zap.Error(err))
		}
	}()

	const sheet = "Sheet1"

	sheetIndex, err := f.NewSheet(sheet)
	if err != nil {
		return nil, err
	}

	f.SetActiveSheet(sheetIndex)

	// Write header row
	for c := 0; c < tabler.ColumnCount(); c++ {
		cfg := tabler.ColumnConfig(c)

		if err := f.SetColWidth(sheet, indexToColName(c), indexToColName(c), cfg.ColumnWidth); err != nil {
			return nil, err
		}

		if err := f.SetCellValue(sheet, indexToCoordinate(0, c), cfg.Name); err != nil {
			return nil, err
		}
	}

	// Write data rows
	for r := 0; r < tabler.RowCount(); r++ {
		for c := 0; c < tabler.ColumnCount(); c++ {
			// Note: shift one row down below the header
			if err := f.SetCellValue(sheet, indexToCoordinate(r+1, c), tabler.CellText(r, c)); err != nil {
				return nil, err
			}
		}
	}

	switch fileType {
	case FileTypeXlsx:
		buf, err := f.WriteToBuffer()
		if err != nil {
			return nil, err
		}

		return buf.Bytes(), nil
	case FileTypeCsv:
		rows, err := f.GetRows(sheet)
		if err != nil {
			return nil, err
		}

		buf := new(bytes.Buffer)

		writer := csv.NewWriter(buf)
		if err := writer.WriteAll(rows); err != nil {
			return nil, err
		}

		return buf.Bytes(), nil
	default:
		return nil, fmt.Errorf("unsupported file type: %s", fileType)
	}
}

// indexToCoordinate converts zero-based index to Excel coordinate.
// For example, indexToCoordinate(0, 0) returns "A1".
// columnIndex should be less than 26.
func indexToCoordinate(rowIndex, columnIndex int) string {
	return fmt.Sprintf("%s%d", indexToColName(columnIndex), rowIndex+1)
}

func indexToColName(columnIndex int) string {
	return string(rune(columnIndex%26 + 65)) //nolint:mnd // ASCII A is 65
}
