package handler

import (
	"context"
	"strconv"

	"go.uber.org/zap"
	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/MoeGolibrary/go-lib/common/proto/money"
	"github.com/MoeGolibrary/go-lib/zlog"
	eventbuspb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/event_bus/v1"
	orderpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/order/v1"
	paymentpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/payment/v2"
	"github.com/MoeGolibrary/moego-svc-order-v2/internal/model"
	"github.com/MoeGolibrary/moego-svc-order-v2/internal/repo"
	"github.com/MoeGolibrary/moego-svc-order-v2/internal/service"
)

type PaymentEventHandler struct {
	refundOrderService     service.RefundOrderService
	orderPaymentRepo       repo.OrderPaymentRepo
	refundOrderPaymentRepo repo.RefundOrderPaymentRepo
	txRepo                 repo.TXRepo
	paymentClient          repo.PaymentClient
}

func NewPaymentEventHandler(
	refundOrderService service.RefundOrderService,
	orderPaymentRepo repo.OrderPaymentRepo,
	refundOrderPaymentRepo repo.RefundOrderPaymentRepo,
	txRepo repo.TXRepo,
	paymentClient repo.PaymentClient,
) *PaymentEventHandler {
	return &PaymentEventHandler{
		refundOrderService:     refundOrderService,
		orderPaymentRepo:       orderPaymentRepo,
		refundOrderPaymentRepo: refundOrderPaymentRepo,
		txRepo:                 txRepo,
		paymentClient:          paymentClient,
	}
}

func (h *PaymentEventHandler) Consume(
	ctx context.Context, eventID string, createTime *timestamppb.Timestamp,
	eventType eventbuspb.EventType, eventData *eventbuspb.EventData,
) error {
	consumeCtx := zlog.NewContext(
		ctx,
		zap.String("eventID", eventID),
		zap.Stringer("eventType", eventType),
	)

	zlog.FromContext(consumeCtx).Info(
		"consume payment event",
		zap.Stringer("createTime", createTime),
		zap.Stringer("eventData", eventData),
	)

	switch eventType {
	// 处理 payment 状态流转事件
	case eventbuspb.EventType_PAYMENT_STATUS_CHANGED:
		return h.handlePaymentStatusChange(consumeCtx, eventData)
	// 处理 refund 状态流转事件
	case eventbuspb.EventType_PAYMENT_REFUND_STATUS_CHANGED:
		return h.handleRefundStatusChange(consumeCtx, eventData)
	default:
		zlog.FromContext(consumeCtx).Warn("unknown event type, ignore", zap.Stringer("eventType", eventType))
		return nil
	}
}

func (h *PaymentEventHandler) handlePaymentStatusChange(ctx context.Context, eventData *eventbuspb.EventData) error {
	// TODO:(yunxiang): 把同步支付结果的逻辑迁移到 order_payment 的 service 去.
	paymentModel := eventData.GetPaymentEvent().GetPayment()
	// 只有对应的external type的payment才需要处理
	switch paymentModel.GetExternalType() {
	case paymentpb.ExternalType_ORDER_PAYMENT, paymentpb.ExternalType_ORDER:
	// 只有对应的external type的payment才需要处理
	default:
		zlog.Warn(ctx, "payment does not need to processed", zap.Stringer("payment", paymentModel))
		return nil
	}

	var opID, orderID int64

	// 根据payment的id查询出order payment
	id, err := strconv.ParseInt(paymentModel.GetExternalId(), 10, 64)
	if err != nil {
		zlog.Error(
			ctx, "parse external id error",
			zap.Stringer("externalType", paymentModel.GetExternalType()),
			zap.String("externalID", paymentModel.GetExternalId()), zap.Error(err),
		)

		return err
	}

	if paymentModel.GetExternalType() == paymentpb.ExternalType_ORDER_PAYMENT {
		opID = id
	} else {
		orderID = id
	}

	if opID > 0 {
		op, getOpErr := h.orderPaymentRepo.Get(ctx, id)
		if getOpErr != nil {
			zlog.Error(
				ctx, "get order payment error",
				zap.Int64("orderPaymentID", id), zap.Error(getOpErr),
			)

			return getOpErr
		}

		orderID = op.OrderID
	}

	if txErr := h.txRepo.Tx(
		func(tx repo.OrderTX) error {
			order := &model.Order{}
			// 支付成功时，需要更新订单，这里先锁，避免幻读.
			if paymentModel.GetStatus() == paymentpb.PaymentModel_AUTHORIZED ||
				paymentModel.GetStatus() == paymentpb.PaymentModel_SUCCEEDED {
				order, err = tx.Order().GetForUpdate(ctx, orderID)
				if err != nil {
					zlog.Error(
						ctx, "get order for update error",
						zap.Int64("orderID", orderID), zap.Error(err),
					)
					return err
				}
			}

			var orderPayment *model.OrderPayment
			if opID > 0 {
				// 锁order payment
				orderPayment, err = tx.OrderPayment().GetForUpdate(ctx, id)
				if err != nil {
					zlog.Error(
						ctx, "get order payment for update error", zap.Int64("orderPaymentID", id), zap.Error(err),
					)
					return err
				}

				// 如果已经是终态了就不需要再处理
				if orderPayment.IsFinalState() {
					zlog.Warn(ctx, "order payment is finished", zap.Any("orderPayment", orderPayment))
					return nil
				}
			}

			// 根据payment不同状态做不同操作
			switch paymentModel.GetStatus() {
			case paymentpb.PaymentModel_AUTHORIZED, paymentpb.PaymentModel_SUCCEEDED:
				// 支付成功的需要更新 order / 以及其他的line extra fee等
				return h.processPaymentSuccess(ctx, orderPayment, paymentModel, order, tx)
			case paymentpb.PaymentModel_CANCELLED:
				if orderPayment != nil {
					_, err := tx.OrderPayment().UpdatePaymentStatus(
						ctx, orderPayment.ID,
						orderpb.OrderPaymentStatus_ORDER_PAYMENT_STATUS_CANCELED,
						paymentModel.GetDisplayPaymentMethod(),
					)
					return err
				}
				return nil
			case paymentpb.PaymentModel_FAILED:
				if orderPayment != nil {
					_, err := tx.OrderPayment().UpdatePaymentStatus(ctx, orderPayment.ID,
						orderpb.OrderPaymentStatus_ORDER_PAYMENT_STATUS_FAILED,
						paymentModel.GetDisplayPaymentMethod(),
					)
					return err
				}
				return nil
			default:
				zlog.Warn(ctx, "payment does not need to processed", zap.Any("payment", paymentModel))
				return nil
			}
		},
	); txErr != nil {
		zlog.Error(ctx, "tx error", zap.Error(txErr))
		return txErr
	}

	return nil
}

func (h *PaymentEventHandler) processPaymentSuccess(
	ctx context.Context,
	orderPayment *model.OrderPayment,
	paymentModel *paymentpb.PaymentModel,
	order *model.Order,
	tx repo.OrderTX,
) error {
	// Order 状态已经是终态的情况下，需要cancel掉支付
	// TODO(yunxiang): Complete & Cancel Order 的时候把进行中的 Order Payment 都 Cancel.
	if order.IsFinalStatus() {
		zlog.Warn(
			ctx, "order is finished, cancel payment",
			zap.Int64("orderID", order.ID),
			zap.Int64("orderPaymentID", orderPayment.GetID()),
			zap.Int64("paymentID", paymentModel.GetId()),
		)

		// cancel order payment
		if _, err := h.orderPaymentRepo.CancelOrderPayment(ctx, orderPayment); err != nil {
			zlog.Error(
				ctx, "cancel order payment error",
				zap.Int64("orderPaymentID", orderPayment.GetID()), zap.Error(err),
			)

			return err
		}

		// cancel payment
		return h.paymentClient.CancelPayment(ctx, paymentModel.GetId())
	}

	// orderPayment 可能是空的
	if orderPayment != nil {
		// 更新支付成功的结果到 OrderPayment.
		orderPayment.AttachPaidPayment(paymentModel)

		if err := tx.OrderPayment().Save(ctx, orderPayment); err != nil {
			return err
		}
	} else {
		// 构建一个 orderPayment
		orderPayment = model.BuildByPayment(paymentModel)
	}

	// 更新支付结果到订单.
	order.AttachPaidOrderPayment(orderPayment)

	_, err := tx.Order().UpdateForPaymentSuccess(ctx, order, orderPayment)
	if err != nil {
		return err
	}

	// fully paid 直接标记为 completed, 发 completed 消息
	if order.IsCompleted() {
		if err := tx.MessageDeliveryRepo().CreateCompleted(ctx, order); err != nil {
			return err
		}
	}

	// 这里超付的时候打个日志.
	if order.IsOverPaid() {
		zlog.Warn(
			ctx, "order overpaid", zap.Int64("orderID", order.ID),
			zap.Stringer("currentRemainAmount", order.RemainAmount),
			zap.Stringer("paymentAmount", money.ToDecimal(paymentModel.GetAmount())),
		)
	}

	return nil
}

func (h *PaymentEventHandler) handleRefundStatusChange(ctx context.Context, eventData *eventbuspb.EventData) error {
	refundModel := eventData.GetPaymentRefundEvent().GetRefund()
	// 只有对应的 external type 的 refund 才需要处理
	if refundModel.ExternalType != paymentpb.ExternalType_REFUND_ORDER_PAYMENT {
		zlog.Warn(ctx, "refund does not need to processed", zap.Stringer("refund", refundModel))
		return nil
	}

	// 查询出 refund order payment
	id, err := strconv.ParseInt(refundModel.GetExternalId(), 10, 64)
	if err != nil {
		zlog.Error(
			ctx, "parse refund order payment id error",
			zap.String("externalID", refundModel.GetExternalId()), zap.Error(err),
		)

		return err
	}

	if err := h.refundOrderService.TrySyncRefundTransactionByRefundPaymentID(
		ctx, id, refundModel,
	); err != nil {
		zlog.Error(
			ctx, "sync refund transaction error", zap.Int64("refundOrderPaymentID", id),
			zap.Stringer("refundModel", refundModel), zap.Error(err),
		)

		return err
	}

	return nil
}
