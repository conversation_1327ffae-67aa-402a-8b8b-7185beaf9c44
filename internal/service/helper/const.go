package helper

import (
	"github.com/shopspring/decimal"
)

const (
	// AmountPrecision defines the count of decimal places used for rounding monetary amounts.
	AmountPrecision = 2

	// RatePrecision defines the count of decimal places used for rounding refund rates.
	RatePrecision = 6

	// CentsInOneDollar defines the number of cents in one dollar.
	CentsInOneDollar = 100
)

// minAmount 表示最小的金额.
var minAmount = decimal.NewFromInt(1).Shift(-AmountPrecision)
