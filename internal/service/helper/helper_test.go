package helper

import (
	"testing"

	"github.com/shopspring/decimal"
	"github.com/stretchr/testify/suite"

	"github.com/MoeGolibrary/go-lib/common/proto/money"
	orderpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/order/v1"
	ordersvcpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/order/v1"
	"github.com/MoeGolibrary/moego-svc-order-v2/internal/model"
)

type RefundHelperTestSuite struct {
	suite.Suite

	rh *RefundHelper
}

func TestRefundHelper(t *testing.T) {
	suite.Run(t, new(RefundHelperTestSuite))
}

func (ts *RefundHelperTestSuite) SetupTest() {
	ts.rh = &RefundHelper{
		od: &model.OrderDetail{
			Order: &model.Order{},
		},
	}
}

func (ts *RefundHelperTestSuite) TestCalculateTaxByRateAndMax_001() {
	expect := ts.mustDecimal("9.03") // Tax 按照四舍五入，最终得到 9.03.
	actual := ts.rh.calculateTaxByRateAndMax(
		ts.mustDecimal("100.00"),
		ts.mustDecimal("0.09025"), // 9.025%
		ts.mustDecimal("100000"),  // Big enough.
	)

	ts.Equal(expect.String(), actual.String())
}

func (ts *RefundHelperTestSuite) TestCalculateTaxByRateAndMax_002() {
	expect := ts.mustDecimal("5.01") // Tax 按照四舍五入，最终得到 5.01
	actual := ts.rh.calculateTaxByRateAndMax(
		ts.mustDecimal("130.00"),
		ts.mustDecimal("0.0385"), // 3.85%
		ts.mustDecimal("100000"), // Big enough.
	)

	ts.Equal(expect.String(), actual.String())
}

func (ts *RefundHelperTestSuite) TestCalculateByRateAndMax() {
	expect := ts.mustDecimal("9.02") // 非 Tax 按银行家算法舍入，最终得到 9.02.
	actual := ts.rh.calculateByRateAndMax(
		ts.mustDecimal("100.00"),
		ts.mustDecimal("0.09025"), // 9.025%
		ts.mustDecimal("100000"),  // Big enough.
	)

	ts.Equal(expect.String(), actual.String())
}

func (ts *RefundHelperTestSuite) mustDecimal(str string) decimal.Decimal {
	dec, err := decimal.NewFromString(str)

	ts.Require().NoError(err)

	return dec
}

func (ts *RefundHelperTestSuite) TestCalculateRefundPayment_HUAZI_001() {
	const paymentID = 1

	rh := RefundHelper{
		od: &model.OrderDetail{
			Order: &model.Order{},
			OrderPayments: []*model.OrderPayment{
				{
					ID:                     paymentID,
					TotalAmount:            ts.mustDecimal("81.25"),
					Amount:                 ts.mustDecimal("78.19"),
					RefundedAmount:         decimal.Zero,
					ProcessingFee:          ts.mustDecimal("3.06"),
					ConvenienceFee:         ts.mustDecimal("3.06"),
					RefundedConvenienceFee: decimal.Zero,
					PaymentStatus:          orderpb.OrderPaymentStatus_ORDER_PAYMENT_STATUS_PAID,
				},
			},
		},
	}

	rop, err := rh.PreviewRefundOrderPayments(
		[]*ordersvcpb.PreviewRefundOrderResponse_RefundableOrderPayment{
			{
				OrderPaymentId:   paymentID,
				RefundableAmount: money.FromDecimal(ts.mustDecimal("81.25"), ""),
			},
		},
		[]int64{paymentID},
		ts.mustDecimal("10.00"),
		&ordersvcpb.RefundOrderRequest_RefundByPayment_RefundAmountFlags{IsConvenienceFeeIncluded: true},
	)
	ts.NoError(err)
	ts.Len(rop, 1)
	ts.Equal(rop[0].GetRefundAmount().String(), ts.mustDecimal("10.00").String())
	ts.Equal(rop[0].GetRefundConvenienceFee().String(), ts.mustDecimal("0.38").String())
}

func (ts *RefundHelperTestSuite) TestCalculateRefundPayment_HUAZI_002() {
	const paymentID = 1

	rh := RefundHelper{
		od: &model.OrderDetail{
			Order: &model.Order{},
			OrderPayments: []*model.OrderPayment{
				{
					ID:                     paymentID,
					TotalAmount:            ts.mustDecimal("565.40"),
					Amount:                 ts.mustDecimal("545.88"),
					ConvenienceFee:         ts.mustDecimal("19.52"),
					RefundedAmount:         ts.mustDecimal("5.00"),
					RefundedConvenienceFee: ts.mustDecimal("0.17"),
					PaymentStatus:          orderpb.OrderPaymentStatus_ORDER_PAYMENT_STATUS_PAID,
				},
			},
		},
	}

	rop, err := rh.PreviewRefundOrderPayments(
		[]*ordersvcpb.PreviewRefundOrderResponse_RefundableOrderPayment{
			{
				OrderPaymentId:   paymentID,
				RefundableAmount: money.FromDecimal(ts.mustDecimal("560.40"), ""),
			},
		},
		[]int64{paymentID},
		ts.mustDecimal("560.40"),
		&ordersvcpb.RefundOrderRequest_RefundByPayment_RefundAmountFlags{IsConvenienceFeeIncluded: true},
	)
	ts.NoError(err)
	ts.Len(rop, 1)
	ts.Equal(rop[0].GetRefundAmount().String(), ts.mustDecimal("560.40").String())
	ts.Equal(rop[0].GetRefundConvenienceFee().String(), ts.mustDecimal("19.35").String())
}

func (ts *RefundHelperTestSuite) TestSplitRefundAmountToOrderPayment_001() {
	// 部分退.
	refundAmount := ts.mustDecimal("30.00")
	orderPayment := &model.OrderPayment{
		TotalAmount:            ts.mustDecimal("50.00"),
		Amount:                 ts.mustDecimal("40.00"),
		RefundedAmount:         decimal.Zero,
		ProcessingFee:          decimal.Zero,
		ConvenienceFee:         ts.mustDecimal("10.00"),
		RefundedConvenienceFee: decimal.Zero,
	}

	for isIncluded, expected := range map[bool]struct {
		rest decimal.Decimal
		rop  *model.RefundOrderPayment
	}{
		true: {
			rest: decimal.Zero,
			rop: &model.RefundOrderPayment{
				RefundAmount:         ts.mustDecimal("30.00"),
				RefundConvenienceFee: ts.mustDecimal("6.00"),
			},
		},
		false: {
			rest: decimal.Zero,
			rop: &model.RefundOrderPayment{
				RefundAmount:         ts.mustDecimal("37.50"),
				RefundConvenienceFee: ts.mustDecimal("7.50"),
			},
		},
	} {
		actualRest, actualRop := ts.rh.splitRefundAmountToOrderPayment(
			refundAmount,
			&ordersvcpb.RefundOrderRequest_RefundByPayment_RefundAmountFlags{IsConvenienceFeeIncluded: isIncluded},
			orderPayment,
		)

		ts.Equal(expected.rest.String(), actualRest.String())
		ts.Equal(expected.rop.RefundAmount.String(), actualRop.RefundAmount.String())
		ts.Equal(expected.rop.RefundConvenienceFee.String(), actualRop.RefundConvenienceFee.String())
	}
}

func (ts *RefundHelperTestSuite) TestSplitRefundAmountToOrderPayment_002() {
	// 全退
	refundAmount := ts.mustDecimal("100.00")
	orderPayment := &model.OrderPayment{
		TotalAmount:            ts.mustDecimal("50.00"),
		Amount:                 ts.mustDecimal("40.00"),
		RefundedAmount:         decimal.Zero,
		ProcessingFee:          decimal.Zero,
		ConvenienceFee:         ts.mustDecimal("10.00"),
		RefundedConvenienceFee: decimal.Zero,
	}

	for isIncluded, expected := range map[bool]struct {
		rest decimal.Decimal
		rop  *model.RefundOrderPayment
	}{
		true: {
			rest: ts.mustDecimal("50.00"),
			rop: &model.RefundOrderPayment{
				RefundAmount:         ts.mustDecimal("50.00"),
				RefundConvenienceFee: ts.mustDecimal("10.00"),
			},
		},
		false: {
			rest: ts.mustDecimal("60.00"),
			rop: &model.RefundOrderPayment{
				RefundAmount:         ts.mustDecimal("50.00"),
				RefundConvenienceFee: ts.mustDecimal("10.00"),
			},
		},
	} {
		actualRest, actualRop := ts.rh.splitRefundAmountToOrderPayment(
			refundAmount,
			&ordersvcpb.RefundOrderRequest_RefundByPayment_RefundAmountFlags{IsConvenienceFeeIncluded: isIncluded},
			orderPayment,
		)

		ts.Equal(expected.rest.String(), actualRest.String())
		ts.Equal(expected.rop.RefundAmount.String(), actualRop.RefundAmount.String())
		ts.Equal(expected.rop.RefundConvenienceFee.String(), actualRop.RefundConvenienceFee.String())
	}
}

func (ts *RefundHelperTestSuite) TestSplitRefundAmountToOrderPayment_003() {
	// 部分一笔已经部分退过的钱.
	refundAmount := ts.mustDecimal("20.00")
	orderPayment := &model.OrderPayment{
		TotalAmount:            ts.mustDecimal("50.00"),
		Amount:                 ts.mustDecimal("40.00"),
		RefundedAmount:         ts.mustDecimal("10.00"), // 已经退了 10 块钱.
		ProcessingFee:          decimal.Zero,
		ConvenienceFee:         ts.mustDecimal("10.00"),
		RefundedConvenienceFee: ts.mustDecimal("2.00"),
	}

	for isIncluded, expected := range map[bool]struct {
		rest decimal.Decimal
		rop  *model.RefundOrderPayment
	}{
		true: {
			rest: decimal.Zero,
			rop: &model.RefundOrderPayment{
				RefundAmount:         ts.mustDecimal("20.00"),
				RefundConvenienceFee: ts.mustDecimal("4.00"),
			},
		},
		false: {
			rest: decimal.Zero,
			rop: &model.RefundOrderPayment{
				RefundAmount:         ts.mustDecimal("25.00"),
				RefundConvenienceFee: ts.mustDecimal("5.00"),
			},
		},
	} {
		actualRest, actualRop := ts.rh.splitRefundAmountToOrderPayment(
			refundAmount,
			&ordersvcpb.RefundOrderRequest_RefundByPayment_RefundAmountFlags{IsConvenienceFeeIncluded: isIncluded},
			orderPayment,
		)

		ts.Equal(expected.rest.String(), actualRest.String())
		ts.Equal(expected.rop.RefundAmount.String(), actualRop.RefundAmount.String())
		ts.Equal(expected.rop.RefundConvenienceFee.String(), actualRop.RefundConvenienceFee.String())
	}
}

func (ts *RefundHelperTestSuite) TestSplitRefundAmountToOrderPayment_004() {
	// 全退一笔已经部分退过的钱.
	refundAmount := ts.mustDecimal("100.00")
	orderPayment := &model.OrderPayment{
		TotalAmount:            ts.mustDecimal("50.00"),
		Amount:                 ts.mustDecimal("40.00"),
		RefundedAmount:         ts.mustDecimal("10.00"), // 已经退了 10 块钱.
		ProcessingFee:          decimal.Zero,
		ConvenienceFee:         ts.mustDecimal("10.00"),
		RefundedConvenienceFee: ts.mustDecimal("2.00"),
	}

	for isIncluded, expected := range map[bool]struct {
		rest decimal.Decimal
		rop  *model.RefundOrderPayment
	}{
		true: {
			rest: ts.mustDecimal("60.00"),
			rop: &model.RefundOrderPayment{
				RefundAmount:         ts.mustDecimal("40.00"),
				RefundConvenienceFee: ts.mustDecimal("8.00"),
			},
		},
		false: {
			rest: ts.mustDecimal("68.00"),
			rop: &model.RefundOrderPayment{
				RefundAmount:         ts.mustDecimal("40.00"),
				RefundConvenienceFee: ts.mustDecimal("8.00"),
			},
		},
	} {
		actualRest, actualRop := ts.rh.splitRefundAmountToOrderPayment(
			refundAmount,
			&ordersvcpb.RefundOrderRequest_RefundByPayment_RefundAmountFlags{IsConvenienceFeeIncluded: isIncluded},
			orderPayment,
		)

		ts.Equal(expected.rest.String(), actualRest.String())
		ts.Equal(expected.rop.RefundAmount.String(), actualRop.RefundAmount.String())
		ts.Equal(expected.rop.RefundConvenienceFee.String(), actualRop.RefundConvenienceFee.String())
	}
}

func (ts *RefundHelperTestSuite) TestPreviewRefundOrderPayments_001() {
	ts.rh.od.OrderPayments = []*model.OrderPayment{
		{
			// 已经全退.
			ID:                     1,
			TotalAmount:            ts.mustDecimal("228.50"),
			Amount:                 ts.mustDecimal("220.00"),
			RefundedAmount:         ts.mustDecimal("228.50"),
			ProcessingFee:          decimal.Zero,
			ConvenienceFee:         ts.mustDecimal("8.50"),
			RefundedConvenienceFee: ts.mustDecimal("8.50"),
			PaymentStatus:          orderpb.OrderPaymentStatus_ORDER_PAYMENT_STATUS_PAID,
		},
		{
			// 无 CV Fee.
			ID:                     2,
			TotalAmount:            ts.mustDecimal("220.00"),
			Amount:                 ts.mustDecimal("220.00"),
			RefundedAmount:         ts.mustDecimal("0.00"),
			ProcessingFee:          decimal.Zero,
			ConvenienceFee:         ts.mustDecimal("0.00"),
			RefundedConvenienceFee: ts.mustDecimal("0.00"),
			PaymentStatus:          orderpb.OrderPaymentStatus_ORDER_PAYMENT_STATUS_PAID,
		},
		{
			// 部分退.
			ID:                     3,
			TotalAmount:            ts.mustDecimal("377.78"),
			Amount:                 ts.mustDecimal("364.64"),
			RefundedAmount:         ts.mustDecimal("317.72"),
			ProcessingFee:          decimal.Zero,
			ConvenienceFee:         ts.mustDecimal("13.14"),
			RefundedConvenienceFee: ts.mustDecimal("11.05"),
			PaymentStatus:          orderpb.OrderPaymentStatus_ORDER_PAYMENT_STATUS_PAID,
		},
	}

	expected := []*model.RefundOrderPayment{
		{
			OrderPaymentID:       3,
			RefundAmount:         ts.mustDecimal("60.06"),
			RefundConvenienceFee: ts.mustDecimal("2.09"),
		},
		{
			OrderPaymentID:       2,
			RefundAmount:         ts.mustDecimal("220.00"),
			RefundConvenienceFee: ts.mustDecimal("0.00"),
		},
	}

	refundAmount := ts.mustDecimal("277.97")
	refundAmountFlags := &ordersvcpb.RefundOrderRequest_RefundByPayment_RefundAmountFlags{IsConvenienceFeeIncluded: false}

	refundableOrderPayments, needCombination, err := ts.rh.BuildRefundablePayments(
		ts.rh.od.OrderPayments,
		refundAmount,
		refundAmountFlags,
	)
	ts.NoError(err)
	ts.True(needCombination)
	ts.Len(refundableOrderPayments, 2)

	actual, err := ts.rh.PreviewRefundOrderPayments(
		refundableOrderPayments, []int64{2, 3}, refundAmount, refundAmountFlags,
	)
	ts.Require().NoError(err)
	ts.Require().Len(actual, len(expected))

	for i := 0; i < len(expected); i++ {
		ts.Equal(expected[i].OrderPaymentID, actual[i].OrderPaymentID)
		ts.Equal(expected[i].RefundAmount.String(), actual[i].RefundAmount.String())
		ts.Equal(expected[i].RefundConvenienceFee.String(), actual[i].RefundConvenienceFee.String())
	}
}

// func (ts *RefundHelperTestSuite) TestBuildRefundablePayments() {
// 	const (
// 		methodCreditCardID = 1
// 		methodCashID       = 2
// 	)
//
// 	methodCreditCard := model.PaymentMethod{ID: methodCreditCardID}
// 	methodCash := model.PaymentMethod{ID: methodCashID}
//
// 	orderPayments := []*model.OrderPayment{
// 		{
// 			ID:                     1,
// 			PaymentMethod:          methodCash,
// 			TotalAmount:            ts.mustDecimal("100.00"),
// 			Amount:                 ts.mustDecimal("100.00"),
// 			ConvenienceFee:         ts.mustDecimal("0.00"),
// 			RefundedAmount:         ts.mustDecimal("0.00"),
// 			RefundedConvenienceFee: ts.mustDecimal("0.00"),
// 		},
// 		{
// 			ID:                     2,
// 			PaymentMethod:          methodCreditCard,
// 			TotalAmount:            ts.mustDecimal("50.00"),
// 			Amount:                 ts.mustDecimal("45.00"),
// 			ConvenienceFee:         ts.mustDecimal("5.00"),
// 			RefundedAmount:         ts.mustDecimal("0.00"),
// 			RefundedConvenienceFee: ts.mustDecimal("0.00"),
// 		},
// 		{
// 			ID:                     3,
// 			PaymentMethod:          methodCreditCard,
// 			TotalAmount:            ts.mustDecimal("50.00"),
// 			Amount:                 ts.mustDecimal("45.00"),
// 			ConvenienceFee:         ts.mustDecimal("5.00"),
// 			RefundedAmount:         ts.mustDecimal("10.00"),
// 			RefundedConvenienceFee: ts.mustDecimal("0.00"),
// 		},
// 		{
// 			ID:                     4,
// 			PaymentMethod:          methodCash,
// 			TotalAmount:            ts.mustDecimal("50.00"),
// 			Amount:                 ts.mustDecimal("50.00"),
// 			ConvenienceFee:         ts.mustDecimal("0.00"),
// 			RefundedAmount:         ts.mustDecimal("0.00"),
// 			RefundedConvenienceFee: ts.mustDecimal("0.00"),
// 		},
// 	}
//
// 	type input struct {
// 		RefundAmount             decimal.Decimal
// 		IsConvenienceFeeIncluded bool
// 	}
//
// 	type output struct {
// 		RefundableOrderPayments []*ordersvcpb.PreviewRefundOrderResponse_RefundableOrderPayment
// 		CanCombineOrderPayment  bool
// 		HasErr                  bool
// 	}
//
// 	for param, expected := range map[input]output{
// 		{RefundAmount: decimal.Zero}: {
// 			RefundableOrderPayments: []*ordersvcpb.PreviewRefundOrderResponse_RefundableOrderPayment{
// 				{
// 					OrderPaymentId:   3,
// 					RefundableAmount: money.FromDecimal(ts.mustDecimal("40.00"), ""),
// 				},
// 				{
// 					OrderPaymentId:   2,
// 					RefundableAmount: money.FromDecimal(ts.mustDecimal("50.00"), ""),
// 				},
// 				{
// 					OrderPaymentId:   4,
// 					RefundableAmount: money.FromDecimal(ts.mustDecimal("50.00"), ""),
// 				},
// 				{
// 					OrderPaymentId:   1,
// 					RefundableAmount: money.FromDecimal(ts.mustDecimal("100.00"), ""),
// 				},
// 			},
// 		},
// 	} {
//
// 	}
// }

func (ts *RefundHelperTestSuite) TestBuildRefundablePayments() {
	const methodCreditCardID = 1

	methodCreditCard := model.PaymentMethod{ID: methodCreditCardID}

	orderPayments := []*model.OrderPayment{
		{
			ID:                     1,
			PaymentMethod:          methodCreditCard,
			PaymentStatus:          orderpb.OrderPaymentStatus_ORDER_PAYMENT_STATUS_PAID,
			TotalAmount:            ts.mustDecimal("565.40"),
			Amount:                 ts.mustDecimal("545.88"),
			ConvenienceFee:         ts.mustDecimal("19.52"),
			RefundedAmount:         ts.mustDecimal("5.00"),
			RefundedConvenienceFee: ts.mustDecimal("0.17"),
		},
	}

	expectedRefundablePayments := []*ordersvcpb.PreviewRefundOrderResponse_RefundableOrderPayment{
		{
			OrderPaymentId:   1,
			RefundableAmount: money.FromDecimal(ts.mustDecimal("100.00"), ""),
		},
	}

	actualRefundablePayments, actualCanCombineOrderPayments, err := ts.rh.BuildRefundablePayments(
		orderPayments, ts.mustDecimal("560.4"),
		&ordersvcpb.RefundOrderRequest_RefundByPayment_RefundAmountFlags{IsConvenienceFeeIncluded: true},
	)
	ts.Require().NoError(err)
	ts.Require().False(actualCanCombineOrderPayments)
	ts.Require().Len(actualRefundablePayments, len(expectedRefundablePayments))
}

func (ts *RefundHelperTestSuite) TestGetRefundableAmount() {
	op := &model.OrderPayment{
		TotalAmount:            ts.mustDecimal("565.40"),
		Amount:                 ts.mustDecimal("545.88"),
		ConvenienceFee:         ts.mustDecimal("19.52"),
		RefundedAmount:         ts.mustDecimal("5.00"),
		RefundedConvenienceFee: ts.mustDecimal("0.17"),
	}

	ts.Equal(
		ts.mustDecimal("560.4").String(),
		ts.rh.getOrderPaymentRefundableAmount(
			op, &ordersvcpb.RefundOrderRequest_RefundByPayment_RefundAmountFlags{IsConvenienceFeeIncluded: true},
		).String(),
	)

	ts.Equal(
		ts.mustDecimal("541.05").String(),
		ts.rh.getOrderPaymentRefundableAmount(
			op, &ordersvcpb.RefundOrderRequest_RefundByPayment_RefundAmountFlags{IsConvenienceFeeIncluded: false},
		).String(),
	)
}
