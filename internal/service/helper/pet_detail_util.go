package helper

import (
	"context"

	"github.com/samber/lo"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	"github.com/MoeGolibrary/moego-svc-order-v2/internal/model"
	"github.com/MoeGolibrary/moego-svc-order-v2/internal/repo/grooming"
)

// FindPetDetailByOrderItem only for servcie item
func FindPetDetailByOrderItem(
	apptDetailList []*grooming.PetDetailDTO,
	orderItem *model.OrderItem,
) (*grooming.PetDetailDTO, error) {
	if !orderItem.IsService() {
		return nil, status.Error(codes.InvalidArgument, "not support edit the given order item")
	}

	for _, petDetail := range apptDetailList {
		// V4 开始引入了精确匹配.
		if orderItem.ExternalUUID != "" {
			if petDetail.ExternalUUID == orderItem.ExternalUUID {
				return petDetail, nil
			}

			continue
		}

		if petDetail.PetID == orderItem.PetID &&
			petDetail.ServiceID == orderItem.ObjectID {
			return petDetail, nil
		}
	}

	return nil, status.Error(codes.NotFound, "pet detail not found for the given order item")
}

func AttachStaff(
	ctx context.Context,
	groomingCli grooming.Client,
	order *model.Order,
	items []*model.OrderItem,
) error {
	petDetails, getErr := groomingCli.ListPetDetailsByGroomingID(ctx, order.BusinessID, order.SourceID)
	if getErr != nil {
		return getErr
	}

	for _, item := range items {
		petDetail, findErr := FindPetDetailByOrderItem(petDetails, item)
		if findErr != nil {
			continue
		}

		if len(petDetail.OperationList) > 0 {
			item.SetStaff(
				lo.Map(
					petDetail.OperationList,
					func(it *grooming.ServiceOperationDTO, _ int) int64 { return it.StaffID },
				)...,
			)
		} else {
			item.SetStaff(petDetail.StaffID)
		}
	}

	return nil
}
