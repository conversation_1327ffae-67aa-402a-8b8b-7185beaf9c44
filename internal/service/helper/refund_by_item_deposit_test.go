// Deposit refund by items 相关测试用例

package helper

import (
	"testing"

	"github.com/shopspring/decimal"
	"github.com/stretchr/testify/assert"

	"github.com/MoeGolibrary/go-lib/common/proto/money"
	orderpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/order/v1"
	ordersvcpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/order/v1"
	"github.com/MoeGolibrary/moego-svc-order-v2/internal/model"
)

// Simplest case: No discount, no tax, no tips, no convenience fee
func TestRefundHelper_RefundByItem_IncludeDepositDeductedItems_Simple(t *testing.T) {
	rh := NewRefundHelper(
		&model.OrderDetail{
			Order: &model.Order{
				ID:             1,
				Status:         orderpb.OrderStatus_COMPLETED,
				OrderVersion:   model.OrderVersionRefund,
				CurrencyCode:   "USD",
				TipsAmount:     decimal.Zero,
				TaxAmount:      decimal.Zero,
				DiscountAmount: decimal.Zero,
				ConvenienceFee: decimal.Zero,
				SubTotalAmount: mustDecimal("180.00"),
				TotalAmount:    mustDecimal("90.00"),
				PaidAmount:     mustDecimal("90.00"),
				RemainAmount:   decimal.Zero,
				RefundedAmount: decimal.Zero,
				DepositAmount:  mustDecimal("90.00"),
			},
			OrderItems: []*model.OrderItem{
				{
					ID:                     11,
					Tax:                    model.Tax{},
					Quantity:               1,
					UnitPrice:              mustDecimal("80.00"),
					TipsAmount:             decimal.Zero,
					DiscountAmount:         decimal.Zero,
					SubTotalAmount:         mustDecimal("80.00"),
					TotalAmount:            mustDecimal("80.00"),
					RefundedAmount:         decimal.Zero,
					RefundedTaxAmount:      decimal.Zero,
					RefundedDiscountAmount: decimal.Zero,
					DepositAmount:          mustDecimal("40.00"),
				},
				{
					ID:                     12,
					Tax:                    model.Tax{},
					Quantity:               1,
					UnitPrice:              mustDecimal("100.00"),
					TipsAmount:             decimal.Zero,
					DiscountAmount:         decimal.Zero,
					SubTotalAmount:         mustDecimal("100.00"),
					TotalAmount:            mustDecimal("100.00"),
					RefundedAmount:         decimal.Zero,
					RefundedTaxAmount:      decimal.Zero,
					RefundedDiscountAmount: decimal.Zero,
					DepositAmount:          mustDecimal("50.00"),
				},
			},
			OrderPayments: []*model.OrderPayment{
				{
					ID:                     21,
					TotalAmount:            mustDecimal("90.00"),
					Amount:                 mustDecimal("90.00"),
					RefundedAmount:         decimal.Zero,
					ProcessingFee:          decimal.Zero,
					ConvenienceFee:         decimal.Zero,
					RefundedConvenienceFee: decimal.Zero,
					PaymentStatus:          orderpb.OrderPaymentStatus_ORDER_PAYMENT_STATUS_PAID,
				},
			},
		},
		nil,
		buildDepositDetail(
			[]*model.DepositChangeLog{
				{
					DepositOrderID: 1,
					ChangeType:     orderpb.DepositChangeType_INCREASE,
					Reason:         orderpb.DepositChangeReason_TOP_UP,
					ChangedAmount:  mustDecimal("90.00"),
					Balance:        mustDecimal("90.00"),
					CurrencyCode:   "USD",
				},
			},
			[]*model.PriceItem{
				{
					ID:           1,
					OrderID:      1,
					OrderItemID:  11,
					Name:         "50%",
					UnitPrice:    mustDecimal("40.00"),
					CurrencyCode: "USD",
					Quantity:     1,
					Subtotal:     mustDecimal("40.00"),
					Operator:     orderpb.PriceDetailModel_PriceItem_ADD,
					ObjectType:   orderpb.ItemType_ITEM_TYPE_SERVICE,
					ObjectID:     1,
				},
				{
					ID:           2,
					OrderID:      2,
					OrderItemID:  12,
					Name:         "50%",
					UnitPrice:    mustDecimal("50.00"),
					CurrencyCode: "USD",
					Quantity:     1,
					Subtotal:     mustDecimal("50.00"),
					Operator:     orderpb.PriceDetailModel_PriceItem_ADD,
					ObjectType:   orderpb.ItemType_ITEM_TYPE_SERVICE,
					ObjectID:     2,
				},
			},
		),
	)

	actual, err := rh.RefundByItem(
		&ordersvcpb.RefundOrderRequest_RefundByItem{
			RefundItems: []*ordersvcpb.RefundOrderRequest_RefundByItem_RefundItem{
				{
					OrderItemId:    11,
					RefundItemMode: orderpb.RefundItemMode_REFUND_ITEM_MODE_BY_AMOUNT,
					RefundItemModeParam: &ordersvcpb.RefundOrderRequest_RefundByItem_RefundItem_RefundAmount{
						RefundAmount: money.FromDecimal(mustDecimal("60.00"), "USD"),
					},
				},
			},
		},
		[]*ordersvcpb.RefundOrderRequest_OrderPayment{{Id: 21}},
		false,
	)

	expectedRO := &model.RefundOrder{
		RefundMode:                orderpb.RefundMode_REFUND_MODE_BY_ITEM,
		CurrencyCode:              "USD",
		RefundTotalAmount:         mustDecimal("30.00"),
		RefundItemSubTotal:        mustDecimal("60.00"),
		RefundDiscountAmount:      decimal.Zero,
		RefundTipsAmount:          decimal.Zero,
		RefundConvenienceFee:      decimal.Zero,
		RefundTaxAmount:           decimal.Zero,
		RefundDepositAmount:       mustDecimal("30.00"),
		RefundDepositToTipsAmount: decimal.Zero,
	}
	expectedROIs := []*model.RefundOrderItem{
		{
			RefundItemMode:       orderpb.RefundItemMode_REFUND_ITEM_MODE_BY_AMOUNT,
			RefundTax:            model.RefundTax{},
			RefundTotalAmount:    mustDecimal("60.00"),
			RefundQuantity:       0,
			RefundAmount:         mustDecimal("60.00"),
			RefundDiscountAmount: decimal.Zero,
			RefundDepositAmount:  mustDecimal("30.00"),
		},
	}
	expectedROPs := []*model.RefundOrderPayment{
		{
			OrderPaymentID:       21,
			CurrencyCode:         "",
			RefundAmount:         mustDecimal("30.00"),
			RefundConvenienceFee: mustDecimal("0.00"),
		},
	}

	ast := assert.New(t)
	assert.NoError(t, err)
	assert.False(t, actual.IsFullyRefund)
	equalRefundOrder(ast, expectedRO, actual.RefundOrderDetail.RefundOrder)
	assert.Len(t, actual.RefundOrderDetail.RefundOrderItems, 1)
	equalRefundOrderItem(ast, expectedROIs[0], actual.RefundOrderDetail.RefundOrderItems[0])
	assert.Len(t, actual.RefundOrderDetail.RefundOrderPayments, 1)
	equalRefundOrderPayment(ast, expectedROPs[0], actual.RefundOrderDetail.RefundOrderPayments[0])
}

// Complex case:
// 1. Refund $38 of item A ($80) which has deposit (30%), discount (5%), tax (10%);
// 2. Refund $50 of item B ($100) which has no deposit;
// 3. The Order has a tips ($10);
// 4. The payment has convenience fee.
func TestRefundHelper_RefundByItem_IncludeDepositDeductedItems_ComplexAndSomeItemNoDeposit(t *testing.T) {
	rh := NewRefundHelper(
		&model.OrderDetail{
			Order: &model.Order{
				ID:             1,
				Status:         orderpb.OrderStatus_COMPLETED,
				OrderVersion:   model.OrderVersionRefund,
				CurrencyCode:   "USD",
				TipsAmount:     mustDecimal("10.00"),
				TaxAmount:      mustDecimal("7.60"),
				DiscountAmount: mustDecimal("4.00"),
				ConvenienceFee: mustDecimal("6.28"),
				SubTotalAmount: mustDecimal("180.00"),
				TotalAmount:    mustDecimal("175.88"),
				PaidAmount:     mustDecimal("175.88"),
				RemainAmount:   decimal.Zero,
				RefundedAmount: decimal.Zero,
				DepositAmount:  mustDecimal("24.00"),
			},
			OrderItems: []*model.OrderItem{
				{
					ID: 11,
					Tax: model.Tax{
						ID:     1,
						Name:   "Consumption Tax",
						Rate:   mustDecimal("10"),
						Amount: mustDecimal("7.60"),
					},
					Quantity:               1,
					UnitPrice:              mustDecimal("80.00"),
					TipsAmount:             decimal.Zero,
					DiscountAmount:         mustDecimal("4.00"),
					SubTotalAmount:         mustDecimal("80.00"),
					TotalAmount:            mustDecimal("76.00"),
					RefundedAmount:         decimal.Zero,
					RefundedTaxAmount:      decimal.Zero,
					RefundedDiscountAmount: decimal.Zero,
					DepositAmount:          mustDecimal("24.00"),
				},
				{
					ID:                     12,
					Tax:                    model.Tax{},
					Quantity:               1,
					UnitPrice:              mustDecimal("100.00"),
					TipsAmount:             decimal.Zero,
					DiscountAmount:         decimal.Zero,
					SubTotalAmount:         mustDecimal("100.00"),
					TotalAmount:            mustDecimal("100.00"),
					RefundedAmount:         decimal.Zero,
					RefundedTaxAmount:      decimal.Zero,
					RefundedDiscountAmount: decimal.Zero,
					DepositAmount:          decimal.Zero,
				},
			},
			OrderPayments: []*model.OrderPayment{
				{
					ID:                     21,
					TotalAmount:            mustDecimal("175.88"),
					Amount:                 mustDecimal("175.88"),
					RefundedAmount:         decimal.Zero,
					ProcessingFee:          decimal.Zero,
					ConvenienceFee:         mustDecimal("6.28"),
					RefundedConvenienceFee: decimal.Zero,
					PaymentStatus:          orderpb.OrderPaymentStatus_ORDER_PAYMENT_STATUS_PAID,
				},
			},
		},
		nil,
		buildDepositDetail(
			[]*model.DepositChangeLog{
				{
					DepositOrderID: 1,
					ChangeType:     orderpb.DepositChangeType_INCREASE,
					Reason:         orderpb.DepositChangeReason_TOP_UP,
					ChangedAmount:  mustDecimal("24.00"),
					Balance:        mustDecimal("24.00"),
					CurrencyCode:   "USD",
				},
			},
			[]*model.PriceItem{
				{
					ID:           1,
					OrderID:      1,
					OrderItemID:  11,
					Name:         "30%",
					UnitPrice:    mustDecimal("24.00"),
					CurrencyCode: "USD",
					Quantity:     1,
					Subtotal:     mustDecimal("24.00"),
					Operator:     orderpb.PriceDetailModel_PriceItem_ADD,
					ObjectType:   orderpb.ItemType_ITEM_TYPE_SERVICE,
					ObjectID:     1,
				},
			},
		),
	)

	actual, err := rh.RefundByItem(
		&ordersvcpb.RefundOrderRequest_RefundByItem{
			RefundItems: []*ordersvcpb.RefundOrderRequest_RefundByItem_RefundItem{
				{
					OrderItemId:    11,
					RefundItemMode: orderpb.RefundItemMode_REFUND_ITEM_MODE_BY_AMOUNT,
					RefundItemModeParam: &ordersvcpb.RefundOrderRequest_RefundByItem_RefundItem_RefundAmount{
						RefundAmount: money.FromDecimal(mustDecimal("38.00"), "USD"),
					},
				},
				{
					OrderItemId:    12,
					RefundItemMode: orderpb.RefundItemMode_REFUND_ITEM_MODE_BY_AMOUNT,
					RefundItemModeParam: &ordersvcpb.RefundOrderRequest_RefundByItem_RefundItem_RefundAmount{
						RefundAmount: money.FromDecimal(mustDecimal("50.00"), "USD"),
					},
				},
			},
		},
		[]*ordersvcpb.RefundOrderRequest_OrderPayment{{Id: 21}},
		false,
	)

	expectedRO := &model.RefundOrder{
		RefundMode:                orderpb.RefundMode_REFUND_MODE_BY_ITEM,
		CurrencyCode:              "USD",
		RefundTotalAmount:         mustDecimal("82.75"),
		RefundItemSubTotal:        mustDecimal("90.00"),
		RefundDiscountAmount:      mustDecimal("2.00"),
		RefundTipsAmount:          decimal.Zero,
		RefundConvenienceFee:      mustDecimal("2.95"),
		RefundTaxAmount:           mustDecimal("3.80"),
		RefundDepositAmount:       mustDecimal("12.00"),
		RefundDepositToTipsAmount: decimal.Zero,
	}
	expectedROIs := []*model.RefundOrderItem{
		{
			RefundItemMode: orderpb.RefundItemMode_REFUND_ITEM_MODE_BY_AMOUNT,
			RefundTax: model.RefundTax{
				ID:     1,
				Name:   "Consumption Tax",
				Rate:   mustDecimal("10"),
				Amount: mustDecimal("3.80"),
			},
			RefundTotalAmount:    mustDecimal("38.00"),
			RefundQuantity:       0,
			RefundAmount:         mustDecimal("40.00"),
			RefundDiscountAmount: mustDecimal("2.00"),
			RefundDepositAmount:  mustDecimal("12.00"),
		},
		{
			RefundItemMode:       orderpb.RefundItemMode_REFUND_ITEM_MODE_BY_AMOUNT,
			RefundTax:            model.RefundTax{},
			RefundTotalAmount:    mustDecimal("50.00"),
			RefundQuantity:       0,
			RefundAmount:         mustDecimal("50.00"),
			RefundDiscountAmount: decimal.Zero,
			RefundDepositAmount:  decimal.Zero,
		},
	}
	expectedROPs := []*model.RefundOrderPayment{
		{
			OrderPaymentID:       21,
			CurrencyCode:         "",
			RefundAmount:         mustDecimal("82.75"),
			RefundConvenienceFee: mustDecimal("2.95"),
		},
	}

	ast := assert.New(t)
	assert.NoError(t, err)
	assert.False(t, actual.IsFullyRefund)
	equalRefundOrder(ast, expectedRO, actual.RefundOrderDetail.RefundOrder)
	assert.Len(t, actual.RefundOrderDetail.RefundOrderItems, 2)
	equalRefundOrderItem(ast, expectedROIs[0], actual.RefundOrderDetail.RefundOrderItems[0])
	equalRefundOrderItem(ast, expectedROIs[1], actual.RefundOrderDetail.RefundOrderItems[1])
	assert.Len(t, actual.RefundOrderDetail.RefundOrderPayments, 1)
	equalRefundOrderPayment(ast, expectedROPs[0], actual.RefundOrderDetail.RefundOrderPayments[0])
}

// Complex case (refund the rest):
// 1. The Order has a tips ($10);
// 2. The payment has convenience fee.
// Already refunded:
// 1. Refund $38 of item A ($80) which has deposit (30%), discount (5%), tax (10%);
// 2. Refund $50 of item B ($100) which has no deposit;
// Now refund the rest:
// 1. Refund the rest $38 of item A;
// 2. Refund the rest $50 of item B;
// 3. Refund the tips;
// 4. Refund the rest of convenience fee.
func TestRefundHelper_RefundByItem_IncludeDepositDeductedItems_ComplexAndSomeItemNoDeposit_RefundTheRest(t *testing.T) {
	rh := NewRefundHelper(
		&model.OrderDetail{
			Order: &model.Order{
				ID:             1,
				Status:         orderpb.OrderStatus_COMPLETED,
				OrderVersion:   model.OrderVersionRefund,
				CurrencyCode:   "USD",
				TipsAmount:     mustDecimal("10.00"),
				TaxAmount:      mustDecimal("7.60"),
				DiscountAmount: mustDecimal("4.00"),
				ConvenienceFee: mustDecimal("6.28"),
				SubTotalAmount: mustDecimal("180.00"),
				TotalAmount:    mustDecimal("175.88"),
				PaidAmount:     mustDecimal("175.88"),
				RemainAmount:   decimal.Zero,
				RefundedAmount: mustDecimal("82.75"),
				DepositAmount:  mustDecimal("24.00"),
			},
			OrderItems: []*model.OrderItem{
				{
					ID: 11,
					Tax: model.Tax{
						ID:     1,
						Name:   "Consumption Tax",
						Rate:   mustDecimal("10"),
						Amount: mustDecimal("7.60"),
					},
					Quantity:               1,
					UnitPrice:              mustDecimal("80.00"),
					TipsAmount:             decimal.Zero,
					DiscountAmount:         mustDecimal("4.00"),
					SubTotalAmount:         mustDecimal("80.00"),
					TotalAmount:            mustDecimal("76.00"),
					RefundedAmount:         mustDecimal("38.00"),
					RefundedTaxAmount:      mustDecimal("3.80"),
					RefundedDiscountAmount: mustDecimal("2.00"),
					DepositAmount:          mustDecimal("24.00"),
					RefundedDepositAmount:  mustDecimal("12.00"),
				},
				{
					ID:                     12,
					Tax:                    model.Tax{},
					Quantity:               1,
					UnitPrice:              mustDecimal("100.00"),
					TipsAmount:             decimal.Zero,
					DiscountAmount:         decimal.Zero,
					SubTotalAmount:         mustDecimal("100.00"),
					TotalAmount:            mustDecimal("100.00"),
					RefundedAmount:         mustDecimal("50.00"),
					RefundedTaxAmount:      decimal.Zero,
					RefundedDiscountAmount: decimal.Zero,
					DepositAmount:          decimal.Zero,
				},
			},
			OrderPayments: []*model.OrderPayment{
				{
					ID:                     21,
					TotalAmount:            mustDecimal("175.88"),
					Amount:                 mustDecimal("175.88"),
					RefundedAmount:         decimal.Zero,
					ProcessingFee:          decimal.Zero,
					ConvenienceFee:         mustDecimal("6.28"),
					RefundedConvenienceFee: decimal.Zero,
					PaymentStatus:          orderpb.OrderPaymentStatus_ORDER_PAYMENT_STATUS_PAID,
				},
			},
		},
		nil,
		buildDepositDetail(
			[]*model.DepositChangeLog{
				{
					DepositOrderID: 1,
					ChangeType:     orderpb.DepositChangeType_INCREASE,
					Reason:         orderpb.DepositChangeReason_TOP_UP,
					ChangedAmount:  mustDecimal("24.00"),
					Balance:        mustDecimal("24.00"),
					CurrencyCode:   "USD",
				},
			},
			[]*model.PriceItem{
				{
					ID:           1,
					OrderID:      1,
					OrderItemID:  11,
					Name:         "30%",
					UnitPrice:    mustDecimal("24.00"),
					CurrencyCode: "USD",
					Quantity:     1,
					Subtotal:     mustDecimal("24.00"),
					Operator:     orderpb.PriceDetailModel_PriceItem_ADD,
					ObjectType:   orderpb.ItemType_ITEM_TYPE_SERVICE,
					ObjectID:     1,
				},
			},
		),
	)

	actual, err := rh.RefundByItem(
		&ordersvcpb.RefundOrderRequest_RefundByItem{
			RefundItems: []*ordersvcpb.RefundOrderRequest_RefundByItem_RefundItem{
				{
					OrderItemId:    11,
					RefundItemMode: orderpb.RefundItemMode_REFUND_ITEM_MODE_BY_AMOUNT,
					RefundItemModeParam: &ordersvcpb.RefundOrderRequest_RefundByItem_RefundItem_RefundAmount{
						RefundAmount: money.FromDecimal(mustDecimal("38.00"), "USD"),
					},
				},
				{
					OrderItemId:    12,
					RefundItemMode: orderpb.RefundItemMode_REFUND_ITEM_MODE_BY_AMOUNT,
					RefundItemModeParam: &ordersvcpb.RefundOrderRequest_RefundByItem_RefundItem_RefundAmount{
						RefundAmount: money.FromDecimal(mustDecimal("50.00"), "USD"),
					},
				},
			},
		},
		[]*ordersvcpb.RefundOrderRequest_OrderPayment{{Id: 21}},
		false,
	)

	expectedRO := &model.RefundOrder{
		RefundMode:                orderpb.RefundMode_REFUND_MODE_BY_ITEM,
		CurrencyCode:              "USD",
		RefundTotalAmount:         mustDecimal("93.13"),
		RefundItemSubTotal:        mustDecimal("90.00"),
		RefundDiscountAmount:      mustDecimal("2.00"),
		RefundTipsAmount:          mustDecimal("10.00"),
		RefundConvenienceFee:      mustDecimal("3.33"),
		RefundTaxAmount:           mustDecimal("3.80"),
		RefundDepositAmount:       mustDecimal("12.00"),
		RefundDepositToTipsAmount: decimal.Zero,
	}
	expectedROIs := []*model.RefundOrderItem{
		{
			RefundItemMode: orderpb.RefundItemMode_REFUND_ITEM_MODE_BY_AMOUNT,
			RefundTax: model.RefundTax{
				ID:     1,
				Name:   "Consumption Tax",
				Rate:   mustDecimal("10"),
				Amount: mustDecimal("3.80"),
			},
			RefundTotalAmount:    mustDecimal("38.00"),
			RefundQuantity:       0,
			RefundAmount:         mustDecimal("40.00"),
			RefundDiscountAmount: mustDecimal("2.00"),
			RefundDepositAmount:  mustDecimal("12.00"),
		},
		{
			RefundItemMode:       orderpb.RefundItemMode_REFUND_ITEM_MODE_BY_AMOUNT,
			RefundTax:            model.RefundTax{},
			RefundTotalAmount:    mustDecimal("50.00"),
			RefundQuantity:       0,
			RefundAmount:         mustDecimal("50.00"),
			RefundDiscountAmount: decimal.Zero,
			RefundDepositAmount:  decimal.Zero,
		},
	}
	expectedROPs := []*model.RefundOrderPayment{
		{
			OrderPaymentID:       21,
			CurrencyCode:         "",
			RefundAmount:         mustDecimal("93.13"),
			RefundConvenienceFee: mustDecimal("3.33"),
		},
	}

	ast := assert.New(t)
	assert.NoError(t, err)
	assert.True(t, actual.IsFullyRefund)
	equalRefundOrder(ast, expectedRO, actual.RefundOrderDetail.RefundOrder)
	assert.Len(t, actual.RefundOrderDetail.RefundOrderItems, 2)
	equalRefundOrderItem(ast, expectedROIs[0], actual.RefundOrderDetail.RefundOrderItems[0])
	equalRefundOrderItem(ast, expectedROIs[1], actual.RefundOrderDetail.RefundOrderItems[1])
	assert.Len(t, actual.RefundOrderDetail.RefundOrderPayments, 1)
	equalRefundOrderPayment(ast, expectedROPs[0], actual.RefundOrderDetail.RefundOrderPayments[0])
}
