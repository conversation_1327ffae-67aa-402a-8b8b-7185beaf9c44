package helper

import (
	"sort"

	"github.com/samber/lo"
	"github.com/shopspring/decimal"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	"github.com/MoeGolibrary/go-lib/common/proto/money"
	orderpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/order/v1"
	ordersvcpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/order/v1"
	"github.com/MoeGolibrary/moego-svc-order-v2/internal/model"
)

type RefundByPaymentResult struct {
	RefundOrderDetail *model.RefundOrderDetail
	IsFullyRefund     bool

	RefundableOrderPayments []*ordersvcpb.PreviewRefundOrderResponse_RefundableOrderPayment
	CanCombineOrderPayments bool

	RefundableTips           decimal.Decimal
	RefundableConvenienceFee decimal.Decimal
	IsConvenienceFeeOptional bool
}

func (res *RefundByPaymentResult) GetRefundOrderDetail() *model.RefundOrderDetail {
	if res == nil {
		return nil
	}

	return res.RefundOrderDetail
}

func (rh *RefundHelper) RefundByPayment(
	paymentIDs []int64,
	amount decimal.Decimal,
	flags *ordersvcpb.RefundOrderRequest_RefundByPayment_RefundAmountFlags,
) (*RefundByPaymentResult, error) {
	// 检查订单能否使用 Refund By Payment 模式.
	if err := rh.validateForRefundByPayment(); err != nil {
		return nil, err
	}

	refundDepositByItem := rh.checkRefundDepositByItem()

	if flags.GetIsDepositAmount() {
		// 退到 deposit_amount 里，走另外的逻辑
		return rh.refundDepositAmountByPayment(amount, flags, refundDepositByItem)
	}

	// 通过订单的 PaidAmount 快速检查是否能足够退钱.
	if rh.od.Order.GetRefundableAmount().LessThan(amount) {
		return nil, status.Error(codes.InvalidArgument, "refundable amount is not enough")
	}

	// 计算能满足退 Amount 金额条件的 Payments.
	refundableOrderPayment, canCombineOrderPayments, err := rh.BuildRefundablePayments(
		rh.od.OrderPayments, amount, flags,
	)
	if err != nil {
		return nil, err
	}

	// 计算预览的 RefundOrderDetail
	refundOrderPayments, err := rh.PreviewRefundOrderPayments(
		refundableOrderPayment, paymentIDs, amount, flags,
	)
	if err != nil {
		return nil, err
	}

	// 检查是否全退完
	isPaymentFullyRefund := true

	for _, op := range rh.applyRefundOrderPayments(refundOrderPayments) {
		if op.GetRefundableAmount().GreaterThan(decimal.Zero) {
			isPaymentFullyRefund = false
			break
		}
	}

	refundedDeposit := decimal.Zero
	for _, rod := range rh.rods {
		refundedDeposit = refundedDeposit.Add(rod.RefundOrder.RefundDepositAmount)
	}

	refundableDeposit := rh.od.Order.DepositAmount.Sub(refundedDeposit)

	// 当 deposit 已经全部退完，尾款金剩余的也全退时，才进行轧差逻辑.
	isFullyRefund := refundableDeposit.IsZero() && isPaymentFullyRefund

	rod := &model.RefundOrderDetail{
		RefundOrder:         rh.initRefundOrder(),
		RefundOrderItems:    nil, // 下面分摊金额的时候填入.
		RefundOrderPayments: refundOrderPayments,
	}

	rod.RefundOrder.RefundMode = orderpb.RefundMode_REFUND_MODE_BY_PAYMENT

	if isFullyRefund {
		// 全退按照轧差逻辑处理
		err = rh.refundByPaymentFully(rod, flags, refundDepositByItem)
	} else {
		// 否则按比例处理
		err = rh.refundByPaymentPartially(rod, amount, flags, refundDepositByItem)
	}

	if err != nil {
		return nil, err
	}

	refundDepositDetail, err := rh.GetRefundDepositDetail(rh.od, rod.RefundOrderItems, false)
	if err != nil {
		return nil, status.Errorf(codes.InvalidArgument, "cannot refund deposit: %v", err)
	}

	rod.ReversalDepositChangeLog = refundDepositDetail.ReversalChangeLog

	return &RefundByPaymentResult{
		RefundOrderDetail:       rod,
		IsFullyRefund:           isFullyRefund,
		RefundableOrderPayments: refundableOrderPayment,
		CanCombineOrderPayments: canCombineOrderPayments,
		// By Payment 时金额是倒算分摊的.
		RefundableTips:           rod.RefundOrder.GetRefundTipsAmount(),
		RefundableConvenienceFee: rod.RefundOrder.GetRefundConvenienceFee(),
		// By Payment 必须退.
		IsConvenienceFeeOptional: false,
	}, nil
}

func (rh *RefundHelper) refundDepositAmountByPayment(
	amount decimal.Decimal,
	flags *ordersvcpb.RefundOrderRequest_RefundByPayment_RefundAmountFlags,
	refundDepositByItem bool,
) (*RefundByPaymentResult, error) {
	refundedDeposit := decimal.Zero
	refundedPaid := decimal.Zero

	for _, rod := range rh.rods {
		refundedDeposit = refundedDeposit.Add(rod.RefundOrder.RefundDepositAmount)
		refundedPaid = refundedPaid.Add(rod.RefundOrder.GetRefundTotalAmount())
	}

	refundableDeposit := rh.od.Order.DepositAmount.Sub(refundedDeposit)
	refundablePaid := rh.od.Order.GetPaidAmount().Sub(refundedPaid)

	// 检查金额够不够退
	if refundableDeposit.LessThan(amount) {
		return nil, status.Error(codes.InvalidArgument, "refundable deposit amount is not enough")
	}

	// 尾款金全部退完、申请退剩余全部订金，才走全退轧差逻辑。
	isFullyRefund := refundableDeposit.Equal(amount) && refundablePaid.IsZero()

	rod := &model.RefundOrderDetail{
		RefundOrder:      rh.initRefundOrder(),
		RefundOrderItems: nil, // 下面分摊金额的时候填入.
	}

	rod.RefundOrder.RefundMode = orderpb.RefundMode_REFUND_MODE_BY_PAYMENT

	var err error

	if isFullyRefund {
		// 全退按照轧差逻辑处理
		err = rh.refundByPaymentFully(rod, flags, refundDepositByItem)
	} else {
		// 否则按比例处理
		err = rh.refundByPaymentPartially(rod, amount, flags, refundDepositByItem)
	}

	if err != nil {
		return nil, err
	}

	return &RefundByPaymentResult{
		RefundOrderDetail: rod,
		IsFullyRefund:     isFullyRefund,
		// By Payment 时金额是倒算分摊的.
		RefundableTips:           rod.RefundOrder.GetRefundTipsAmount(),
		RefundableConvenienceFee: rod.RefundOrder.GetRefundConvenienceFee(),
		// By Payment 必须退.
		IsConvenienceFeeOptional: false,
	}, nil
}

func (rh *RefundHelper) refundByPaymentFully(
	rod *model.RefundOrderDetail,
	flags *ordersvcpb.RefundOrderRequest_RefundByPayment_RefundAmountFlags,
	refundDepositByItem bool,
) error {
	od := rh.od.Order

	if !rod.IsCreatedAfterCompleted() {
		// 关单前只处理 DepositAmount, PaidAmount 和 ConvenienceFee
		rod.RefundOrder.RefundDepositAmount = od.GetDepositAmount()
		rod.RefundOrder.RefundTotalAmount = od.GetPaidAmount()
		rod.RefundOrder.RefundConvenienceFee = od.GetConvenienceFee()

		return nil
	}

	// 考虑到订单里面有 Partial Pay 强制关单的，这里需要用 PaidAmount.
	paidAmount := od.GetPaidAmount()
	subTotalAmount := od.GetSubTotalAmount()
	discountAmount := od.GetDiscountAmount()
	depositAmount := od.DepositAmount
	tipsAmount := od.GetTipsAmount()
	convenienceFee := od.GetConvenienceFee()
	taxAmount := od.GetTaxAmount()

	// 轧 RefundOrder.
	for _, refunded := range rh.rods {
		// 关单前只轧关单前的记录，关单后也只需要轧关单后的记录
		if rod.IsCreatedAfterCompleted() && !refunded.IsCreatedAfterCompleted() {
			continue
		}

		paidAmount = paidAmount.Sub(refunded.RefundOrder.GetRefundTotalAmount())
		subTotalAmount = subTotalAmount.Sub(refunded.RefundOrder.GetRefundItemSubTotal())
		discountAmount = discountAmount.Sub(refunded.RefundOrder.GetRefundDiscountAmount())
		depositAmount = depositAmount.Sub(refunded.RefundOrder.RefundDepositAmount)
		tipsAmount = tipsAmount.Sub(refunded.RefundOrder.GetRefundTipsAmount())
		convenienceFee = convenienceFee.Sub(refunded.RefundOrder.GetRefundConvenienceFee())
		taxAmount = taxAmount.Sub(refunded.RefundOrder.GetRefundTaxAmount())
	}

	rod.RefundOrder.RefundItemSubTotal = subTotalAmount
	rod.RefundOrder.RefundDiscountAmount = discountAmount
	rod.RefundOrder.RefundTipsAmount = tipsAmount
	rod.RefundOrder.RefundConvenienceFee = convenienceFee
	rod.RefundOrder.RefundTaxAmount = taxAmount

	// 全退的只能是订金或者尾款金，不能两者一起全退
	if flags.GetIsDepositAmount() {
		rod.RefundOrder.RefundDepositAmount = depositAmount
	} else {
		rod.RefundOrder.RefundTotalAmount = paidAmount
	}

	// 轧 RefundOrderItems.
	rod.RefundOrderItems = make([]*model.RefundOrderItem, 0, len(rh.od.OrderItems))

	for _, orderItem := range rh.od.OrderItems {
		// 这个函数可能会在退 deposit payment 的时候被调用，而 deposit payment 不一定包含了所有的 item，也就是说有些 items 可能已经
		// 在 final payment 里就退完了，这里过滤掉
		if orderItem.GetTotalAmount().Sub(orderItem.GetRefundedAmount()).IsZero() {
			continue
		}

		// 直接复用 RefundByItem 里面的 RefundByAmount 的全退轧差逻辑.
		refundByAmount := &ordersvcpb.RefundOrderRequest_RefundByItem_RefundItem{
			OrderItemId:    orderItem.ID,
			RefundItemMode: orderpb.RefundItemMode_REFUND_ITEM_MODE_BY_AMOUNT,
			RefundItemModeParam: &ordersvcpb.RefundOrderRequest_RefundByItem_RefundItem_RefundAmount{
				RefundAmount: money.FromDecimal(
					orderItem.GetTotalAmount().Sub(orderItem.GetRefundedAmount()), orderItem.CurrencyCode,
				),
			},
		}

		depositMode := refundToTotalOnly
		if flags.GetIsDepositAmount() && refundDepositByItem {
			depositMode = refundAllToDeposit
		}

		refundOrderItem, err := rh.calculateRefundItem(orderItem, refundByAmount, depositMode)
		if err != nil {
			return err
		}

		rod.RefundOrderItems = append(rod.RefundOrderItems, refundOrderItem)
	}

	return nil
}

func (rh *RefundHelper) refundByPaymentPartially(
	rod *model.RefundOrderDetail,
	refundAmount decimal.Decimal,
	flags *ordersvcpb.RefundOrderRequest_RefundByPayment_RefundAmountFlags,
	refundDepositByItem bool,
) error {
	od := rh.od.Order

	// 通过轧差计算最多可以退的金额明细，用来防止多退.
	refundableOrder := rh.initRefundOrder()
	if err := rh.refundByPaymentFully(
		&model.RefundOrderDetail{RefundOrder: refundableOrder}, flags, refundDepositByItem); err != nil {
		return err
	}

	// 需要优先保障退款的总金额等于 amount.
	// refund_total_amount = refund_item_sub_total
	//                     + refund_convenience_fee (与选择的支付渠道有关)
	//                     - refund_item_discount_total
	//                     + refund_tax_fee
	//                     + refund_tips
	//                     - refund_deposit_amount
	if flags.GetIsDepositAmount() {
		rod.RefundOrder.RefundDepositAmount = refundAmount
	} else {
		rod.RefundOrder.RefundTotalAmount = refundAmount
	}

	// 聚合 RefundOrderPayment 上退还的 Convenience Fee 到 RefundOrder.
	refundConvenienceFee := decimal.Zero
	for _, rop := range rod.RefundOrderPayments {
		refundConvenienceFee = rod.RefundOrder.GetRefundConvenienceFee().Add(rop.GetRefundConvenienceFee())
	}

	rod.RefundOrder.RefundConvenienceFee = refundConvenienceFee
	if !flags.GetIsConvenienceFeeIncluded() {
		// RefundAmount 中未包含 ConvenienceFee 的时, 需要把 Convenience Fee 也计入 TotalAmount.
		rod.RefundOrder.RefundTotalAmount = rod.RefundOrder.GetRefundTotalAmount().
			Add(rod.RefundOrder.GetRefundConvenienceFee())
	}

	// 关单之前不分摊到 Tips & Items.
	if !rod.IsCreatedAfterCompleted() {
		return nil
	}

	// 分摊 tips：如果 deposit 在抵扣的时候就分摊到了 items 和 tips 上，所以这里退的 tips 金额要根据这个 payment 对应的 tips 为总金额
	// 来计算
	var refundTipsAmount decimal.Decimal

	if refundDepositByItem {
		if flags.GetIsDepositAmount() {
			tipsInPayment := od.DepositToTipsAmount
			tipsRate := rh.calculateTaxRate(tipsInPayment, od.GetDepositAmount())
			refundTipsAmount = rh.calculateByRateAndMax(refundAmount, tipsRate, refundableOrder.GetRefundTipsAmount())
		} else {
			var refundRate decimal.Decimal
			if flags.GetIsConvenienceFeeIncluded() {
				// RefundAmount 包含 Convenience Fee, 所以计算比例的时候也用包含的算.
				// 这里的 refundAmount 是含税的, 因此按税计算比例.
				refundRate = rh.calculateTaxRate(refundAmount, od.GetPaidAmount())
			} else {
				// refundAmount 本身不包含 Convenience Fee, 所以计算比例的时候要用不包含的算.
				refundRate = rh.calculateTaxRate(refundAmount, od.GetPaidAmount().Sub(od.GetConvenienceFee()))
			}

			refundTipsAmount = rh.calculateByRateAndMax(
				od.GetTipsAmount().Sub(od.DepositToTipsAmount), refundRate, refundableOrder.GetRefundTipsAmount())
		}
	} else {
		var refundRate decimal.Decimal
		if flags.GetIsConvenienceFeeIncluded() {
			// RefundAmount 包含 Convenience Fee, 所以计算比例的时候也用包含的算.
			// 这里的 refundAmount 是含税的, 因此按税计算比例.
			refundRate = rh.calculateTaxRate(refundAmount, od.GetPaidAmount().Add(od.DepositAmount))
		} else {
			// refundAmount 本身不包含 Convenience Fee, 所以计算比例的时候要用不包含的算.
			refundRate = rh.calculateTaxRate(
				refundAmount, od.GetPaidAmount().Add(od.DepositAmount).Sub(od.GetConvenienceFee()))
		}

		refundTipsAmount = rh.calculateByRateAndMax(od.GetTipsAmount(), refundRate, refundableOrder.GetRefundTipsAmount())
	}

	rod.RefundOrder.RefundTipsAmount = refundTipsAmount
	if flags.GetIsDepositAmount() && refundDepositByItem {
		rod.RefundOrder.RefundDepositToTipsAmount = refundTipsAmount
	}

	var refundItemTotal decimal.Decimal
	if flags.GetIsDepositAmount() {
		refundItemTotal = rod.RefundOrder.RefundDepositAmount.
			Sub(refundTipsAmount)
	} else {
		refundItemTotal = rod.RefundOrder.GetRefundTotalAmount().
			Sub(rod.RefundOrder.GetRefundConvenienceFee()).
			Sub(refundTipsAmount)
	}

	// 从 Item 聚合出来的几个字段 通过 Amount 和上面的明细轧差来确保最终退的金额恰好等于 amount.
	refundOrderItems, err := rh.splitRefundPaymentAmountToItem(
		refundItemTotal, refundDepositByItem, flags.GetIsDepositAmount())
	if err != nil {
		return err
	}

	refundSubTotalAmount := decimal.Zero
	refundDiscountAmount := decimal.Zero
	refundTaxAmount := decimal.Zero

	for _, roi := range refundOrderItems {
		refundSubTotalAmount = refundSubTotalAmount.Add(roi.GetRefundAmount())
		refundDiscountAmount = refundDiscountAmount.Add(roi.GetRefundDiscountAmount())
		refundTaxAmount = refundTaxAmount.Add(roi.RefundTax.GetAmount())
	}

	rod.RefundOrderItems = refundOrderItems
	rod.RefundOrder.RefundItemSubTotal = refundSubTotalAmount
	rod.RefundOrder.RefundDiscountAmount = refundDiscountAmount
	rod.RefundOrder.RefundTaxAmount = refundTaxAmount

	return nil
}

// 先把钱分摊到 item 上，再考虑内部的细分.
type splitHelper struct {
	refundableAmount decimal.Decimal
	splitAmount      decimal.Decimal
}

// buildSplitRefundPaymentAmountToItemHelper 分摊 by payment 退款的金额到每个 Item 上。
// - 如果没有记录 by item deposit deduction，那么按照每个 item 的含税支付金额在所有 item 中的占比分摊；
// - 如果有记录 by item deposit deduction：
// - - 如果退的 payment 是 deposit 的：根据每个 item 抵扣的 deposit 金额比例分摊这些金额；
// - - 如果退的 payment 不是 deposit 的：根据每个 item 在尾款单中的金额比例分摊这些金额。
func (rh *RefundHelper) buildSplitRefundPaymentAmountToItemHelper(
	refundItemTotal decimal.Decimal, // 扣除 Convenience Fee， Tips 等与 Item 无关的字段后需要退的金额.
	refundDepositByItem bool,
	isDeposit bool,
) (orderItems []*model.OrderItem, itemIDToSplit map[int64]splitHelper) {
	type splitTarget struct {
		item *model.OrderItem
		// 计算分摊比例时该 item 对应的金额
		itemTotal decimal.Decimal
		// 退款上限，也就是 itemTotal 再减掉已经退的金额
		refundableAmount decimal.Decimal
		// 分摊到的金额
		splitAmount decimal.Decimal
	}

	// 不同情况下，计算占比所用的金额不同
	splitTargets := lo.Map(
		lo.Filter(rh.od.OrderItems, func(it *model.OrderItem, _ int) bool { return !it.IsDeleted }),
		func(it *model.OrderItem, _ int) *splitTarget {
			var itemTotal decimal.Decimal

			switch {
			case !refundDepositByItem:
				itemTotal = it.GetTotalAmountIncludingTax()
			case isDeposit:
				itemTotal = it.DepositAmount
			default:
				itemTotal = it.GetTotalAmountIncludingTax().Sub(it.DepositAmount)
			}

			var refundableAmount decimal.Decimal

			switch {
			case !refundDepositByItem:
				refundableAmount = it.GetRefundableAmountIncludingTax()
			case isDeposit:
				refundableAmount = it.DepositAmount.Sub(it.RefundedDepositAmount)
			default:
				refundableAmount = itemTotal.Sub(it.RefundedAmount.Sub(it.RefundedDepositAmount))
			}

			return &splitTarget{
				item:             it,
				itemTotal:        itemTotal,
				refundableAmount: refundableAmount,
			}
		},
	)
	// 提前过滤掉分摊不到的 items
	splitTargets = lo.Filter(
		splitTargets,
		func(st *splitTarget, _ int) bool { return st.refundableAmount.GreaterThan(decimal.Zero) },
	)
	// 在分摊到 Item 之前，用 Item 可退金额从小到大排序.
	// 让最后一个 Item 能退的钱比需要轧差退的钱少的概率变低.
	sort.SliceStable(
		splitTargets,
		func(i, j int) bool {
			return splitTargets[i].refundableAmount.LessThan(splitTargets[j].refundableAmount)
		},
	)

	itemsTotal := lo.Reduce(
		splitTargets,
		func(total decimal.Decimal, st *splitTarget, _ int) decimal.Decimal { return total.Add(st.itemTotal) },
		decimal.Zero,
	)
	restRefundItemTotal := refundItemTotal

	for idx, target := range splitTargets {
		// 全部分摊完.
		if restRefundItemTotal.IsZero() {
			break
		}

		// 用该 Item 实际收取的金额占整个订单 Item 收取价格和的比例作为分摊比例.
		itemSplitRate := rh.calculateTaxRate(target.itemTotal, itemsTotal)
		itemRefundableAmount := target.refundableAmount
		// 该 Item 无可退的金额.
		if itemRefundableAmount.IsZero() {
			continue
		}

		// 该 Item 本次分摊的含税金额.
		itemSplitRefundAmount := rh.calculateByRateAndMax(refundItemTotal, itemSplitRate, restRefundItemTotal)
		// 最后一个 Item 需要轧差.
		if len(rh.od.OrderItems) == idx+1 {
			itemSplitRefundAmount = restRefundItemTotal
		}

		// 分摊的钱比最多能退的多，修正为全退.
		if itemSplitRefundAmount.GreaterThan(itemRefundableAmount) {
			itemSplitRefundAmount = itemRefundableAmount
		}

		target.splitAmount = itemSplitRefundAmount

		// 从总的里面减去当前 Item 已经退掉的价格
		restRefundItemTotal = restRefundItemTotal.Sub(itemSplitRefundAmount)
	}

	// 因为按比例分摊存在舍入的场景，直接比例分摊完会存在一点点余额.
	// 此时需要再分给有余力的 item，这里余额理论上只有几分钱，因此直接逐个 Item 进行分摊，每个分摊一份最小金额.
	// 直到余额被分摊完毕.
	for !restRefundItemTotal.IsZero() {
		for i := 0; i < len(splitTargets) && !restRefundItemTotal.IsZero(); i++ {
			split := splitTargets[i]

			if split.refundableAmount.LessThanOrEqual(split.splitAmount) {
				continue
			}

			split.splitAmount = split.splitAmount.Add(minAmount)
			restRefundItemTotal = restRefundItemTotal.Sub(minAmount)
		}
	}

	// 过滤掉分摊为 0 的 item
	splitTargets = lo.Filter(splitTargets, func(st *splitTarget, _ int) bool { return st.splitAmount.IsPositive() })

	orderItems = lo.Map(splitTargets, func(st *splitTarget, _ int) *model.OrderItem { return st.item })
	itemIDToSplit = lo.SliceToMap(
		splitTargets,
		func(st *splitTarget) (int64, splitHelper) {
			return st.item.ID, splitHelper{
				refundableAmount: st.refundableAmount,
				splitAmount:      st.splitAmount,
			}
		},
	)

	return orderItems, itemIDToSplit
}

func (rh *RefundHelper) splitRefundPaymentAmountToItem(
	refundItemTotal decimal.Decimal, // 扣除 Convenience Fee， Tips 等与 Item 无关的字段后需要退的金额.
	refundDepositByItem bool,
	isDeposit bool,
) ([]*model.RefundOrderItem, error) {
	sortedOrderItems, itemIDToSplit := rh.buildSplitRefundPaymentAmountToItemHelper(
		refundItemTotal, refundDepositByItem, isDeposit,
	)

	refundOrderItems := make([]*model.RefundOrderItem, 0, len(rh.od.OrderItems))

	for _, orderItem := range sortedOrderItems {
		itemSplitRefundAmount := itemIDToSplit[orderItem.ID].splitAmount

		// 注意这里的判断逻辑：由于有 Deposit Order 的存在，分摊的钱与最多能退的钱相等时该 item 可能还没有全额退款，只是在订金或尾款单里按
		// 比例分摊到的部分全额退款。轧差仅在该 item 在整个单里全退时才生效。
		if orderItem.GetRefundableAmountIncludingTax().Equal(itemSplitRefundAmount) {
			// itemSplitRefundAmount 是含税的，所以这里重新计算一个不含税的金额,
			// 然后直接复用 RefundByItem 里面的 RefundByAmount 的全退轧差逻辑.
			refundByAmount := &ordersvcpb.RefundOrderRequest_RefundByItem_RefundItem{
				OrderItemId:    orderItem.ID,
				RefundItemMode: orderpb.RefundItemMode_REFUND_ITEM_MODE_BY_AMOUNT,
				RefundItemModeParam: &ordersvcpb.RefundOrderRequest_RefundByItem_RefundItem_RefundAmount{
					RefundAmount: money.FromDecimal(
						orderItem.GetTotalAmount().Sub(orderItem.GetRefundedAmount()), orderItem.CurrencyCode,
					),
				},
			}

			depositMode := refundToTotalOnly
			if refundDepositByItem && isDeposit {
				depositMode = refundAllToDeposit
			}

			refundOrderItem, err := rh.calculateRefundItem(orderItem, refundByAmount, depositMode)
			if err != nil {
				return nil, err
			}

			refundOrderItems = append(refundOrderItems, refundOrderItem)

			continue
		}

		// 部分退的场景.
		// 从正向考虑，该 Item 的含税总价 = (subTotal - discount) * (1+taxRate)
		//                            = (subTotal - subtotal * discountRate) * (1+taxRate)
		//
		// 所以 subTotal = 该 Item 的含税总价 / (1+taxRate) / (1 - discountRate)
		// Discount 是税前比例计算的，所以这里用税前金额计算.
		itemDiscountRate := rh.calculateRate(orderItem.GetDiscountAmount(), orderItem.GetSubTotalAmount())

		// 税率存的是百分号前的数字，所以熟悉需要右移两位（即除以 100）.
		itemTaxRate := orderItem.Tax.Rate.Shift(-2).Round(RatePrecision)
		// 倒算退的 SubTotal 金额.
		refundSubTotal := itemSplitRefundAmount.
			// 倒算回税前（四舍五入），
			DivRound(decimal.NewFromInt(1).Add(itemTaxRate), AmountPrecision).
			// 倒算回折前（银行家）
			Div(decimal.NewFromInt(1).Sub(itemDiscountRate)).RoundBank(AmountPrecision)

		// 按折扣率计算退的折扣.
		refundDiscountAmount := rh.calculateByRateAndMax(
			refundSubTotal, itemDiscountRate, orderItem.GetRefundableDiscountAmount(),
		)

		// 轧差计算退的 Tax.
		// split = subtotal - discount + tax
		// tax = split + discount - subtotal
		refundTaxAmount := itemSplitRefundAmount.Add(refundDiscountAmount).Sub(refundSubTotal)
		if refundableTaxAmount := orderItem.GetRefundableTaxAmount(); refundTaxAmount.GreaterThan(refundableTaxAmount) {
			refundTaxAmount = refundableTaxAmount
		}

		// 考虑到 Tax 和 Discount 可能因为舍入超出可退上限而被修正。
		// 因此这里用需要分摊的金额重新轧差计算 refundSubTotal。
		// split = subtotal - discount + tax
		// subtotal = split + discount - tax
		refundSubTotal = itemSplitRefundAmount.Add(refundDiscountAmount).Sub(refundTaxAmount)

		roi := rh.initRefundOrderItem(orderItem)
		roi.RefundItemMode = orderpb.RefundItemMode_REFUND_ITEM_MODE_BY_AMOUNT
		roi.RefundTotalAmount = refundSubTotal.Sub(refundDiscountAmount) // 跟 OrderItem 对齐，TotalAmount 里面不包含 Tax
		roi.RefundAmount = refundSubTotal
		roi.RefundDiscountAmount = refundDiscountAmount
		roi.RefundTax.Amount = refundTaxAmount

		if refundDepositByItem && isDeposit {
			roi.RefundDepositAmount = roi.GetRefundTotalAmount().Add(roi.RefundTax.GetAmount())
		}

		refundOrderItems = append(refundOrderItems, roi)
	}

	return refundOrderItems, nil
}

func (rh *RefundHelper) validateForRefundByPayment() error {
	if !rh.od.IsComplete() {
		// 未关单的订单只能 Refund By Payment
		return nil
	}

	if rh.od.OrderVersion().Lt(model.OrderVersionRefund) {
		// 老版本只支持 RefundByPayment
		return nil
	}

	// TODO(Perqin, P1): RefundableMode 在构造 order detail 的时候就有计算逻辑，不必要在这里再重做一次，这里的逻辑也应该转移到那边去

	// 已经抵扣过的 deposit order，超付的部分必须 by item 先退完才能 by payment 退款；没有抵扣过的 deposit order 不受影响
	if rh.od.Order.OrderType == orderpb.OrderModel_DEPOSIT {
		if _, found := lo.Find(
			rh.dd.DepositChangeLogs,
			func(it *model.DepositChangeLog) bool { return it.Reason == orderpb.DepositChangeReason_DEDUCTION },
		); found && rh.dd.LatestChangeLog.Balance.IsPositive() {
			return status.Error(codes.InvalidArgument, "overpaid deposit amount must be refunded by item")
		}

		// Deposit order 就算 refund by payment 最终也是 refund by item，所以这里不校验
		return nil
	}

	// 关单后只能使用一种退款模式.
	refundOrder, found := lo.Find(
		rh.rods,
		func(it *model.RefundOrderDetail) bool {
			return it.IsCreatedAfterCompleted() &&
				it.RefundOrder.RefundMode != orderpb.RefundMode_REFUND_MODE_BY_PAYMENT
		},
	)

	if !found {
		// 关单后没有退过款.
		return nil
	}

	return status.Newf(
		codes.InvalidArgument,
		"found refunded order[%d] with another mode[%s]",
		refundOrder.RefundOrder.ID,
		refundOrder.RefundOrder.RefundMode,
	).Err()
}

func (rh *RefundHelper) applyRefundOrderPayments(
	refundOrderPayments []*model.RefundOrderPayment,
) []*model.OrderPayment {
	idToRefundOrderPayment := lo.KeyBy(
		refundOrderPayments,
		func(it *model.RefundOrderPayment) int64 { return it.OrderPaymentID },
	)

	newOrderPayments := make([]*model.OrderPayment, 0, len(refundOrderPayments))

	for _, payment := range rh.od.OrderPayments {
		newPayment := payment.Clone()

		refundOrderPayment, found := idToRefundOrderPayment[payment.ID]
		if !found {
			// 当前 Payment 无退款.
			newOrderPayments = append(newOrderPayments, newPayment)
			continue
		}

		newPayment.RefundedAmount = newPayment.GetRefundedAmount().
			Add(refundOrderPayment.GetRefundAmount())
		newPayment.RefundedConvenienceFee = newPayment.GetRefundedConvenienceFee().
			Add(refundOrderPayment.GetRefundConvenienceFee())

		newOrderPayments = append(newOrderPayments, newPayment)
	}

	return newOrderPayments
}
