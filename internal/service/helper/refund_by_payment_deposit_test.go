// Deposit refund by items 相关测试用例

package helper

import (
	"testing"

	"github.com/shopspring/decimal"
	"github.com/stretchr/testify/assert"
	"google.golang.org/protobuf/proto"

	orderpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/order/v1"
	ordersvcpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/order/v1"
	"github.com/MoeGolibrary/moego-svc-order-v2/internal/model"
)

// Simplest case: No discount, no tax, no tips, no convenience fee
// 1. Deposit order $90, $40 for item A, $50 for item B;
// 2. Final order $90, $40 for item A, $50 for item B;
// Refund the whole deposit payment.
func TestRefundHelper_RefundByPayment_Simple_Deposit(t *testing.T) {
	rh := NewRefundHelper(
		&model.OrderDetail{
			Order: &model.Order{
				ID:             1,
				Status:         orderpb.OrderStatus_COMPLETED,
				OrderVersion:   model.OrderVersionRefund,
				CurrencyCode:   "USD",
				TipsAmount:     decimal.Zero,
				TaxAmount:      decimal.Zero,
				DiscountAmount: decimal.Zero,
				ConvenienceFee: decimal.Zero,
				SubTotalAmount: mustDecimal("180.00"),
				TotalAmount:    mustDecimal("90.00"),
				PaidAmount:     mustDecimal("90.00"),
				RemainAmount:   decimal.Zero,
				RefundedAmount: decimal.Zero,
				DepositAmount:  mustDecimal("90.00"),
			},
			OrderItems: []*model.OrderItem{
				{
					ID:                     11,
					Tax:                    model.Tax{},
					Quantity:               1,
					UnitPrice:              mustDecimal("80.00"),
					TipsAmount:             decimal.Zero,
					DiscountAmount:         decimal.Zero,
					SubTotalAmount:         mustDecimal("80.00"),
					TotalAmount:            mustDecimal("80.00"),
					RefundedAmount:         decimal.Zero,
					RefundedTaxAmount:      decimal.Zero,
					RefundedDiscountAmount: decimal.Zero,
					DepositAmount:          mustDecimal("40.00"),
				},
				{
					ID:                     12,
					Tax:                    model.Tax{},
					Quantity:               1,
					UnitPrice:              mustDecimal("100.00"),
					TipsAmount:             decimal.Zero,
					DiscountAmount:         decimal.Zero,
					SubTotalAmount:         mustDecimal("100.00"),
					TotalAmount:            mustDecimal("100.00"),
					RefundedAmount:         decimal.Zero,
					RefundedTaxAmount:      decimal.Zero,
					RefundedDiscountAmount: decimal.Zero,
					DepositAmount:          mustDecimal("50.00"),
				},
			},
			OrderPayments: []*model.OrderPayment{
				{
					ID:                     21,
					TotalAmount:            mustDecimal("90.00"),
					Amount:                 mustDecimal("90.00"),
					RefundedAmount:         decimal.Zero,
					ProcessingFee:          decimal.Zero,
					ConvenienceFee:         decimal.Zero,
					RefundedConvenienceFee: decimal.Zero,
					PaymentStatus:          orderpb.OrderPaymentStatus_ORDER_PAYMENT_STATUS_PAID,
				},
			},
		},
		nil,
		nil,
	)

	actual, err := rh.RefundByPayment(
		nil,
		mustDecimal("90.00"),
		&ordersvcpb.RefundOrderRequest_RefundByPayment_RefundAmountFlags{
			IsConvenienceFeeIncluded: true,
			IsDepositAmount:          proto.Bool(true),
		},
	)

	expectedRO := &model.RefundOrder{
		RefundMode:                orderpb.RefundMode_REFUND_MODE_BY_PAYMENT,
		CurrencyCode:              "USD",
		RefundTotalAmount:         decimal.Zero,
		RefundItemSubTotal:        mustDecimal("90.00"),
		RefundDiscountAmount:      decimal.Zero,
		RefundTipsAmount:          decimal.Zero,
		RefundConvenienceFee:      decimal.Zero,
		RefundTaxAmount:           decimal.Zero,
		RefundDepositAmount:       mustDecimal("90.00"),
		RefundDepositToTipsAmount: decimal.Zero,
	}
	expectedROIs := []*model.RefundOrderItem{
		{
			RefundItemMode:       orderpb.RefundItemMode_REFUND_ITEM_MODE_BY_AMOUNT,
			RefundTax:            model.RefundTax{},
			RefundTotalAmount:    mustDecimal("40.00"),
			RefundQuantity:       0,
			RefundAmount:         mustDecimal("40.00"),
			RefundDiscountAmount: decimal.Zero,
			RefundDepositAmount:  mustDecimal("40.00"),
		},
		{
			RefundItemMode:       orderpb.RefundItemMode_REFUND_ITEM_MODE_BY_AMOUNT,
			RefundTax:            model.RefundTax{},
			RefundTotalAmount:    mustDecimal("50.00"),
			RefundQuantity:       0,
			RefundAmount:         mustDecimal("50.00"),
			RefundDiscountAmount: decimal.Zero,
			RefundDepositAmount:  mustDecimal("50.00"),
		},
	}

	ast := assert.New(t)
	ast.NoError(err)
	ast.False(actual.IsFullyRefund)
	equalRefundOrder(ast, expectedRO, actual.RefundOrderDetail.RefundOrder)
	assert.Len(t, actual.RefundOrderDetail.RefundOrderItems, 2)
	equalRefundOrderItem(ast, expectedROIs[0], actual.RefundOrderDetail.RefundOrderItems[0])
	equalRefundOrderItem(ast, expectedROIs[1], actual.RefundOrderDetail.RefundOrderItems[1])
	assert.Empty(t, actual.RefundOrderDetail.RefundOrderPayments)
}

// Simplest case: No discount, no tax, no tips, no convenience fee
// 1. Deposit order $90, $40 for item A, $50 for item B;
// 2. Final order $90, $40 for item A, $50 for item B;
// Refund the whole final payment.
func TestRefundHelper_RefundByPayment_Simple_Final(t *testing.T) {
	rh := NewRefundHelper(
		&model.OrderDetail{
			Order: &model.Order{
				ID:             1,
				Status:         orderpb.OrderStatus_COMPLETED,
				OrderVersion:   model.OrderVersionRefund,
				CurrencyCode:   "USD",
				TipsAmount:     decimal.Zero,
				TaxAmount:      decimal.Zero,
				DiscountAmount: decimal.Zero,
				ConvenienceFee: decimal.Zero,
				SubTotalAmount: mustDecimal("180.00"),
				TotalAmount:    mustDecimal("90.00"),
				PaidAmount:     mustDecimal("90.00"),
				RemainAmount:   decimal.Zero,
				RefundedAmount: decimal.Zero,
				DepositAmount:  mustDecimal("90.00"),
			},
			OrderItems: []*model.OrderItem{
				{
					ID:                     11,
					Tax:                    model.Tax{},
					Quantity:               1,
					UnitPrice:              mustDecimal("80.00"),
					TipsAmount:             decimal.Zero,
					DiscountAmount:         decimal.Zero,
					SubTotalAmount:         mustDecimal("80.00"),
					TotalAmount:            mustDecimal("80.00"),
					RefundedAmount:         decimal.Zero,
					RefundedTaxAmount:      decimal.Zero,
					RefundedDiscountAmount: decimal.Zero,
					DepositAmount:          mustDecimal("40.00"),
				},
				{
					ID:                     12,
					Tax:                    model.Tax{},
					Quantity:               1,
					UnitPrice:              mustDecimal("100.00"),
					TipsAmount:             decimal.Zero,
					DiscountAmount:         decimal.Zero,
					SubTotalAmount:         mustDecimal("100.00"),
					TotalAmount:            mustDecimal("100.00"),
					RefundedAmount:         decimal.Zero,
					RefundedTaxAmount:      decimal.Zero,
					RefundedDiscountAmount: decimal.Zero,
					DepositAmount:          mustDecimal("50.00"),
				},
			},
			OrderPayments: []*model.OrderPayment{
				{
					ID:                     21,
					TotalAmount:            mustDecimal("90.00"),
					Amount:                 mustDecimal("90.00"),
					RefundedAmount:         decimal.Zero,
					ProcessingFee:          decimal.Zero,
					ConvenienceFee:         decimal.Zero,
					RefundedConvenienceFee: decimal.Zero,
					PaymentStatus:          orderpb.OrderPaymentStatus_ORDER_PAYMENT_STATUS_PAID,
				},
			},
		},
		nil,
		nil,
	)

	actual, err := rh.RefundByPayment(
		[]int64{21},
		mustDecimal("90.00"),
		&ordersvcpb.RefundOrderRequest_RefundByPayment_RefundAmountFlags{
			IsConvenienceFeeIncluded: true,
		},
	)

	expectedRO := &model.RefundOrder{
		RefundMode:                orderpb.RefundMode_REFUND_MODE_BY_PAYMENT,
		CurrencyCode:              "USD",
		RefundTotalAmount:         mustDecimal("90.00"),
		RefundItemSubTotal:        mustDecimal("90.00"),
		RefundDiscountAmount:      decimal.Zero,
		RefundTipsAmount:          decimal.Zero,
		RefundConvenienceFee:      decimal.Zero,
		RefundTaxAmount:           decimal.Zero,
		RefundDepositAmount:       decimal.Zero,
		RefundDepositToTipsAmount: decimal.Zero,
	}
	expectedROIs := []*model.RefundOrderItem{
		{
			RefundItemMode:       orderpb.RefundItemMode_REFUND_ITEM_MODE_BY_AMOUNT,
			RefundTax:            model.RefundTax{},
			RefundTotalAmount:    mustDecimal("40.00"),
			RefundQuantity:       0,
			RefundAmount:         mustDecimal("40.00"),
			RefundDiscountAmount: decimal.Zero,
			RefundDepositAmount:  decimal.Zero,
		},
		{
			RefundItemMode:       orderpb.RefundItemMode_REFUND_ITEM_MODE_BY_AMOUNT,
			RefundTax:            model.RefundTax{},
			RefundTotalAmount:    mustDecimal("50.00"),
			RefundQuantity:       0,
			RefundAmount:         mustDecimal("50.00"),
			RefundDiscountAmount: decimal.Zero,
			RefundDepositAmount:  decimal.Zero,
		},
	}
	expectedROPs := []*model.RefundOrderPayment{
		{
			OrderPaymentID:       21,
			CurrencyCode:         "",
			RefundAmount:         mustDecimal("90.00"),
			RefundConvenienceFee: mustDecimal("0.00"),
		},
	}

	ast := assert.New(t)
	ast.NoError(err)
	ast.False(actual.IsFullyRefund)
	equalRefundOrder(ast, expectedRO, actual.RefundOrderDetail.RefundOrder)
	assert.Len(t, actual.RefundOrderDetail.RefundOrderItems, 2)
	equalRefundOrderItem(ast, expectedROIs[0], actual.RefundOrderDetail.RefundOrderItems[0])
	equalRefundOrderItem(ast, expectedROIs[1], actual.RefundOrderDetail.RefundOrderItems[1])
	assert.Len(t, actual.RefundOrderDetail.RefundOrderPayments, 1)
	equalRefundOrderPayment(ast, expectedROPs[0], actual.RefundOrderDetail.RefundOrderPayments[0])
}

// Complex 场景有三个用例要写：
// 1. Deposit payment 首次发起退款
// 2. Deposit payment 再次发起退款并退完，但 final payment 没有退完
// 3. Deposit payment 再次发起退款并退完，且 final payment 也已经退完
// Final payment 同理。
//
// Complex case:
// 1. Item A ($80) which has deposit (30%), discount (5%) and tax (10%);
// 2. Item B ($100) which has deposit (30%);
// 3. Item C ($120) which has no deposit;
// 4. The Order has tips ($10);
// 5. Both payments have convenience fee.
// Refunds:
// Refund $27 from the deposit payment.
func TestRefundHelper_RefundByPayment_Complex_RefundInitialDepositPayment(t *testing.T) {
	rh := NewRefundHelper(
		&model.OrderDetail{
			Order: &model.Order{
				ID:             1,
				Status:         orderpb.OrderStatus_COMPLETED,
				OrderVersion:   model.OrderVersionRefund,
				CurrencyCode:   "USD",
				TipsAmount:     mustDecimal("10.00"),
				TaxAmount:      mustDecimal("7.60"),
				DiscountAmount: mustDecimal("4.00"),
				ConvenienceFee: mustDecimal("9.45"),
				SubTotalAmount: mustDecimal("300.00"),
				TotalAmount:    mustDecimal("259.60"),
				PaidAmount:     mustDecimal("259.60"),
				RemainAmount:   decimal.Zero,
				RefundedAmount: decimal.Zero,
				DepositAmount:  mustDecimal("54.00"),
			},
			OrderItems: []*model.OrderItem{
				{
					ID: 11,
					Tax: model.Tax{
						ID:     1,
						Name:   "Consumption Tax",
						Rate:   mustDecimal("10"),
						Amount: mustDecimal("7.60"),
					},
					Quantity:               1,
					UnitPrice:              mustDecimal("80.00"),
					TipsAmount:             decimal.Zero,
					DiscountAmount:         mustDecimal("4.00"),
					SubTotalAmount:         mustDecimal("80.00"),
					TotalAmount:            mustDecimal("76.00"),
					RefundedAmount:         decimal.Zero,
					RefundedTaxAmount:      decimal.Zero,
					RefundedDiscountAmount: decimal.Zero,
					DepositAmount:          mustDecimal("24.00"),
				},
				{
					ID:                     12,
					Tax:                    model.Tax{},
					Quantity:               1,
					UnitPrice:              mustDecimal("100.00"),
					TipsAmount:             decimal.Zero,
					DiscountAmount:         decimal.Zero,
					SubTotalAmount:         mustDecimal("100.00"),
					TotalAmount:            mustDecimal("100.00"),
					RefundedAmount:         decimal.Zero,
					RefundedTaxAmount:      decimal.Zero,
					RefundedDiscountAmount: decimal.Zero,
					DepositAmount:          mustDecimal("30.00"),
				},
			},
			OrderPayments: []*model.OrderPayment{
				{
					ID:                     21,
					TotalAmount:            mustDecimal("259.60"),
					Amount:                 mustDecimal("259.60"),
					RefundedAmount:         decimal.Zero,
					ProcessingFee:          decimal.Zero,
					ConvenienceFee:         mustDecimal("9.45"),
					RefundedConvenienceFee: decimal.Zero,
					PaymentStatus:          orderpb.OrderPaymentStatus_ORDER_PAYMENT_STATUS_PAID,
				},
			},
		},
		nil,
		nil,
	)

	actual, err := rh.RefundByPayment(
		[]int64{},
		mustDecimal("27.00"),
		&ordersvcpb.RefundOrderRequest_RefundByPayment_RefundAmountFlags{
			IsConvenienceFeeIncluded: true,
			IsDepositAmount:          proto.Bool(true),
		},
	)

	expectedRO := &model.RefundOrder{
		RefundMode:                orderpb.RefundMode_REFUND_MODE_BY_PAYMENT,
		CurrencyCode:              "USD",
		RefundTotalAmount:         decimal.Zero,
		RefundItemSubTotal:        mustDecimal("26.48"),
		RefundDiscountAmount:      mustDecimal("0.57"),
		RefundTipsAmount:          decimal.Zero,
		RefundConvenienceFee:      decimal.Zero,
		RefundTaxAmount:           mustDecimal("1.09"),
		RefundDepositAmount:       mustDecimal("27.00"),
		RefundDepositToTipsAmount: decimal.Zero,
	}
	expectedROIs := []*model.RefundOrderItem{
		{
			RefundItemMode: orderpb.RefundItemMode_REFUND_ITEM_MODE_BY_AMOUNT,
			RefundTax: model.RefundTax{
				ID:     1,
				Name:   "Consumption Tax",
				Rate:   mustDecimal("10"),
				Amount: mustDecimal("1.09"),
			},
			RefundTotalAmount:    mustDecimal("10.91"),
			RefundQuantity:       0,
			RefundAmount:         mustDecimal("11.48"),
			RefundDiscountAmount: mustDecimal("0.57"),
			RefundDepositAmount:  mustDecimal("12.00"),
		},
		{
			RefundItemMode:       orderpb.RefundItemMode_REFUND_ITEM_MODE_BY_AMOUNT,
			RefundTax:            model.RefundTax{},
			RefundTotalAmount:    mustDecimal("15.00"),
			RefundQuantity:       0,
			RefundAmount:         mustDecimal("15.00"),
			RefundDiscountAmount: decimal.Zero,
			RefundDepositAmount:  mustDecimal("15.00"),
		},
	}

	ast := assert.New(t)
	ast.NoError(err)
	ast.False(actual.IsFullyRefund)
	equalRefundOrder(ast, expectedRO, actual.RefundOrderDetail.RefundOrder)
	assert.Len(t, actual.RefundOrderDetail.RefundOrderItems, 2)
	equalRefundOrderItem(ast, expectedROIs[0], actual.RefundOrderDetail.RefundOrderItems[0])
	equalRefundOrderItem(ast, expectedROIs[1], actual.RefundOrderDetail.RefundOrderItems[1])
	assert.Empty(t, actual.RefundOrderDetail.RefundOrderPayments)
}

// Complex case:
// 1. Item A ($80) which has deposit (30%), discount (5%) and tax (10%);
// 2. Item B ($100) which has deposit (30%);
// 3. Item C ($120) which has no deposit;
// 4. The Order has tips ($10);
// 5. Both payments have convenience fee.
// Refunds:
// 1. Already refunded $27 from the deposit payment;
// 2. Refund the rest $27 from the deposit payment.
func TestRefundHelper_RefundByPayment_Complex_RefundRestDepositPayment(t *testing.T) {
	rh := NewRefundHelper(
		&model.OrderDetail{
			Order: &model.Order{
				ID:             1,
				Status:         orderpb.OrderStatus_COMPLETED,
				OrderVersion:   model.OrderVersionRefund,
				CurrencyCode:   "USD",
				TipsAmount:     mustDecimal("10.00"),
				TaxAmount:      mustDecimal("7.60"),
				DiscountAmount: mustDecimal("4.00"),
				ConvenienceFee: mustDecimal("9.45"),
				SubTotalAmount: mustDecimal("300.00"),
				TotalAmount:    mustDecimal("259.60"),
				PaidAmount:     mustDecimal("259.60"),
				RemainAmount:   decimal.Zero,
				RefundedAmount: decimal.Zero,
				DepositAmount:  mustDecimal("54.00"),
			},
			OrderItems: []*model.OrderItem{
				{
					ID: 11,
					Tax: model.Tax{
						ID:     1,
						Name:   "Consumption Tax",
						Rate:   mustDecimal("10"),
						Amount: mustDecimal("7.60"),
					},
					Quantity:               1,
					UnitPrice:              mustDecimal("80.00"),
					TipsAmount:             decimal.Zero,
					DiscountAmount:         mustDecimal("4.00"),
					SubTotalAmount:         mustDecimal("80.00"),
					TotalAmount:            mustDecimal("76.00"),
					RefundedAmount:         mustDecimal("10.91"),
					RefundedTaxAmount:      mustDecimal("1.09"),
					RefundedDiscountAmount: mustDecimal("0.57"),
					RefundedDepositAmount:  mustDecimal("12.00"),
					DepositAmount:          mustDecimal("24.00"),
				},
				{
					ID:                     12,
					Tax:                    model.Tax{},
					Quantity:               1,
					UnitPrice:              mustDecimal("100.00"),
					TipsAmount:             decimal.Zero,
					DiscountAmount:         decimal.Zero,
					SubTotalAmount:         mustDecimal("100.00"),
					TotalAmount:            mustDecimal("100.00"),
					RefundedAmount:         mustDecimal("15.00"),
					RefundedTaxAmount:      decimal.Zero,
					RefundedDiscountAmount: decimal.Zero,
					RefundedDepositAmount:  mustDecimal("15.00"),
					DepositAmount:          mustDecimal("30.00"),
				},
			},
			OrderPayments: []*model.OrderPayment{
				{
					ID:                     21,
					TotalAmount:            mustDecimal("259.60"),
					Amount:                 mustDecimal("259.60"),
					RefundedAmount:         decimal.Zero,
					ProcessingFee:          decimal.Zero,
					ConvenienceFee:         mustDecimal("9.45"),
					RefundedConvenienceFee: decimal.Zero,
					PaymentStatus:          orderpb.OrderPaymentStatus_ORDER_PAYMENT_STATUS_PAID,
				},
			},
		},
		[]*model.RefundOrderDetail{
			{
				RefundOrder: &model.RefundOrder{
					RefundMode:                orderpb.RefundMode_REFUND_MODE_BY_PAYMENT,
					CurrencyCode:              "USD",
					RefundTotalAmount:         decimal.Zero,
					RefundItemSubTotal:        mustDecimal("26.48"),
					RefundDiscountAmount:      mustDecimal("0.57"),
					RefundTipsAmount:          decimal.Zero,
					RefundConvenienceFee:      decimal.Zero,
					RefundTaxAmount:           mustDecimal("1.09"),
					RefundDepositAmount:       mustDecimal("27.00"),
					RefundDepositToTipsAmount: decimal.Zero,
				},
				RefundOrderItems: []*model.RefundOrderItem{
					{
						RefundItemMode: orderpb.RefundItemMode_REFUND_ITEM_MODE_BY_AMOUNT,
						RefundTax: model.RefundTax{
							ID:     1,
							Name:   "Consumption Tax",
							Rate:   mustDecimal("10"),
							Amount: mustDecimal("1.09"),
						},
						RefundTotalAmount:    mustDecimal("10.91"),
						RefundQuantity:       0,
						RefundAmount:         mustDecimal("11.48"),
						RefundDiscountAmount: mustDecimal("0.57"),
						RefundDepositAmount:  mustDecimal("12.00"),
					},
					{
						RefundItemMode:       orderpb.RefundItemMode_REFUND_ITEM_MODE_BY_AMOUNT,
						RefundTax:            model.RefundTax{},
						RefundTotalAmount:    mustDecimal("15.00"),
						RefundQuantity:       0,
						RefundAmount:         mustDecimal("15.00"),
						RefundDiscountAmount: decimal.Zero,
						RefundDepositAmount:  decimal.Zero,
					},
				},
				RefundOrderPayments: nil,
			},
		},
		nil,
	)

	actual, err := rh.RefundByPayment(
		[]int64{},
		mustDecimal("27.00"),
		&ordersvcpb.RefundOrderRequest_RefundByPayment_RefundAmountFlags{
			IsConvenienceFeeIncluded: true,
			IsDepositAmount:          proto.Bool(true),
		},
	)

	expectedRO := &model.RefundOrder{
		RefundMode:                orderpb.RefundMode_REFUND_MODE_BY_PAYMENT,
		CurrencyCode:              "USD",
		RefundTotalAmount:         decimal.Zero,
		RefundItemSubTotal:        mustDecimal("26.48"),
		RefundDiscountAmount:      mustDecimal("0.57"),
		RefundTipsAmount:          decimal.Zero,
		RefundConvenienceFee:      decimal.Zero,
		RefundTaxAmount:           mustDecimal("1.09"),
		RefundDepositAmount:       mustDecimal("27.00"),
		RefundDepositToTipsAmount: decimal.Zero,
	}
	expectedROIs := []*model.RefundOrderItem{
		{
			RefundItemMode: orderpb.RefundItemMode_REFUND_ITEM_MODE_BY_AMOUNT,
			RefundTax: model.RefundTax{
				ID:     1,
				Name:   "Consumption Tax",
				Rate:   mustDecimal("10"),
				Amount: mustDecimal("1.09"),
			},
			RefundTotalAmount:    mustDecimal("10.91"),
			RefundQuantity:       0,
			RefundAmount:         mustDecimal("11.48"),
			RefundDiscountAmount: mustDecimal("0.57"),
			RefundDepositAmount:  mustDecimal("12.00"),
		},
		{
			RefundItemMode:       orderpb.RefundItemMode_REFUND_ITEM_MODE_BY_AMOUNT,
			RefundTax:            model.RefundTax{},
			RefundTotalAmount:    mustDecimal("15.00"),
			RefundQuantity:       0,
			RefundAmount:         mustDecimal("15.00"),
			RefundDiscountAmount: decimal.Zero,
			RefundDepositAmount:  mustDecimal("15.00"),
		},
	}

	ast := assert.New(t)
	ast.NoError(err)
	ast.False(actual.IsFullyRefund)
	equalRefundOrder(ast, expectedRO, actual.RefundOrderDetail.RefundOrder)
	assert.Len(t, actual.RefundOrderDetail.RefundOrderItems, 2)
	equalRefundOrderItem(ast, expectedROIs[0], actual.RefundOrderDetail.RefundOrderItems[0])
	equalRefundOrderItem(ast, expectedROIs[1], actual.RefundOrderDetail.RefundOrderItems[1])
	assert.Empty(t, actual.RefundOrderDetail.RefundOrderPayments)
}

// Complex case:
// 1. Item A ($80) which has deposit (30%), discount (5%) and tax (10%);
// 2. Item B ($100) which has deposit (30%);
// 3. Item C ($120) which has no deposit;
// 4. The Order has tips ($10);
// 5. Both payments have convenience fee.
// Refunds:
// 1. Already refunded $27 from the deposit payment;
// 2. Already refunded $259.6 from the final payment;
// 3. Refund the rest $27 from the deposit payment, fully refunding the order.
func TestRefundHelper_RefundByPayment_Complex_FullyRefundRestDepositPayment(t *testing.T) {
	rh := NewRefundHelper(
		&model.OrderDetail{
			Order: &model.Order{
				ID:             1,
				Status:         orderpb.OrderStatus_COMPLETED,
				OrderVersion:   model.OrderVersionRefund,
				CurrencyCode:   "USD",
				TipsAmount:     mustDecimal("10.00"),
				TaxAmount:      mustDecimal("7.60"),
				DiscountAmount: mustDecimal("4.00"),
				ConvenienceFee: mustDecimal("9.45"),
				SubTotalAmount: mustDecimal("300.00"),
				TotalAmount:    mustDecimal("259.60"),
				PaidAmount:     mustDecimal("259.60"),
				RemainAmount:   decimal.Zero,
				RefundedAmount: mustDecimal("259.60"),
				DepositAmount:  mustDecimal("54.00"),
			},
			OrderItems: []*model.OrderItem{
				{
					ID: 11,
					Tax: model.Tax{
						ID:     1,
						Name:   "Consumption Tax",
						Rate:   mustDecimal("10"),
						Amount: mustDecimal("7.60"),
					},
					Quantity:               1,
					UnitPrice:              mustDecimal("80.00"),
					TipsAmount:             decimal.Zero,
					DiscountAmount:         mustDecimal("4.00"),
					SubTotalAmount:         mustDecimal("80.00"),
					TotalAmount:            mustDecimal("76.00"),
					RefundedAmount:         mustDecimal("65.09"), // 10.91 (Deposit) + 54.18 (Final)
					RefundedTaxAmount:      mustDecimal("6.51"),  // 1.09 (Deposit) + 5.42 (Final)
					RefundedDiscountAmount: mustDecimal("3.42"),  // 0.57 (Deposit) + 2.85 (Final)
					RefundedDepositAmount:  mustDecimal("12.00"),
					DepositAmount:          mustDecimal("24.00"),
				},
				{
					ID:                     12,
					Tax:                    model.Tax{},
					Quantity:               1,
					UnitPrice:              mustDecimal("100.00"),
					TipsAmount:             decimal.Zero,
					DiscountAmount:         decimal.Zero,
					SubTotalAmount:         mustDecimal("100.00"),
					TotalAmount:            mustDecimal("100.00"),
					RefundedAmount:         mustDecimal("85.00"),
					RefundedTaxAmount:      decimal.Zero,
					RefundedDiscountAmount: decimal.Zero,
					RefundedDepositAmount:  mustDecimal("15.00"),
					DepositAmount:          mustDecimal("30.00"),
				},
				{
					ID:                     13,
					Tax:                    model.Tax{},
					Quantity:               1,
					UnitPrice:              mustDecimal("120.00"),
					TipsAmount:             decimal.Zero,
					DiscountAmount:         decimal.Zero,
					SubTotalAmount:         mustDecimal("120.00"),
					TotalAmount:            mustDecimal("120.00"),
					RefundedAmount:         mustDecimal("120.00"),
					RefundedTaxAmount:      decimal.Zero,
					RefundedDiscountAmount: decimal.Zero,
					RefundedDepositAmount:  decimal.Zero,
					DepositAmount:          decimal.Zero,
				},
			},
			OrderPayments: []*model.OrderPayment{
				{
					ID:                     21,
					TotalAmount:            mustDecimal("259.60"),
					Amount:                 mustDecimal("259.60"),
					RefundedAmount:         mustDecimal("259.60"),
					ProcessingFee:          decimal.Zero,
					ConvenienceFee:         mustDecimal("9.45"),
					RefundedConvenienceFee: mustDecimal("9.45"),
					PaymentStatus:          orderpb.OrderPaymentStatus_ORDER_PAYMENT_STATUS_PAID,
				},
			},
		},
		[]*model.RefundOrderDetail{
			{
				RefundOrder: &model.RefundOrder{
					ID:                        2,
					OrderStatusSnapshot:       orderpb.OrderStatus_COMPLETED,
					RefundMode:                orderpb.RefundMode_REFUND_MODE_BY_PAYMENT,
					CurrencyCode:              "USD",
					RefundTotalAmount:         mustDecimal("259.60"),
					RefundItemSubTotal:        mustDecimal("247.03"),
					RefundDiscountAmount:      mustDecimal("2.85"),
					RefundTipsAmount:          mustDecimal("10.00"),
					RefundConvenienceFee:      mustDecimal("9.45"),
					RefundTaxAmount:           mustDecimal("5.42"),
					RefundDepositAmount:       decimal.Zero,
					RefundDepositToTipsAmount: decimal.Zero,
				},
				RefundOrderItems: []*model.RefundOrderItem{
					{
						ID:             3,
						RefundItemMode: orderpb.RefundItemMode_REFUND_ITEM_MODE_BY_AMOUNT,
						RefundTax: model.RefundTax{
							ID:     1,
							Name:   "Consumption Tax",
							Rate:   mustDecimal("10"),
							Amount: mustDecimal("5.42"),
						},
						RefundTotalAmount:    mustDecimal("54.18"),
						RefundQuantity:       0,
						RefundAmount:         mustDecimal("57.03"),
						RefundDiscountAmount: mustDecimal("2.85"),
						RefundDepositAmount:  decimal.Zero,
					},
					{
						ID:                   4,
						RefundItemMode:       orderpb.RefundItemMode_REFUND_ITEM_MODE_BY_AMOUNT,
						RefundTax:            model.RefundTax{},
						RefundTotalAmount:    mustDecimal("70.00"),
						RefundQuantity:       0,
						RefundAmount:         mustDecimal("70.00"),
						RefundDiscountAmount: decimal.Zero,
						RefundDepositAmount:  decimal.Zero,
					},
					{
						ID:                   5,
						RefundItemMode:       orderpb.RefundItemMode_REFUND_ITEM_MODE_BY_AMOUNT,
						RefundTax:            model.RefundTax{},
						RefundTotalAmount:    mustDecimal("120.00"),
						RefundQuantity:       0,
						RefundAmount:         mustDecimal("120.00"),
						RefundDiscountAmount: decimal.Zero,
						RefundDepositAmount:  decimal.Zero,
					},
				},
				RefundOrderPayments: []*model.RefundOrderPayment{
					{
						OrderPaymentID:       21,
						CurrencyCode:         "",
						RefundAmount:         mustDecimal("259.60"),
						RefundConvenienceFee: mustDecimal("9.45"),
					},
				},
			},
			{
				RefundOrder: &model.RefundOrder{
					ID:                        1,
					OrderStatusSnapshot:       orderpb.OrderStatus_COMPLETED,
					RefundMode:                orderpb.RefundMode_REFUND_MODE_BY_PAYMENT,
					CurrencyCode:              "USD",
					RefundTotalAmount:         decimal.Zero,
					RefundItemSubTotal:        mustDecimal("26.48"),
					RefundDiscountAmount:      mustDecimal("0.57"),
					RefundTipsAmount:          decimal.Zero,
					RefundConvenienceFee:      decimal.Zero,
					RefundTaxAmount:           mustDecimal("1.09"),
					RefundDepositAmount:       mustDecimal("27.00"),
					RefundDepositToTipsAmount: decimal.Zero,
				},
				RefundOrderItems: []*model.RefundOrderItem{
					{
						ID:             1,
						RefundItemMode: orderpb.RefundItemMode_REFUND_ITEM_MODE_BY_AMOUNT,
						RefundTax: model.RefundTax{
							ID:     1,
							Name:   "Consumption Tax",
							Rate:   mustDecimal("10"),
							Amount: mustDecimal("1.09"),
						},
						RefundTotalAmount:    mustDecimal("10.91"),
						RefundQuantity:       0,
						RefundAmount:         mustDecimal("11.48"),
						RefundDiscountAmount: mustDecimal("0.57"),
						RefundDepositAmount:  mustDecimal("12.00"),
					},
					{
						ID:                   2,
						RefundItemMode:       orderpb.RefundItemMode_REFUND_ITEM_MODE_BY_AMOUNT,
						RefundTax:            model.RefundTax{},
						RefundTotalAmount:    mustDecimal("15.00"),
						RefundQuantity:       0,
						RefundAmount:         mustDecimal("15.00"),
						RefundDiscountAmount: decimal.Zero,
						RefundDepositAmount:  decimal.Zero,
					},
				},
				RefundOrderPayments: nil,
			},
		},
		nil,
	)

	actual, err := rh.RefundByPayment(
		[]int64{},
		mustDecimal("27.00"),
		&ordersvcpb.RefundOrderRequest_RefundByPayment_RefundAmountFlags{
			IsConvenienceFeeIncluded: true,
			IsDepositAmount:          proto.Bool(true),
		},
	)

	expectedRO := &model.RefundOrder{
		RefundMode:                orderpb.RefundMode_REFUND_MODE_BY_PAYMENT,
		CurrencyCode:              "USD",
		RefundTotalAmount:         decimal.Zero,
		RefundItemSubTotal:        mustDecimal("26.49"), // Netting
		RefundDiscountAmount:      mustDecimal("0.58"),  // Netting
		RefundTipsAmount:          decimal.Zero,
		RefundConvenienceFee:      decimal.Zero,
		RefundTaxAmount:           mustDecimal("1.09"),
		RefundDepositAmount:       mustDecimal("27.00"),
		RefundDepositToTipsAmount: decimal.Zero,
	}
	expectedROIs := []*model.RefundOrderItem{
		{
			RefundItemMode: orderpb.RefundItemMode_REFUND_ITEM_MODE_BY_AMOUNT,
			RefundTax: model.RefundTax{
				ID:     1,
				Name:   "Consumption Tax",
				Rate:   mustDecimal("10"),
				Amount: mustDecimal("1.09"),
			},
			RefundTotalAmount:    mustDecimal("10.91"),
			RefundQuantity:       0,
			RefundAmount:         mustDecimal("11.49"),
			RefundDiscountAmount: mustDecimal("0.58"),
			RefundDepositAmount:  mustDecimal("12.00"),
		},
		{
			RefundItemMode:       orderpb.RefundItemMode_REFUND_ITEM_MODE_BY_AMOUNT,
			RefundTax:            model.RefundTax{},
			RefundTotalAmount:    mustDecimal("15.00"),
			RefundQuantity:       0,
			RefundAmount:         mustDecimal("15.00"),
			RefundDiscountAmount: decimal.Zero,
			RefundDepositAmount:  mustDecimal("15.00"),
		},
	}

	ast := assert.New(t)
	ast.NoError(err)
	ast.True(actual.IsFullyRefund)
	equalRefundOrder(ast, expectedRO, actual.RefundOrderDetail.RefundOrder)
	assert.Len(t, actual.RefundOrderDetail.RefundOrderItems, 2)
	equalRefundOrderItem(ast, expectedROIs[0], actual.RefundOrderDetail.RefundOrderItems[0])
	equalRefundOrderItem(ast, expectedROIs[1], actual.RefundOrderDetail.RefundOrderItems[1])
	assert.Empty(t, actual.RefundOrderDetail.RefundOrderPayments)
}

// Complex case:
// 1. Item A ($80) which has deposit (30%), discount (5%) and tax (10%);
// 2. Item B ($100) which has deposit (30%);
// 3. Item C ($120) which has no deposit;
// 4. The Order has tips ($10);
// 5. Both payments have convenience fee.
// Refunds:
// Refund $129.8 from the final payment.
func TestRefundHelper_RefundByPayment_Complex_RefundInitialFinalPayment(t *testing.T) {
	rh := NewRefundHelper(
		&model.OrderDetail{
			Order: &model.Order{
				ID:             1,
				Status:         orderpb.OrderStatus_COMPLETED,
				OrderVersion:   model.OrderVersionRefund,
				CurrencyCode:   "USD",
				TipsAmount:     mustDecimal("10.00"),
				TaxAmount:      mustDecimal("7.60"),
				DiscountAmount: mustDecimal("4.00"),
				ConvenienceFee: mustDecimal("9.45"),
				SubTotalAmount: mustDecimal("300.00"),
				TotalAmount:    mustDecimal("269.05"),
				PaidAmount:     mustDecimal("269.05"),
				RemainAmount:   decimal.Zero,
				RefundedAmount: decimal.Zero,
				DepositAmount:  mustDecimal("54.00"),
			},
			OrderItems: []*model.OrderItem{
				{
					ID: 11,
					Tax: model.Tax{
						ID:     1,
						Name:   "Consumption Tax",
						Rate:   mustDecimal("10"),
						Amount: mustDecimal("7.60"),
					},
					Quantity:               1,
					UnitPrice:              mustDecimal("80.00"),
					TipsAmount:             decimal.Zero,
					DiscountAmount:         mustDecimal("4.00"),
					SubTotalAmount:         mustDecimal("80.00"),
					TotalAmount:            mustDecimal("76.00"),
					RefundedAmount:         decimal.Zero,
					RefundedTaxAmount:      decimal.Zero,
					RefundedDiscountAmount: decimal.Zero,
					DepositAmount:          mustDecimal("24.00"),
				},
				{
					ID:                     12,
					Tax:                    model.Tax{},
					Quantity:               1,
					UnitPrice:              mustDecimal("100.00"),
					TipsAmount:             decimal.Zero,
					DiscountAmount:         decimal.Zero,
					SubTotalAmount:         mustDecimal("100.00"),
					TotalAmount:            mustDecimal("100.00"),
					RefundedAmount:         decimal.Zero,
					RefundedTaxAmount:      decimal.Zero,
					RefundedDiscountAmount: decimal.Zero,
					DepositAmount:          mustDecimal("30.00"),
				},
				{
					ID:                     13,
					Tax:                    model.Tax{},
					Quantity:               1,
					UnitPrice:              mustDecimal("120.00"),
					TipsAmount:             decimal.Zero,
					DiscountAmount:         decimal.Zero,
					SubTotalAmount:         mustDecimal("120.00"),
					TotalAmount:            mustDecimal("120.00"),
					RefundedAmount:         decimal.Zero,
					RefundedTaxAmount:      decimal.Zero,
					RefundedDiscountAmount: decimal.Zero,
					DepositAmount:          decimal.Zero,
				},
			},
			OrderPayments: []*model.OrderPayment{
				{
					ID:                     21,
					TotalAmount:            mustDecimal("269.05"),
					Amount:                 mustDecimal("269.05"),
					RefundedAmount:         decimal.Zero,
					ProcessingFee:          decimal.Zero,
					ConvenienceFee:         mustDecimal("9.45"),
					RefundedConvenienceFee: decimal.Zero,
					PaymentStatus:          orderpb.OrderPaymentStatus_ORDER_PAYMENT_STATUS_PAID,
				},
			},
		},
		nil,
		nil,
	)

	actual, err := rh.RefundByPayment(
		[]int64{21},
		mustDecimal("134.53"),
		&ordersvcpb.RefundOrderRequest_RefundByPayment_RefundAmountFlags{
			IsConvenienceFeeIncluded: true,
			IsDepositAmount:          proto.Bool(false),
		},
	)

	expectedRO := &model.RefundOrder{
		RefundMode:                orderpb.RefundMode_REFUND_MODE_BY_PAYMENT,
		CurrencyCode:              "USD",
		RefundTotalAmount:         mustDecimal("134.53"), // Fee: 4.73; tips: 5.00; Item total: 124.8
		RefundItemSubTotal:        mustDecimal("123.52"),
		RefundDiscountAmount:      mustDecimal("1.43"),
		RefundTipsAmount:          mustDecimal("5.00"),
		RefundConvenienceFee:      mustDecimal("4.73"),
		RefundTaxAmount:           mustDecimal("2.71"),
		RefundDepositAmount:       decimal.Zero,
		RefundDepositToTipsAmount: decimal.Zero,
	}
	expectedROIs := []*model.RefundOrderItem{
		{
			RefundItemMode: orderpb.RefundItemMode_REFUND_ITEM_MODE_BY_AMOUNT,
			RefundTax: model.RefundTax{
				ID:     1,
				Name:   "Consumption Tax",
				Rate:   mustDecimal("10"),
				Amount: mustDecimal("2.71"),
			},
			RefundTotalAmount:    mustDecimal("27.09"),
			RefundQuantity:       0,
			RefundAmount:         mustDecimal("28.52"),
			RefundDiscountAmount: mustDecimal("1.43"),
			RefundDepositAmount:  decimal.Zero,
		},
		{
			RefundItemMode:       orderpb.RefundItemMode_REFUND_ITEM_MODE_BY_AMOUNT,
			RefundTax:            model.RefundTax{},
			RefundTotalAmount:    mustDecimal("35.00"),
			RefundQuantity:       0,
			RefundAmount:         mustDecimal("35.00"),
			RefundDiscountAmount: decimal.Zero,
			RefundDepositAmount:  decimal.Zero,
		},
		{
			RefundItemMode:       orderpb.RefundItemMode_REFUND_ITEM_MODE_BY_AMOUNT,
			RefundTax:            model.RefundTax{},
			RefundTotalAmount:    mustDecimal("60.00"),
			RefundQuantity:       0,
			RefundAmount:         mustDecimal("60.00"),
			RefundDiscountAmount: decimal.Zero,
			RefundDepositAmount:  decimal.Zero,
		},
	}
	expectedROPs := []*model.RefundOrderPayment{
		{
			OrderPaymentID:       21,
			CurrencyCode:         "",
			RefundAmount:         mustDecimal("134.53"),
			RefundConvenienceFee: mustDecimal("4.73"),
		},
	}

	ast := assert.New(t)
	ast.NoError(err)
	ast.False(actual.IsFullyRefund)
	equalRefundOrder(ast, expectedRO, actual.RefundOrderDetail.RefundOrder)
	assert.Len(t, actual.RefundOrderDetail.RefundOrderItems, 3)
	equalRefundOrderItem(ast, expectedROIs[0], actual.RefundOrderDetail.RefundOrderItems[0])
	equalRefundOrderItem(ast, expectedROIs[1], actual.RefundOrderDetail.RefundOrderItems[1])
	equalRefundOrderItem(ast, expectedROIs[2], actual.RefundOrderDetail.RefundOrderItems[2])
	assert.Len(t, actual.RefundOrderDetail.RefundOrderPayments, 1)
	equalRefundOrderPayment(ast, expectedROPs[0], actual.RefundOrderDetail.RefundOrderPayments[0])
}

// Complex case:
// 1. Item A ($80) which has deposit (30%), discount (5%) and tax (10%);
// 2. Item B ($100) which has deposit (30%);
// 3. Item C ($120) which has no deposit;
// 4. The Order has tips ($10);
// 5. Both payments have convenience fee.
// Refunds:
// 1. Already refunded $129.8 from the final payment;
// 2. Refund the rest $129.8 from the final payment.
func TestRefundHelper_RefundByPayment_Complex_RefundRestFinalPayment(t *testing.T) {
	rh := NewRefundHelper(
		&model.OrderDetail{
			Order: &model.Order{
				ID:             1,
				Status:         orderpb.OrderStatus_COMPLETED,
				OrderVersion:   model.OrderVersionRefund,
				CurrencyCode:   "USD",
				TipsAmount:     mustDecimal("10.00"),
				TaxAmount:      mustDecimal("7.60"),
				DiscountAmount: mustDecimal("4.00"),
				ConvenienceFee: mustDecimal("9.45"),
				SubTotalAmount: mustDecimal("300.00"),
				TotalAmount:    mustDecimal("269.05"),
				PaidAmount:     mustDecimal("269.05"),
				RemainAmount:   decimal.Zero,
				RefundedAmount: mustDecimal("134.53"),
				DepositAmount:  mustDecimal("54.00"),
			},
			OrderItems: []*model.OrderItem{
				{
					ID: 11,
					Tax: model.Tax{
						ID:     1,
						Name:   "Consumption Tax",
						Rate:   mustDecimal("10"),
						Amount: mustDecimal("7.60"),
					},
					Quantity:               1,
					UnitPrice:              mustDecimal("80.00"),
					TipsAmount:             decimal.Zero,
					DiscountAmount:         mustDecimal("4.00"),
					SubTotalAmount:         mustDecimal("80.00"),
					TotalAmount:            mustDecimal("76.00"),
					RefundedAmount:         mustDecimal("27.09"),
					RefundedTaxAmount:      mustDecimal("2.71"),
					RefundedDiscountAmount: mustDecimal("1.43"),
					DepositAmount:          mustDecimal("24.00"),
					RefundedDepositAmount:  decimal.Zero,
				},
				{
					ID:                     12,
					Tax:                    model.Tax{},
					Quantity:               1,
					UnitPrice:              mustDecimal("100.00"),
					TipsAmount:             decimal.Zero,
					DiscountAmount:         decimal.Zero,
					SubTotalAmount:         mustDecimal("100.00"),
					TotalAmount:            mustDecimal("100.00"),
					RefundedAmount:         mustDecimal("35.00"),
					RefundedTaxAmount:      decimal.Zero,
					RefundedDiscountAmount: decimal.Zero,
					DepositAmount:          mustDecimal("30.00"),
					RefundedDepositAmount:  decimal.Zero,
				},
				{
					ID:                     13,
					Tax:                    model.Tax{},
					Quantity:               1,
					UnitPrice:              mustDecimal("120.00"),
					TipsAmount:             decimal.Zero,
					DiscountAmount:         decimal.Zero,
					SubTotalAmount:         mustDecimal("120.00"),
					TotalAmount:            mustDecimal("120.00"),
					RefundedAmount:         mustDecimal("60.00"),
					RefundedTaxAmount:      decimal.Zero,
					RefundedDiscountAmount: decimal.Zero,
					DepositAmount:          decimal.Zero,
					RefundedDepositAmount:  decimal.Zero,
				},
			},
			OrderPayments: []*model.OrderPayment{
				{
					ID:                     21,
					TotalAmount:            mustDecimal("269.05"),
					Amount:                 mustDecimal("269.05"),
					RefundedAmount:         mustDecimal("134.53"),
					ProcessingFee:          decimal.Zero,
					ConvenienceFee:         mustDecimal("9.45"),
					RefundedConvenienceFee: mustDecimal("4.73"),
					PaymentStatus:          orderpb.OrderPaymentStatus_ORDER_PAYMENT_STATUS_PAID,
				},
			},
		},
		[]*model.RefundOrderDetail{
			{
				RefundOrder: &model.RefundOrder{
					ID:                        1,
					OrderStatusSnapshot:       orderpb.OrderStatus_COMPLETED,
					RefundMode:                orderpb.RefundMode_REFUND_MODE_BY_PAYMENT,
					CurrencyCode:              "USD",
					RefundTotalAmount:         mustDecimal("134.53"), // Fee: 4.73; tips: 5.00; Item total: 124.8
					RefundItemSubTotal:        mustDecimal("123.52"),
					RefundDiscountAmount:      mustDecimal("1.43"),
					RefundTipsAmount:          mustDecimal("5.00"),
					RefundConvenienceFee:      mustDecimal("4.73"),
					RefundTaxAmount:           mustDecimal("2.71"),
					RefundDepositAmount:       decimal.Zero,
					RefundDepositToTipsAmount: decimal.Zero,
				},
				RefundOrderItems: []*model.RefundOrderItem{
					{
						ID:             1,
						RefundItemMode: orderpb.RefundItemMode_REFUND_ITEM_MODE_BY_AMOUNT,
						RefundTax: model.RefundTax{
							ID:     1,
							Name:   "Consumption Tax",
							Rate:   mustDecimal("10"),
							Amount: mustDecimal("2.71"),
						},
						RefundTotalAmount:    mustDecimal("27.09"),
						RefundQuantity:       0,
						RefundAmount:         mustDecimal("28.52"),
						RefundDiscountAmount: mustDecimal("1.43"),
						RefundDepositAmount:  decimal.Zero,
					},
					{
						ID:                   2,
						RefundItemMode:       orderpb.RefundItemMode_REFUND_ITEM_MODE_BY_AMOUNT,
						RefundTax:            model.RefundTax{},
						RefundTotalAmount:    mustDecimal("35.00"),
						RefundQuantity:       0,
						RefundAmount:         mustDecimal("35.00"),
						RefundDiscountAmount: decimal.Zero,
						RefundDepositAmount:  decimal.Zero,
					},
					{
						ID:                   3,
						RefundItemMode:       orderpb.RefundItemMode_REFUND_ITEM_MODE_BY_AMOUNT,
						RefundTax:            model.RefundTax{},
						RefundTotalAmount:    mustDecimal("60.00"),
						RefundQuantity:       0,
						RefundAmount:         mustDecimal("60.00"),
						RefundDiscountAmount: decimal.Zero,
						RefundDepositAmount:  decimal.Zero,
					},
				},
				RefundOrderPayments: []*model.RefundOrderPayment{
					{
						ID:                   1,
						OrderPaymentID:       21,
						CurrencyCode:         "",
						RefundAmount:         mustDecimal("134.53"),
						RefundConvenienceFee: mustDecimal("4.73"),
					},
				},
			},
		},
		nil,
	)

	actual, err := rh.RefundByPayment(
		[]int64{21},
		mustDecimal("134.52"),
		&ordersvcpb.RefundOrderRequest_RefundByPayment_RefundAmountFlags{
			IsConvenienceFeeIncluded: true,
			IsDepositAmount:          proto.Bool(false),
		},
	)

	expectedRO := &model.RefundOrder{
		RefundMode:                orderpb.RefundMode_REFUND_MODE_BY_PAYMENT,
		CurrencyCode:              "USD",
		RefundTotalAmount:         mustDecimal("134.52"), // Fee: 4.72; tips: 5.00; Item total: 124.8
		RefundItemSubTotal:        mustDecimal("123.52"),
		RefundDiscountAmount:      mustDecimal("1.43"),
		RefundTipsAmount:          mustDecimal("5.00"),
		RefundConvenienceFee:      mustDecimal("4.72"),
		RefundTaxAmount:           mustDecimal("2.71"),
		RefundDepositAmount:       decimal.Zero,
		RefundDepositToTipsAmount: decimal.Zero,
	}
	expectedROIs := []*model.RefundOrderItem{
		{
			RefundItemMode: orderpb.RefundItemMode_REFUND_ITEM_MODE_BY_AMOUNT,
			RefundTax: model.RefundTax{
				ID:     1,
				Name:   "Consumption Tax",
				Rate:   mustDecimal("10"),
				Amount: mustDecimal("2.71"),
			},
			RefundTotalAmount:    mustDecimal("27.09"),
			RefundQuantity:       0,
			RefundAmount:         mustDecimal("28.52"),
			RefundDiscountAmount: mustDecimal("1.43"),
			RefundDepositAmount:  decimal.Zero,
		},
		{
			RefundItemMode:       orderpb.RefundItemMode_REFUND_ITEM_MODE_BY_AMOUNT,
			RefundTax:            model.RefundTax{},
			RefundTotalAmount:    mustDecimal("35.00"),
			RefundQuantity:       0,
			RefundAmount:         mustDecimal("35.00"),
			RefundDiscountAmount: decimal.Zero,
			RefundDepositAmount:  decimal.Zero,
		},
		{
			RefundItemMode:       orderpb.RefundItemMode_REFUND_ITEM_MODE_BY_AMOUNT,
			RefundTax:            model.RefundTax{},
			RefundTotalAmount:    mustDecimal("60.00"),
			RefundQuantity:       0,
			RefundAmount:         mustDecimal("60.00"),
			RefundDiscountAmount: decimal.Zero,
			RefundDepositAmount:  decimal.Zero,
		},
	}
	expectedROPs := []*model.RefundOrderPayment{
		{
			OrderPaymentID:       21,
			CurrencyCode:         "",
			RefundAmount:         mustDecimal("134.52"),
			RefundConvenienceFee: mustDecimal("4.72"),
		},
	}

	ast := assert.New(t)
	ast.NoError(err)
	ast.False(actual.IsFullyRefund)
	equalRefundOrder(ast, expectedRO, actual.RefundOrderDetail.RefundOrder)
	assert.Len(t, actual.RefundOrderDetail.RefundOrderItems, 3)
	equalRefundOrderItem(ast, expectedROIs[0], actual.RefundOrderDetail.RefundOrderItems[0])
	equalRefundOrderItem(ast, expectedROIs[1], actual.RefundOrderDetail.RefundOrderItems[1])
	equalRefundOrderItem(ast, expectedROIs[2], actual.RefundOrderDetail.RefundOrderItems[2])
	assert.Len(t, actual.RefundOrderDetail.RefundOrderPayments, 1)
	equalRefundOrderPayment(ast, expectedROPs[0], actual.RefundOrderDetail.RefundOrderPayments[0])
}

// Complex case:
// 1. Item A ($80) which has deposit (30%), discount (5%) and tax (10%);
// 2. Item B ($100) which has deposit (30%);
// 3. Item C ($120) which has no deposit;
// 4. The Order has tips ($10);
// 5. Both payments have convenience fee.
// Refunds:
// 1. Already refunded $129.8 from the final payment;
// 2. Already refunded $54 from the deposit payment;
// 3. Refund the rest $129.8 from the final payment, fully refunding the order.
func TestRefundHelper_RefundByPayment_Complex_FullyRefundRestFinalPayment(t *testing.T) {
	rh := NewRefundHelper(
		&model.OrderDetail{
			Order: &model.Order{
				ID:             1,
				Status:         orderpb.OrderStatus_COMPLETED,
				OrderVersion:   model.OrderVersionRefund,
				CurrencyCode:   "USD",
				TipsAmount:     mustDecimal("10.00"),
				TaxAmount:      mustDecimal("7.60"),
				DiscountAmount: mustDecimal("4.00"),
				ConvenienceFee: mustDecimal("9.45"),
				SubTotalAmount: mustDecimal("300.00"),
				TotalAmount:    mustDecimal("269.05"),
				PaidAmount:     mustDecimal("269.05"),
				RemainAmount:   decimal.Zero,
				RefundedAmount: mustDecimal("134.53"),
				DepositAmount:  mustDecimal("54.00"),
			},
			OrderItems: []*model.OrderItem{
				{
					ID: 11,
					Tax: model.Tax{
						ID:     1,
						Name:   "Consumption Tax",
						Rate:   mustDecimal("10"),
						Amount: mustDecimal("7.60"),
					},
					Quantity:               1,
					UnitPrice:              mustDecimal("80.00"),
					TipsAmount:             decimal.Zero,
					DiscountAmount:         mustDecimal("4.00"),
					SubTotalAmount:         mustDecimal("80.00"),
					TotalAmount:            mustDecimal("76.00"),
					RefundedAmount:         mustDecimal("48.91"),
					RefundedTaxAmount:      mustDecimal("4.89"),
					RefundedDiscountAmount: mustDecimal("2.58"),
					DepositAmount:          mustDecimal("24.00"),
					RefundedDepositAmount:  mustDecimal("24.00"),
				},
				{
					ID:                     12,
					Tax:                    model.Tax{},
					Quantity:               1,
					UnitPrice:              mustDecimal("100.00"),
					TipsAmount:             decimal.Zero,
					DiscountAmount:         decimal.Zero,
					SubTotalAmount:         mustDecimal("100.00"),
					TotalAmount:            mustDecimal("100.00"),
					RefundedAmount:         mustDecimal("65.00"),
					RefundedTaxAmount:      decimal.Zero,
					RefundedDiscountAmount: decimal.Zero,
					DepositAmount:          mustDecimal("30.00"),
					RefundedDepositAmount:  mustDecimal("30.00"),
				},
				{
					ID:                     13,
					Tax:                    model.Tax{},
					Quantity:               1,
					UnitPrice:              mustDecimal("120.00"),
					TipsAmount:             decimal.Zero,
					DiscountAmount:         decimal.Zero,
					SubTotalAmount:         mustDecimal("120.00"),
					TotalAmount:            mustDecimal("120.00"),
					RefundedAmount:         mustDecimal("60.00"),
					RefundedTaxAmount:      decimal.Zero,
					RefundedDiscountAmount: decimal.Zero,
					DepositAmount:          decimal.Zero,
					RefundedDepositAmount:  decimal.Zero,
				},
			},
			OrderPayments: []*model.OrderPayment{
				{
					ID:                     21,
					TotalAmount:            mustDecimal("269.05"),
					Amount:                 mustDecimal("269.05"),
					RefundedAmount:         mustDecimal("134.53"),
					ProcessingFee:          decimal.Zero,
					ConvenienceFee:         mustDecimal("9.45"),
					RefundedConvenienceFee: mustDecimal("4.73"),
					PaymentStatus:          orderpb.OrderPaymentStatus_ORDER_PAYMENT_STATUS_PAID,
				},
			},
		},
		[]*model.RefundOrderDetail{
			{
				RefundOrder: &model.RefundOrder{
					ID:                        2,
					OrderStatusSnapshot:       orderpb.OrderStatus_COMPLETED,
					RefundMode:                orderpb.RefundMode_REFUND_MODE_BY_PAYMENT,
					CurrencyCode:              "USD",
					RefundTotalAmount:         decimal.Zero,
					RefundItemSubTotal:        mustDecimal("52.97"),
					RefundDiscountAmount:      mustDecimal("1.15"),
					RefundTipsAmount:          decimal.Zero,
					RefundConvenienceFee:      decimal.Zero,
					RefundTaxAmount:           mustDecimal("2.18"),
					RefundDepositAmount:       mustDecimal("54.00"),
					RefundDepositToTipsAmount: decimal.Zero,
				},
				RefundOrderItems: []*model.RefundOrderItem{
					{
						ID:             4,
						RefundItemMode: orderpb.RefundItemMode_REFUND_ITEM_MODE_BY_AMOUNT,
						RefundTax: model.RefundTax{
							ID:     1,
							Name:   "Consumption Tax",
							Rate:   mustDecimal("10"),
							Amount: mustDecimal("2.18"),
						},
						RefundTotalAmount:    mustDecimal("21.82"),
						RefundQuantity:       0,
						RefundAmount:         mustDecimal("22.97"),
						RefundDiscountAmount: mustDecimal("1.15"),
						RefundDepositAmount:  mustDecimal("24.00"),
					},
					{
						ID:                   5,
						RefundItemMode:       orderpb.RefundItemMode_REFUND_ITEM_MODE_BY_AMOUNT,
						RefundTax:            model.RefundTax{},
						RefundTotalAmount:    mustDecimal("30.00"),
						RefundQuantity:       0,
						RefundAmount:         mustDecimal("30.00"),
						RefundDiscountAmount: decimal.Zero,
						RefundDepositAmount:  mustDecimal("30.00"),
					},
				},
				RefundOrderPayments: nil,
			},
			{
				RefundOrder: &model.RefundOrder{
					ID:                        1,
					OrderStatusSnapshot:       orderpb.OrderStatus_COMPLETED,
					RefundMode:                orderpb.RefundMode_REFUND_MODE_BY_PAYMENT,
					CurrencyCode:              "USD",
					RefundTotalAmount:         mustDecimal("134.53"), // Fee: 4.73; tips: 5.00; Item total: 124.8
					RefundItemSubTotal:        mustDecimal("123.52"),
					RefundDiscountAmount:      mustDecimal("1.43"),
					RefundTipsAmount:          mustDecimal("5.00"),
					RefundConvenienceFee:      mustDecimal("4.73"),
					RefundTaxAmount:           mustDecimal("2.71"),
					RefundDepositAmount:       decimal.Zero,
					RefundDepositToTipsAmount: decimal.Zero,
				},
				RefundOrderItems: []*model.RefundOrderItem{
					{
						ID:             1,
						RefundItemMode: orderpb.RefundItemMode_REFUND_ITEM_MODE_BY_AMOUNT,
						RefundTax: model.RefundTax{
							ID:     1,
							Name:   "Consumption Tax",
							Rate:   mustDecimal("10"),
							Amount: mustDecimal("2.71"),
						},
						RefundTotalAmount:    mustDecimal("27.09"),
						RefundQuantity:       0,
						RefundAmount:         mustDecimal("28.52"),
						RefundDiscountAmount: mustDecimal("1.43"),
						RefundDepositAmount:  decimal.Zero,
					},
					{
						ID:                   2,
						RefundItemMode:       orderpb.RefundItemMode_REFUND_ITEM_MODE_BY_AMOUNT,
						RefundTax:            model.RefundTax{},
						RefundTotalAmount:    mustDecimal("35.00"),
						RefundQuantity:       0,
						RefundAmount:         mustDecimal("35.00"),
						RefundDiscountAmount: decimal.Zero,
						RefundDepositAmount:  decimal.Zero,
					},
					{
						ID:                   3,
						RefundItemMode:       orderpb.RefundItemMode_REFUND_ITEM_MODE_BY_AMOUNT,
						RefundTax:            model.RefundTax{},
						RefundTotalAmount:    mustDecimal("60.00"),
						RefundQuantity:       0,
						RefundAmount:         mustDecimal("60.00"),
						RefundDiscountAmount: decimal.Zero,
						RefundDepositAmount:  decimal.Zero,
					},
				},
				RefundOrderPayments: []*model.RefundOrderPayment{
					{
						ID:                   1,
						OrderPaymentID:       21,
						CurrencyCode:         "",
						RefundAmount:         mustDecimal("134.53"),
						RefundConvenienceFee: mustDecimal("4.73"),
					},
				},
			},
		},
		nil,
	)

	actual, err := rh.RefundByPayment(
		[]int64{21},
		mustDecimal("134.52"),
		&ordersvcpb.RefundOrderRequest_RefundByPayment_RefundAmountFlags{
			IsConvenienceFeeIncluded: true,
			IsDepositAmount:          proto.Bool(false),
		},
	)

	expectedRO := &model.RefundOrder{
		RefundMode:                orderpb.RefundMode_REFUND_MODE_BY_PAYMENT,
		CurrencyCode:              "USD",
		RefundTotalAmount:         mustDecimal("134.52"), // Fee: 4.72; tips: 5.00; Item total: 124.8
		RefundItemSubTotal:        mustDecimal("123.51"),
		RefundDiscountAmount:      mustDecimal("1.42"),
		RefundTipsAmount:          mustDecimal("5.00"),
		RefundConvenienceFee:      mustDecimal("4.72"),
		RefundTaxAmount:           mustDecimal("2.71"),
		RefundDepositAmount:       decimal.Zero,
		RefundDepositToTipsAmount: decimal.Zero,
	}
	expectedROIs := []*model.RefundOrderItem{
		{
			RefundItemMode: orderpb.RefundItemMode_REFUND_ITEM_MODE_BY_AMOUNT,
			RefundTax: model.RefundTax{
				ID:     1,
				Name:   "Consumption Tax",
				Rate:   mustDecimal("10"),
				Amount: mustDecimal("2.71"),
			},
			RefundTotalAmount:    mustDecimal("27.09"),
			RefundQuantity:       0,
			RefundAmount:         mustDecimal("28.51"),
			RefundDiscountAmount: mustDecimal("1.42"),
			RefundDepositAmount:  decimal.Zero,
		},
		{
			RefundItemMode:       orderpb.RefundItemMode_REFUND_ITEM_MODE_BY_AMOUNT,
			RefundTax:            model.RefundTax{},
			RefundTotalAmount:    mustDecimal("35.00"),
			RefundQuantity:       0,
			RefundAmount:         mustDecimal("35.00"),
			RefundDiscountAmount: decimal.Zero,
			RefundDepositAmount:  decimal.Zero,
		},
		{
			RefundItemMode:       orderpb.RefundItemMode_REFUND_ITEM_MODE_BY_AMOUNT,
			RefundTax:            model.RefundTax{},
			RefundTotalAmount:    mustDecimal("60.00"),
			RefundQuantity:       0,
			RefundAmount:         mustDecimal("60.00"),
			RefundDiscountAmount: decimal.Zero,
			RefundDepositAmount:  decimal.Zero,
		},
	}
	expectedROPs := []*model.RefundOrderPayment{
		{
			OrderPaymentID:       21,
			CurrencyCode:         "",
			RefundAmount:         mustDecimal("134.52"),
			RefundConvenienceFee: mustDecimal("4.72"),
		},
	}

	ast := assert.New(t)
	ast.NoError(err)
	ast.True(actual.IsFullyRefund)
	equalRefundOrder(ast, expectedRO, actual.RefundOrderDetail.RefundOrder)
	assert.Len(t, actual.RefundOrderDetail.RefundOrderItems, 3)
	equalRefundOrderItem(ast, expectedROIs[0], actual.RefundOrderDetail.RefundOrderItems[0])
	equalRefundOrderItem(ast, expectedROIs[1], actual.RefundOrderDetail.RefundOrderItems[1])
	equalRefundOrderItem(ast, expectedROIs[2], actual.RefundOrderDetail.RefundOrderItems[2])
	assert.Len(t, actual.RefundOrderDetail.RefundOrderPayments, 1)
	equalRefundOrderPayment(ast, expectedROPs[0], actual.RefundOrderDetail.RefundOrderPayments[0])
}

// Tips case:
// 1. Deposit order $90, $40 for item A, $50 for item B;
// 2. Remove item B;
// 2. Final order $10, $0 for item A (already fully deducted the deposit), $10 for tips.
// Refunds:
// Refund $9 from deposit order.
func TestRefundHelper_RefundByPayment_DepositDeductedToTips(t *testing.T) {
	rh := NewRefundHelper(
		&model.OrderDetail{
			Order: &model.Order{
				ID:                  1,
				Status:              orderpb.OrderStatus_COMPLETED,
				OrderVersion:        model.OrderVersionRefund,
				CurrencyCode:        "USD",
				TipsAmount:          mustDecimal("20.00"),
				TaxAmount:           decimal.Zero,
				DiscountAmount:      decimal.Zero,
				ConvenienceFee:      decimal.Zero,
				SubTotalAmount:      mustDecimal("100.00"),
				TotalAmount:         mustDecimal("10.00"),
				PaidAmount:          mustDecimal("10.00"),
				RemainAmount:        decimal.Zero,
				RefundedAmount:      decimal.Zero,
				DepositAmount:       mustDecimal("90.00"),
				DepositToTipsAmount: mustDecimal("10.00"),
			},
			OrderItems: []*model.OrderItem{
				{
					ID:                     11,
					Tax:                    model.Tax{},
					Quantity:               1,
					UnitPrice:              mustDecimal("80.00"),
					TipsAmount:             decimal.Zero,
					DiscountAmount:         decimal.Zero,
					SubTotalAmount:         mustDecimal("80.00"),
					TotalAmount:            mustDecimal("80.00"),
					RefundedAmount:         decimal.Zero,
					RefundedTaxAmount:      decimal.Zero,
					RefundedDiscountAmount: decimal.Zero,
					DepositAmount:          mustDecimal("80.00"),
				},
			},
			OrderPayments: []*model.OrderPayment{
				{
					ID:                     21,
					TotalAmount:            mustDecimal("10.00"),
					Amount:                 mustDecimal("10.00"),
					RefundedAmount:         decimal.Zero,
					ProcessingFee:          decimal.Zero,
					ConvenienceFee:         decimal.Zero,
					RefundedConvenienceFee: decimal.Zero,
					PaymentStatus:          orderpb.OrderPaymentStatus_ORDER_PAYMENT_STATUS_PAID,
				},
			},
		},
		nil,
		nil,
	)

	actual, err := rh.RefundByPayment(
		nil,
		mustDecimal("9.00"),
		&ordersvcpb.RefundOrderRequest_RefundByPayment_RefundAmountFlags{
			IsConvenienceFeeIncluded: true,
			IsDepositAmount:          proto.Bool(true),
		},
	)

	expectedRO := &model.RefundOrder{
		RefundMode:                orderpb.RefundMode_REFUND_MODE_BY_PAYMENT,
		CurrencyCode:              "USD",
		RefundTotalAmount:         decimal.Zero,
		RefundItemSubTotal:        mustDecimal("8.00"),
		RefundDiscountAmount:      decimal.Zero,
		RefundTipsAmount:          mustDecimal("1.00"),
		RefundConvenienceFee:      decimal.Zero,
		RefundTaxAmount:           decimal.Zero,
		RefundDepositAmount:       mustDecimal("9.00"),
		RefundDepositToTipsAmount: mustDecimal("1.00"),
	}
	expectedROIs := []*model.RefundOrderItem{
		{
			RefundItemMode:       orderpb.RefundItemMode_REFUND_ITEM_MODE_BY_AMOUNT,
			RefundTax:            model.RefundTax{},
			RefundTotalAmount:    mustDecimal("8.00"),
			RefundQuantity:       0,
			RefundAmount:         mustDecimal("8.00"),
			RefundDiscountAmount: decimal.Zero,
			RefundDepositAmount:  mustDecimal("8.00"),
		},
	}

	ast := assert.New(t)
	ast.NoError(err)
	ast.False(actual.IsFullyRefund)
	equalRefundOrder(ast, expectedRO, actual.RefundOrderDetail.RefundOrder)
	assert.Len(t, actual.RefundOrderDetail.RefundOrderItems, 1)
	equalRefundOrderItem(ast, expectedROIs[0], actual.RefundOrderDetail.RefundOrderItems[0])
	assert.Empty(t, actual.RefundOrderDetail.RefundOrderPayments)
}
