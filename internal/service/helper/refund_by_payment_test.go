package helper

import (
	"testing"

	"github.com/shopspring/decimal"
	"github.com/stretchr/testify/suite"

	orderpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/order/v1"
	ordersvcpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/order/v1"
	"github.com/MoeGolibrary/moego-svc-order-v2/internal/model"
)

type RefundByPaymentTestSuite struct {
	suite.Suite

	rh *RefundHelper
}

func TestRefundByPayment(t *testing.T) {
	suite.Run(t, new(RefundByPaymentTestSuite))
}

func (ts *RefundByPaymentTestSuite) SetupTest() {
	ts.rh = NewRefundHelper(
		&model.OrderDetail{
			Order: &model.Order{
				Status:          orderpb.OrderStatus_COMPLETED,
				TipsAmount:      ts.mustDecimal("10.00"),
				TaxAmount:       ts.mustDecimal("0.29"),
				DiscountAmount:  ts.mustDecimal("2.98"),
				ConvenienceFee:  ts.mustDecimal("4.09"),
				SubTotalAmount:  ts.mustDecimal("100.00"),
				TipsBasedAmount: ts.mustDecimal("40.00"),
				TotalAmount:     ts.mustDecimal("111.40"),
				PaidAmount:      ts.mustDecimal("111.40"),
				RefundedAmount:  ts.mustDecimal("0.00"),
			},
			OrderItems: []*model.OrderItem{
				{
					ID:             1,
					UnitPrice:      ts.mustDecimal("10.00"),
					Quantity:       1,
					TipsAmount:     ts.mustDecimal("10.00"),
					DiscountAmount: ts.mustDecimal("0.30"),
					SubTotalAmount: ts.mustDecimal("10.00"),
					TotalAmount:    ts.mustDecimal("9.70"),
					Tax: model.Tax{
						ID:     7623,
						Name:   "",
						Rate:   ts.mustDecimal("0.0000"),
						Amount: ts.mustDecimal("0.00"),
					},
					RefundedAmount:         ts.mustDecimal("0.00"),
					RefundedTaxAmount:      ts.mustDecimal("0.00"),
					RefundedDiscountAmount: ts.mustDecimal("0.00"),
				},
				{
					ID:             2,
					UnitPrice:      ts.mustDecimal("10.00"),
					Quantity:       1,
					TipsAmount:     ts.mustDecimal("0.00"),
					DiscountAmount: ts.mustDecimal("0.30"),
					SubTotalAmount: ts.mustDecimal("10.00"),
					TotalAmount:    ts.mustDecimal("9.70"),
					Tax: model.Tax{
						ID:     7625,
						Name:   "",
						Rate:   ts.mustDecimal("1.0000"),
						Amount: ts.mustDecimal("0.10"),
					},
					RefundedAmount:         ts.mustDecimal("0.00"),
					RefundedTaxAmount:      ts.mustDecimal("0.00"),
					RefundedDiscountAmount: ts.mustDecimal("0.00"),
				},
				{
					ID:             3,
					UnitPrice:      ts.mustDecimal("20.00"),
					Quantity:       1,
					TipsAmount:     ts.mustDecimal("0.00"),
					DiscountAmount: ts.mustDecimal("0.60"),
					SubTotalAmount: ts.mustDecimal("20.00"),
					TotalAmount:    ts.mustDecimal("19.40"),
					Tax: model.Tax{
						ID:     7625,
						Name:   "",
						Rate:   ts.mustDecimal("1.0000"),
						Amount: ts.mustDecimal("0.19"),
					},
					RefundedAmount:         ts.mustDecimal("0.00"),
					RefundedTaxAmount:      ts.mustDecimal("0.00"),
					RefundedDiscountAmount: ts.mustDecimal("0.00"),
				},
				{
					ID:             4,
					UnitPrice:      ts.mustDecimal("20.00"),
					Quantity:       1,
					TipsAmount:     ts.mustDecimal("0.00"),
					DiscountAmount: ts.mustDecimal("0.60"),
					SubTotalAmount: ts.mustDecimal("20.00"),
					TotalAmount:    ts.mustDecimal("19.40"),
					Tax: model.Tax{
						ID:     0,
						Name:   "",
						Rate:   ts.mustDecimal("0.0000"),
						Amount: ts.mustDecimal("0.00"),
					},
					RefundedAmount:         ts.mustDecimal("0.00"),
					RefundedTaxAmount:      ts.mustDecimal("0.00"),
					RefundedDiscountAmount: ts.mustDecimal("0.00"),
				},
				{
					ID:             5,
					UnitPrice:      ts.mustDecimal("20.00"),
					Quantity:       2,
					TipsAmount:     ts.mustDecimal("0.00"),
					DiscountAmount: ts.mustDecimal("1.18"),
					SubTotalAmount: ts.mustDecimal("40.00"),
					TotalAmount:    ts.mustDecimal("38.82"),
					Tax: model.Tax{
						ID:     0,
						Name:   "",
						Rate:   ts.mustDecimal("0.0000"),
						Amount: ts.mustDecimal("0.00"),
					},
					RefundedAmount:         ts.mustDecimal("0.00"),
					RefundedTaxAmount:      ts.mustDecimal("0.00"),
					RefundedDiscountAmount: ts.mustDecimal("0.00"),
				},
			},
			OrderPayments: []*model.OrderPayment{
				{
					ID:                      11,
					TotalAmount:             ts.mustDecimal("111.40"),
					Amount:                  ts.mustDecimal("107.31"),
					PaymentTips:             ts.mustDecimal("-1.00"),
					PaymentTipsBeforeCreate: ts.mustDecimal("-1.00"),
					PaymentTipsAfterCreate:  ts.mustDecimal("-1.00"),
					ConvenienceFee:          ts.mustDecimal("4.09"),
					RefundedAmount:          ts.mustDecimal("0.00"),
					RefundedConvenienceFee:  ts.mustDecimal("0.00"),
					PaymentStatus:           orderpb.OrderPaymentStatus_ORDER_PAYMENT_STATUS_PAID,
				},
			},
		}, nil, nil,
	)
}

func (ts *RefundByPaymentTestSuite) TestRefundCompletedOrderByPayment_HUAZI_001() {
	expectedRefundOrder := &model.RefundOrder{
		RefundMode:           orderpb.RefundMode_REFUND_MODE_BY_PAYMENT,
		OrderStatusSnapshot:  orderpb.OrderStatus_COMPLETED,
		RefundTotalAmount:    ts.mustDecimal("10.00"),
		RefundItemSubTotal:   ts.mustDecimal("8.97"),
		RefundDiscountAmount: ts.mustDecimal("0.27"),
		RefundTipsAmount:     ts.mustDecimal("0.90"),
		RefundConvenienceFee: ts.mustDecimal("0.37"),
		RefundTaxAmount:      ts.mustDecimal("0.03"),
	}
	expectedRefundOrderPayment := &model.RefundOrderPayment{
		RefundAmount:         ts.mustDecimal("10.00"),
		RefundConvenienceFee: ts.mustDecimal("0.37"),
	}
	expectedRefundOrderItems := []*model.RefundOrderItem{
		{
			OrderItemID:          1,
			RefundItemMode:       orderpb.RefundItemMode_REFUND_ITEM_MODE_BY_AMOUNT,
			RefundTax:            model.RefundTax{Amount: ts.mustDecimal("0.00")},
			RefundTotalAmount:    ts.mustDecimal("0.87"),
			RefundQuantity:       0,
			RefundAmount:         ts.mustDecimal("0.90"),
			RefundDiscountAmount: ts.mustDecimal("0.03"),
		},
		{
			OrderItemID:          2,
			RefundItemMode:       orderpb.RefundItemMode_REFUND_ITEM_MODE_BY_AMOUNT,
			RefundTax:            model.RefundTax{Amount: ts.mustDecimal("0.01")},
			RefundTotalAmount:    ts.mustDecimal("0.87"),
			RefundQuantity:       0,
			RefundAmount:         ts.mustDecimal("0.90"),
			RefundDiscountAmount: ts.mustDecimal("0.03"),
		},
		{
			OrderItemID:          4,
			RefundItemMode:       orderpb.RefundItemMode_REFUND_ITEM_MODE_BY_AMOUNT,
			RefundTax:            model.RefundTax{Amount: ts.mustDecimal("0.00")},
			RefundTotalAmount:    ts.mustDecimal("1.74"),
			RefundQuantity:       0,
			RefundAmount:         ts.mustDecimal("1.79"),
			RefundDiscountAmount: ts.mustDecimal("0.05"),
		},
		{
			OrderItemID:          3,
			RefundItemMode:       orderpb.RefundItemMode_REFUND_ITEM_MODE_BY_AMOUNT,
			RefundTax:            model.RefundTax{Amount: ts.mustDecimal("0.02")},
			RefundTotalAmount:    ts.mustDecimal("1.74"),
			RefundQuantity:       0,
			RefundAmount:         ts.mustDecimal("1.79"),
			RefundDiscountAmount: ts.mustDecimal("0.05"),
		},
		{
			OrderItemID:          5,
			RefundItemMode:       orderpb.RefundItemMode_REFUND_ITEM_MODE_BY_AMOUNT,
			RefundTax:            model.RefundTax{Amount: ts.mustDecimal("0.00")},
			RefundTotalAmount:    ts.mustDecimal("3.48"),
			RefundQuantity:       0,
			RefundAmount:         ts.mustDecimal("3.59"),
			RefundDiscountAmount: ts.mustDecimal("0.11"),
		},
	}

	actual, err := ts.rh.RefundByPayment(
		[]int64{11},
		ts.mustDecimal("10.00"),
		&ordersvcpb.RefundOrderRequest_RefundByPayment_RefundAmountFlags{IsConvenienceFeeIncluded: true},
	)
	ts.Require().NoError(err)

	// 验证 Refund Order Payments
	ts.Require().Len(actual.RefundOrderDetail.RefundOrderPayments, 1)
	ts.refundOrderPaymentEqual(expectedRefundOrderPayment, actual.RefundOrderDetail.RefundOrderPayments[0])

	// 验证 Refund Order Item
	ts.Require().Len(actual.RefundOrderDetail.RefundOrderItems, len(expectedRefundOrderItems))

	// 顺序也是校验的一部分.
	// 因为需要按可退金额从小到大排序处理.
	for i := 0; i < len(expectedRefundOrderItems); i++ {
		ts.refundOrderItemEqual(expectedRefundOrderItems[i], actual.RefundOrderDetail.RefundOrderItems[i])
	}

	// 验证 Refund Order
	ts.refundOrderEqual(expectedRefundOrder, actual.RefundOrderDetail.RefundOrder)
}

func (ts *RefundByPaymentTestSuite) TestRefundUncompletedOrderByPayment_001() {
	// 通过 PaymentActivity 部分退一笔关单前 FullyPaid 的单.
	ts.rh = NewRefundHelper(
		&model.OrderDetail{
			Order: &model.Order{
				Status:          orderpb.OrderStatus_PROCESSING,
				TipsAmount:      ts.mustDecimal("0.00"),
				TaxAmount:       ts.mustDecimal("0.00"),
				DiscountAmount:  ts.mustDecimal("0.00"),
				ConvenienceFee:  ts.mustDecimal("3.83"),
				SubTotalAmount:  ts.mustDecimal("100.00"),
				TipsBasedAmount: ts.mustDecimal("100.00"),
				TotalAmount:     ts.mustDecimal("103.83"),
				PaidAmount:      ts.mustDecimal("103.83"),
				RefundedAmount:  ts.mustDecimal("0.00"),
			},
			OrderItems: []*model.OrderItem{
				{
					ID:             1,
					UnitPrice:      ts.mustDecimal("100.00"),
					Quantity:       1,
					TipsAmount:     ts.mustDecimal("100.00"),
					DiscountAmount: ts.mustDecimal("0.00"),
					SubTotalAmount: ts.mustDecimal("100.00"),
					TotalAmount:    ts.mustDecimal("100.00"),
					Tax: model.Tax{
						ID:     7623,
						Name:   "",
						Rate:   ts.mustDecimal("0.0000"),
						Amount: ts.mustDecimal("0.00"),
					},
					RefundedAmount:         ts.mustDecimal("0.00"),
					RefundedTaxAmount:      ts.mustDecimal("0.00"),
					RefundedDiscountAmount: ts.mustDecimal("0.00"),
				},
			},
			OrderPayments: []*model.OrderPayment{
				{
					ID:                      11,
					TotalAmount:             ts.mustDecimal("103.83"),
					Amount:                  ts.mustDecimal("100.00"),
					PaymentTips:             ts.mustDecimal("-1.00"),
					PaymentTipsBeforeCreate: ts.mustDecimal("-1.00"),
					PaymentTipsAfterCreate:  ts.mustDecimal("-1.00"),
					ConvenienceFee:          ts.mustDecimal("3.83"),
					RefundedAmount:          ts.mustDecimal("0.00"),
					RefundedConvenienceFee:  ts.mustDecimal("0.00"),
					PaymentStatus:           orderpb.OrderPaymentStatus_ORDER_PAYMENT_STATUS_PAID,
				},
			},
		}, nil, nil,
	)

	expectedRefundOrder := &model.RefundOrder{
		RefundMode:           orderpb.RefundMode_REFUND_MODE_BY_PAYMENT,
		OrderStatusSnapshot:  orderpb.OrderStatus_PROCESSING,
		RefundTotalAmount:    ts.mustDecimal("103.81"),
		RefundItemSubTotal:   ts.mustDecimal("0.00"),
		RefundDiscountAmount: ts.mustDecimal("0.00"),
		RefundTipsAmount:     ts.mustDecimal("0.00"),
		RefundConvenienceFee: ts.mustDecimal("3.83"),
		RefundTaxAmount:      ts.mustDecimal("0.00"),
	}

	expectedRefundOrderPayment := &model.RefundOrderPayment{
		RefundAmount:         ts.mustDecimal("103.81"),
		RefundConvenienceFee: ts.mustDecimal("3.83"),
	}

	actual, err := ts.rh.RefundByPayment(
		[]int64{11}, ts.mustDecimal("103.81"),
		&ordersvcpb.RefundOrderRequest_RefundByPayment_RefundAmountFlags{IsConvenienceFeeIncluded: true},
	)

	ts.Require().NoError(err)

	// 验证不是全退.
	ts.Require().False(actual.IsFullyRefund)

	// 验证 Refund Order Payments.
	ts.Require().Len(actual.RefundOrderDetail.RefundOrderPayments, 1)
	ts.refundOrderPaymentEqual(expectedRefundOrderPayment, actual.RefundOrderDetail.RefundOrderPayments[0])

	// 验证 Refund Order Item.
	// 关单前不分摊到 Item.
	ts.Require().Len(actual.RefundOrderDetail.RefundOrderItems, 0)

	// 验证 Refund Order
	ts.refundOrderEqual(expectedRefundOrder, actual.RefundOrderDetail.RefundOrder)
}

func (ts *RefundByPaymentTestSuite) TestRefundUncompletedOrderByPayment_002() {
	// 通过 PaymentActivity 全退一笔退过的 Payment.
	ts.rh = NewRefundHelper(
		&model.OrderDetail{
			Order: &model.Order{
				Status:          orderpb.OrderStatus_PROCESSING,
				TipsAmount:      ts.mustDecimal("0.00"),
				DiscountAmount:  ts.mustDecimal("0.00"),
				ConvenienceFee:  ts.mustDecimal("0.00"),
				SubTotalAmount:  ts.mustDecimal("100.00"),
				TipsBasedAmount: ts.mustDecimal("100.00"),
				TotalAmount:     ts.mustDecimal("100.00"),
				PaidAmount:      ts.mustDecimal("0.02"),
				RefundedAmount:  ts.mustDecimal("0.00"),
			},
			OrderItems: []*model.OrderItem{
				{
					ID:             1,
					UnitPrice:      ts.mustDecimal("100.00"),
					Quantity:       1,
					TipsAmount:     ts.mustDecimal("100.00"),
					DiscountAmount: ts.mustDecimal("0.00"),
					SubTotalAmount: ts.mustDecimal("100.00"),
					TotalAmount:    ts.mustDecimal("100.00"),
					Tax: model.Tax{
						ID:     7623,
						Name:   "",
						Rate:   ts.mustDecimal("0.0000"),
						Amount: ts.mustDecimal("0.00"),
					},
					RefundedAmount:         ts.mustDecimal("0.00"),
					RefundedTaxAmount:      ts.mustDecimal("0.00"),
					RefundedDiscountAmount: ts.mustDecimal("0.00"),
				},
			},
			OrderPayments: []*model.OrderPayment{
				{
					ID:                      11,
					TotalAmount:             ts.mustDecimal("103.83"),
					Amount:                  ts.mustDecimal("100.00"),
					PaymentTips:             ts.mustDecimal("-1.00"),
					PaymentTipsBeforeCreate: ts.mustDecimal("-1.00"),
					PaymentTipsAfterCreate:  ts.mustDecimal("-1.00"),
					ConvenienceFee:          ts.mustDecimal("3.83"),
					RefundedAmount:          ts.mustDecimal("103.81"),
					RefundedConvenienceFee:  ts.mustDecimal("3.83"),
					PaymentStatus:           orderpb.OrderPaymentStatus_ORDER_PAYMENT_STATUS_PAID,
				},
			},
		},
		[]*model.RefundOrderDetail{
			{
				RefundOrder: &model.RefundOrder{
					OrderStatusSnapshot:  orderpb.OrderStatus_PROCESSING,
					RefundTotalAmount:    ts.mustDecimal("103.81"),
					RefundConvenienceFee: ts.mustDecimal("3.83"),
					RefundItemSubTotal:   ts.mustDecimal("0.00"),
					RefundDiscountAmount: ts.mustDecimal("0.00"),
					RefundTipsAmount:     ts.mustDecimal("0.00"),
					RefundTaxAmount:      ts.mustDecimal("0.00"),
				},
			},
		}, nil,
	)

	expectedRefundOrder := &model.RefundOrder{
		RefundMode:           orderpb.RefundMode_REFUND_MODE_BY_PAYMENT,
		OrderStatusSnapshot:  orderpb.OrderStatus_PROCESSING,
		RefundTotalAmount:    ts.mustDecimal("0.02"),
		RefundItemSubTotal:   ts.mustDecimal("0.00"),
		RefundDiscountAmount: ts.mustDecimal("0.00"),
		RefundTipsAmount:     ts.mustDecimal("0.00"),
		RefundConvenienceFee: ts.mustDecimal("0.00"),
		RefundTaxAmount:      ts.mustDecimal("0.00"),
	}

	expectedRefundOrderPayment := &model.RefundOrderPayment{
		RefundAmount:         ts.mustDecimal("0.02"),
		RefundConvenienceFee: ts.mustDecimal("0.00"),
	}

	actual, err := ts.rh.RefundByPayment(
		[]int64{11}, ts.mustDecimal("0.02"),
		&ordersvcpb.RefundOrderRequest_RefundByPayment_RefundAmountFlags{IsConvenienceFeeIncluded: true},
	)

	ts.Require().NoError(err)

	// 验证全退判定.
	ts.Require().True(actual.IsFullyRefund)

	// 验证 Refund Order Payments
	ts.Require().Len(actual.RefundOrderDetail.RefundOrderPayments, 1)
	ts.refundOrderPaymentEqual(expectedRefundOrderPayment, actual.RefundOrderDetail.RefundOrderPayments[0])

	// 验证 Refund Order Item
	// 关单前不分摊到 Item.
	ts.Require().Len(actual.RefundOrderDetail.RefundOrderItems, 0)

	// 验证 Refund Order
	ts.refundOrderEqual(expectedRefundOrder, actual.RefundOrderDetail.RefundOrder)
}

func (ts *RefundByPaymentTestSuite) TestRefundUncompletedOrderByPayment_HUAZI_001() {
	// 通过 PaymentActivity 全退一笔 Partial Paid 但是强制关单的单.
	ts.rh = NewRefundHelper(
		&model.OrderDetail{
			Order: &model.Order{
				Status:          orderpb.OrderStatus_PROCESSING,
				TipsAmount:      ts.mustDecimal("0.00"),
				TaxAmount:       ts.mustDecimal("0.00"),
				DiscountAmount:  ts.mustDecimal("0.00"),
				ConvenienceFee:  ts.mustDecimal("3.83"),
				SubTotalAmount:  ts.mustDecimal("100.00"),
				TipsBasedAmount: ts.mustDecimal("100.00"),
				TotalAmount:     ts.mustDecimal("103.83"),
				PaidAmount:      ts.mustDecimal("103.82"), // Partial Paid.
				RefundedAmount:  ts.mustDecimal("0.00"),
			},
			OrderItems: []*model.OrderItem{
				{
					ID:             1,
					UnitPrice:      ts.mustDecimal("100.00"),
					Quantity:       1,
					TipsAmount:     ts.mustDecimal("100.00"),
					DiscountAmount: ts.mustDecimal("0.00"),
					SubTotalAmount: ts.mustDecimal("100.00"),
					TotalAmount:    ts.mustDecimal("100.00"),
					Tax: model.Tax{
						ID:     7623,
						Name:   "",
						Rate:   ts.mustDecimal("0.0000"),
						Amount: ts.mustDecimal("0.00"),
					},
					RefundedAmount:         ts.mustDecimal("0.00"),
					RefundedTaxAmount:      ts.mustDecimal("0.00"),
					RefundedDiscountAmount: ts.mustDecimal("0.00"),
				},
			},
			OrderPayments: []*model.OrderPayment{
				{
					ID:                      11,
					TotalAmount:             ts.mustDecimal("103.82"),
					Amount:                  ts.mustDecimal("100.00"),
					PaymentTips:             ts.mustDecimal("-1.00"),
					PaymentTipsBeforeCreate: ts.mustDecimal("-1.00"),
					PaymentTipsAfterCreate:  ts.mustDecimal("-1.00"),
					ConvenienceFee:          ts.mustDecimal("3.82"),
					RefundedAmount:          ts.mustDecimal("0.00"),
					RefundedConvenienceFee:  ts.mustDecimal("0.00"),
					PaymentStatus:           orderpb.OrderPaymentStatus_ORDER_PAYMENT_STATUS_PAID,
				},
			},
		}, nil, nil,
	)

	expectedRefundOrder := &model.RefundOrder{
		RefundMode:           orderpb.RefundMode_REFUND_MODE_BY_PAYMENT,
		OrderStatusSnapshot:  orderpb.OrderStatus_PROCESSING,
		RefundTotalAmount:    ts.mustDecimal("103.82"),
		RefundItemSubTotal:   ts.mustDecimal("0.00"),
		RefundDiscountAmount: ts.mustDecimal("0.00"),
		RefundTipsAmount:     ts.mustDecimal("0.00"),
		RefundConvenienceFee: ts.mustDecimal("3.83"),
		RefundTaxAmount:      ts.mustDecimal("0.00"),
	}

	expectedRefundOrderPayment := &model.RefundOrderPayment{
		RefundAmount:         ts.mustDecimal("103.82"),
		RefundConvenienceFee: ts.mustDecimal("3.82"),
	}

	actual, err := ts.rh.RefundByPayment(
		[]int64{11}, ts.mustDecimal("103.82"),
		&ordersvcpb.RefundOrderRequest_RefundByPayment_RefundAmountFlags{IsConvenienceFeeIncluded: true},
	)

	ts.Require().NoError(err)

	// 验证全退.
	ts.Require().True(actual.IsFullyRefund)

	// 验证 Refund Order Payments.
	ts.Require().Len(actual.RefundOrderDetail.RefundOrderPayments, 1)
	ts.refundOrderPaymentEqual(expectedRefundOrderPayment, actual.RefundOrderDetail.RefundOrderPayments[0])

	// 验证 Refund Order Item.
	// 关单前不分摊到 Item.
	ts.Require().Len(actual.RefundOrderDetail.RefundOrderItems, 0)

	// 验证 Refund Order
	ts.refundOrderEqual(expectedRefundOrder, actual.RefundOrderDetail.RefundOrder)
}

func (ts *RefundByPaymentTestSuite) TestSplitRefundPaymentAmountToOrderItems_HUAZI_001() {
	// 97.31 Item总应收
	// 1.88  Item应退
	// ID -- 税后应收 -- 占比      -- 分摊退 -- 税前折后 -- Subtotal  -- 退折    -- 退税
	// 1  -- 9.70    -- 0.099681 -- 0.19  -- 0.19    -- 0.20      -- 0.01   -- 0.00
	// 2  -- 9.80    -- 0.100709 -- 0.19  -- 0.19    -- 0.20      -- 0.01   -- 0.00
	// 4  -- 19.40   -- 0.199363 -- 0.37  -- 0.37    -- 0.38      -- 0.01   -- 0.00
	// 3  -- 19.59   -- 0.201315 -- 0.38  -- 0.38    -- 0.39      -- 0.01   -- 0.00
	// 5  -- 38.82   -- 0.398931 -- 0.75  -- 0.75    -- 0.76      -- 0.02   -- 0.00
	expectedRefundOrderItems := []*model.RefundOrderItem{
		{
			OrderItemID:          1,
			RefundItemMode:       orderpb.RefundItemMode_REFUND_ITEM_MODE_BY_AMOUNT,
			RefundTax:            model.RefundTax{Amount: ts.mustDecimal("0.00")},
			RefundTotalAmount:    ts.mustDecimal("0.19"),
			RefundQuantity:       0,
			RefundAmount:         ts.mustDecimal("0.20"),
			RefundDiscountAmount: ts.mustDecimal("0.01"),
		},
		{
			OrderItemID:          2,
			RefundItemMode:       orderpb.RefundItemMode_REFUND_ITEM_MODE_BY_AMOUNT,
			RefundTax:            model.RefundTax{Amount: ts.mustDecimal("0.00")},
			RefundTotalAmount:    ts.mustDecimal("0.19"),
			RefundQuantity:       0,
			RefundAmount:         ts.mustDecimal("0.20"),
			RefundDiscountAmount: ts.mustDecimal("0.01"),
		},
		{
			OrderItemID:          4,
			RefundItemMode:       orderpb.RefundItemMode_REFUND_ITEM_MODE_BY_AMOUNT,
			RefundTax:            model.RefundTax{Amount: ts.mustDecimal("0.00")},
			RefundTotalAmount:    ts.mustDecimal("0.37"),
			RefundQuantity:       0,
			RefundAmount:         ts.mustDecimal("0.38"),
			RefundDiscountAmount: ts.mustDecimal("0.01"),
		},
		{
			OrderItemID:          3,
			RefundItemMode:       orderpb.RefundItemMode_REFUND_ITEM_MODE_BY_AMOUNT,
			RefundTax:            model.RefundTax{Amount: ts.mustDecimal("0.00")},
			RefundTotalAmount:    ts.mustDecimal("0.38"),
			RefundQuantity:       0,
			RefundAmount:         ts.mustDecimal("0.39"),
			RefundDiscountAmount: ts.mustDecimal("0.01"),
		},
		{
			OrderItemID:          5,
			RefundItemMode:       orderpb.RefundItemMode_REFUND_ITEM_MODE_BY_AMOUNT,
			RefundTax:            model.RefundTax{Amount: ts.mustDecimal("0.00")},
			RefundTotalAmount:    ts.mustDecimal("0.75"),
			RefundQuantity:       0,
			RefundAmount:         ts.mustDecimal("0.77"),
			RefundDiscountAmount: ts.mustDecimal("0.02"),
		},
	}

	actualItems, err := ts.rh.splitRefundPaymentAmountToItem(ts.mustDecimal("1.88"), false, false)
	ts.Require().NoError(err)

	// 验证 Refund Order Item
	ts.Require().Len(actualItems, len(expectedRefundOrderItems))

	// 顺序也是校验的一部分.
	// 需要按可退金额从小到大排序处理.
	for i := 0; i < len(expectedRefundOrderItems); i++ {
		ts.refundOrderItemEqual(expectedRefundOrderItems[i], actualItems[i])
	}
}

func (ts *RefundByPaymentTestSuite) refundOrderEqual(expect, actual *model.RefundOrder) {
	ts.T().Helper()

	ts.Equal(expect.RefundMode, actual.RefundMode)
	ts.Equal(expect.OrderStatusSnapshot, actual.OrderStatusSnapshot)
	ts.Equal(expect.RefundTotalAmount.String(), actual.RefundTotalAmount.String())
	ts.Equal(expect.RefundItemSubTotal.String(), actual.RefundItemSubTotal.String())
	ts.Equal(expect.RefundDiscountAmount.String(), actual.RefundDiscountAmount.String())
	ts.Equal(expect.RefundTipsAmount.String(), actual.RefundTipsAmount.String())
	ts.Equal(expect.RefundTaxAmount.String(), actual.RefundTaxAmount.String())
	ts.Equal(expect.RefundConvenienceFee.String(), actual.RefundConvenienceFee.String())
}

func (ts *RefundByPaymentTestSuite) refundOrderPaymentEqual(expect, actual *model.RefundOrderPayment) {
	ts.T().Helper()

	ts.Equal(expect.RefundAmount.String(), actual.RefundAmount.String())
	ts.Equal(expect.RefundConvenienceFee.String(), actual.RefundConvenienceFee.String())
}

func (ts *RefundByPaymentTestSuite) refundOrderItemEqual(expect, actual *model.RefundOrderItem) {
	ts.T().Helper()

	ts.Require().Equalf(
		expect.OrderItemID, actual.OrderItemID,
		"orderItemID: %d", expect.OrderItemID,
	)
	ts.Require().Equalf(
		expect.RefundItemMode, actual.RefundItemMode,
		"orderItemID: %d", expect.OrderItemID,
	)
	ts.Require().Equalf(
		expect.RefundTax.Amount.String(), actual.RefundTax.Amount.String(),
		"orderItemID: %d", expect.OrderItemID,
	)
	ts.Require().Equalf(
		expect.RefundTotalAmount.String(), actual.RefundTotalAmount.String(),
		"orderItemID: %d", expect.OrderItemID,
	)
	ts.Require().Equalf(
		expect.RefundQuantity, actual.RefundQuantity,
		"orderItemID: %d", expect.OrderItemID,
	)
	ts.Require().Equalf(
		expect.RefundAmount.String(), actual.RefundAmount.String(),
		"orderItemID: %d", expect.OrderItemID,
	)
	ts.Require().Equalf(
		expect.RefundDiscountAmount.String(), actual.RefundDiscountAmount.String(),
		"orderItemID: %d", expect.OrderItemID,
	)
}

func (ts *RefundByPaymentTestSuite) mustDecimal(str string) decimal.Decimal {
	dec, err := decimal.NewFromString(str)
	ts.Require().NoError(err)

	return dec
}
