package helper

import (
	"testing"

	"github.com/shopspring/decimal"

	appointment "github.com/MoeGolibrary/moego-svc-order-v2/internal/repo/grooming"
)

func TestBuildSplitRateAndAmount(t *testing.T) {
	type args struct {
		amount                   decimal.Decimal
		groomingPetDetailDTOList []*appointment.PetDetailDTO
	}

	tests := []struct {
		name string
		args args
		want map[int64]Pair
	}{
		{
			name: "Basic case",
			args: args{
				amount: decimal.NewFromFloat(100.00),
				groomingPetDetailDTOList: []*appointment.PetDetailDTO{
					{
						ID:           10001,
						StaffID:      1,
						ServicePrice: decimal.NewFromFloat(50.00),
						OperationList: []*appointment.ServiceOperationDTO{
							{StaffID: 1, PriceRatio: decimal.NewFromFloat(1)},
						},
					},
					{
						ID:           10002,
						StaffID:      2,
						ServicePrice: decimal.NewFromFloat(50.00),
						OperationList: []*appointment.ServiceOperationDTO{
							{StaffID: 2, PriceRatio: decimal.NewFromFloat(1)},
						},
					},
				},
			},
			want: map[int64]Pair{
				1: {SplitRate: decimal.NewFromFloat(0.5), Amount: decimal.NewFromFloat(50.00)},
				2: {SplitRate: decimal.NewFromFloat(0.5), Amount: decimal.NewFromFloat(50.00)},
			},
		},
		{
			name: "Basic case 3/7",
			args: args{
				amount: decimal.NewFromFloat(10.00),
				groomingPetDetailDTOList: []*appointment.PetDetailDTO{
					{
						ID:           10001,
						StaffID:      1,
						ServicePrice: decimal.NewFromFloat(30.00),
					},
					{
						ID:           10002,
						StaffID:      2,
						ServicePrice: decimal.NewFromFloat(70.00),
					},
				},
			},
			want: map[int64]Pair{
				1: {SplitRate: decimal.NewFromFloat(0.3), Amount: decimal.NewFromFloat(3)},
				2: {SplitRate: decimal.NewFromFloat(0.7), Amount: decimal.NewFromFloat(7)},
			},
		},
		{
			name: "With unassigned staff services",
			args: args{
				amount: decimal.NewFromFloat(10.00),
				groomingPetDetailDTOList: []*appointment.PetDetailDTO{
					{
						ID:           10001,
						StaffID:      1,
						ServicePrice: decimal.NewFromFloat(30.00),
					},
					{
						ID:           10002,
						StaffID:      0,
						ServicePrice: decimal.NewFromFloat(70.00),
					},
					{
						ID:           10003,
						StaffID:      3,
						ServicePrice: decimal.NewFromFloat(70.00),
					},
				},
			},
			want: map[int64]Pair{
				1: {SplitRate: decimal.NewFromFloat(0.30), Amount: decimal.NewFromFloat(3)},
				3: {SplitRate: decimal.NewFromFloat(0.70), Amount: decimal.NewFromFloat(7)},
			},
		},
		{
			name: "With netting",
			args: args{
				amount: decimal.NewFromFloat(10.00),
				groomingPetDetailDTOList: []*appointment.PetDetailDTO{
					{
						ID:           10001,
						StaffID:      1,
						ServicePrice: decimal.NewFromFloat(30.00),
					},
					{
						ID:           10002,
						StaffID:      2,
						ServicePrice: decimal.NewFromFloat(30.00),
					},
					{
						ID:           10003,
						StaffID:      3,
						ServicePrice: decimal.NewFromFloat(30.00),
					},
				},
			},
			want: map[int64]Pair{
				1: {SplitRate: decimal.NewFromFloat(0.333333), Amount: decimal.NewFromFloat(3.33)},
				2: {SplitRate: decimal.NewFromFloat(0.333333), Amount: decimal.NewFromFloat(3.33)},
				3: {SplitRate: decimal.NewFromFloat(0.333334), Amount: decimal.NewFromFloat(3.34)},
			},
		},
		{
			name: "Basic case (1+1)/8",
			args: args{
				amount: decimal.NewFromFloat(10.00),
				groomingPetDetailDTOList: []*appointment.PetDetailDTO{
					{
						ID:           10001,
						StaffID:      1,
						ServicePrice: decimal.NewFromFloat(20.00),
						OperationList: []*appointment.ServiceOperationDTO{
							{StaffID: 1, PriceRatio: decimal.NewFromFloat(0.5)},
							{StaffID: 3, PriceRatio: decimal.NewFromFloat(0.5)},
						},
					},
					{
						ID:           10002,
						StaffID:      2,
						ServicePrice: decimal.NewFromFloat(80.00),
					},
				},
			},
			want: map[int64]Pair{
				1: {SplitRate: decimal.NewFromFloat(0.1), Amount: decimal.NewFromFloat(1)},
				2: {SplitRate: decimal.NewFromFloat(0.8), Amount: decimal.NewFromFloat(8)},
				3: {SplitRate: decimal.NewFromFloat(0.1), Amount: decimal.NewFromFloat(1)},
			},
		},
	}
	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				got := BuildSplitRateAndAmountByService(tt.args.amount, tt.args.groomingPetDetailDTOList)
				if !mapsEqual(got, tt.want) {
					t.Errorf("BuildSplitRateAndAmountByService() = %v, want %v", got, tt.want)
				}
			},
		)
	}
}

func mapsEqual(got, want map[int64]Pair) bool {
	if len(got) != len(want) {
		return false
	}

	for k, gotVal := range got {
		wantVal, exists := want[k]
		if !exists || !gotVal.SplitRate.Equal(wantVal.SplitRate) || !gotVal.Amount.Equal(wantVal.Amount) {
			return false
		}
	}

	return true
}

func TestBuildStaffSplitRateAndAmountEqually(t *testing.T) {
	type args struct {
		staffIDSet      []int64
		totalTipsAmount decimal.Decimal
	}

	tests := []struct {
		name string
		args args
		want map[int64]Pair
	}{
		{
			name: "Basic case 5/5",
			args: args{
				staffIDSet:      []int64{1, 2},
				totalTipsAmount: decimal.NewFromInt(10),
			},
			want: map[int64]Pair{
				1: {SplitRate: decimal.NewFromFloat(0.5), Amount: decimal.NewFromFloat(5)},
				2: {SplitRate: decimal.NewFromFloat(0.5), Amount: decimal.NewFromFloat(5)},
			},
		},
		{
			name: "Basic case 3/3/3 with 轧差",
			args: args{
				staffIDSet:      []int64{1, 2, 3},
				totalTipsAmount: decimal.NewFromInt(10),
			},
			want: map[int64]Pair{
				1: {SplitRate: decimal.NewFromFloat(0.3333), Amount: decimal.NewFromFloat(3.34)},
				2: {SplitRate: decimal.NewFromFloat(0.3333), Amount: decimal.NewFromFloat(3.33)},
				3: {SplitRate: decimal.NewFromFloat(0.3333), Amount: decimal.NewFromFloat(3.33)},
			},
		},
	}
	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				got := BuildStaffSplitRateAndAmountEqually(tt.args.staffIDSet, tt.args.totalTipsAmount)
				if !mapsEqual(got, tt.want) {
					t.Errorf("BuildSplitRateAndAmountByService() = %v, want %v", got, tt.want)
				}
			},
		)
	}
}
