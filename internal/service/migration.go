package service

import (
	"context"
	"fmt"

	"github.com/samber/lo"

	orderpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/order/v1"
	"github.com/MoeGolibrary/moego-svc-order-v2/internal/model"
	"github.com/MoeGolibrary/moego-svc-order-v2/internal/repo"
)

type MigrationService interface {
	MigrateToV4(ctx context.Context, apptID int64, orderDetails []*model.OrderDetail) (bool, string, []int64)
}

type migrationService struct {
	userFlag repo.UserFlagClient
	txRepo   repo.TXRepo
}

func NewMigrationService(userFlag repo.UserFlagClient, txRepo repo.TXRepo) MigrationService {
	return &migrationService{
		userFlag: userFlag,
		txRepo:   txRepo,
	}
}

func (s *migrationService) MigrateToV4(ctx context.Context, apptID int64, orderDetails []*model.OrderDetail) (
	sucecss bool, failedReason string, relatedOrderIDs []int64,
) {
	// 关联的所有订单都通过检查才可以迁移.
	for _, od := range orderDetails {
		canMigrate, reason := s.canMigrateToV4(od)
		if !canMigrate {
			reason = fmt.Sprintf("order[%d]: %s", od.GetID(), reason)
			return false, reason, nil
		}
	}

	// 迁移流程.
	var orderIDs []int64

	if txErr := s.txRepo.Tx(
		func(tx repo.OrderTX) error {
			orders, err := tx.Order().ListByAppointmentForUpdate(ctx, apptID)
			if err != nil {
				return err
			}

			orderIDs = lo.Map(orderDetails, func(od *model.OrderDetail, _ int) int64 { return od.GetID() })

			return tx.MigrationRepo().MigrateToV4(ctx, orders)
		},
	); txErr != nil {
		return false, txErr.Error(), nil
	}

	return true, "", orderIDs
}

func (s *migrationService) canMigrateToV4(od *model.OrderDetail) (can bool, why string) {
	if od.Order.GetDiscountAmount().IsPositive() {
		return false, "order has applied membership/discount"
	}

	if od.Order.GetTipsAmount().IsPositive() {
		return false, "order has tips"
	}

	if len(od.OrderPayments) > 0 {
		return false, "order has payment"
	}

	for _, item := range od.OrderItems {
		if item.PurchasedQuantity > 0 {
			return false, "order has applied membership/package"
		}

		switch item.GetItemType() {
		case orderpb.ItemType_ITEM_TYPE_SERVICE, orderpb.ItemType_ITEM_TYPE_EVALUATION_SERVICE:
			// pass

		default:
			return false, "order has non-service/evaluation typed item"
		}
	}

	return true, ""
}
