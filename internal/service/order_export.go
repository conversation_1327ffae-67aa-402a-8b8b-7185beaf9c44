package service

import (
	"context"
	"fmt"
	"strconv"
	"time"

	"github.com/google/uuid"
	"github.com/samber/lo"

	orderpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/order/v1"
	organizationpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/organization/v1"
	orderv2svcpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/order/v2"
	"github.com/MoeGolibrary/moego-svc-order-v2/internal/model"
	"github.com/MoeGolibrary/moego-svc-order-v2/internal/service/export"
	customerpb "github.com/MoeGolibrary/moego/backend/proto/customer/v1"
)

// ExportOrderPaymentDetailList
//
//nolint:dupl // 整体逻辑和 ExportRefundOrderPaymentDetailList 是一样的
func (svc *orderService) ExportOrderPaymentDetailList(
	ctx context.Context, req *orderv2svcpb.ExportOrderPaymentDetailListRequest,
) (int64, error) {
	details, _, err := svc.listOrderPaymentDetail(ctx, req.GetCompanyId(), req.GetFilter(), req.GetOrderBys(), nil)
	if err != nil {
		return 0, err
	}

	tabler, err := svc.createOrderPaymentDetailExportTabler(ctx, req.GetCompanyId(), details)
	if err != nil {
		return 0, err
	}

	fileType := req.GetFileType()
	if fileType == "" {
		fileType = export.FileTypeXlsx
	}

	fileName := svc.generateListOrderPaymentDetailExportFileName(req.GetFileName(), req.GetFilter(), fileType)

	fileID, err := svc.fileClient.Create(ctx, req.GetStaffId(), req.GetCompanyId(), fileName)
	if err != nil {
		return 0, err
	}

	content, err := export.BuildTableExportContent(ctx, tabler, fileType)
	if err != nil {
		return 0, err
	}

	if err := svc.fileClient.Upload(ctx, fileID, content); err != nil {
		return 0, err
	}

	return fileID, nil
}

func (svc *orderService) createOrderPaymentDetailExportTabler(
	ctx context.Context, companyID int64, details []*orderPaymentDetailItem,
) (export.Tabler, error) {
	pref, err := svc.organizationRepo.GetCompanyPreference(ctx, companyID)
	if err != nil {
		return nil, err
	}

	locationIDs := lo.Uniq(lo.Map(
		details,
		func(detail *orderPaymentDetailItem, _ int) int64 { return detail.OrderPayment.BusinessID },
	))

	locations, err := svc.organizationRepo.BatchGetLocation(ctx, locationIDs)
	if err != nil {
		return nil, err
	}

	locationMap := lo.KeyBy(locations, func(it *organizationpb.LocationModel) int64 { return it.GetId() })

	customerIDs := lo.Uniq(lo.Map(
		details,
		func(detail *orderPaymentDetailItem, _ int) int64 { return detail.OrderPayment.CustomerID },
	))

	customers, err := svc.customerRepo.BatchGetCustomer(ctx, companyID, customerIDs)
	if err != nil {
		return nil, err
	}

	customerMap := lo.KeyBy(customers, func(it *customerpb.Customer) int64 { return it.GetId() })

	orderIDs := lo.Uniq(lo.Map(
		details,
		func(detail *orderPaymentDetailItem, _ int) int64 { return detail.OrderPayment.OrderID },
	))

	orders, err := svc.orderRepo.BatchGetOrders(ctx, orderIDs)
	if err != nil {
		return nil, err
	}

	orderMap := lo.KeyBy(orders, func(order *model.Order) int64 { return order.ID })

	return &orderPaymentDetailExportTabler{
		timezoneLocation: timeZonePBToLocation(pref.GetTimeZone()),
		currencySymbol:   pref.GetCurrencySymbol(),
		customerMap:      customerMap,
		locationMap:      locationMap,
		orderMap:         orderMap,
		details:          details,
	}, nil
}

func (svc *orderService) generateListOrderPaymentDetailExportFileName(
	fileName string, filter *orderv2svcpb.ListOrderPaymentDetailRequest_Filter, fileType string,
) string {
	if fileName == "" {
		fileName = uuid.New().String()
	}

	var interval string

	start := filter.GetStartTime().AsTime()

	end := filter.GetEndTime().AsTime()
	if !start.Equal(time.Unix(0, 0)) && !end.Equal(time.Unix(0, 0)) {
		interval = fmt.Sprintf("(%s - %s)", start.Format("2006-01-02"), end.Format("2006-01-02"))
	}

	return fmt.Sprintf("%s%s.%s", fileName, interval, fileType)
}

const (
	columnPaymentDateTime      = "date_time"
	columnPaymentClientName    = "client_name"
	columnPaymentAmount        = "amount"
	columnPaymentPaidBy        = "paid_by"
	columnPaymentMethod        = "method"
	columnPaymentStatus        = "status"
	columnPaymentAppointmentID = "appointment_id"
	columnPaymentBusiness      = "business"
)

// TODO(P2): 产品没说需要这个表格，不小心提前实现了……先按照设计稿里的表格来定义，后续启用这个接口之前要和产品确认一下导出的具体内容
//
//nolint:mnd // column width are constants
var orderPaymentDetailExportColumnConfigs = []export.ColumnConfig{
	// 06/05/2025 10:02 am
	{Key: columnPaymentDateTime, Name: "Date & time", ColumnWidth: 30},
	{Key: columnPaymentClientName, Name: "Client name", ColumnWidth: 25},
	{Key: columnPaymentAmount, Name: "Amount", ColumnWidth: 25},
	{Key: columnPaymentPaidBy, Name: "Paid by", ColumnWidth: 25},
	// Credit card (Visa 1118)
	{Key: columnPaymentMethod, Name: "Method", ColumnWidth: 25},
	{Key: columnPaymentStatus, Name: "Status", ColumnWidth: 25},
	{Key: columnPaymentAppointmentID, Name: "Appointment ID", ColumnWidth: 20},
	{Key: columnPaymentBusiness, Name: "Business", ColumnWidth: 25},
}

type orderPaymentDetailExportTabler struct {
	timezoneLocation *time.Location
	currencySymbol   string
	customerMap      map[int64]*customerpb.Customer
	locationMap      map[int64]*organizationpb.LocationModel
	orderMap         map[int64]*model.Order
	details          []*orderPaymentDetailItem
}

func (t *orderPaymentDetailExportTabler) RowCount() int {
	return len(t.details)
}

func (t *orderPaymentDetailExportTabler) ColumnCount() int {
	return len(orderPaymentDetailExportColumnConfigs)
}

func (t *orderPaymentDetailExportTabler) ColumnConfig(col int) export.ColumnConfig {
	return orderPaymentDetailExportColumnConfigs[col]
}

var displayPaymentStatusMap = map[orderpb.OrderPaymentStatus]string{
	orderpb.OrderPaymentStatus_ORDER_PAYMENT_STATUS_CREATED:             "Processing",
	orderpb.OrderPaymentStatus_ORDER_PAYMENT_STATUS_TRANSACTION_CREATED: "Processing",
	orderpb.OrderPaymentStatus_ORDER_PAYMENT_STATUS_PAID:                "Paid",
	orderpb.OrderPaymentStatus_ORDER_PAYMENT_STATUS_FAILED:              "Failed",
	orderpb.OrderPaymentStatus_ORDER_PAYMENT_STATUS_CANCELED:            "Canceled",
}

func (t *orderPaymentDetailExportTabler) CellText(row, col int) string {
	const sourceTypeAppointment = "appointment"

	data := t.details[row]
	orderPayment := data.OrderPayment
	order := t.orderMap[orderPayment.OrderID]
	cfg := orderPaymentDetailExportColumnConfigs[col]

	switch cfg.Key {
	case columnPaymentDateTime:
		return time.Unix(orderPayment.CreateTime, 0).In(t.timezoneLocation).Format("01/02/2006 03:04 PM")
	case columnPaymentClientName:
		if customer, ok := t.customerMap[orderPayment.CustomerID]; ok {
			return fmt.Sprintf("%s %s", customer.GetGivenName(), customer.GetFamilyName())
		}

		return ""
	case columnPaymentAmount:
		const places = 2

		return fmt.Sprintf("%s%s", t.currencySymbol, orderPayment.GetTotalAmount().StringFixed(places))
	case columnPaymentPaidBy:
		return orderPayment.PaidBy
	case columnPaymentMethod:
		return orderPayment.PaymentMethod.GetDisplayName()
	case columnPaymentStatus:
		if status, ok := displayPaymentStatusMap[orderPayment.PaymentStatus]; ok {
			return status
		}

		return ""
	case columnPaymentAppointmentID:
		if order.SourceType == sourceTypeAppointment {
			return strconv.FormatInt(order.SourceID, 10)
		}

		return ""
	case columnPaymentBusiness:
		return t.locationMap[orderPayment.BusinessID].GetName()
	default:
		return ""
	}
}
