package service

import (
	"context"
	"encoding/json"
	"fmt"
	"strconv"
	"strings"
	"time"

	"github.com/google/uuid"
	"github.com/samber/lo"
	"github.com/shopspring/decimal"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	"github.com/MoeGolibrary/go-lib/common/proto/money"
	"github.com/MoeGolibrary/go-lib/redis"
	apptpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/appointment/v1"
	orderpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/order/v1"
	orderv2pb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/order/v2"
	paymentpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/payment/v2"
	ordersvcv2pb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/order/v2"
	"github.com/MoeGolibrary/moego-svc-order-v2/internal/model"
	"github.com/MoeGolibrary/moego-svc-order-v2/internal/repo"
	"github.com/MoeGolibrary/moego-svc-order-v2/internal/repo/appointment"
	"github.com/MoeGolibrary/moego-svc-order-v2/internal/repo/grooming"
)

const (
	// prefix for legacy deposit.
	legacyDepositPrefix = "de_"
	// prefix for bulk payment
	bulkPaymentPrefix = "bulk"
	// Set the expiration to 1 month (30 days)
	expiration = 7 * 24 * time.Hour
)

type OrderPaymentService interface {
	PayOrder(ctx context.Context, req *ordersvcv2pb.PayOrderRequest) (*model.OrderPayment, error)
	CombinedPayOrder(ctx context.Context, req *ordersvcv2pb.CombinedPayOrderRequest) (
		int64, []*orderpb.OrderPaymentModel, error,
	)
	GetOrderGuid(ctx context.Context, req *ordersvcv2pb.GetOrderGuidRequest) (string, error)
	GetOrderIDsByGUID(ctx context.Context, guid string) ([]int64, error)
	ListOrdersForCombinedPayment(
		ctx context.Context,
		req *ordersvcv2pb.ListOrdersForCombinedPaymentRequest,
	) ([]*model.Order, error)
}

func NewOrderPaymentService(
	txRepo repo.TXRepo,
	orderRepo repo.OrderRepo,
	orderPaymentRepo repo.OrderPaymentRepo,
	paymentCli repo.PaymentClient,
	appointmentCli appointment.Client,
	redisCli redis.UniversalClient,
	groomingClient grooming.Client,
) OrderPaymentService {
	return &orderPaymentService{
		txRepo:           txRepo,
		orderRepo:        orderRepo,
		orderPaymentRepo: orderPaymentRepo,
		paymentCli:       paymentCli,
		appointmentCli:   appointmentCli,
		redisCli:         redisCli,
		groomingClient:   groomingClient,
	}
}

type orderPaymentService struct {
	txRepo           repo.TXRepo
	orderRepo        repo.OrderRepo
	orderPaymentRepo repo.OrderPaymentRepo
	paymentCli       repo.PaymentClient
	appointmentCli   appointment.Client
	redisCli         redis.UniversalClient
	groomingClient   grooming.Client
}

func (s *orderPaymentService) PayOrder(ctx context.Context, req *ordersvcv2pb.PayOrderRequest) (
	*model.OrderPayment, error,
) {
	order, err := s.orderRepo.Get(ctx, req.GetOrderId())
	if err != nil {
		return nil, err
	}

	// 创建order payment
	amount := money.ToDecimal(req.GetAmount())
	tips := money.ToDecimal(req.GetPaymentTipsBeforeCreate())

	// status 判断
	if order.IsFinalStatus() {
		return nil, status.Error(codes.InvalidArgument, "order is in final status")
	}

	if amount.GreaterThan(order.RemainAmount) {
		return nil, status.Error(codes.InvalidArgument, "request amount is greater than remaining amount")
	}

	orderPayment := &model.OrderPayment{
		OrderID:                 order.ID,
		CompanyID:               order.CompanyID,
		BusinessID:              order.BusinessID,
		StaffID:                 req.GetStaffId(),
		CustomerID:              order.CustomerID,
		CurrencyCode:            order.CurrencyCode,
		TotalAmount:             amount.Add(tips),
		Amount:                  amount,
		PaymentTipsBeforeCreate: tips,
		PaymentTips:             tips,
		PaymentStatus:           orderpb.OrderPaymentStatus_ORDER_PAYMENT_STATUS_CREATED,
	}

	if copErr := s.orderPaymentRepo.Create(ctx, orderPayment); copErr != nil {
		return nil, copErr
	}

	// 调用payment 创建payment单据
	paymentModel, err := s.paymentCli.CreatePayment(ctx, orderPayment)
	if err != nil {
		return nil, status.Errorf(codes.Internal, "invoke payment to create error, %s", err.Error())
	}

	// 更新order payment状态为 TRANSACTION_CREATED
	if _, err := s.orderPaymentRepo.UpdateTransactionCreated(ctx, &model.OrderPayment{
		ID:            orderPayment.ID,
		PaymentStatus: orderpb.OrderPaymentStatus_ORDER_PAYMENT_STATUS_TRANSACTION_CREATED,
		PaymentID:     paymentModel.PaymentID,
	}); err != nil {
		return nil, err
	}

	// 设置 transaction id, payment id
	orderPayment.PaymentID = paymentModel.PaymentID
	orderPayment.TransactionID = paymentModel.TransactionID

	return orderPayment, nil
}

// GetOrderIDsByGUID implements OrderPaymentService.
func (s *orderPaymentService) GetOrderIDsByGUID(ctx context.Context, guid string) ([]int64, error) {
	if strings.HasPrefix(guid, legacyDepositPrefix) {
		orderID, err := s.groomingClient.GetInvoiceByGUID(ctx, guid)
		if err != nil {
			return nil, err
		}

		return []int64{orderID}, nil
	}

	if !strings.HasPrefix(guid, bulkPaymentPrefix) {
		// 兼容老的 order guid
		order, err := s.orderRepo.GetByGUID(ctx, guid)
		if err != nil {
			return nil, err
		}

		return []int64{order.ID}, nil
	}

	// 从 redis 中获取 order ids
	orderIDsJSON, err := s.redisCli.Get(ctx, guid).Result()
	if err != nil {
		return nil, err
	}
	// 解析 JSON 字符串为 order ids
	var orderIDs []int64
	if err := json.Unmarshal([]byte(orderIDsJSON), &orderIDs); err != nil {
		return nil, err
	}

	return orderIDs, nil
}

// ListOrdersForCombinedPayment implements OrderPaymentService.
func (s *orderPaymentService) ListOrdersForCombinedPayment(
	ctx context.Context,
	req *ordersvcv2pb.ListOrdersForCombinedPaymentRequest,
) ([]*model.Order, error) {
	apptResp, err := s.appointmentCli.ListAppointmentsForCustomers(
		ctx,
		req.CustomerId,
		req.CompanyId,
		req.BusinessId,
		nil,
	)
	if err != nil {
		return nil, err
	}

	// 根据 appt 查询所有的 sourceIDs
	sourceIDs := lo.Uniq(
		lo.Map(
			apptResp.GetAppointments(), func(appt *apptpb.AppointmentModel, _ int) int64 {
				return appt.GetId()
			},
		),
	)
	if len(sourceIDs) == 0 {
		return []*model.Order{}, nil
	}

	// 根据 sourceIDs 查询所有的 order
	orders, err := s.orderRepo.ListByAppointmentIDs(ctx, req.GetCustomerId(), sourceIDs)
	if err != nil {
		return nil, err
	}
	// 过滤出来有remaining amount 的
	orders = lo.Filter(
		orders, func(order *model.Order, _ int) bool {
			return order.GetRemainAmount().IsPositive()
		},
	)

	// filter pre auth tickets
	preAuthRecords, err := s.paymentCli.ListPreAuthRecords(ctx, req.CompanyId, sourceIDs)
	if err != nil {
		return nil, err
	}

	// get ticket ids in pre auth records
	ticketIDs := lo.Map(
		preAuthRecords, func(preAuthRecord *repo.PreAuthDTO, _ int) int64 {
			return preAuthRecord.TicketID
		},
	)
	// filter payment status and order source id
	return lo.Filter(
		orders, func(order *model.Order, _ int) bool {
			return order.PaymentStatus != orderpb.OrderModel_PAID && !lo.Contains(ticketIDs, order.SourceID)
		},
	), nil
}

// GetOrderGuid implements OrderPaymentService.
func (s *orderPaymentService) GetOrderGuid(
	ctx context.Context,
	req *ordersvcv2pb.GetOrderGuidRequest,
) (string, error) {
	// 如果是只有一个订单，走老逻辑
	if len(req.GetOrderIds()) == 1 {
		orderDO, err := s.orderRepo.Get(ctx, req.GetOrderIds()[0])
		if err != nil {
			return "", err
		}

		return s.groomingClient.GenerateOrderGUID(ctx, orderDO, req.GetRequiredCvf())
	}

	o, err := s.orderRepo.Get(ctx, req.GetOrderIds()[0])
	if err != nil {
		return "", err
	}

	if batchSaveFeeErr := s.groomingClient.BatchSaveOnlineFeeInvoice(
		ctx, grooming.BatchSaveOnlineFeeInvoiceParam{
			BusinessID:    o.BusinessID,
			InvoiceIDList: req.GetOrderIds(),
			RequiredCvf:   req.GetRequiredCvf(),
		},
	); batchSaveFeeErr != nil {
		return "", err
	}

	// 生成一个 redis key， 前缀为bulk_guid, value 为 order ids json string
	orderIDsJSON, err := json.Marshal(req.GetOrderIds())
	if err != nil {
		return "", err
	}

	// 生成UUID作为GUID
	guid := uuid.New().String()
	// 构建Redis key
	key := fmt.Sprintf("%s_%s", bulkPaymentPrefix, guid)

	// 将订单ID列表存储到Redis
	if err := s.redisCli.Set(ctx, key, string(orderIDsJSON), expiration).Err(); err != nil {
		return "", fmt.Errorf("failed to set bulk guid to redis: %w", err)
	}

	return key, nil
}

func (s *orderPaymentService) CombinedPayOrder(ctx context.Context, req *ordersvcv2pb.CombinedPayOrderRequest) (
	int64, []*orderpb.OrderPaymentModel, error,
) {
	// 先查询所有的order出来，考虑加锁？
	orderIDs := lo.Uniq(
		lo.Map(
			req.GetCombinedItems(), func(i *orderv2pb.CombinedItem, _ int) int64 {
				return i.GetOrderId()
			},
		),
	)

	orderModels, err := s.orderRepo.BatchGetOrders(ctx, orderIDs)
	if err != nil {
		return 0, nil, err
	}

	// 过滤掉 fully paid 的
	orderModels = lo.Filter(
		orderModels, func(order *model.Order, _ int) bool {
			return order.GetRemainAmount().IsPositive()
		},
	)

	// 保存需要落库的 order payment
	saveOrderPayment := make([]*model.OrderPayment, 0)

	// 创建 payment 单据，这里是所有的 order 都应该创建 payment 单据
	orderPayments := lo.Map(
		orderModels, func(order *model.Order, _ int) *model.OrderPayment {
			op := &model.OrderPayment{
				OrderID:                 order.ID,
				CompanyID:               order.CompanyID,
				BusinessID:              order.BusinessID,
				StaffID:                 req.GetStaffId(),
				CustomerID:              order.CustomerID,
				CurrencyCode:            order.CurrencyCode,
				TotalAmount:             order.RemainAmount,
				Amount:                  order.RemainAmount, // 剩余未支付的金额全都要发起支付
				PaymentTipsBeforeCreate: decimal.Zero,       // 合单支付对于order payment来说无before tips
				PaymentStatus:           orderpb.OrderPaymentStatus_ORDER_PAYMENT_STATUS_CREATED,
				Module:                  order.GetModule(),
				ModuleID:                order.SourceID,
			}

			// order version >= 2的需要创建 order payment，这里会有新老order混合的情况
			if order.SupportOrderPayment() {
				saveOrderPayment = append(saveOrderPayment, op)
			}

			return op
		},
	)

	// 保存 orderVersion 大于等于2 的 order payment
	if copErr := s.orderPaymentRepo.BatchCreate(ctx, saveOrderPayment); copErr != nil {
		return 0, nil, copErr
	}

	// 调用 svc-payment 创建支付单据
	txnID, paymentModels, err := s.paymentCli.CreateCombinedPayment(ctx, orderPayments)
	if err != nil {
		return 0, nil, err
	}

	paymentMap := lo.SliceToMap(
		paymentModels, func(p *paymentpb.PaymentModel) (string, *paymentpb.PaymentModel) {
			return p.GetExternalId(), p //nolint:gocritic //这里直接return没有问题
		},
	)

	// 创建完 payment 更新 order payment
	updateOrderPayments := lo.Map(
		saveOrderPayment, func(op *model.OrderPayment, _ int) *model.OrderPayment {
			updateOp := &model.OrderPayment{
				ID:            op.ID,
				PaymentStatus: orderpb.OrderPaymentStatus_ORDER_PAYMENT_STATUS_TRANSACTION_CREATED,
			}
			if payment, ok := paymentMap[strconv.FormatInt(op.ID, 10)]; ok {
				updateOp.PaymentID = payment.GetId()
				op.PaymentID = payment.GetId()
			}

			return updateOp
		},
	)
	if _, err := s.orderPaymentRepo.BatchUpdateTransactionCreated(ctx, updateOrderPayments); err != nil {
		return 0, nil, err
	}

	opModels := lo.Map(
		orderPayments, func(op *model.OrderPayment, _ int) *orderpb.OrderPaymentModel {
			pb := op.ToPB()
			if payment, ok := paymentMap[strconv.FormatInt(op.ID, 10)]; ok {
				pb.PaymentId = payment.Id
			}

			return pb
		},
	)

	return txnID, opModels, nil
}
