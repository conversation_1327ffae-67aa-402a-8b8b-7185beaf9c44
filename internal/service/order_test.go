package service

import (
	"testing"

	"github.com/shopspring/decimal"
	"github.com/stretchr/testify/assert"

	orderpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/order/v1"
	"github.com/MoeGolibrary/moego-svc-order-v2/internal/model"
)

// Deposit order 未抵扣、未退款（最初始的状态）
func TestOrderService_calculateRefundableModes_DepositUnDeducted(t *testing.T) {
	orderService := &orderService{}

	assert.ElementsMatch(
		t,
		[]orderpb.RefundMode{
			orderpb.RefundMode_REFUND_MODE_BY_ITEM,
			orderpb.RefundMode_REFUND_MODE_BY_PAYMENT,
		},
		orderService.calculateRefundableModes(
			&model.OrderDetail{
				Order: &model.Order{
					ID:     123456,
					Status: orderpb.OrderStatus_COMPLETED,
				},
			},
			nil,
			depositDetailFrom([]*model.DepositChangeLog{
				{
					ID:            1,
					ChangeType:    orderpb.DepositChangeType_INCREASE,
					Reason:        orderpb.DepositChangeReason_TOP_UP,
					ChangedAmount: decimal.NewFromInt(10),
					Balance:       decimal.NewFromInt(10),
				},
			}),
		),
	)
}

// Deposit order 未抵扣、已经有过 by item 的退款
func TestOrderService_calculateRefundableModes_DepositUnDeductedRefundedByItem(t *testing.T) {
	orderService := &orderService{}

	assert.ElementsMatch(
		t,
		[]orderpb.RefundMode{orderpb.RefundMode_REFUND_MODE_BY_ITEM},
		orderService.calculateRefundableModes(
			&model.OrderDetail{
				Order: &model.Order{
					ID:     123456,
					Status: orderpb.OrderStatus_COMPLETED,
				},
			},
			[]*model.RefundOrderDetail{
				{
					RefundOrder: &model.RefundOrder{
						ID:                  2123456,
						OrderID:             123456,
						RefundMode:          orderpb.RefundMode_REFUND_MODE_BY_ITEM,
						OrderStatusSnapshot: orderpb.OrderStatus_COMPLETED,
					},
					RefundOrderItems: []*model.RefundOrderItem{
						{
							ID:            278,
							OrderID:       123456,
							OrderItemID:   78,
							RefundOrderID: 2123456,
							ItemUnitPrice: decimal.NewFromFloat(10),
							RefundAmount:  decimal.NewFromInt(7),
						},
					},
					RefundOrderPayments: []*model.RefundOrderPayment{
						{
							ID:              289,
							OrderID:         123456,
							OrderPaymentID:  89,
							RefundOrderID:   2123456,
							RefundPaymentID: 1089,
							RefundAmount:    decimal.NewFromInt(7),
						},
					},
				},
			},
			depositDetailFrom([]*model.DepositChangeLog{
				{
					ID:            2,
					ChangeType:    orderpb.DepositChangeType_DECREASE,
					Reason:        orderpb.DepositChangeReason_OVERPAYMENT_REVERSAL,
					ChangedAmount: decimal.NewFromInt(7),
					Balance:       decimal.NewFromInt(3),
				},
				{
					ID:            1,
					ChangeType:    orderpb.DepositChangeType_INCREASE,
					Reason:        orderpb.DepositChangeReason_TOP_UP,
					ChangedAmount: decimal.NewFromInt(10),
					Balance:       decimal.NewFromInt(10),
				},
			}),
		),
	)
}

func depositDetailFrom(changeLogs []*model.DepositChangeLog) *model.DepositDetail {
	ret := &model.DepositDetail{
		DepositChangeLogs: changeLogs,
	}

	if len(changeLogs) > 0 {
		ret.LatestChangeLog = changeLogs[0]
	}

	return ret
}
