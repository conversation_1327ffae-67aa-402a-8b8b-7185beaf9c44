package service

import (
	"context"

	"github.com/samber/lo"

	orderpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/order/v1"
	"github.com/MoeGolibrary/moego-svc-order-v2/internal/model"
	"github.com/MoeGolibrary/moego-svc-order-v2/internal/repo"
)

func (svc *orderService) CreateOrder(ctx context.Context, orderDetail *model.OrderDetail) (*model.OrderDetail, error) {
	if txErr := svc.txRepo.Tx(
		func(tx repo.OrderTX) error { return svc.createOrderTX(ctx, tx, orderDetail) },
	); txErr != nil {
		return nil, txErr
	}

	return orderDetail, nil
}

func (svc *orderService) createOrderTX(ctx context.Context, tx repo.OrderTX, orderDetail *model.OrderDetail) error {
	// 创建 Order.
	order := orderDetail.Order
	if err := tx.Order().Create(ctx, order); err != nil {
		return err
	}

	// 创建 OrderCreated 事件
	if err := tx.MessageDeliveryRepo().CreateCreated(ctx, order); err != nil {
		return err
	}

	// 如果订单直接 Completed 了，这里还需要补一个 Completed 事件.
	if order.Status == orderpb.OrderStatus_COMPLETED {
		if err := tx.MessageDeliveryRepo().CreateCompleted(ctx, order); err != nil {
			return err
		}
	}

	// 更新 Order ID.
	items := orderDetail.OrderItems
	for _, it := range items {
		it.OrderID = order.ID
	}

	promotions := orderDetail.OrderPromotions
	for _, it := range promotions {
		it.OrderID = order.ID
		// Create 的时候需要从 Preview 扭转为 created.
		it.Status = orderpb.OrderPromotionModel_CREATED
	}

	// 使用了 Deposit 进行抵扣
	if order.GetDepositAmount().IsPositive() {
		orderDetail.DepositChangeLog.DestOrderID = order.ID

		// Redeem deposit.
		if err := tx.DepositChangeLog().Create(ctx, orderDetail.DepositChangeLog); err != nil {
			return err
		}
	}

	// 创建 items. （price detail, tax 都在这里处理）
	// 在之前的流程可能产生 fake item id 用来辅助，这里以防万一，统一清理一遍 item id.
	for _, it := range items {
		it.ID = 0
	}

	if err := tx.OrderItem().BatchCreate(ctx, items); err != nil {
		return err
	}

	// 更新 Item ID 到 Discount.
	externalUUIDToItemID := lo.Associate(
		items, func(it *model.OrderItem) (string, int64) {
			return it.ExternalUUID, it.ID
		},
	)

	discounts := orderDetail.OrderDiscount
	for _, it := range discounts {
		it.OrderID = order.ID
		it.OrderItemID = externalUUIDToItemID[it.OrderItemExternalUUID]
	}

	// 为了兼容，创建 order line discount.
	if err := tx.OrderLineDiscount().BatchCreate(ctx, discounts); err != nil {
		return err
	}

	// 创建 order promotion.
	if err := tx.OrderPromotion().BatchCreate(ctx, promotions); err != nil {
		return err
	}

	// 更新 order promotion item 的 order_promotion_id.
	orderPromotionItems := make([]*model.OrderPromotionItem, 0)

	for _, promotion := range promotions {
		promotionItems := promotion.PromotionItems
		for _, promotionItem := range promotionItems {
			promotionItem.OrderPromotionID = promotion.ID
			promotionItem.OrderItemID = externalUUIDToItemID[promotionItem.ExternalUUID]
		}

		orderPromotionItems = append(orderPromotionItems, promotionItems...)
	}

	// 批量写入 order promotion item
	if err := tx.OrderPromotionItem().BatchCreate(ctx, orderPromotionItems); err != nil {
		return err
	}

	return nil
}
