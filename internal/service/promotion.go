package service

import (
	"context"
	"fmt"
	"slices"
	"sort"
	"time"

	"github.com/samber/lo"
	"github.com/shopspring/decimal"
	"go.uber.org/zap"
	gdecimal "google.golang.org/genproto/googleapis/type/decimal"
	gmoney "google.golang.org/genproto/googleapis/type/money"

	"github.com/MoeGolibrary/go-lib/ants"
	"github.com/MoeGolibrary/go-lib/common/proto/money"
	"github.com/MoeGolibrary/go-lib/zlog"
	orderpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/order/v1"
	promotionpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/promotion/v1"
	ordersvcpb2 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/order/v2"
	"github.com/MoeGolibrary/moego-svc-order-v2/internal/core"
	"github.com/MoeGolibrary/moego-svc-order-v2/internal/model"
	"github.com/MoeGolibrary/moego-svc-order-v2/internal/repo"
	"github.com/MoeGolibrary/moego-svc-order-v2/internal/repo/promotion"
	"github.com/MoeGolibrary/moego-svc-order-v2/internal/repo/subscription"
	"github.com/MoeGolibrary/moego-svc-order-v2/internal/service/helper"
)

type PromotionService interface {
	PreviewCoupons(
		ctx context.Context,
		autoApply bool,
		cartItems []*ordersvcpb2.PreviewCreateOrderRequest_CartItem,
		appliedPromotions *ordersvcpb2.PreviewCreateOrderRequest_AppliedPromotions,
		customerID int64,
	) ([]*model.OrderPromotion, error)
	RedeemCoupons(
		ctx context.Context,
		redeemParams *model.RedeemPromotionParams,
	) error
	AsyncRedeemCoupons(
		ctx context.Context,
		redeemParams *model.RedeemPromotionParams,
	)
	RetryRedeem(ctx context.Context)
}

func NewPromotionService(
	promotionClient promotion.Client,
	subscriptionClient subscription.Client,
	orderPromotionRepo repo.OrderPromotionRepo,
	orderPromotionItemRepo repo.OrderPromotionItemRepo,
	orderRepo repo.OrderRepo,
) PromotionService {
	return &promotionService{
		promotionClient:        promotionClient,
		subscriptionClient:     subscriptionClient,
		orderPromotionRepo:     orderPromotionRepo,
		orderPromotionItemRepo: orderPromotionItemRepo,
		orderRepo:              orderRepo,
	}
}

type promotionService struct {
	promotionClient        promotion.Client
	subscriptionClient     subscription.Client
	orderPromotionRepo     repo.OrderPromotionRepo
	orderPromotionItemRepo repo.OrderPromotionItemRepo
	orderRepo              repo.OrderRepo
}

func (ps *promotionService) RetryRedeem(ctx context.Context) {
	// 捞取 2min 前状态为 CREATED 的 promotion 记录
	const redeemTimeOffset = 2 * time.Minute

	promotionList, err := ps.orderPromotionRepo.ListByStatus(ctx,
		[]orderpb.OrderPromotionModel_Status{orderpb.OrderPromotionModel_CREATED},
		redeemTimeOffset,
	)
	if err != nil {
		zlog.Error(ctx, "list promotion by status err", zap.Error(err))

		return
	}

	if len(promotionList) == 0 {
		return
	}

	promotionIDList := lo.Map(promotionList, func(p *model.OrderPromotion, _ int) int64 {
		return p.ID
	})

	// 捞取对应的 promotion items
	promotionItemList, err := ps.orderPromotionItemRepo.ListByPromotionIDs(ctx, promotionIDList)
	if err != nil {
		zlog.Error(ctx, "list promotion item by promotionIDList err",
			zap.Any("promotionIDList", promotionIDList),
			zap.Error(err))

		return
	}

	promotionIDToItems := lo.GroupBy(promotionItemList, func(pi *model.OrderPromotionItem) int64 {
		return pi.OrderPromotionID
	})
	for _, p := range promotionList {
		if itemList, ok := promotionIDToItems[p.ID]; ok {
			p.PromotionItems = itemList
		}
	}

	orderIDToPromotions := lo.GroupBy(promotionList, func(p *model.OrderPromotion) int64 {
		return p.OrderID
	})

	orderIDList := make([]int64, 0, len(orderIDToPromotions))
	for k := range orderIDToPromotions {
		orderIDList = append(orderIDList, k)
	}

	// 捞取 order ， redeem 的时候按 order 纬度进行
	orderList, err := ps.orderRepo.BatchGetOrders(ctx, orderIDList)
	if err != nil {
		zlog.Error(ctx, "list order err",
			zap.Any("orderIDList", orderIDList),
			zap.Error(err))

		return
	}

	if len(orderList) == 0 {
		return
	}

	for _, order := range orderList {
		promotions, ok := orderIDToPromotions[order.ID]
		if !ok {
			continue
		}

		redeemParams := &model.RedeemPromotionParams{
			OrderID:          order.ID,
			CustomerID:       order.CustomerID,
			AppointmentID:    order.SourceID,
			OrderTotalAmount: order.TotalAmount,
			OrderPromotions:  promotions,
		}
		// 异步触发 redeem
		ps.AsyncRedeemCoupons(ctx, redeemParams)
	}
}

func beforePreviewFilter(
	appliedPromotionList []*ordersvcpb2.PreviewCreateOrderRequest_Promotion,
) []*ordersvcpb2.PreviewCreateOrderRequest_Promotion {
	finalAppliedPromotionList := make([]*ordersvcpb2.PreviewCreateOrderRequest_Promotion, 0)

	lastAppliedPromotion := appliedPromotionList[len(appliedPromotionList)-1]
	if lastAppliedPromotion.GetCouponSource().GetMembership() != nil {
		// 如果最后一个是 membership , 那么只需要保留最后所有连续的 membership
		for i := len(appliedPromotionList) - 1; i >= 0; i-- {
			if appliedPromotionList[i].GetCouponSource().GetMembership() == nil {
				break
			}

			finalAppliedPromotionList = append(finalAppliedPromotionList, appliedPromotionList[i])
		}
	} else {
		// 如果最后一个不是 membership , 那么只需要保留最后所有连续的非 membership
		for i := len(appliedPromotionList) - 1; i >= 0; i-- {
			if appliedPromotionList[i].GetCouponSource().GetMembership() != nil {
				break
			}

			finalAppliedPromotionList = append(finalAppliedPromotionList, appliedPromotionList[i])
		}
	}

	slices.Reverse(finalAppliedPromotionList)

	return finalAppliedPromotionList
}

func (ps *promotionService) PreviewCoupons(
	ctx context.Context,
	_ bool, // autoApply 目前只支持手动输入的优惠券
	cartItems []*ordersvcpb2.PreviewCreateOrderRequest_CartItem,
	appliedPromotions *ordersvcpb2.PreviewCreateOrderRequest_AppliedPromotions,
	customerID int64,
) ([]*model.OrderPromotion, error) {
	itemMap := make(map[string]*ordersvcpb2.PreviewCreateOrderRequest_CartItem)

	// 从 1 开始，避免和其他的 order item ID 冲突.
	var currentOrderItemID int64 = 1

	externalUUIDOrderIDMap := make(map[string]int64)

	for _, it := range cartItems {
		if it.GetExternalUuid() != "" {
			itemMap[it.GetExternalUuid()] = it

			if _, ok := externalUUIDOrderIDMap[it.ExternalUuid]; !ok {
				externalUUIDOrderIDMap[it.ExternalUuid] = currentOrderItemID
				currentOrderItemID++
			}
		}
	}

	// 没有 apply 任何优惠券.
	if appliedPromotions == nil || len(appliedPromotions.GetPromotions()) == 0 {
		return nil, nil
	}

	// 先进行前置过滤，membership 和 其他的互斥
	finalAppliedPromotionList := beforePreviewFilter(appliedPromotions.GetPromotions())
	couponUsages := make([]*promotionpb.CouponUsage, 0)
	orderItemID2ExternalUUIDMap := make(map[int64]string)

	couponKeyToIdxMap := make(map[string]int) // 为了记录顺序

	for idx, promotion := range finalAppliedPromotionList {
		if promotion.GetStoreCredit() != nil ||
			promotion.GetOneTimeDiscount() != nil ||
			promotion.GetCouponSource() == nil {
			continue
		}

		targets := make([]*promotionpb.CouponApplicationTarget, 0)

		for _, externalUUID := range promotion.GetCartItemExternalUuids() {
			orderItemID := externalUUIDOrderIDMap[externalUUID]
			orderItemID2ExternalUUIDMap[orderItemID] = externalUUID

			item, ok := itemMap[externalUUID]
			if !ok {
				continue
			}

			targetType := model.ConvertItemType2TargetType(item.GetItemType())
			if targetType == promotionpb.TargetType_TARGET_TYPE_UNSPECIFIED {
				continue
			}

			targets = append(
				targets, &promotionpb.CouponApplicationTarget{
					TargetType:     targetType,
					TargetId:       item.GetItemId(),
					UnitPrice:      item.GetUnitPrice(),
					TargetQuantity: item.GetQuantity(),
					OrderItemId:    lo.ToPtr(orderItemID),
				},
			)
		}

		// 存储 idx ，为了保持后面的 apply 顺序
		couponKeyToIdxMap[getCouponDeductionKey(promotion.GetCouponSource())] = idx

		couponUsages = append(
			couponUsages, &promotionpb.CouponUsage{
				Coupon: &promotionpb.Coupon{
					Source: promotion.GetCouponSource(),
				},
				Targets: targets,
			},
		)
	}

	resp, err := ps.promotionClient.PreviewCoupons(ctx, customerID, couponUsages)
	if err != nil {
		return nil, err
	}

	var needPreviewPromotionsIdx int

	needPreviewPromotions := convertDeductionsToPromotions(
		resp.Deductions,
		orderItemID2ExternalUUIDMap,
	)

	promotions := make([]*model.OrderPromotion, 0)

	for idx, promotion := range finalAppliedPromotionList {
		if promotion.GetStoreCredit() != nil {
			promotions = append(
				promotions, &model.OrderPromotion{
					SourceType:     promotionpb.Source_DISCOUNT,
					SourceID:       model.StoreCreditSourceID,
					DiscountType:   orderpb.DiscountType_FIXED_AMOUNT,
					DiscountValue:  money.ToDecimal(promotion.GetStoreCredit()),
					AppliedAmount:  money.ToDecimal(promotion.GetStoreCredit()),
					Status:         orderpb.OrderPromotionModel_PREVIEW,
					PromotionItems: nil,
				},
			)

			continue
		}

		if promotion.GetOneTimeDiscount() != nil {
			promotions = append(promotions, convertOneTimeDiscountToPromotion(
				cartItems,
				promotion,
			))

			continue
		}

		if couponKeyToIdxMap[getCouponDeductionKey(promotion.GetCouponSource())] == idx &&
			needPreviewPromotionsIdx < len(needPreviewPromotions) {
			// 如果是 membership discount && fixed amount 类型，需要打平，所有 service 应该单独享有 discount 而不是共享 discount
			if needPreviewPromotions[needPreviewPromotionsIdx].SourceType == promotionpb.Source_MEMBERSHIP_DISCOUNT &&
				needPreviewPromotions[needPreviewPromotionsIdx].DiscountType == orderpb.DiscountType_FIXED_AMOUNT {
				for _, item := range needPreviewPromotions[needPreviewPromotionsIdx].PromotionItems {
					promotions = append(promotions, &model.OrderPromotion{
						ID:              needPreviewPromotions[needPreviewPromotionsIdx].ID,
						PromotionID:     needPreviewPromotions[needPreviewPromotionsIdx].PromotionID,
						SourceType:      needPreviewPromotions[needPreviewPromotionsIdx].SourceType,
						SourceID:        needPreviewPromotions[needPreviewPromotionsIdx].SourceID,
						SourceSubjectID: needPreviewPromotions[needPreviewPromotionsIdx].SourceSubjectID,
						Name:            needPreviewPromotions[needPreviewPromotionsIdx].Name,
						DiscountType:    needPreviewPromotions[needPreviewPromotionsIdx].DiscountType,
						DiscountValue:   needPreviewPromotions[needPreviewPromotionsIdx].DiscountValue,
						AppliedAmount:   needPreviewPromotions[needPreviewPromotionsIdx].AppliedAmount,
						Status:          orderpb.OrderPromotionModel_PREVIEW,
						PromotionItems: []*model.OrderPromotionItem{
							{
								OrderItemID:    item.OrderItemID,
								CartItemType:   item.CartItemType,
								CartItemID:     item.CartItemID,
								Subtotal:       item.Subtotal,
								ExternalUUID:   item.ExternalUUID,
								DeductQuantity: item.DeductQuantity,
							},
						},
					})
				}
			} else {
				promotions = append(promotions, needPreviewPromotions[needPreviewPromotionsIdx])
			}

			needPreviewPromotionsIdx++
		}
	}

	return promotions, nil
}

func (ps *promotionService) AsyncRedeemCoupons(
	ctx context.Context,
	redeemParams *model.RedeemPromotionParams,
) {
	if err := ants.Go(ctx, time.Minute, func(ctx context.Context) {
		if err := ps.RedeemCoupons(ctx, redeemParams); err != nil {
			zlog.Error(ctx, "redeem promotion err, wait for retry",
				zap.Any("redeemParams", redeemParams),
				zap.Error(err))
		}
	},
	); err != nil {
		zlog.Error(ctx, "go redeem promotion err", zap.Error(err))
	}
}

func (ps *promotionService) redeemStoreCredit(
	ctx context.Context,
	orderID int64,
	customerID int64,
	storeCredit *model.OrderPromotion,
) error {
	if storeCredit == nil {
		return nil
	}

	if storeCredit.Status != orderpb.OrderPromotionModel_CREATED {
		zlog.Info(
			ctx, "order promotion status is not created, skip redeem",
			zap.Int64("order_promotion_id", storeCredit.ID),
			zap.Stringer("status", storeCredit.Status),
		)

		return nil
	}

	// 这里只会有一个的
	if err := ps.subscriptionClient.RedeemStoreCredit(ctx,
		storeCredit.AppliedAmount.Mul(core.MinorUnits).IntPart(),
		orderID,
		customerID,
	); err != nil {
		return err
	}

	// update db
	if err := ps.orderPromotionRepo.BatchUpdateToApplied(ctx,
		[]int64{storeCredit.ID},
		[]orderpb.OrderPromotionModel_Status{
			orderpb.OrderPromotionModel_CREATED,
		},
	); err != nil {
		return err
	}

	return nil
}

func (ps *promotionService) RedeemCoupons(
	ctx context.Context,
	redeemParams *model.RedeemPromotionParams,
) error {
	var storeCreditPromotion *model.OrderPromotion

	unStoreCreditPromotions := make([]*model.OrderPromotion, 0, len(redeemParams.OrderPromotions))

	for _, op := range redeemParams.OrderPromotions {
		if op.IsStoreCredit() {
			storeCreditPromotion = op
			continue
		}

		unStoreCreditPromotions = append(unStoreCreditPromotions, op)
	}

	// store credit 需要单独redeem, 理论上这里只会有一个 store credit
	if err := ps.redeemStoreCredit(ctx, redeemParams.OrderID, redeemParams.CustomerID, storeCreditPromotion); err != nil {
		return err
	}

	redeems := make([]*promotionpb.CouponRedeem, 0, len(unStoreCreditPromotions))

	orderPromotionIDList := make([]int64, 0, len(unStoreCreditPromotions))

	for _, op := range unStoreCreditPromotions {
		// 如果不是 CRAETED 状态，则不需要 redeem
		if op.Status != orderpb.OrderPromotionModel_CREATED {
			zlog.Info(
				ctx, "order promotion status is not created, skip redeem",
				zap.Int64("order_promotion_id", op.ID),
				zap.Stringer("status", op.Status),
			)

			continue
		}

		orderPromotionIDList = append(orderPromotionIDList, op.ID)

		// one time discount 和 store credit 不需要 redeem
		if op.IsOneTimeDiscount() || op.IsStoreCredit() {
			continue
		}

		couponSources := &promotionpb.Source{
			Id:   op.SourceID,
			Type: op.SourceType,
		}

		switch op.SourceType {
		case promotionpb.Source_DISCOUNT:
			couponSources.Subject = &promotionpb.Source_Discount{
				Discount: &promotionpb.DiscountSubject{
					Id: op.SourceSubjectID,
				},
			}
		case promotionpb.Source_PACKAGE:
			couponSources.Subject = &promotionpb.Source_Package{
				Package: &promotionpb.PackageSubject{
					Id: op.SourceSubjectID,
				},
			}
		case promotionpb.Source_MEMBERSHIP_DISCOUNT, promotionpb.Source_MEMBERSHIP_QUANTITY:
			couponSources.Subject = &promotionpb.Source_Membership{
				Membership: &promotionpb.MembershipSubject{
					Id: op.SourceSubjectID,
				},
			}
		default:
		}

		targets := make([]*promotionpb.CouponRedeem_RedeemTarget, 0, len(op.PromotionItems))

		for _, item := range op.PromotionItems {
			amount := item.Subtotal.Mul(core.MinorUnits).IntPart()

			// 如果是 pkg / membership quantity 类型的需要传递对应的抵扣个数
			if op.IsDeduction() {
				amount = int64(item.DeductQuantity)
			}

			var targetType promotionpb.TargetType

			switch item.CartItemType {
			case orderpb.ItemType_ITEM_TYPE_PRODUCT:
				targetType = promotionpb.TargetType_PRODUCT
			case orderpb.ItemType_ITEM_TYPE_SERVICE:
				targetType = promotionpb.TargetType_SERVICE
			case orderpb.ItemType_ITEM_TYPE_SERVICE_CHARGE:
				targetType = promotionpb.TargetType_SERVICE_CHARGE
			default:
				continue
			}

			targets = append(
				targets, &promotionpb.CouponRedeem_RedeemTarget{
					TargetType:     targetType,
					TargetId:       item.CartItemID,
					OrderItemId:    item.OrderItemID,
					IdempotenceKey: fmt.Sprintf("order_promotion_item_%d", item.ID),
					Amount:         amount,
					TargetSales:    money.FromDecimal(item.AppliedAmount, ""), // 这里指的是 promotion 具体抵扣了多少钱
				},
			)
		}

		redeems = append(
			redeems, &promotionpb.CouponRedeem{
				CouponSources: couponSources,
				Targets:       targets,
			},
		)
	}

	_, err := ps.promotionClient.RedeemCoupons(
		ctx,
		redeemParams.OrderID,
		redeemParams.CustomerID,
		redeemParams.AppointmentID,
		redeemParams.OrderTotalAmount,
		redeems,
	)
	if err != nil {
		return err
	}

	if updatePromotionErr := ps.orderPromotionRepo.BatchUpdateToApplied(
		ctx,
		orderPromotionIDList,
		[]orderpb.OrderPromotionModel_Status{
			orderpb.OrderPromotionModel_CREATED,
		},
	); updatePromotionErr != nil {
		return updatePromotionErr
	}

	return nil
}

// 如果传入的是 autoApply, 则需要调用 PromotionService 进行推荐.
//
//nolint:unused //TODO:(kuroko) 暂时先不管，在 API-V3 先进行了 auto 到 manual 的转换
func (ps *promotionService) autoApply(
	ctx context.Context,
	customerID int64,
	cartItems []*ordersvcpb2.PreviewCreateOrderRequest_CartItem,
) ([]*model.OrderPromotion, error) {
	items := make([]*promotionpb.CouponApplicationTarget, 0)

	for _, item := range cartItems {
		targetType := model.ConvertItemType2TargetType(item.GetItemType())
		if targetType == promotionpb.TargetType_TARGET_TYPE_UNSPECIFIED {
			continue
		}

		items = append(items, &promotionpb.CouponApplicationTarget{
			TargetType:     targetType,
			TargetId:       item.GetItemId(),
			UnitPrice:      item.GetUnitPrice(),
			TargetQuantity: item.GetQuantity(),
		})
	}

	couponResp, err := ps.promotionClient.RecommendCoupons(
		ctx,
		customerID,
		items,
	)
	if err != nil {
		return nil, err
	}

	return lo.Map(
		couponResp.GetRecommendedCoupons(),
		func(cu *promotionpb.CouponUsage, _ int) *model.OrderPromotion {
			return model.ConvertCouponUsageToOrderPromotion(cu)
		},
	), nil
}

func getCouponDeductionKey(source *promotionpb.Source) string {
	var ID int64

	switch {
	case source.GetDiscount() != nil:
		ID = source.GetDiscount().GetId()
	case source.GetMembership() != nil:
		ID = source.GetMembership().GetId()
	case source.GetPackage() != nil:
		ID = source.GetPackage().GetId()
	default:
		return ""
	}

	return fmt.Sprintf("%s_%d_%d", source.GetType().String(), source.GetId(), ID)
}

func convertDeductionsToPromotions(
	deductions []*promotionpb.TargetDeduction,
	orderItemID2ExternalUUIDMap map[int64]string,
) []*model.OrderPromotion {
	if deductions == nil {
		return nil
	}

	type OrderItem struct {
		TargetType        promotionpb.TargetType
		TargetID          int64
		OrderItemID       int64
		OriginalAmount    *gmoney.Money
		DeductionQuantity int32
	}

	couponDeductionMap := make(
		map[string]struct {
			CouponDeduction *promotionpb.CouponDeduction
			OrderItemList   []OrderItem
		}, 0,
	)

	for _, deduction := range deductions {
		for _, couponDeduction := range deduction.GetCouponDeductions() {
			key := getCouponDeductionKey(couponDeduction.GetCoupon().GetSource())
			currentCouponDeduction := couponDeductionMap[key]
			currentCouponDeduction.CouponDeduction = couponDeduction

			if deduction.OrderItemId != nil {
				currentCouponDeduction.OrderItemList = append(
					currentCouponDeduction.OrderItemList,
					OrderItem{
						TargetType:        deduction.GetTargetType(),
						TargetID:          deduction.GetTargetId(),
						OrderItemID:       deduction.GetOrderItemId(),
						OriginalAmount:    deduction.GetOriginalAmount(),
						DeductionQuantity: couponDeduction.GetQuantity(),
					},
				)
			}

			couponDeductionMap[key] = currentCouponDeduction
		}
	}

	// 保存着哪些 order item 的 external UUID 已经在 package 中了
	alreadyInPkgUUID := make(map[string]struct{})
	promotions := make([]*model.OrderPromotion, 0)

	for _, data := range couponDeductionMap {
		orderItemList := lo.UniqBy(
			data.OrderItemList, func(o OrderItem) int64 {
				return o.OrderItemID
			},
		)

		promotionItems := make([]*model.OrderPromotionItem, 0, len(orderItemList))
		totalAmount := decimal.Zero

		for _, o := range orderItemList {
			totalAmount = totalAmount.Add(money.ToDecimal(o.OriginalAmount))

			itemType := model.ConvertTargetType2ItemType(o.TargetType)
			if itemType == orderpb.ItemType_ITEM_TYPE_UNSPECIFIED {
				continue
			}

			// 如果是 pkg 抵扣类型，记录下来item
			if data.CouponDeduction.GetCoupon().GetSource().GetType() == promotionpb.Source_PACKAGE ||
				data.CouponDeduction.GetCoupon().GetSource().GetType() == promotionpb.Source_MEMBERSHIP_QUANTITY {
				alreadyInPkgUUID[orderItemID2ExternalUUIDMap[o.OrderItemID]] = struct{}{}

				promotionItems = append(
					promotionItems, &model.OrderPromotionItem{
						OrderItemID:    o.OrderItemID,
						CartItemType:   itemType,
						CartItemID:     o.TargetID,
						Subtotal:       money.ToDecimal(o.OriginalAmount),
						ExternalUUID:   orderItemID2ExternalUUIDMap[o.OrderItemID],
						DeductQuantity: o.DeductionQuantity,
					},
				)

				continue
			}

			// 如果不是 pkg 抵扣类型，且已经在 package 中了，则跳过
			if _, ok := alreadyInPkgUUID[orderItemID2ExternalUUIDMap[o.OrderItemID]]; !ok {
				var discountCodeID int64
				if data.CouponDeduction.GetCoupon().GetSource().GetType() == promotionpb.Source_DISCOUNT {
					discountCodeID = data.CouponDeduction.GetCoupon().GetSource().GetId()
				}

				promotionItems = append(
					promotionItems, &model.OrderPromotionItem{
						OrderItemID:    o.OrderItemID,
						CartItemType:   itemType,
						CartItemID:     o.TargetID,
						Subtotal:       money.ToDecimal(o.OriginalAmount),
						ExternalUUID:   orderItemID2ExternalUUIDMap[o.OrderItemID],
						DeductQuantity: 0,
						DiscountCodeID: discountCodeID,
					},
				)
			}
		}

		var appliedAmount, discountValue decimal.Decimal

		var discountType orderpb.DiscountType

		switch {
		case data.CouponDeduction.GetCoupon().GetDiscount().GetFixedAmount() != nil:
			discountType = orderpb.DiscountType_FIXED_AMOUNT
			discountValue = money.ToDecimal(data.CouponDeduction.GetCoupon().GetDiscount().GetFixedAmount())
			appliedAmount = discountValue
		case data.CouponDeduction.GetCoupon().GetDiscount().GetPercentage() != nil:
			discountType = orderpb.DiscountType_PERCENTAGE
			discountValue = convertGoogleDecimal(data.CouponDeduction.GetCoupon().GetDiscount().GetPercentage())
			// 这里 percentage 是百分号钱的数字，所以需要除以 100
			appliedAmount = totalAmount.Mul(core.RateForCal(discountValue))
		default:
			discountType = orderpb.DiscountType_ITEM_DEDUCTION
			discountValue = decimal.NewFromInt(data.CouponDeduction.GetCoupon().GetDiscount().GetDeduction())
			appliedAmount = totalAmount
		}

		// 计算折扣金额
		promotionItems = allocateDiscountWithRounding(
			totalAmount,
			promotionItems,
			appliedAmount,
			helper.AmountPrecision,
		)

		promotions = append(
			promotions, &model.OrderPromotion{
				ID:              data.CouponDeduction.GetCoupon().GetId(),
				PromotionID:     data.CouponDeduction.GetCoupon().GetPromotionId(),
				SourceType:      data.CouponDeduction.GetCoupon().GetSource().GetType(),
				SourceID:        data.CouponDeduction.GetCoupon().GetSource().GetId(),
				SourceSubjectID: getSourceSubjectID(data.CouponDeduction.GetCoupon().GetSource()),
				Name:            data.CouponDeduction.GetCoupon().GetName(),
				DiscountType:    discountType,
				DiscountValue:   discountValue,
				AppliedAmount:   appliedAmount,
				Status:          orderpb.OrderPromotionModel_PREVIEW,
				PromotionItems:  promotionItems,
			},
		)
	}

	return promotions
}

func getSourceSubjectID(source *promotionpb.Source) int64 {
	if source.GetDiscount() != nil {
		return source.GetDiscount().GetId()
	}

	if source.GetPackage() != nil {
		return source.GetPackage().GetId()
	}

	if source.GetMembership() != nil {
		return source.GetMembership().GetId()
	}

	return 0
}

func convertOneTimeDiscountToPromotion(
	cartItems []*ordersvcpb2.PreviewCreateOrderRequest_CartItem,
	applyPromotion *ordersvcpb2.PreviewCreateOrderRequest_Promotion,
) *model.OrderPromotion {
	totalAmount := decimal.Zero
	oneTimeDiscount := applyPromotion.GetOneTimeDiscount()

	// 转成 map ，判断快速一点
	uuidSet := lo.SliceToMap(applyPromotion.GetCartItemExternalUuids(), func(s string) (string, struct{}) {
		return s, struct{}{}
	})

	// 过滤出来需要作用 one time discount 的 cart item
	finalCartItem := lo.Filter(cartItems, func(cartItem *ordersvcpb2.PreviewCreateOrderRequest_CartItem, _ int) bool {
		_, exists := uuidSet[cartItem.GetExternalUuid()]
		return exists
	})

	for _, cartItem := range finalCartItem {
		unitPrice := money.ToDecimal(cartItem.GetUnitPrice())
		subTotal := unitPrice.Mul(decimal.NewFromInt32(cartItem.GetQuantity()))
		totalAmount = totalAmount.Add(subTotal)
	}

	var discountType orderpb.DiscountType

	var discountValue, totalDiscountAmount decimal.Decimal

	var promotionItems []*model.OrderPromotionItem

	if oneTimeDiscount.GetDiscountPercentage() != nil {
		discountType = orderpb.DiscountType_PERCENTAGE
		discountValue = convertGoogleDecimal(oneTimeDiscount.GetDiscountPercentage())

		promotionItems = lo.Map(finalCartItem,
			func(it *ordersvcpb2.PreviewCreateOrderRequest_CartItem, _ int) *model.OrderPromotionItem {
				unitPrice := money.ToDecimal(it.GetUnitPrice())
				subTotal := unitPrice.Mul(decimal.NewFromInt32(it.GetQuantity()))
				discountRate := core.RateForCal(discountValue)
				appliedAmount := core.RoundPreTaxAmount(subTotal.Mul(discountRate))
				totalDiscountAmount = totalDiscountAmount.Add(appliedAmount)

				return &model.OrderPromotionItem{
					AppliedAmount:  appliedAmount,
					DeductQuantity: 0,
					CartItemType:   it.ItemType,
					CartItemID:     it.ItemId,
					ExternalUUID:   it.ExternalUuid,
					UnitPrice:      unitPrice,
					Subtotal:       subTotal,
					DiscountCodeID: 0,
				}
			})
	} else {
		discountType = orderpb.DiscountType_FIXED_AMOUNT
		discountValue = money.ToDecimal(oneTimeDiscount.GetDiscountAmount())
		totalDiscountAmount = discountValue

		promotionItems = lo.Map(finalCartItem,
			func(it *ordersvcpb2.PreviewCreateOrderRequest_CartItem, _ int) *model.OrderPromotionItem {
				unitPrice := money.ToDecimal(it.GetUnitPrice())
				subTotal := unitPrice.Mul(decimal.NewFromInt32(it.GetQuantity()))

				return &model.OrderPromotionItem{
					DeductQuantity: 0,
					CartItemType:   it.ItemType,
					CartItemID:     it.ItemId,
					ExternalUUID:   it.ExternalUuid,
					UnitPrice:      unitPrice,
					Subtotal:       subTotal,
					DiscountCodeID: 0,
				}
			})

		promotionItems = allocateDiscountWithRounding(totalAmount, promotionItems,
			totalDiscountAmount, helper.AmountPrecision)
	}

	return &model.OrderPromotion{
		SourceType:     promotionpb.Source_DISCOUNT,
		SourceID:       model.OneTimeDiscountSourceID,
		DiscountType:   discountType,
		DiscountValue:  discountValue,
		AppliedAmount:  totalDiscountAmount,
		Status:         orderpb.OrderPromotionModel_PREVIEW,
		PromotionItems: promotionItems,
	}
}

func allocateDiscountWithRounding(
	totalSubtotal decimal.Decimal,
	items []*model.OrderPromotionItem,
	totalDiscount decimal.Decimal,
	precision int32,
) []*model.OrderPromotionItem {
	if len(items) == 0 {
		return items
	}

	// Step 1: Weighted allocation
	distributedTotal := decimal.NewFromInt(0)

	for i := range items {
		if totalSubtotal.IsZero() {
			items[i].AppliedAmount = decimal.Zero
			continue
		}

		ratio := items[i].Subtotal.Div(totalSubtotal)
		rounded := ratio.Mul(totalDiscount).RoundBank(precision)
		items[i].AppliedAmount = rounded
		distributedTotal = distributedTotal.Add(rounded)
	}

	// Step 2: Compute remainder
	diff := totalDiscount.Sub(distributedTotal)

	// Step 3: Apply remainder to the item with the largest subtotal
	if !diff.IsZero() {
		// Find index with largest subtotal
		sort.SliceStable(
			items, func(i, j int) bool {
				return items[i].Subtotal.GreaterThan(items[j].Subtotal)
			},
		)

		items[0].AppliedAmount = items[0].AppliedAmount.Add(diff)
	}

	return items
}

func convertGoogleDecimal(g *gdecimal.Decimal) decimal.Decimal {
	if g == nil {
		return decimal.Zero
	}

	d, err := decimal.NewFromString(g.GetValue())
	if err != nil {
		return decimal.Zero
	}

	return d
}
