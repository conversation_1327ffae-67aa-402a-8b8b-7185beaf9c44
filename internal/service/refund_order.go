package service

import (
	"context"
	"errors"
	"sort"
	"strings"
	"time"

	"github.com/samber/lo"
	"go.uber.org/zap"
	"golang.org/x/time/rate"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/proto"

	"github.com/MoeGolibrary/go-lib/common/proto/money"
	"github.com/MoeGolibrary/go-lib/merror"
	"github.com/MoeGolibrary/go-lib/zlog"
	errorspb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/errors/v1"
	orderpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/order/v1"
	paymentpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/payment/v1"
	paymentv2pb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/payment/v2"
	ordersvcpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/order/v1"
	orderv2svcpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/order/v2"
	utilsV2 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/utils/v2"
	"github.com/MoeGolibrary/moego-svc-order-v2/internal/model"
	"github.com/MoeGolibrary/moego-svc-order-v2/internal/repo"
	"github.com/MoeGolibrary/moego-svc-order-v2/internal/repo/depositrule"
	"github.com/MoeGolibrary/moego-svc-order-v2/internal/repo/file"
	"github.com/MoeGolibrary/moego-svc-order-v2/internal/repo/grooming"
	"github.com/MoeGolibrary/moego-svc-order-v2/internal/repo/message"
	"github.com/MoeGolibrary/moego-svc-order-v2/internal/service/helper"
)

// OrderDetailGetter 是 preview 过程中用的，因为 deposit order 和 sales order 互相之间会需要反查对面。
// TODO(Perqin, P2): 有更好的设计吗？deductedDepositOrderDetail 是不是也能改用这个来获取？
type OrderDetailGetter interface {
	GetDetail(ctx context.Context, orderID int64) (*model.OrderDetail, error)
}

type RefundOrderService interface {
	PreviewRefundOrder(
		ctx context.Context, orderDetail *model.OrderDetail, req *ordersvcpb.PreviewRefundOrderRequest,
		og OrderDetailGetter,
	) (*ordersvcpb.PreviewRefundOrderResponse, error)
	PreviewRefundOrderPayments(
		ctx context.Context, req *ordersvcpb.PreviewRefundOrderPaymentsRequest,
	) (*ordersvcpb.PreviewRefundOrderPaymentsResponse, error)
	RefundOrder(
		ctx context.Context, orderDetail *model.OrderDetail, req *ordersvcpb.RefundOrderRequest,
		og OrderDetailGetter,
	) (*ordersvcpb.RefundOrderResponse, error)

	GetDepositDetailForRefund(ctx context.Context, order *model.Order) (*model.DepositDetail, error)

	GetRefundDetail(ctx context.Context, refundID int64) (*model.RefundOrderDetail, error)
	ListRefund(ctx context.Context, orderID int64) ([]*model.RefundOrder, error)
	ListRefundDetail(ctx context.Context, orderID int64) ([]*model.RefundOrderDetail, error)

	SyncRefundOrderPayment(ctx context.Context, refundOrderPaymentID int64) (total, synced int64, err error)
	TrySyncRefundTransaction(
		ctx context.Context, rop *model.RefundOrderPayment, rp model.RefundPaymentor,
	) error
	TrySyncRefundTransactionByRefundPaymentID(
		ctx context.Context, ropID int64, refundModel *paymentv2pb.RefundModel,
	) error
	// ListRefundOrderPaymentDetail lists RefundOrderPayment **as well as** legacy Refund, and merges them into a list.
	ListRefundOrderPaymentDetail(
		ctx context.Context, req *orderv2svcpb.ListRefundOrderPaymentDetailRequest,
	) (*orderv2svcpb.ListRefundOrderPaymentDetailResponse, error)
	ExportRefundOrderPaymentDetailList(
		ctx context.Context, req *orderv2svcpb.ExportRefundOrderPaymentDetailListRequest,
	) (int64, error)
}

func NewRefundOrderService(
	orderRepo repo.OrderRepo,
	orderItemRepo repo.OrderItemRepo,
	orderPaymentRepo repo.OrderPaymentRepo,

	refundRepo repo.RefundOrderRepo,
	refundItemRepo repo.RefundOrderItemRepo,
	refundPaymentRepo repo.RefundOrderPaymentRepo,

	depositChangeLogRepo repo.DepositChangeLogRepo,

	legacyPaymentRepo repo.LegacyPaymentRepo,
	legacyRefundRepo repo.LegacyRefundRepo,

	organizationRepo depositrule.OrganizationRepo,
	customerRepo depositrule.CustomerRepo,

	txRepo repo.TXRepo,

	paymentCli repo.PaymentClient,
	groomingCli grooming.Client,
	messageCli message.Client,
	fileClient file.Client,
) RefundOrderService {
	return &refundOrderService{
		orderRepo:        orderRepo,
		orderItemRepo:    orderItemRepo,
		orderPaymentRepo: orderPaymentRepo,

		refundRepo:        refundRepo,
		refundItemRepo:    refundItemRepo,
		refundPaymentRepo: refundPaymentRepo,

		depositChangeLogRepo: depositChangeLogRepo,

		legacyPaymentRepo: legacyPaymentRepo,
		legacyRefundRepo:  legacyRefundRepo,

		organizationRepo: organizationRepo,
		customerRepo:     customerRepo,

		txRepo: txRepo,

		paymentCli:  paymentCli,
		groomingCli: groomingCli,
		messageCli:  messageCli,
		fileClient:  fileClient,
	}
}

type refundOrderService struct {
	orderRepo        repo.OrderRepo
	orderItemRepo    repo.OrderItemRepo
	orderPaymentRepo repo.OrderPaymentRepo

	refundRepo        repo.RefundOrderRepo
	refundItemRepo    repo.RefundOrderItemRepo
	refundPaymentRepo repo.RefundOrderPaymentRepo

	depositChangeLogRepo repo.DepositChangeLogRepo

	legacyPaymentRepo repo.LegacyPaymentRepo
	legacyRefundRepo  repo.LegacyRefundRepo

	organizationRepo depositrule.OrganizationRepo
	customerRepo     depositrule.CustomerRepo

	txRepo repo.TXRepo

	paymentCli  repo.PaymentClient
	groomingCli grooming.Client
	messageCli  message.Client
	fileClient  file.Client
}

func (svc *refundOrderService) ListRefund(ctx context.Context, orderID int64) ([]*model.RefundOrder, error) {
	return svc.refundRepo.ListByOrderID(ctx, orderID)
}

func (svc *refundOrderService) ListRefundDetail(ctx context.Context, orderID int64) (
	[]*model.RefundOrderDetail, error,
) {
	refundOrderDetails, err := svc.refundRepo.ListDetailByOrderID(ctx, orderID)
	if err != nil {
		return nil, err
	}

	if err := svc.attachExtraData(ctx, refundOrderDetails...); err != nil {
		return nil, err
	}

	return refundOrderDetails, nil
}

func (svc *refundOrderService) GetRefundDetail(ctx context.Context, refundOrderID int64) (
	*model.RefundOrderDetail, error,
) {
	refundOrderDetail, err := svc.refundRepo.GetDetail(ctx, refundOrderID)
	if err != nil {
		return nil, err
	}

	if err := svc.attachExtraData(ctx, refundOrderDetail); err != nil {
		return nil, err
	}

	return refundOrderDetail, nil
}

func (svc *refundOrderService) RefundOrder(
	ctx context.Context,
	orderDetail *model.OrderDetail,
	req *ordersvcpb.RefundOrderRequest,
	og OrderDetailGetter,
) (*ordersvcpb.RefundOrderResponse, error) {
	// 老订单走兼容逻辑.
	if orderDetail.OrderVersion().Lt(model.OrderVersionRefund) {
		return &ordersvcpb.RefundOrderResponse{}, svc.refundLegacyOrder(ctx, orderDetail, req)
	}

	rod, relatedOrderDetail, relatedROD, err := svc.buildRefundOrderDetailsByPreview(ctx, orderDetail, req, og)
	if err != nil {
		return nil, err
	}

	// 事务里更新后的 Order
	var updatedOrder, updatedRelatedOrder *model.Order

	// 一个大事务内更新 Order/Refund Order 以及对应的 Item / Order Payment 等
	txErr := svc.txRepo.Tx(
		func(tx repo.OrderTX) error {
			order, err := svc.getAndValidateOrderForUpdate(ctx, tx, orderDetail)
			if err != nil {
				return err
			}

			// 前端虽然约束了要先把 overpaid deposit refund，但我们还是在这里对关联的 deposit order / sales order 也加锁
			relatedOrder, err := svc.getAndValidateOrderForUpdate(ctx, tx, relatedOrderDetail)
			if err != nil {
				return err
			}

			if err := svc.saveRefund(ctx, tx, order, rod); err != nil {
				return err
			}

			if err := svc.saveRefund(ctx, tx, relatedOrder, relatedROD); err != nil {
				return err
			}

			updatedOrder = order
			updatedRelatedOrder = relatedOrder

			return nil
		},
	)
	if txErr != nil {
		return nil, txErr
	}

	// 尝试调用 Payment 发起退款，忽略错误.
	svc.tryCreateRefundTransaction(
		ctx,
		updatedOrder,
		rod.RefundOrder.RefundReason,
		orderDetail.OrderPayments,
		rod.RefundOrderPayments,
	)

	if relatedROD != nil {
		svc.tryCreateRefundTransaction(
			ctx,
			updatedRelatedOrder,
			relatedROD.RefundOrder.RefundReason,
			relatedOrderDetail.OrderPayments,
			relatedROD.RefundOrderPayments,
		)
	}

	ro, roi, rop := svc.buildRefundResponse(rod)

	var relatedRefundOrders []*ordersvcpb.RefundOrderResponse_RelatedRefundOrder

	if relatedROD != nil {
		rro, rroi, rrop := svc.buildRefundResponse(relatedROD)
		relatedRefundOrders = append(
			relatedRefundOrders, &ordersvcpb.RefundOrderResponse_RelatedRefundOrder{
				RefundOrder:        rro,
				RefundOrderItems:   rroi,
				RefundOrderPayment: rrop,
			},
		)
	}

	return &ordersvcpb.RefundOrderResponse{
		RefundOrder:         ro,
		RefundOrderItems:    roi,
		RefundOrderPayment:  rop,
		RelatedRefundOrders: relatedRefundOrders,
	}, nil
}

// buildRefundOrderDetailsByPreview 复用 preview 接口获取退款 result，加上实际退款的请求参数来构建和校验 RefundOrderDetail。由于
// sales order 可能实付金额不足，需要同时发起 deposit order 的退款，所以返回值有两个 RefundOrderDetail。如果不需要发起 deposit
// refund，则第二个为 nil。
func (svc *refundOrderService) buildRefundOrderDetailsByPreview(
	ctx context.Context,
	orderDetail *model.OrderDetail,
	req *ordersvcpb.RefundOrderRequest,
	og OrderDetailGetter,
) (targetROD *model.RefundOrderDetail, relatedOD *model.OrderDetail, relatedROD *model.RefundOrderDetail, err error) {
	// 复用 Preview 逻辑
	var (
		refundOrderGetter, relatedRefundOrderGetter interface {
			GetOrderDetail() *model.OrderDetail
			GetRefundOrderDetail() *model.RefundOrderDetail
		}
		previewErr error
	)

	depositDetail, err := svc.GetDepositDetailForRefund(ctx, orderDetail.Order)
	if err != nil {
		return nil, nil, nil, err
	}

	refundOrderGetter, relatedRefundOrderGetter, previewErr = svc.previewRefundOrder(
		ctx, orderDetail, depositDetail, req, og, true,
	)
	if previewErr != nil {
		return nil, nil, nil, previewErr
	}

	targetROD = refundOrderGetter.GetRefundOrderDetail()
	relatedOD = relatedRefundOrderGetter.GetOrderDetail()
	relatedROD = relatedRefundOrderGetter.GetRefundOrderDetail()

	if targetROD.RefundOrder.GetRefundTotalAmount().IsPositive() && len(targetROD.RefundOrderPayments) == 0 {
		return nil, nil, nil, status.Error(codes.InvalidArgument, "must have at least one payment to refund")
	}

	targetROD.RefundOrder.RefundReason = req.GetRefundReason()
	targetROD.RefundOrder.StaffID = req.GetStaffId()

	// 对 deposit refund 做同样的处理
	if relatedROD != nil {
		if relatedROD.RefundOrder.GetRefundTotalAmount().IsPositive() && len(relatedROD.RefundOrderPayments) == 0 {
			return nil, nil, nil, status.Error(
				codes.InvalidArgument, "deposit must have at least one payment to refund",
			)
		}

		relatedROD.RefundOrder.RefundReason = req.GetRefundReason()
		relatedROD.RefundOrder.StaffID = req.GetStaffId()
	}

	return targetROD, relatedOD, relatedROD, nil
}

func (svc *refundOrderService) getAndValidateOrderForUpdate(
	ctx context.Context, tx repo.OrderTX, orderDetail *model.OrderDetail,
) (*model.Order, error) {
	// 不需要退到 deposit order 时可能传入 nil。
	if orderDetail == nil {
		return nil, nil
	}

	// 通过锁订单来规避并发问题.
	order, err := tx.Order().GetForUpdate(ctx, orderDetail.GetID())
	if err != nil {
		return nil, err
	}

	// 快速检查订单与 Preview 时查到的是否一致.
	if !order.RefundedAmount.Equal(orderDetail.Order.RefundedAmount) {
		return nil, status.Errorf(codes.FailedPrecondition, "order refunded amount changed: %d", orderDetail.GetID())
	}

	return order, nil
}

// saveRefund saves all related entities of a refund to the database. The persisting process is expected to be run
// within a transaction tx. All the related entities come from the previous preview API and are packed into the
// refundOrderDetail struct.
// Note:
// 1. The entities passed in are modified as needed;
// 2. The refundOrderDetail may be nil (like, un-needed to refund deposit), in which case the function does nothing.
func (svc *refundOrderService) saveRefund(
	ctx context.Context, tx repo.OrderTX, order *model.Order, refundOrderDetail *model.RefundOrderDetail,
) error {
	if refundOrderDetail == nil {
		return nil
	}

	refundOrder := refundOrderDetail.RefundOrder
	refundOrderItems := refundOrderDetail.RefundOrderItems
	refundOrderPayments := refundOrderDetail.RefundOrderPayments

	if order.SupportOrderPayment() {
		// 支持 Order Payment 的版本才创建 Refund Order 这一套实体.
		if err := tx.RefundOrder().Create(ctx, refundOrder); err != nil {
			return err
		}

		for _, it := range refundOrderItems {
			it.RefundOrderID = refundOrder.ID
			it.CompanyID = refundOrder.CompanyID
		}

		if err := tx.RefundItem().BatchCreate(ctx, refundOrderItems); err != nil {
			return err
		}

		for _, it := range refundOrderPayments {
			it.RefundOrderID = refundOrder.ID
			it.CompanyID = refundOrder.CompanyID
			it.StaffID = refundOrder.StaffID
		}

		if err := tx.RefundPayment().BatchCreate(ctx, refundOrderPayments); err != nil {
			return err
		}

		// 创建 DepositChangeLog: deposit order 依赖 order version 2
		if refundOrderDetail.ReversalDepositChangeLog != nil {
			refundOrderDetail.ReversalDepositChangeLog.DestOrderID = refundOrder.ID

			if err := tx.DepositChangeLog().Create(ctx, refundOrderDetail.ReversalDepositChangeLog); err != nil {
				return err
			}
		}
	}

	// 更新 Order.
	switch affectedRows, err := tx.Order().UpdateRefund(ctx, refundOrder); {
	case err != nil:
		return err

	case affectedRows == 0:
		return status.Error(codes.FailedPrecondition, "cannot update order")
	}

	// 更新 Item.
	for _, it := range refundOrderItems {
		switch affectedRows, err := tx.OrderItem().UpdateRefund(ctx, it); {
		case err != nil:
			return err

		case affectedRows == 0:
			return status.Error(codes.FailedPrecondition, "cannot update order item")
		}
	}

	// 更新 Payments.
	for _, it := range refundOrderPayments {
		switch affectedRows, err := tx.OrderPayment().UpdateRefund(ctx, it); {
		case err != nil:
			return err

		case affectedRows == 0:
			return status.Error(codes.FailedPrecondition, "cannot update order payment")
		}
	}

	return nil
}

func (svc *refundOrderService) buildRefundResponse(
	refundOrderDetail *model.RefundOrderDetail,
) (*orderpb.RefundOrderModel, []*orderpb.RefundOrderItemModel, []*orderpb.RefundOrderPaymentModel) {
	if refundOrderDetail == nil {
		return nil, nil, nil
	}

	return refundOrderDetail.RefundOrder.ToPB(),
		lo.Map(
			refundOrderDetail.RefundOrderItems,
			func(it *model.RefundOrderItem, _ int) *orderpb.RefundOrderItemModel { return it.ToPB() },
		),
		lo.Map(
			refundOrderDetail.RefundOrderPayments,
			func(it *model.RefundOrderPayment, _ int) *orderpb.RefundOrderPaymentModel { return it.ToPB() },
		)
}

func (svc *refundOrderService) refundLegacyOrder(
	ctx context.Context,
	od *model.OrderDetail,
	req *ordersvcpb.RefundOrderRequest,
) error {
	if req.GetRefundMode() != orderpb.RefundMode_REFUND_MODE_BY_PAYMENT {
		return status.Error(codes.InvalidArgument, "invalid refund mode")
	}

	refundByPayment := req.GetRefundByPayment()
	if !refundByPayment.GetRefundAmountFlags().GetIsConvenienceFeeIncluded() {
		for _, op := range od.OrderPayments {
			if !op.GetConvenienceFee().IsZero() {
				// 老的 Order Payment 本身没有记录 Convenience Fee.
				// 不应该进入这个 Case，不过以防万一，拦截一下.
				return status.Errorf(codes.InvalidArgument, "convenience fee must be included")
			}
		}
	}

	refundableChannels, err := svc.paymentCli.GetRefundableChannel(
		ctx,
		req.GetBusinessId(),
		req.GetOrderId(),
		money.ToDecimal(refundByPayment.GetRefundAmount()),
	)
	if err != nil {
		return err
	}

	// 过滤出选中的 RefundableChannels.
	idToChannel := lo.KeyBy(
		refundableChannels,
		func(rc *repo.RefundableChannel) int64 { return rc.PaymentID },
	)

	selectedChannels := make([]*repo.RefundableChannel, 0, len(refundByPayment.OrderPaymentIds))

	for _, id := range refundByPayment.OrderPaymentIds {
		if channel, ok := idToChannel[id]; ok {
			selectedChannels = append(selectedChannels, channel)
			continue
		}

		return status.Errorf(codes.NotFound, "payment[%d] not refundable", id)
	}

	if len(selectedChannels) == 0 {
		return status.Errorf(codes.InvalidArgument, "no refundable channel")
	}

	// 发起退款.
	return svc.paymentCli.CreateRefundByOrder(
		ctx, req.GetOrderId(), req.GetRefundReason(),
		money.ToDecimal(refundByPayment.GetRefundAmount()),
		selectedChannels,
	)
}

func (svc *refundOrderService) tryCreateRefundTransaction(
	ctx context.Context,
	order *model.Order,
	refundReason string,
	ops []*model.OrderPayment,
	rops []*model.RefundOrderPayment,
) {
	if len(rops) == 0 {
		return
	}

	idToOrderPayment := lo.SliceToMap(
		ops,
		func(it *model.OrderPayment) (int64, int64) { return it.ID, it.PaymentID },
	)

	reasonGetter := func() (string, error) { return refundReason, nil }
	paymentIDGetter := func(orderPaymentID int64) (int64, bool, error) {
		paymentID, ok := idToOrderPayment[orderPaymentID]

		return paymentID, ok, nil
	}

	svc.syncRefundOrderPaymentByOrder(
		ctx, order, reasonGetter, paymentIDGetter, rops,
		false, // 都是新的，没必要查
	)
}

func (svc *refundOrderService) TrySyncRefundTransactionByRefundPaymentID(
	ctx context.Context, ropID int64, refundModel *paymentv2pb.RefundModel,
) error {
	rop, err := svc.refundPaymentRepo.Get(ctx, ropID)
	if err != nil {
		zlog.Error(
			ctx, "get refund order payment error",
			zap.Int64("refundOrderPaymentID", ropID), zap.Error(err),
		)

		return err
	}

	return svc.TrySyncRefundTransaction(
		ctx, rop, repo.RefundPaymentBriefFromPB(refundModel),
	)
}

func (svc *refundOrderService) TrySyncRefundTransaction(
	ctx context.Context, rop *model.RefundOrderPayment, rp model.RefundPaymentor,
) error {
	var err error

	// 状态流转之前如果是卡在余额不足状态，需要发消息。ApplyRefundPayment 里会清空 reason，这里先判断好
	wasInsufficient := rop.IsStatusReasonInsufficient()

	updated, err := rop.ApplyRefundPayment(rp)
	if err != nil {
		return err
	}

	if !updated {
		// 没有变化.
		return nil
	}

	switch rop.RefundStatus {
	case orderpb.RefundOrderPaymentStatus_REFUND_ORDER_PAYMENT_STATUS_TRANSACTION_CREATED:
		return svc.refundPaymentRepo.UpdateTrxCreated(ctx, rop)

	case orderpb.RefundOrderPaymentStatus_REFUND_ORDER_PAYMENT_STATUS_REFUNDED:
		// 只有 Refunded 才需要考虑把对应的 RefundOrder 也更新成完成.
		if err := svc.trySyncRefundedOrderPayment(ctx, rop); err != nil {
			return err
		}

		if wasInsufficient {
			svc.tryNotifyRefundedOrderPayment(ctx, rop)
		}

		return nil

	case orderpb.RefundOrderPaymentStatus_REFUND_ORDER_PAYMENT_STATUS_FAILED:
		return svc.refundPaymentRepo.UpdateFailed(ctx, rop)

	default:
		return status.Errorf(codes.Internal, "unsupported refund order payment status: %s", rop.RefundStatus)
	}
}

func (svc *refundOrderService) trySyncRefundedOrderPayment(ctx context.Context, rop *model.RefundOrderPayment) error {
	if rop.RefundStatus != orderpb.RefundOrderPaymentStatus_REFUND_ORDER_PAYMENT_STATUS_REFUNDED {
		return status.Error(codes.FailedPrecondition, "refund order payment not refunded")
	}

	return svc.txRepo.Tx(
		func(tx repo.OrderTX) error {
			rod, err := tx.RefundOrder().GetForUpdate(ctx, rop.RefundOrderID)
			if err != nil {
				return err
			}

			refundPayments, err := tx.RefundPayment().ListByRefundOrderID(ctx, rod.ID)
			if err != nil {
				return err
			}

			// 除当前传入的 RefundOrderPayment 外，其他的 RefundOrderPayment 都已完成退款.
			// 那么就把 RefundOrder 也扭转到完成.
			canCompleteRefundOrder := true

			for _, rp := range refundPayments {
				if rp.ID == rop.ID {
					continue
				}

				if rp.RefundStatus != orderpb.RefundOrderPaymentStatus_REFUND_ORDER_PAYMENT_STATUS_REFUNDED {
					canCompleteRefundOrder = false
					break
				}
			}

			if canCompleteRefundOrder {
				updated, err := rod.MarkCompleted()
				if err != nil {
					return err
				}

				if !updated {
					// 不应该进入这个状态需要报错.
					return status.Error(codes.Internal, "cannot mark refund order completed")
				}

				if err := tx.RefundOrder().UpdateCompleted(ctx, rod); err != nil {
					return err
				}

				// 创建一个订单消息，通知 Accounting.
				if err := tx.MessageDeliveryRepo().CreateRefunded(ctx, rod); err != nil {
					return err
				}
			}

			// 不论要不要更新 RefundOrder 都应该更新 RefundOrderPayment.
			return tx.RefundPayment().UpdateRefunded(ctx, rop)
		},
	)
}

func (svc *refundOrderService) tryNotifyRefundedOrderPayment(ctx context.Context, rop *model.RefundOrderPayment) {
	req := &message.NotifyPaymentRefundedParams{
		BusinessID:     rop.BusinessID,
		StaffID:        rop.StaffID,
		CustomerID:     rop.CustomerID,
		OrderID:        rop.OrderID,
		RefundedAmount: rop.GetRefundAmount(),
	}
	if err := svc.messageCli.NotifyPaymentRefunded(ctx, req); err != nil {
		zlog.Warn(ctx, "failed to notify invoice refunded", zap.Error(err))
	}
}

func (svc *refundOrderService) attachExtraData(
	ctx context.Context, refundOrderDetails ...*model.RefundOrderDetail,
) error {
	allRefundOrderPayments := make([]*model.RefundOrderPayment, 0, len(refundOrderDetails))
	allRefundOrderItems := make([]*model.RefundOrderItem, 0, len(refundOrderDetails))

	for _, rod := range refundOrderDetails {
		allRefundOrderPayments = append(allRefundOrderPayments, rod.RefundOrderPayments...)
		allRefundOrderItems = append(allRefundOrderItems, rod.RefundOrderItems...)
	}

	if err := svc.attachPaymentExtra(ctx, allRefundOrderPayments); err != nil {
		return err
	}

	if err := svc.attachOrderItems(ctx, allRefundOrderItems); err != nil {
		return err
	}

	return nil
}

func (svc *refundOrderService) attachPaymentExtra(
	ctx context.Context, refundOrderPayments []*model.RefundOrderPayment,
) error {
	paymentIDs := lo.Map(
		lo.UniqBy(
			refundOrderPayments,
			func(it *model.RefundOrderPayment) int64 { return it.OrderPaymentID },
		),
		func(it *model.RefundOrderPayment, _ int) int64 { return it.OrderPaymentID },
	)

	sort.Slice(paymentIDs, func(i, j int) bool { return paymentIDs[i] < paymentIDs[j] })

	payments, err := svc.orderPaymentRepo.BatchGet(ctx, paymentIDs)
	if err != nil {
		return err
	}

	idToPayment := lo.KeyBy(payments, func(it *model.OrderPayment) int64 { return it.ID })

	for _, rop := range refundOrderPayments {
		rop.RefundPaymentMethod.AttachOrderPayment(idToPayment[rop.OrderPaymentID])
	}

	return nil
}

func (svc *refundOrderService) attachOrderItems(
	ctx context.Context, refundOrderItems []*model.RefundOrderItem,
) error {
	itemIDs := lo.Keys(lo.KeyBy(refundOrderItems, func(it *model.RefundOrderItem) int64 { return it.OrderItemID }))

	orderItems, err := svc.orderItemRepo.BatchGet(ctx, itemIDs)
	if err != nil {
		return err
	}

	if len(orderItems) == 0 {
		return nil
	}
	// attach staffs for service items from grooming pet details
	order, err := svc.orderRepo.Get(ctx, orderItems[0].OrderID)
	if err != nil {
		return err
	}

	if order.IsAppointment() {
		if err := helper.AttachStaff(ctx, svc.groomingCli, order, orderItems); err != nil {
			return err
		}
	}

	idToItem := lo.KeyBy(orderItems, func(it *model.OrderItem) int64 { return it.ID })

	for _, rop := range refundOrderItems {
		item, ok := idToItem[rop.OrderItemID]
		if !ok {
			continue
		}

		rop.AttachOrderItem(item)
	}

	return nil
}

func (svc *refundOrderService) SyncRefundOrderPayment(ctx context.Context, refundOrderPaymentID int64) (
	total, synced int64, err error,
) {
	logger := zlog.FromContext(ctx)

	var refundOrderPayments []*model.RefundOrderPayment

	if refundOrderPaymentID != 0 {
		// 指定只处理特定的 Refund Order Payment.
		var rop *model.RefundOrderPayment

		rop, err = svc.refundPaymentRepo.Get(ctx, refundOrderPaymentID)
		if err != nil {
			logger.Error(
				"get refund order payment failed",
				zap.Error(err),
				zap.Int64("refundOrderPaymentID", refundOrderPaymentID),
			)

			return 0, 0, err
		}

		refundOrderPayments = append(refundOrderPayments, rop)
	}

	if refundOrderPaymentID == 0 {
		// 定时任务只处理被异常中断的流程.
		// 这里偏移个 5 分钟减少与正常流程的冲突.
		const timeShift = 5 * time.Minute

		fiveMinutesAgo := time.Now().Add(timeShift).Unix()

		for _, targetStatus := range []orderpb.RefundOrderPaymentStatus{
			orderpb.RefundOrderPaymentStatus_REFUND_ORDER_PAYMENT_STATUS_CREATED,
			orderpb.RefundOrderPaymentStatus_REFUND_ORDER_PAYMENT_STATUS_TRANSACTION_CREATED,
		} {
			var rops []*model.RefundOrderPayment

			rops, err = svc.refundPaymentRepo.ListByStatusAndMaxUpdateTime(
				ctx, targetStatus, fiveMinutesAgo,
			)
			if err != nil {
				logger.Error(
					"list refund order payments failed",
					zap.Stringer(
						"refundOrderPaymentStatus",
						orderpb.RefundOrderPaymentStatus_REFUND_ORDER_PAYMENT_STATUS_CREATED,
					),
					zap.Error(err),
				)

				continue
			}

			refundOrderPayments = append(refundOrderPayments, rops...)
		}
	}

	total = int64(len(refundOrderPayments))
	if total == 0 {
		logger.Info("no refund order payment need to sync")
		return 0, 0, nil
	}

	groupByOrderID := lo.GroupBy(
		refundOrderPayments,
		func(it *model.RefundOrderPayment) int64 { return it.OrderID },
	)

	// 查 Order，后续会用到
	orders, err := svc.orderRepo.BatchGetOrders(ctx, lo.Keys(groupByOrderID))
	if err != nil {
		return 0, 0, err
	}

	ordersMap := lo.KeyBy(orders, func(it *model.Order) int64 { return it.ID })

	// 限制 50 TPS
	const TPS, Burst = 50, 1

	rateLimit := rate.NewLimiter(rate.Every(time.Second/TPS), Burst)

	// OrderID -> RefundOrderID -> RefundOrderPayments.
	for orderID := range groupByOrderID {
		order := ordersMap[orderID]

		groupByRefundOrderID := lo.GroupBy(
			groupByOrderID[orderID],
			func(it *model.RefundOrderPayment) int64 { return it.RefundOrderID },
		)

		paymentIDGetter := svc.syncHelperPaymentIDGetter(ctx, orderID)

		if err := rateLimit.Wait(ctx); err != nil {
			logger.Error("rate limit error", zap.Error(err))

			// 只有 ctx 超时，吞掉错误，返回已经同步的情况.
			return total, synced, nil
		}

		for refundOrderID := range groupByRefundOrderID {
			reasonGetter := svc.syncHelperRefundReasonGetter(ctx, refundOrderID)
			synced += svc.syncRefundOrderPaymentByOrder(
				ctx,
				order,
				reasonGetter,
				paymentIDGetter,
				groupByRefundOrderID[refundOrderID],
				true, // 补偿任务，先查一下
			)
		}
	}

	logger.Info("synced refund order payments", zap.Int64("total", total), zap.Int64("synced", synced))

	return total, synced, nil
}

func (svc *refundOrderService) syncRefundOrderPaymentByOrder(
	ctx context.Context,
	order *model.Order,
	reasonGetter func() (string, error),
	paymentIDGetter func(int64) (int64, bool, error),
	refundOrderPayments []*model.RefundOrderPayment,
	getBeforeCreate bool, // 用于控制是否在调用创建接口之前先尝试查询一下，主要用于补偿任务
) int64 {
	var synced int64

	getRefundPayment := func(ctx context.Context, rop *model.RefundOrderPayment) (model.RefundPaymentor, error) {
		if !getBeforeCreate {
			// 复用逻辑，直接返回不存在.
			return nil, merror.NewBizErrorWithCodeDefaultMsg(errorspb.Code_CODE_REFUND_NOT_FOUND)
		}

		return svc.paymentCli.GetRefundPayment(ctx, rop)
	}

	for _, rop := range refundOrderPayments {
		ropLog := zlog.FromContext(ctx).With(
			zap.Int64("refundOrderPaymentID", rop.ID),
			zap.Int64("refundOrderID", rop.RefundOrderID),
			zap.Int64("orderPaymentID", rop.OrderPaymentID),
			zap.Int64("businessID", rop.BusinessID),
		)

		rp, err := getRefundPayment(ctx, rop)
		if err != nil {
			var bizErr *merror.BizError
			if !errors.As(err, &bizErr) || bizErr.ErrCode() != errorspb.Code_CODE_REFUND_NOT_FOUND {
				ropLog.Error("get refund payment failed", zap.Error(err))
				continue
			}

			reason, err := reasonGetter()
			if err != nil {
				ropLog.Error("get refund reason failed", zap.Error(err))
				// 循环以 RefundOrder 展开，这里失败的话，都会失败，因此直接终止循环.
				return synced
			}

			// 之前未发起退款，需要补偿发起.
			paymentID, ok, err := paymentIDGetter(rop.OrderPaymentID)
			if err != nil {
				ropLog.Error("get refund payment failed", zap.Error(err))
				// 循环以 RefundOrder 展开，这里失败的话，都会失败，因此直接终止循环.
				return synced
			}

			// 不应该出现这个情况.
			if paymentID == 0 {
				ropLog.Error("paymentID is ZERO")

				continue
			}

			if !ok {
				ropLog.Error(
					"not found payment by orderPaymentID",
					zap.Int64("orderPaymentID", rop.OrderPaymentID),
				)

				continue
			}

			rp, err = svc.paymentCli.CreateRefundPayment(ctx, paymentID, reason, rop)
			if err != nil {
				ropLog.Error(
					"create refund transaction for refund order payment failed",
					zap.Error(err),
				)
				// 把失败信息记录到 reason，前端用于展示；失败了也不阻塞循环
				svc.trySyncFailedRefund(ctx, ropLog, order, rop, err)

				continue
			}

			ropLog.Info("refund transaction created", zap.Int64("refundPaymentID", rp.GetID()))
		}

		// 退款已经发起，复用同步流程.
		if err := svc.TrySyncRefundTransaction(ctx, rop, rp); err != nil {
			ropLog.Error(
				"sync refund transaction for refund order payment failed",
				zap.Int64("refundPaymentID", rp.GetID()),
				zap.Error(err),
			)

			continue
		}

		ropLog.Info(
			"synced refund order payment status",
			zap.Stringer("refundOrderPaymentStatus", rop.RefundStatus),
		)

		synced++
	}

	return synced
}

func (svc *refundOrderService) trySyncFailedRefund(
	ctx context.Context, ropLog *zap.Logger, order *model.Order, rop *model.RefundOrderPayment, err error,
) {
	// Parse reason
	reason := err.Error()

	var bizErr *merror.BizError
	if errors.As(err, &bizErr) {
		reason = bizErr.ErrMsg()
	}

	// 错误消息来自 server-payment，那边不知道该 refund 是否会被重试，所以这里特殊处理一下，把错误信息设置为带有重试的文案
	if order.OrderVersion >= model.OrderVersionRefund && strings.Contains(strings.ToLower(reason), "insufficient") {
		reason = "Your MoeGo Pay balance is currently insufficient to process. " +
			"The system will continue retrying periodically until the refund succeeds."
	}

	// 注意这里仅更新原因，不会流转 ROP 状态，余额不足导致创建 RefundPayment 失败时 ROP 需要保持 CREATED 状态以被筛选出来进行重试
	if err := svc.refundPaymentRepo.UpdateReason(
		ctx, rop.ID, reason, orderpb.RefundOrderPaymentStatus_REFUND_ORDER_PAYMENT_STATUS_CREATED,
	); err != nil {
		ropLog.Warn("failed to update refund order payment reason", zap.Error(err))
	}
}

func (svc *refundOrderService) syncHelperRefundReasonGetter(
	ctx context.Context, refundOrderID int64,
) func() (string, error) {
	var (
		refundOrder *model.RefundOrder
		err         error
	)

	return func() (string, error) {
		if err != nil {
			return "", err
		}

		if refundOrder != nil {
			return refundOrder.RefundReason, nil
		}

		refundOrder, err = svc.refundRepo.Get(ctx, refundOrderID)
		if err != nil {
			return "", err
		}

		return refundOrder.RefundReason, nil
	}
}

func (svc *refundOrderService) syncHelperPaymentIDGetter(
	ctx context.Context, orderID int64,
) func(int64) (int64, bool, error) {
	var (
		idToPaymentID map[int64]int64
		err           error
	)

	return func(id int64) (int64, bool, error) {
		if err != nil {
			return 0, false, err
		}

		if len(idToPaymentID) > 0 {
			paymentID, ok := idToPaymentID[id]
			return paymentID, ok, nil
		}

		orderPayments, listErr := svc.orderPaymentRepo.ListByOrderID(ctx, orderID)
		if listErr != nil {
			err = listErr
			return 0, false, listErr
		}

		idToPaymentID = lo.SliceToMap(
			orderPayments,
			func(it *model.OrderPayment) (int64, int64) { return it.ID, it.PaymentID },
		)

		paymentID, ok := idToPaymentID[id]

		return paymentID, ok, nil
	}
}

func (svc *refundOrderService) ListRefundOrderPaymentDetail(
	ctx context.Context, req *orderv2svcpb.ListRefundOrderPaymentDetailRequest,
) (*orderv2svcpb.ListRefundOrderPaymentDetailResponse, error) {
	// 确保一会传入的 PaginationRequest 一定不为 nil
	pageReq := &utilsV2.PaginationRequest{
		PageSize: proto.Int32(req.GetPagination().GetPageSize()),
		PageNum:  proto.Int32(req.GetPagination().GetPageNum()),
	}

	details, pageResp, err := svc.listRefundOrderPaymentDetail(
		ctx, req.GetCompanyId(), req.GetFilter(), req.GetOrderBys(), pageReq,
	)
	if err != nil {
		return nil, err
	}

	return &orderv2svcpb.ListRefundOrderPaymentDetailResponse{
		Pagination: pageResp,
		RefundOrderPaymentDetails: lo.Map(details, func(it *refundOrderPaymentDetailItem,
			_ int,
		) *orderv2svcpb.ListRefundOrderPaymentDetailResponse_RefundOrderPaymentDetail {
			return &orderv2svcpb.ListRefundOrderPaymentDetailResponse_RefundOrderPaymentDetail{
				Source:             it.Source,
				RefundOrderPayment: it.RefundOrderPayment.ToPB(),
			}
		}),
	}, nil
}

type refundOrderPaymentDetailItem struct {
	Source             *orderv2svcpb.ListRefundOrderPaymentDetailResponse_Source
	RefundOrderPayment *model.RefundOrderPayment
}

func (svc *refundOrderService) listRefundOrderPaymentDetail(
	ctx context.Context, companyID int64, filter *orderv2svcpb.ListRefundOrderPaymentDetailRequest_Filter,
	orderBys []*orderv2svcpb.ListRefundOrderPaymentDetailRequest_OrderBy, pageReq *utilsV2.PaginationRequest,
) (details []*refundOrderPaymentDetailItem, pageResp *utilsV2.PaginationResponse, err error) {
	// 拉取符合条件的全量数据，直接在内存中分页。
	// Refund 来自两个数据库，数据量又较小（截止 2025/08/26 最多的 company 也只有 1000 多单），所以可以容忍这种实现方案
	rops, legacyROPs, err := svc.listRefundOrderPaymentByConditions(ctx, companyID, filter)
	if err != nil {
		return nil, nil, err
	}

	unifiedROPs, pageResp := svc.unifyRefundOrderPayments(rops, legacyROPs, orderBys, pageReq)

	opMap, legacyOPMap, err := svc.getDetailMapsForRefundOrderPayments(ctx, rops, legacyROPs)
	if err != nil {
		return nil, nil, err
	}

	details = lo.Map(
		unifiedROPs,
		func(it *unifiedRefundOrderPayment, _ int) *refundOrderPaymentDetailItem {
			var op *model.OrderPayment

			switch it.Source {
			case orderv2svcpb.ListRefundOrderPaymentDetailResponse_REFUND_ORDER_PAYMENT:
				op = opMap[it.OrderPaymentID]
			case orderv2svcpb.ListRefundOrderPaymentDetailResponse_LEGACY_REFUND:
				op = legacyOPMap[it.OrderPaymentID]
			default:
			}

			// 把 OrderPayment 上的 extra 信息挂到 RefundOrderPayment 上
			it.RefundPaymentMethod.AttachOrderPayment(op)

			return &refundOrderPaymentDetailItem{
				Source: &orderv2svcpb.ListRefundOrderPaymentDetailResponse_Source{
					SourceType: it.Source,
					SourceId:   it.ID,
				},
				RefundOrderPayment: it.RefundOrderPayment,
			}
		},
	)

	return details, pageResp, nil
}

func (svc *refundOrderService) listRefundOrderPaymentByConditions(
	ctx context.Context, companyID int64, filter *orderv2svcpb.ListRefundOrderPaymentDetailRequest_Filter,
) (rops, legacyROPs []*model.RefundOrderPayment, err error) {
	conditions := &repo.ListRefundConditions{
		IDs:           filter.GetIds(),
		BusinessID:    filter.GetBusinessId(),
		CustomerID:    filter.GetCustomerId(),
		PaymentMethod: filter.GetPaymentMethod(),
		Vendor:        filter.GetVendor(),
		StartTime:     filter.GetStartTime().AsTime(),
		EndTime:       filter.GetEndTime().AsTime(),
		Statuses:      filter.GetStatuses(),
	}

	rops, err = svc.refundPaymentRepo.ListByConditions(ctx, companyID, conditions)
	if err != nil {
		return nil, nil, err
	}

	legacyROPs, err = svc.legacyRefundRepo.ListByConditions(ctx, companyID, conditions, true)
	if err != nil {
		return nil, nil, err
	}

	// Legacy refund 没有 method_id, vendor 字段，所以通过 payment 来过滤 - -
	if len(legacyROPs) > 0 &&
		(filter.GetPaymentMethod() != paymentpb.PaymentMethod_PAYMENT_METHOD_UNSPECIFIED || filter.GetVendor() != "") {
		legacyPaymentIDs := lo.Map(
			legacyROPs,
			func(it *model.RefundOrderPayment, _ int) int64 { return it.OrderPaymentID },
		)

		vendorOPs, verr := svc.legacyPaymentRepo.ListByIDsAndConditions(
			ctx, legacyPaymentIDs, filter.GetPaymentMethod(), filter.GetVendor(),
		)
		if verr != nil {
			return nil, nil, verr
		}

		vendorOPMap := lo.SliceToMap(vendorOPs, func(it *model.OrderPayment) (int64, bool) { return it.ID, true })
		legacyROPs = lo.Filter(legacyROPs, func(it *model.RefundOrderPayment, _ int) bool {
			_, ok := vendorOPMap[it.OrderPaymentID]
			return ok
		})
	}

	return rops, legacyROPs, nil
}

// RefundOrderPayment 带上 Source 字段，用来合并新老 Refund 进行排序。
type unifiedRefundOrderPayment struct {
	Source orderv2svcpb.ListRefundOrderPaymentDetailResponse_SourceType
	*model.RefundOrderPayment
}

func (svc *refundOrderService) unifyRefundOrderPayments(
	rops, legacyROPs []*model.RefundOrderPayment,
	orderBys []*orderv2svcpb.ListRefundOrderPaymentDetailRequest_OrderBy, pageReq *utilsV2.PaginationRequest,
) (finalROPs []*unifiedRefundOrderPayment, pageResp *utilsV2.PaginationResponse) {
	if len(orderBys) == 0 {
		orderBys = []*orderv2svcpb.ListRefundOrderPaymentDetailRequest_OrderBy{
			{
				Field: orderv2svcpb.ListRefundOrderPaymentDetailRequest_CREATE_TIME,
				Desc:  true,
			},
		}
	}

	total := len(rops) + len(legacyROPs)
	start := 0
	end := total

	if pageReq != nil {
		pageResp = &utilsV2.PaginationResponse{
			Total:    int32(total), //nolint:gosec // pb field
			PageSize: pageReq.GetPageSize(),
			PageNum:  pageReq.GetPageNum(),
		}

		start = int((pageReq.GetPageNum() - 1) * pageReq.GetPageSize())
		end = start + int(pageReq.GetPageSize())
	}

	if total == 0 || start >= total {
		return nil, pageResp
	}

	if end > total {
		end = total
	}

	mergedROPs := make([]*unifiedRefundOrderPayment, 0, len(rops)+len(legacyROPs))
	unifiedROPs := lo.Map(rops, func(rop *model.RefundOrderPayment, _ int) *unifiedRefundOrderPayment {
		return &unifiedRefundOrderPayment{
			Source:             orderv2svcpb.ListRefundOrderPaymentDetailResponse_REFUND_ORDER_PAYMENT,
			RefundOrderPayment: rop,
		}
	})
	mergedROPs = append(mergedROPs, unifiedROPs...)
	unifiedLegacyROPs := lo.Map(legacyROPs, func(rop *model.RefundOrderPayment, _ int) *unifiedRefundOrderPayment {
		return &unifiedRefundOrderPayment{
			Source:             orderv2svcpb.ListRefundOrderPaymentDetailResponse_LEGACY_REFUND,
			RefundOrderPayment: rop,
		}
	})
	mergedROPs = append(mergedROPs, unifiedLegacyROPs...)

	sort.Slice(mergedROPs, func(i, j int) bool {
		for _, orderBy := range orderBys {
			switch orderBy.Field {
			case orderv2svcpb.ListRefundOrderPaymentDetailRequest_CREATE_TIME:
				if mergedROPs[i].CreateTime == mergedROPs[j].CreateTime {
					continue
				}

				if orderBy.Desc {
					return mergedROPs[i].CreateTime > mergedROPs[j].CreateTime
				} else {
					return mergedROPs[i].CreateTime < mergedROPs[j].CreateTime
				}
			default:
			}
		}

		return false
	})

	return mergedROPs[start:end], pageResp
}

func (svc *refundOrderService) getDetailMapsForRefundOrderPayments(
	ctx context.Context, rops []*model.RefundOrderPayment, legacyROPs []*model.RefundOrderPayment,
) (opMap, legacyOPMap map[int64]*model.OrderPayment, err error) {
	// Get OPs
	opIDs := lo.Uniq(lo.Map(rops, func(it *model.RefundOrderPayment, _ int) int64 { return it.OrderPaymentID }))

	ops, err := svc.orderPaymentRepo.BatchGet(ctx, opIDs)
	if err != nil {
		return nil, nil, err
	}

	opMap = lo.KeyBy(ops, func(it *model.OrderPayment) int64 { return it.ID })

	legacyPaymentIDs := lo.Uniq(
		lo.Map(legacyROPs, func(it *model.RefundOrderPayment, _ int) int64 { return it.OrderPaymentID }),
	)

	legacyOPs, err := svc.legacyPaymentRepo.BatchGet(ctx, legacyPaymentIDs)
	if err != nil {
		return nil, nil, err
	}

	legacyOPMap = lo.KeyBy(legacyOPs, func(it *model.OrderPayment) int64 { return it.ID })

	return opMap, legacyOPMap, nil
}
