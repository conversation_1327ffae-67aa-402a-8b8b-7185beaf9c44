package service

import (
	"context"
	"fmt"
	"strconv"
	"time"

	"github.com/google/uuid"
	"github.com/samber/lo"

	orderpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/order/v1"
	organizationpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/organization/v1"
	orderv2svcpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/order/v2"
	"github.com/MoeGolibrary/moego-svc-order-v2/internal/model"
	"github.com/MoeGolibrary/moego-svc-order-v2/internal/service/export"
	customerpb "github.com/MoeGolibrary/moego/backend/proto/customer/v1"
)

// ExportRefundOrderPaymentDetailList
//
//nolint:dupl // 整体逻辑和 ExportOrderPaymentDetailList 是一样的
func (svc *refundOrderService) ExportRefundOrderPaymentDetailList(
	ctx context.Context, req *orderv2svcpb.ExportRefundOrderPaymentDetailListRequest,
) (int64, error) {
	details, _, err := svc.listRefundOrderPaymentDetail(ctx, req.GetCompanyId(), req.GetFilter(), req.GetOrderBys(), nil)
	if err != nil {
		return 0, err
	}

	tabler, err := svc.createRefundOrderPaymentDetailExportTabler(ctx, req.GetCompanyId(), details)
	if err != nil {
		return 0, err
	}

	fileType := req.GetFileType()
	if fileType == "" {
		fileType = export.FileTypeXlsx
	}

	fileName := svc.generateListRefundOrderPaymentDetailExportFileName(req.GetFileName(), req.GetFilter(), fileType)

	fileID, err := svc.fileClient.Create(ctx, req.GetStaffId(), req.GetCompanyId(), fileName)
	if err != nil {
		return 0, err
	}

	content, err := export.BuildTableExportContent(ctx, tabler, fileType)
	if err != nil {
		return 0, err
	}

	if err := svc.fileClient.Upload(ctx, fileID, content); err != nil {
		return 0, err
	}

	return fileID, nil
}

func (svc *refundOrderService) createRefundOrderPaymentDetailExportTabler(
	ctx context.Context, companyID int64,
	details []*refundOrderPaymentDetailItem,
) (export.Tabler, error) {
	pref, err := svc.organizationRepo.GetCompanyPreference(ctx, companyID)
	if err != nil {
		return nil, err
	}

	locationIDs := lo.Uniq(lo.Map(
		details,
		func(detail *refundOrderPaymentDetailItem, _ int) int64 { return detail.RefundOrderPayment.BusinessID },
	))

	locations, err := svc.organizationRepo.BatchGetLocation(ctx, locationIDs)
	if err != nil {
		return nil, err
	}

	locationMap := lo.KeyBy(locations, func(it *organizationpb.LocationModel) int64 { return it.GetId() })

	customerIDs := lo.Uniq(lo.Map(
		details,
		func(detail *refundOrderPaymentDetailItem, _ int) int64 { return detail.RefundOrderPayment.CustomerID },
	))

	customers, err := svc.customerRepo.BatchGetCustomer(ctx, companyID, customerIDs)
	if err != nil {
		return nil, err
	}

	customerMap := lo.KeyBy(customers, func(it *customerpb.Customer) int64 { return it.GetId() })

	orderIDs := lo.Uniq(lo.Map(
		details,
		func(detail *refundOrderPaymentDetailItem, _ int) int64 { return detail.RefundOrderPayment.OrderID },
	))

	orders, err := svc.orderRepo.BatchGetOrders(ctx, orderIDs)
	if err != nil {
		return nil, err
	}

	orderMap := lo.KeyBy(orders, func(order *model.Order) int64 { return order.ID })

	return &refundOrderPaymentDetailExportTabler{
		timezoneLocation: timeZonePBToLocation(pref.GetTimeZone()),
		currencySymbol:   pref.GetCurrencySymbol(),
		customerMap:      customerMap,
		locationMap:      locationMap,
		orderMap:         orderMap,
		details:          details,
	}, nil
}

func (svc *refundOrderService) generateListRefundOrderPaymentDetailExportFileName(
	fileName string, filter *orderv2svcpb.ListRefundOrderPaymentDetailRequest_Filter, fileType string,
) string {
	if fileName == "" {
		fileName = uuid.New().String()
	}

	var interval string

	start := filter.GetStartTime().AsTime()

	end := filter.GetEndTime().AsTime()

	if !start.Equal(time.Unix(0, 0)) && !end.Equal(time.Unix(0, 0)) {
		interval = fmt.Sprintf("(%s - %s)", start.Format("2006-01-02"), end.Format("2006-01-02"))
	}

	return fmt.Sprintf("%s%s.%s", fileName, interval, fileType)
}

const (
	columnRefundDateTime      = "date_time"
	columnRefundClientName    = "client_name"
	columnRefundAmount        = "amount"
	columnRefundMethod        = "method"
	columnRefundStatus        = "status"
	columnRefundAppointmentID = "appointment_id"
	columnRefundBusiness      = "business"
)

//nolint:mnd // column width are constants
var refundOrderPaymentDetailExportColumnConfigs = []export.ColumnConfig{
	// 06/05/2025 10:02 am
	{Key: columnRefundDateTime, Name: "Date & time", ColumnWidth: 30},
	{Key: columnRefundClientName, Name: "Client name", ColumnWidth: 25},
	{Key: columnRefundAmount, Name: "Amount", ColumnWidth: 25},
	// Credit card (Visa 1118)
	{Key: columnRefundMethod, Name: "Method", ColumnWidth: 25},
	{Key: columnRefundStatus, Name: "Status", ColumnWidth: 25},
	{Key: columnRefundAppointmentID, Name: "Appointment ID", ColumnWidth: 20},
	{Key: columnRefundBusiness, Name: "Business", ColumnWidth: 25},
}

type refundOrderPaymentDetailExportTabler struct {
	timezoneLocation *time.Location
	currencySymbol   string
	customerMap      map[int64]*customerpb.Customer
	locationMap      map[int64]*organizationpb.LocationModel
	orderMap         map[int64]*model.Order
	details          []*refundOrderPaymentDetailItem
}

func (t *refundOrderPaymentDetailExportTabler) RowCount() int {
	return len(t.details)
}

func (t *refundOrderPaymentDetailExportTabler) ColumnCount() int {
	return len(refundOrderPaymentDetailExportColumnConfigs)
}

func (t *refundOrderPaymentDetailExportTabler) ColumnConfig(col int) export.ColumnConfig {
	return refundOrderPaymentDetailExportColumnConfigs[col]
}

var displayRefundStatusMap = map[orderpb.RefundOrderPaymentStatus]string{
	orderpb.RefundOrderPaymentStatus_REFUND_ORDER_PAYMENT_STATUS_CREATED:             "Processing",
	orderpb.RefundOrderPaymentStatus_REFUND_ORDER_PAYMENT_STATUS_TRANSACTION_CREATED: "Processing",
	orderpb.RefundOrderPaymentStatus_REFUND_ORDER_PAYMENT_STATUS_REFUNDED:            "Successful",
	orderpb.RefundOrderPaymentStatus_REFUND_ORDER_PAYMENT_STATUS_FAILED:              "Failed",
	orderpb.RefundOrderPaymentStatus_REFUND_ORDER_PAYMENT_STATUS_CANCELED:            "Canceled",
}

func (t *refundOrderPaymentDetailExportTabler) CellText(row, col int) string {
	const sourceTypeAppointment = "appointment"

	data := t.details[row]
	refundOrderPayment := data.RefundOrderPayment
	order := t.orderMap[refundOrderPayment.OrderID]
	cfg := t.ColumnConfig(col)

	switch cfg.Key {
	case columnRefundDateTime:
		return time.Unix(refundOrderPayment.CreateTime, 0).
			In(t.timezoneLocation).Format("01/02/2006 03:04 PM")
	case columnRefundClientName:
		if customer, ok := t.customerMap[refundOrderPayment.CustomerID]; ok {
			return fmt.Sprintf("%s %s", customer.GetGivenName(), customer.GetFamilyName())
		}

		return ""
	case columnRefundAmount:
		const places = 2

		return fmt.Sprintf("%s%s", t.currencySymbol, refundOrderPayment.GetRefundAmount().StringFixed(places))
	case columnRefundMethod:
		return data.RefundOrderPayment.RefundPaymentMethod.GetDisplayName()
	case columnRefundStatus:
		if status, ok := displayRefundStatusMap[refundOrderPayment.RefundStatus]; ok {
			return status
		}

		return ""
	case columnRefundAppointmentID:
		if order.SourceType == sourceTypeAppointment {
			return strconv.FormatInt(order.SourceID, 10)
		}

		return ""
	case columnRefundBusiness:
		return t.locationMap[refundOrderPayment.BusinessID].GetName()
	default:
		return ""
	}
}
