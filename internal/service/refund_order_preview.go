package service

import (
	"context"

	"github.com/samber/lo"
	"github.com/shopspring/decimal"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/proto"

	"github.com/MoeGolibrary/go-lib/common/proto/money"
	orderpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/order/v1"
	ordersvcpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/order/v1"
	"github.com/MoeGolibrary/moego-svc-order-v2/internal/model"
	"github.com/MoeGolibrary/moego-svc-order-v2/internal/service/helper"
)

// PreviewRefundOrderPayments 是一个纯计算接口，所有输入都依赖外部传入.
// 因此这里不定义接收器.
func (*refundOrderService) PreviewRefundOrderPayments(
	_ context.Context, req *ordersvcpb.PreviewRefundOrderPaymentsRequest,
) (*ordersvcpb.PreviewRefundOrderPaymentsResponse, error) {
	if len(req.GetSourceOrderPayments()) == 0 {
		return nil, status.Error(codes.InvalidArgument, "source order payments is empty")
	}

	currencyCode := req.GetSourceOrderPayments()[0].GetCurrencyCode()
	refundAmount := money.ToDecimal(req.GetRefundAmount())
	orderPaymentIDs := lo.Map(
		req.GetSourceOrderPayments(),
		func(op *orderpb.OrderPaymentModel, _ int) int64 { return op.GetId() },
	)
	orderPayments := lo.Map(
		req.GetSourceOrderPayments(),
		func(op *orderpb.OrderPaymentModel, _ int) *model.OrderPayment {
			// 仅保留计算必要的字段，其他全部舍去.
			return &model.OrderPayment{
				ID: op.GetId(),
				PaymentMethod: model.PaymentMethod{
					ID:     op.GetPaymentMethodId(),
					Method: op.GetPaymentMethod(),
				},
				CurrencyCode:            op.GetCurrencyCode(),
				TotalAmount:             money.ToDecimal(op.GetTotalAmount()),
				Amount:                  money.ToDecimal(op.GetAmount()),
				RefundedAmount:          money.ToDecimal(op.GetRefundedAmount()),
				ProcessingFee:           money.ToDecimal(op.GetProcessingFee()),
				ConvenienceFee:          money.ToDecimal(op.GetPaymentConvenienceFee()),
				RefundedConvenienceFee:  money.ToDecimal(op.GetRefundedPaymentConvenienceFee()),
				PaymentTips:             money.ToDecimal(op.GetPaymentTips()),
				PaymentTipsBeforeCreate: money.ToDecimal(op.GetPaymentTipsBeforeCreate()),
				PaymentTipsAfterCreate:  money.ToDecimal(op.GetPaymentTipsAfterCreate()),
				PaymentStatus:           op.GetPaymentStatus(),
			}
		},
	)

	// 空 RefundHelper，仅调用纯计算的部分.
	rh := helper.NewRefundHelper(
		&model.OrderDetail{
			Order: &model.Order{
				CurrencyCode: currencyCode,
			},
			OrderPayments: orderPayments,
		}, nil, nil,
	)

	refundableOrderPayment, _, err := rh.BuildRefundablePayments(
		orderPayments, refundAmount, req.GetRefundAmountFlag(),
	)
	if err != nil {
		return nil, err
	}

	refundOrderPayments, err := rh.PreviewRefundOrderPayments(
		refundableOrderPayment, orderPaymentIDs, refundAmount, req.GetRefundAmountFlag(),
	)
	if err != nil {
		return nil, err
	}

	refundTotalAmount := decimal.Zero
	refundConvenienceFee := decimal.Zero

	for _, rop := range refundOrderPayments {
		refundTotalAmount = refundTotalAmount.Add(rop.GetRefundAmount())
		refundConvenienceFee = refundConvenienceFee.Add(rop.GetRefundConvenienceFee())
	}

	return &ordersvcpb.PreviewRefundOrderPaymentsResponse{
		RefundTotalAmount:    money.FromDecimal(refundTotalAmount, currencyCode),
		RefundConvenienceFee: money.FromDecimal(refundConvenienceFee, currencyCode),
		RefundOrderPayments: lo.Map(
			refundOrderPayments,
			func(rop *model.RefundOrderPayment, _ int) *orderpb.RefundOrderPaymentModel { return rop.ToPB() },
		),
	}, nil
}

func (svc *refundOrderService) PreviewRefundOrder(
	ctx context.Context, orderDetail *model.OrderDetail, req *ordersvcpb.PreviewRefundOrderRequest,
	og OrderDetailGetter,
) (*ordersvcpb.PreviewRefundOrderResponse, error) {
	depositDetail, err := svc.GetDepositDetailForRefund(ctx, orderDetail.Order)
	if err != nil {
		return nil, err
	}

	res, relatedRes, err := svc.previewRefundOrder(ctx, orderDetail, depositDetail, req, og, false)
	if err != nil {
		return nil, err
	}

	return svc.buildPreviewRefundOrderResponse(orderDetail, depositDetail, req.GetRefundMode(), res, relatedRes)
}

// PreviewRefundOrderRequest 是 PreviewRefundOrder 和 RefundOrder 的 request 模型都应该能返回的字段。
type PreviewRefundOrderRequest interface {
	GetRefundMode() orderpb.RefundMode
	GetRefundByPayment() *ordersvcpb.RefundOrderRequest_RefundByPayment
	GetRefundByItem() *ordersvcpb.RefundOrderRequest_RefundByItem
	GetSourceOrderPayments() []*ordersvcpb.RefundOrderRequest_OrderPayment
}

type PreviewRefundOrderResult struct {
	OrderDetail              *model.OrderDetail
	RefundOrderDetail        *model.RefundOrderDetail
	RefundableTips           decimal.Decimal
	RefundableConvenienceFee decimal.Decimal
	IsConvenienceFeeOptional bool
	RefundableOrderPayments  []*ordersvcpb.PreviewRefundOrderResponse_RefundableOrderPayment
	CanCombineOrderPayments  bool
}

func (r *PreviewRefundOrderResult) GetOrderDetail() *model.OrderDetail {
	if r == nil {
		return nil
	}

	return r.OrderDetail
}

func (r *PreviewRefundOrderResult) GetRefundOrderDetail() *model.RefundOrderDetail {
	if r == nil {
		return nil
	}

	return r.RefundOrderDetail
}

// previewRefundOrder 返回预览的 RefundOrderDetail。由于 sales order 可能实付金额不足，需要同时发起 deposit order 的退款，所以返回
// 值有两个 RefundOrderDetail。
// 当指定的 order 为 sales order 且退款金额超出 sales order 支付的金额时：第二个返回值为 deposit order 的 RefundOrderDetail。
// 当指定的 order 为 deposit order 且该订金已经被抵扣时，对该 order 发起 by payment 退款，第二个返回值为 sales order 的
// RefundOrderDetail。
// 其他情况下，第二个 RefundOrderDetail 返回 nil。
func (svc *refundOrderService) previewRefundOrder(
	ctx context.Context, orderDetail *model.OrderDetail, depositDetail *model.DepositDetail,
	req PreviewRefundOrderRequest, og OrderDetailGetter, previewRelatedPayments bool,
) (result, relatedResult *PreviewRefundOrderResult, err error) {
	if orderDetail.IsDeposit() {
		return svc.previewRefundDepositOrder(ctx, orderDetail, req, depositDetail, og)
	}

	return svc.previewRefundSalesOrder(ctx, orderDetail, depositDetail, req, og, previewRelatedPayments)
}

// previewRefundSalesOrder
// previewRelatedPayments - 仅 by item 的场景下有含义，表示对 related refund order 进行 preview 的时候，是否传入 order payments
// 以 preview 出正确的fee。前端在 by item refund UI 里通过 PreviewRefundOrder 接口 preview 出来的结果都是不含 fee 的，但是最终调用
// RefundOrder 的时候是应当含 fee 的，因此需要这个参数来区分。
func (svc *refundOrderService) previewRefundSalesOrder(
	ctx context.Context, orderDetail *model.OrderDetail, depositDetail *model.DepositDetail,
	req PreviewRefundOrderRequest, og OrderDetailGetter, previewRelatedPayments bool,
) (result, relatedResult *PreviewRefundOrderResult, err error) {
	switch req.GetRefundMode() {
	case orderpb.RefundMode_REFUND_MODE_BY_PAYMENT:
		res, err := svc.previewRefundOrderByPayment(ctx, orderDetail, req.GetRefundByPayment(), depositDetail)
		if err != nil {
			return nil, nil, err
		}

		return res, nil, nil

	case orderpb.RefundMode_REFUND_MODE_BY_ITEM:
		res, err := svc.previewRefundOrderByItem(
			ctx, orderDetail, req.GetRefundByItem(), req.GetSourceOrderPayments(), depositDetail, false,
		)
		if err != nil {
			return nil, nil, err
		}

		var deductedDepositRes *PreviewRefundOrderResult

		if res.RefundOrderDetail.RefundOrder.RefundDepositAmount.IsPositive() {
			depositOrderID, err := svc.getDeductedDepositOrderID(ctx, orderDetail.Order.ID)
			if err != nil {
				return nil, nil, err
			}

			deductedDepositOrderDetail, err := og.GetDetail(ctx, depositOrderID)
			if err != nil {
				return nil, nil, err
			}

			var orderPaymentID int64

			if previewRelatedPayments {
				// TODO(Perqin, P2): 暂时不考虑 deposit order 有多个 OrderPayment 的情况
				orderPayment, _ := lo.Find(
					deductedDepositOrderDetail.OrderPayments,
					func(it *model.OrderPayment) bool { return it.IsPaid() },
				)
				orderPaymentID = orderPayment.ID
			}

			byItem, orderPayments := svc.buildByItemParamsForDeductedDeposit(
				deductedDepositOrderDetail,
				res.RefundOrderDetail.RefundOrder.RefundDepositAmount,
				orderPaymentID,
			)

			// 外面那个 depositDetail 是上层通过 sales order id 查到的，是空的
			depositDetail, err := svc.GetDepositDetailForRefund(ctx, deductedDepositOrderDetail.Order)
			if err != nil {
				return nil, nil, err
			}

			deductedDepositRes, err = svc.previewRefundOrderByItem(
				ctx, deductedDepositOrderDetail, byItem, orderPayments, depositDetail, true,
			)
			if err != nil {
				return nil, nil, err
			}
		}

		return res, deductedDepositRes, nil

	default:
		return nil, nil, status.Error(codes.InvalidArgument, "invalid refund mode")
	}
}

// 目标 order 为 deposit order 的时候，需要分情况考虑：
// 1. 没有抵扣过：一律按照 by item 模式退，by payment 的情况下需要计算出对应的 by item。
// 2. 抵扣过：
// --1. 有 overpaid：必须先按照上面的 by item 退
// --2. 没有 overpaid：
// ----1. By item：不允许，因为已经没有 un-deducted deposit 了
// ----2. By payment：相当于针对抵扣的 sales order 进行 by payment 退款（但是指定了退到 deposit 部分的钱）
func (svc *refundOrderService) previewRefundDepositOrder(
	ctx context.Context, orderDetail *model.OrderDetail,
	req PreviewRefundOrderRequest, depositDetail *model.DepositDetail, og OrderDetailGetter,
) (result, relatedResult *PreviewRefundOrderResult, _ error) {
	byPayment := req.GetRefundByPayment()
	byPaymentCurrency := byPayment.GetRefundAmount().GetCurrencyCode()

	if !depositDetail.IsDeducted() {
		if req.GetRefundMode() == orderpb.RefundMode_REFUND_MODE_BY_PAYMENT {
			res, _, err := svc.previewRefundDepositAmountByPayment(ctx, orderDetail, byPayment, depositDetail)
			if err != nil {
				return nil, nil, err
			}

			return res, nil, nil
		}

		res, err := svc.previewRefundOrderByItem(
			ctx, orderDetail, req.GetRefundByItem(), req.GetSourceOrderPayments(), depositDetail, false,
		)
		if err != nil {
			return nil, nil, err
		}

		return res, nil, nil
	}

	if depositDetail.LatestChangeLog.Balance.IsPositive() {
		if req.GetRefundMode() != orderpb.RefundMode_REFUND_MODE_BY_ITEM {
			return nil, nil, status.Error(codes.InvalidArgument, "overpaid deposit must be refunded by item first")
		}

		res, err := svc.previewRefundOrderByItem(
			ctx, orderDetail, req.GetRefundByItem(), req.GetSourceOrderPayments(), depositDetail, false,
		)
		if err != nil {
			return nil, nil, err
		}

		return res, nil, nil
	}

	if req.GetRefundMode() != orderpb.RefundMode_REFUND_MODE_BY_PAYMENT {
		return nil, nil, status.Error(codes.InvalidArgument, "deposit order fully deducted, only by payment is allowed")
	}

	res, depositAmount, err := svc.previewRefundDepositAmountByPayment(
		ctx, orderDetail, byPayment, depositDetail,
	)
	if err != nil {
		return nil, nil, err
	}

	// 根据上面的 preview 先确定本次 by payment 退款实际退了多少 deposit，再从 sales order 里退同样的钱到 refunded_deposit_amount
	salesOrderID := depositDetail.GetDeductionDestOrderID()

	salesOrderDetail, err := og.GetDetail(ctx, salesOrderID)
	if err != nil {
		return nil, nil, err
	}

	salesRes, err := svc.previewRefundOrderByPayment(
		ctx,
		salesOrderDetail,
		&ordersvcpb.RefundOrderRequest_RefundByPayment{
			RefundAmount: money.FromDecimal(depositAmount, byPaymentCurrency),
			RefundAmountFlags: &ordersvcpb.RefundOrderRequest_RefundByPayment_RefundAmountFlags{
				// TODO(Perqin, P0): 和 yunxiang 确认一下这个标志的含义以及到底应该怎么用
				IsConvenienceFeeIncluded: true,
				IsDepositAmount:          proto.Bool(true),
			},
		},
		nil,
	)
	if err != nil {
		return nil, nil, err
	}

	return res, salesRes, nil
}

// 指定包含 fee 的金额来退 deposit，场景：
// 1. Deposit order 没有抵扣过
// 2. Deposit order 抵扣过，且没有 overpaid（这种情况实质上是对 sales order 进行 by payment 退款）
// 但因为 deposit order 里只有一个 item，我们规定所有退款实际上都走 by item 模式，因此需要一种方式保证倒算出来的 amount 正算回去后总金额
// 还是对的。这里的策略是：当倒算出的 item 金额 + fee 金额不等于指定的总金额时，优先增大 fee 金额的占比（即减少 item 金额或者增加 fee
// 金额），这样在试图退最后一笔的时候，直接将 item 金额指定为剩余的 item 金额，这个金额基本上是只会偏大的，正算出来的 fee 金额即使偏大也会
// 被复用的 helper 中的 calculateTaxByRateAndMax 的 max 参数限制住。
// TODO(Perqin, P1): Add test cases
func (svc *refundOrderService) previewRefundDepositAmountByPayment(
	ctx context.Context, orderDetail *model.OrderDetail, byPayment *ordersvcpb.RefundOrderRequest_RefundByPayment,
	depositDetail *model.DepositDetail,
) (*PreviewRefundOrderResult, decimal.Decimal, error) {
	// TODO(Perqin, P2): 暂时不考虑 deposit order 有多个 OrderPayment 的情况
	if len(byPayment.GetOrderPaymentIds()) != 1 {
		return nil, decimal.Zero, status.Error(
			codes.InvalidArgument, "exactly one order payment should be provided for refunding deposit order")
	}

	amountIncludingFee := money.ToDecimal(byPayment.GetRefundAmount())
	orderPaymentID := byPayment.GetOrderPaymentIds()[0]

	// 提前检查能不能退钱：因为 helper 里 by item 退的时候只能保证 item 不爆，但最后一笔退款按比例可能刚好算出来一个 item 不爆但 fee 爆
	// 了的金额，这里就提前拦截掉了
	if orderDetail.Order.GetRefundableAmount().LessThan(amountIncludingFee) {
		return nil, decimal.Zero, status.Error(codes.InvalidArgument, "refundable amount is not enough")
	}

	depositAmount, err := svc.calculateAmountBeforeFee(orderDetail, orderPaymentID, amountIncludingFee)
	if err != nil {
		return nil, decimal.Zero, err
	}

	byItem, orderPayments := svc.buildByItemParamsForDeductedDeposit(orderDetail, depositAmount, orderPaymentID)

	refundedOrders, err := svc.refundRepo.ListDetailByOrderID(ctx, orderDetail.GetID())
	if err != nil {
		return nil, decimal.Zero, err
	}

	res, err := svc.previewRefundOrderByItemWithROs(orderDetail, refundedOrders, byItem, orderPayments, depositDetail)
	if err != nil {
		return nil, decimal.Zero, err
	}

	diff := depositAmount.Add(res.RefundOrderDetail.RefundOrder.RefundConvenienceFee).Sub(amountIncludingFee)

	// Preview 出来的总金额多了，削掉 item 金额重新 preview 一下
	if diff.IsPositive() {
		depositAmount = depositAmount.Sub(diff)

		byItem, orderPayments = svc.buildByItemParamsForDeductedDeposit(orderDetail, depositAmount, orderPaymentID)

		res, err = svc.previewRefundOrderByItemWithROs(orderDetail, refundedOrders, byItem, orderPayments, depositDetail)
		if err != nil {
			return nil, decimal.Zero, err
		}

		diff = depositAmount.Add(res.RefundOrderDetail.RefundOrder.RefundConvenienceFee).Sub(amountIncludingFee)
	}

	// Preview 出来的总金额少了，直接补在 fee 上
	if diff.IsNegative() {
		adiff := diff.Abs()
		res.RefundOrderDetail.RefundOrder.RefundConvenienceFee = res.
			RefundOrderDetail.RefundOrder.GetRefundConvenienceFee().Add(adiff)
		res.RefundOrderDetail.RefundOrder.RefundTotalAmount = res.
			RefundOrderDetail.RefundOrder.GetRefundTotalAmount().Add(adiff)
		res.RefundOrderDetail.RefundOrderPayments[0].RefundConvenienceFee = res.
			RefundOrderDetail.RefundOrderPayments[0].GetRefundConvenienceFee().Add(adiff)
		res.RefundOrderDetail.RefundOrderPayments[0].RefundAmount = res.
			RefundOrderDetail.RefundOrderPayments[0].GetRefundAmount().Add(adiff)
		res.RefundableConvenienceFee = res.RefundableConvenienceFee.Add(adiff)
	}

	return res, depositAmount, nil
}

func (svc *refundOrderService) previewRefundOrderByPayment(
	ctx context.Context,
	od *model.OrderDetail,
	byPayment *ordersvcpb.RefundOrderRequest_RefundByPayment,
	depositDetail *model.DepositDetail,
) (*PreviewRefundOrderResult, error) {
	refundedOrders, err := svc.refundRepo.ListDetailByOrderID(ctx, od.GetID())
	if err != nil {
		return nil, err
	}

	input, err := helper.NewRefundHelper(od, refundedOrders, depositDetail).
		RefundByPayment(
			byPayment.GetOrderPaymentIds(),
			money.ToDecimal(byPayment.GetRefundAmount()),
			byPayment.GetRefundAmountFlags(),
		)
	if err != nil {
		return nil, err
	}

	return &PreviewRefundOrderResult{
		OrderDetail:              od,
		RefundOrderDetail:        input.RefundOrderDetail,
		RefundableTips:           input.RefundableTips,
		RefundableConvenienceFee: input.RefundableConvenienceFee,
		IsConvenienceFeeOptional: input.IsConvenienceFeeOptional,
		RefundableOrderPayments:  input.RefundableOrderPayments,
		CanCombineOrderPayments:  input.CanCombineOrderPayments,
	}, nil
}

func (svc *refundOrderService) previewRefundOrderByItem(
	ctx context.Context,
	od *model.OrderDetail,
	byItem *ordersvcpb.RefundOrderRequest_RefundByItem,
	orderPayments []*ordersvcpb.RefundOrderRequest_OrderPayment,
	depositDetail *model.DepositDetail,
	// 一般情况下，by item 退定金都是退的未抵扣的，但是在 overpaid 的情况下，退款的确有可能包含未抵扣的和已抵扣的，所以此时用这个参数来指定
	// 是否要忽略 change log 的生成，如果是退已抵扣的那部分就需要忽略。
	ignoreChangeLog bool,
) (*PreviewRefundOrderResult, error) {
	refundedOrders, err := svc.refundRepo.ListDetailByOrderID(ctx, od.GetID())
	if err != nil {
		return nil, err
	}

	input, err := helper.NewRefundHelper(od, refundedOrders, depositDetail).
		RefundByItem(byItem, orderPayments, ignoreChangeLog)
	if err != nil {
		return nil, err
	}

	return &PreviewRefundOrderResult{
		OrderDetail:              od,
		RefundOrderDetail:        input.RefundOrderDetail,
		RefundableTips:           input.RefundableTips,
		RefundableConvenienceFee: input.RefundableConvenienceFee,
		IsConvenienceFeeOptional: input.IsConvenienceFeeOptional,
		RefundableOrderPayments:  input.RefundableOrderPayments,
		CanCombineOrderPayments:  input.CanCombineOrderPayments,
	}, nil
}

// 为了避免重复调用 refundRepo.ListDetailByOrderID 单独抽出来的。
func (svc *refundOrderService) previewRefundOrderByItemWithROs(
	od *model.OrderDetail,
	refundedOrders []*model.RefundOrderDetail,
	byItem *ordersvcpb.RefundOrderRequest_RefundByItem,
	orderPayments []*ordersvcpb.RefundOrderRequest_OrderPayment,
	depositDetail *model.DepositDetail,
) (*PreviewRefundOrderResult, error) {
	input, err := helper.NewRefundHelper(od, refundedOrders, depositDetail).RefundByItem(byItem, orderPayments, false)
	if err != nil {
		return nil, err
	}

	return &PreviewRefundOrderResult{
		OrderDetail:              od,
		RefundOrderDetail:        input.RefundOrderDetail,
		RefundableTips:           input.RefundableTips,
		RefundableConvenienceFee: input.RefundableConvenienceFee,
		IsConvenienceFeeOptional: input.IsConvenienceFeeOptional,
		RefundableOrderPayments:  input.RefundableOrderPayments,
		CanCombineOrderPayments:  input.CanCombineOrderPayments,
	}, nil
}

func (svc *refundOrderService) buildPreviewRefundOrderResponse(
	orderDetail *model.OrderDetail, depositDetail *model.DepositDetail, refundMode orderpb.RefundMode,
	res *PreviewRefundOrderResult, relatedRes *PreviewRefundOrderResult,
) (*ordersvcpb.PreviewRefundOrderResponse, error) {
	currencyCode := orderDetail.Order.CurrencyCode

	var relatedRefundOrders []*ordersvcpb.PreviewRefundOrderResponse_RelatedRefundOrder

	if relatedRes != nil {
		relatedRefundOrders = append(relatedRefundOrders, &ordersvcpb.PreviewRefundOrderResponse_RelatedRefundOrder{
			Order:              relatedRes.OrderDetail.ToPB(),
			PreviewRefundOrder: relatedRes.RefundOrderDetail.ToPB(),
		})
	}

	var refundableItems []*ordersvcpb.PreviewRefundOrderResponse_RefundableItem

	// By Payment 模式没有可退的 Items.
	if refundMode == orderpb.RefundMode_REFUND_MODE_BY_ITEM {
		refundableItems = svc.buildRefundableItems(orderDetail.OrderItems, depositDetail)
	}

	return &ordersvcpb.PreviewRefundOrderResponse{
		Order:               orderDetail.ToPB(),
		PreviewRefundOrder:  res.RefundOrderDetail.ToPB(),
		RelatedRefundOrders: relatedRefundOrders,
		RefundableItems:     refundableItems,
		RefundableTips:      money.FromDecimal(res.RefundableTips, currencyCode),
		RefundFlags: &ordersvcpb.PreviewRefundOrderResponse_RefundFlags{
			CanCombineOrderPayments:        res.CanCombineOrderPayments,
			IsRefundConvenienceFeeOptional: res.IsConvenienceFeeOptional,
		},
		RefundableOrderPayments:  res.RefundableOrderPayments,
		RefundableConvenienceFee: money.FromDecimal(res.RefundableConvenienceFee, currencyCode),
	}, nil
}

// 对 Sales order 退款时，实付金额不足，多余的退款金额需要退到当初抵扣的 deposit order 去，用这个函数构建参数，直接指定需要退的金额。
func (svc *refundOrderService) buildByItemParamsForDeductedDeposit(
	orderDetail *model.OrderDetail, refundDepositAmount decimal.Decimal, orderPaymentID int64,
) (*ordersvcpb.RefundOrderRequest_RefundByItem, []*ordersvcpb.RefundOrderRequest_OrderPayment) {
	byItem := &ordersvcpb.RefundOrderRequest_RefundByItem{
		RefundItems: []*ordersvcpb.RefundOrderRequest_RefundByItem_RefundItem{
			{
				OrderItemId:    orderDetail.OrderItems[0].ID,
				RefundItemMode: orderpb.RefundItemMode_REFUND_ITEM_MODE_BY_AMOUNT,
				RefundItemModeParam: &ordersvcpb.RefundOrderRequest_RefundByItem_RefundItem_RefundAmount{
					RefundAmount: money.FromDecimal(refundDepositAmount, orderDetail.Order.CurrencyCode),
				},
			},
		},
		RefundTips: money.FromDecimal(decimal.Zero, orderDetail.Order.CurrencyCode),
	}

	var orderPayments []*ordersvcpb.RefundOrderRequest_OrderPayment
	if orderPaymentID != 0 {
		orderPayments = append(orderPayments, &ordersvcpb.RefundOrderRequest_OrderPayment{Id: orderPaymentID})
	}

	return byItem, orderPayments
}

func (svc *refundOrderService) calculateAmountBeforeFee(
	orderDetail *model.OrderDetail, orderPaymentID int64, amountIncludingFee decimal.Decimal,
) (decimal.Decimal, error) {
	orderPayment, found := lo.Find(
		orderDetail.OrderPayments,
		func(op *model.OrderPayment) bool { return op.ID == orderPaymentID },
	)
	if !found {
		return decimal.Zero, status.Error(codes.InvalidArgument, "order payment not found")
	}

	convenienceFeeRate := decimal.Zero
	if !orderPayment.GetTotalAmount().IsZero() {
		convenienceFeeRate = orderPayment.GetConvenienceFee().
			Div(orderPayment.GetTotalAmount()).Round(helper.RatePrecision)
	}

	refundConvenienceFeeAmount := amountIncludingFee.Mul(convenienceFeeRate).Round(helper.AmountPrecision)
	refundItemAmount := amountIncludingFee.Sub(refundConvenienceFeeAmount)

	return refundItemAmount, nil
}

func (svc *refundOrderService) buildRefundableItems(
	orderItems []*model.OrderItem,
	depositDetail *model.DepositDetail,
) []*ordersvcpb.PreviewRefundOrderResponse_RefundableItem {
	return lo.Map(
		orderItems, func(it *model.OrderItem, _ int) *ordersvcpb.PreviewRefundOrderResponse_RefundableItem {
			if it.ItemType == model.OrderItemTypeDeposit {
				return &ordersvcpb.PreviewRefundOrderResponse_RefundableItem{
					OrderItemId:    it.ID,
					RefundItemMode: orderpb.RefundItemMode_REFUND_ITEM_MODE_BY_AMOUNT,
					IsRefundable:   true,
					RefundBy: &ordersvcpb.PreviewRefundOrderResponse_RefundableItem_RefundableAmount{
						// deposit by item 退款只能退还未使用的金额，从 deposit balance 中获取
						RefundableAmount: money.FromDecimal(depositDetail.LatestChangeLog.Balance, it.CurrencyCode),
					},
				}
			}

			return it.ToRefundableItem()
		},
	)
}

func (svc *refundOrderService) GetDepositDetailForRefund(
	ctx context.Context, order *model.Order,
) (*model.DepositDetail, error) {
	if order.OrderType != orderpb.OrderModel_DEPOSIT {
		return nil, nil
	}

	changeLogs, err := svc.depositChangeLogRepo.ListByDepositOrderID(ctx, order.ID)
	if err != nil {
		return nil, err
	}

	if len(changeLogs) == 0 {
		return nil, status.Error(codes.FailedPrecondition, "no deposit change log found")
	}

	return &model.DepositDetail{
		DepositChangeLogs: changeLogs,
		LatestChangeLog:   changeLogs[0],
	}, nil
}

// getDeductedDepositOrderID 查询指定订单所抵扣的 deposit order 的 ID。
func (svc *refundOrderService) getDeductedDepositOrderID(ctx context.Context, destOrderID int64) (int64, error) {
	changeLog, err := svc.depositChangeLogRepo.GetDeductionByDestOrderID(ctx, destOrderID)
	if err != nil {
		return 0, err
	}

	return changeLog.DepositOrderID, nil
}
