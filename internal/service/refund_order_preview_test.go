package service

import (
	"context"
	"testing"

	"github.com/shopspring/decimal"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/suite"
	"google.golang.org/protobuf/proto"

	"github.com/MoeGolibrary/go-lib/common/proto/money"
	orderpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/order/v1"
	ordersvcpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/order/v1"
	mocks "github.com/MoeGolibrary/moego-svc-order-v2/internal/mocks/repo"
	"github.com/MoeGolibrary/moego-svc-order-v2/internal/model"
)

type RefundOrderPreviewTestSuite struct {
	suite.Suite
}

func TestRefundOrderPreview(t *testing.T) {
	suite.Run(t, new(RefundOrderPreviewTestSuite))
}

func (ts *RefundOrderPreviewTestSuite) TestPreviewRefundOrderPayments() {
	req := &ordersvcpb.PreviewRefundOrderPaymentsRequest{
		RefundAmount:     money.FromDecimal(ts.mustDecimal("277.97"), "USD"),
		RefundAmountFlag: &ordersvcpb.RefundOrderRequest_RefundByPayment_RefundAmountFlags{IsConvenienceFeeIncluded: false},
		SourceOrderPayments: []*orderpb.OrderPaymentModel{
			{
				Id:                            1,
				CurrencyCode:                  "USD",
				TotalAmount:                   money.FromDecimal(ts.mustDecimal("377.78"), "USD"),
				Amount:                        money.FromDecimal(ts.mustDecimal("364.64"), "USD"),
				RefundedAmount:                money.FromDecimal(ts.mustDecimal("317.72"), "USD"),
				ProcessingFee:                 money.FromDecimal(ts.mustDecimal("13.14"), "USD"),
				PaymentConvenienceFee:         money.FromDecimal(ts.mustDecimal("13.14"), "USD"),
				RefundedPaymentConvenienceFee: money.FromDecimal(ts.mustDecimal("11.05"), "USD"),
				PaymentTips:                   money.FromDecimal(ts.mustDecimal("-1.00"), "USD"),
				PaymentTipsBeforeCreate:       money.FromDecimal(ts.mustDecimal("-1.00"), "USD"),
				PaymentTipsAfterCreate:        money.FromDecimal(ts.mustDecimal("-1.00"), "USD"),
				PaymentStatus:                 orderpb.OrderPaymentStatus_ORDER_PAYMENT_STATUS_PAID,
			},
			{
				Id:                            2,
				CurrencyCode:                  "USD",
				TotalAmount:                   money.FromDecimal(ts.mustDecimal("220.00"), "USD"),
				Amount:                        money.FromDecimal(ts.mustDecimal("0.00"), "USD"),
				RefundedAmount:                money.FromDecimal(ts.mustDecimal("0.00"), "USD"),
				ProcessingFee:                 money.FromDecimal(ts.mustDecimal("-1.00"), "USD"),
				PaymentConvenienceFee:         money.FromDecimal(ts.mustDecimal("0.00"), "USD"),
				RefundedPaymentConvenienceFee: money.FromDecimal(ts.mustDecimal("0.00"), "USD"),
				PaymentTips:                   money.FromDecimal(ts.mustDecimal("-1.00"), "USD"),
				PaymentTipsBeforeCreate:       money.FromDecimal(ts.mustDecimal("-1.00"), "USD"),
				PaymentTipsAfterCreate:        money.FromDecimal(ts.mustDecimal("-1.00"), "USD"),
				PaymentStatus:                 orderpb.OrderPaymentStatus_ORDER_PAYMENT_STATUS_PAID,
			},
		},
	}

	svc := &refundOrderService{}
	expected := &ordersvcpb.PreviewRefundOrderPaymentsResponse{
		RefundTotalAmount:    money.FromDecimal(ts.mustDecimal("280.06"), "USD"),
		RefundConvenienceFee: money.FromDecimal(ts.mustDecimal("2.09"), "USD"),
		RefundOrderPayments: []*orderpb.RefundOrderPaymentModel{
			{
				OrderPaymentId:              1,
				CurrencyCode:                "USD",
				RefundTotalAmount:           money.FromDecimal(ts.mustDecimal("60.06"), "USD"),
				RefundPaymentConvenienceFee: money.FromDecimal(ts.mustDecimal("2.09"), "USD"),
			},
			{
				OrderPaymentId:              2,
				CurrencyCode:                "USD",
				RefundTotalAmount:           money.FromDecimal(ts.mustDecimal("220.00"), "USD"),
				RefundPaymentConvenienceFee: money.FromDecimal(ts.mustDecimal("0.00"), "USD"),
			},
		},
	}

	actual, err := svc.PreviewRefundOrderPayments(context.Background(), req)
	ts.Require().NoError(err)
	ts.Require().NotNil(actual)
	ts.Require().Len(actual.RefundOrderPayments, len(expected.RefundOrderPayments))

	ts.Equal(expected.RefundTotalAmount.String(), actual.RefundTotalAmount.String())
	ts.Equal(expected.RefundConvenienceFee.String(), actual.RefundConvenienceFee.String())

	for i := 0; i < len(expected.RefundOrderPayments); i++ {
		actualRop := actual.RefundOrderPayments[i]
		expectedRop := expected.RefundOrderPayments[i]

		ts.Equal(expectedRop.GetOrderPaymentId(), actualRop.GetOrderPaymentId())
		ts.Equal(expectedRop.GetCurrencyCode(), actualRop.GetCurrencyCode())
		ts.Equal(expectedRop.GetRefundTotalAmount().String(), actualRop.GetRefundTotalAmount().String())
		ts.Equal(
			expectedRop.GetRefundPaymentConvenienceFee().String(),
			actualRop.GetRefundPaymentConvenienceFee().String(),
		)
	}
}

// 退 sales order，该 order 使用了全部订金抵扣，剩余的金额已另行支付，现在 by item 退款，但是金额小，只需要退到尾款单。
func (ts *RefundOrderPreviewTestSuite) TestRefundOrderService_previewRefundOrder_DepositDeductedSalesPaidRefundByItemToOneDest() {
	refundRepo := new(mocks.RefundOrderRepo)

	srv := &refundOrderService{
		refundRepo: refundRepo,
	}

	refundRepo.EXPECT().ListDetailByOrderID(mock.Anything, int64(1)).Return(nil, nil)
	salesResult, deductedDepositResult, err := srv.previewRefundOrder(
		context.Background(),
		&model.OrderDetail{
			Order: &model.Order{
				ID:              1,
				Status:          orderpb.OrderStatus_COMPLETED,
				OrderVersion:    model.OrderVersionRefund,
				CurrencyCode:    "USD",
				TipsAmount:      decimal.Zero,
				TaxAmount:       decimal.Zero,
				DiscountAmount:  decimal.Zero,
				DepositAmount:   ts.mustDecimal("20"),
				ConvenienceFee:  ts.mustDecimal("4.36"),
				SubTotalAmount:  ts.mustDecimal("135.00"),
				TipsBasedAmount: decimal.Zero,
				TotalAmount:     ts.mustDecimal("119.36"),
				PaidAmount:      ts.mustDecimal("119.36"),
				RemainAmount:    decimal.Zero,
				RefundedAmount:  decimal.Zero,
			},
			OrderItems: []*model.OrderItem{
				{
					ID: 11,
					Tax: model.Tax{
						Rate:   decimal.Zero,
						Amount: decimal.Zero,
					},
					Quantity:               1,
					UnitPrice:              ts.mustDecimal("45.00"),
					TipsAmount:             decimal.Zero,
					DiscountAmount:         decimal.Zero,
					SubTotalAmount:         ts.mustDecimal("45.00"),
					TotalAmount:            ts.mustDecimal("45.00"),
					RefundedAmount:         decimal.Zero,
					RefundedTaxAmount:      decimal.Zero,
					RefundedDiscountAmount: decimal.Zero,
				},
				{
					ID: 12,
					Tax: model.Tax{
						Rate:   decimal.Zero,
						Amount: decimal.Zero,
					},
					Quantity:               1,
					UnitPrice:              ts.mustDecimal("90.00"),
					TipsAmount:             decimal.Zero,
					DiscountAmount:         decimal.Zero,
					SubTotalAmount:         ts.mustDecimal("90.00"),
					TotalAmount:            ts.mustDecimal("90.00"),
					RefundedAmount:         decimal.Zero,
					RefundedTaxAmount:      decimal.Zero,
					RefundedDiscountAmount: decimal.Zero,
				},
			},
			OrderPayments: []*model.OrderPayment{
				{
					ID:                     22,
					CurrencyCode:           "USD",
					TotalAmount:            ts.mustDecimal("119.36"),
					Amount:                 ts.mustDecimal("115"),
					RefundedAmount:         decimal.Zero,
					ProcessingFee:          ts.mustDecimal("4.36"),
					ConvenienceFee:         ts.mustDecimal("4.36"),
					RefundedConvenienceFee: decimal.Zero,
					PaymentStatus:          orderpb.OrderPaymentStatus_ORDER_PAYMENT_STATUS_PAID,
				},
			},
		},
		nil,
		&ordersvcpb.RefundOrderRequest{
			OrderId:             1,
			BusinessId:          100001,
			StaffId:             200001,
			RefundReason:        "Unsatisfactory",
			RefundMode:          orderpb.RefundMode_REFUND_MODE_BY_ITEM,
			SourceOrderPayments: []*ordersvcpb.RefundOrderRequest_OrderPayment{{Id: 22}},
			RefundBy: &ordersvcpb.RefundOrderRequest_RefundByItem_{
				RefundByItem: &ordersvcpb.RefundOrderRequest_RefundByItem{
					RefundItems: []*ordersvcpb.RefundOrderRequest_RefundByItem_RefundItem{
						{
							OrderItemId:    11,
							RefundItemMode: orderpb.RefundItemMode_REFUND_ITEM_MODE_BY_AMOUNT,
							RefundItemModeParam: &ordersvcpb.RefundOrderRequest_RefundByItem_RefundItem_RefundAmount{
								RefundAmount: money.FromDecimal(ts.mustDecimal("10"), "USD"),
							},
						},
					},
					RefundTips: money.FromDecimal(decimal.Zero, "USD"),
				},
			},
		},
		newMockOG(&model.OrderDetail{
			OrderPayments: []*model.OrderPayment{
				{
					ID:                     21,
					CurrencyCode:           "USD",
					TotalAmount:            ts.mustDecimal("21.01"),
					Amount:                 ts.mustDecimal("20"),
					RefundedAmount:         decimal.Zero,
					ProcessingFee:          ts.mustDecimal("1.01"),
					ConvenienceFee:         ts.mustDecimal("1.01"),
					RefundedConvenienceFee: decimal.Zero,
					PaymentStatus:          orderpb.OrderPaymentStatus_ORDER_PAYMENT_STATUS_PAID,
				},
			},
		}),
		true,
	)

	ts.Assert().NoError(err)
	equalRefundOrder(ts.Assert(), &model.RefundOrder{
		RefundMode:           orderpb.RefundMode_REFUND_MODE_BY_ITEM,
		CurrencyCode:         "USD",
		RefundTotalAmount:    ts.mustDecimal("10.38"),
		RefundItemSubTotal:   ts.mustDecimal("10"),
		RefundDiscountAmount: decimal.Zero,
		RefundTipsAmount:     decimal.Zero,
		RefundConvenienceFee: ts.mustDecimal("0.38"),
		RefundTaxAmount:      decimal.Zero,
	}, salesResult.RefundOrderDetail.RefundOrder)
	equalRefundOrderItem(ts.Assert(), &model.RefundOrderItem{
		RefundItemMode: orderpb.RefundItemMode_REFUND_ITEM_MODE_BY_AMOUNT,
		RefundTax: model.RefundTax{
			Rate:   decimal.Zero,
			Amount: decimal.Zero,
		},
		RefundTotalAmount:    ts.mustDecimal("10"),
		RefundQuantity:       0,
		RefundAmount:         ts.mustDecimal("10"),
		RefundDiscountAmount: decimal.Zero,
	}, salesResult.RefundOrderDetail.RefundOrderItems[0])
	equalRefundOrderPayment(ts.Assert(), &model.RefundOrderPayment{
		OrderPaymentID:       22,
		CurrencyCode:         "USD",
		RefundAmount:         ts.mustDecimal("10.38"),
		RefundConvenienceFee: ts.mustDecimal("0.38"),
	}, salesResult.RefundOrderDetail.RefundOrderPayments[0])
	ts.Assert().Nil(deductedDepositResult)
}

// 退 sales order，该 order 使用了全部订金抵扣，剩余的金额已另行支付，现在 by item 退款，但是金额大，同时退到订金单和尾款单。
func (ts *RefundOrderPreviewTestSuite) TestRefundOrderService_previewRefundOrder_DepositDeductedSalesPaidRefundByItemToBothDest() {
	refundRepo := new(mocks.RefundOrderRepo)
	depositRepo := new(mocks.DepositChangeLogRepo)

	srv := &refundOrderService{
		refundRepo:           refundRepo,
		depositChangeLogRepo: depositRepo,
	}

	refundRepo.EXPECT().ListDetailByOrderID(mock.Anything, int64(1)).Return(nil, nil)
	refundRepo.EXPECT().ListDetailByOrderID(mock.Anything, int64(2)).Return(nil, nil)
	depositRepo.EXPECT().GetDeductionByDestOrderID(mock.Anything, int64(2)).Return(&model.DepositChangeLog{
		ID:             1,
		DepositOrderID: 1,
		ChangeType:     orderpb.DepositChangeType_DECREASE,
		Reason:         orderpb.DepositChangeReason_DEDUCTION,
		DestOrderID:    2,
	}, nil)
	salesResult, deductedDepositResult, err := srv.previewRefundOrder(
		context.Background(),
		&model.OrderDetail{
			Order: &model.Order{
				ID:              2,
				Status:          orderpb.OrderStatus_COMPLETED,
				OrderVersion:    model.OrderVersionRefund,
				CurrencyCode:    "USD",
				TipsAmount:      decimal.Zero,
				TaxAmount:       decimal.Zero,
				DiscountAmount:  decimal.Zero,
				DepositAmount:   ts.mustDecimal("20"),
				ConvenienceFee:  ts.mustDecimal("4.36"),
				SubTotalAmount:  ts.mustDecimal("135.00"),
				TipsBasedAmount: decimal.Zero,
				TotalAmount:     ts.mustDecimal("119.36"),
				PaidAmount:      ts.mustDecimal("119.36"),
				RemainAmount:    decimal.Zero,
				RefundedAmount:  decimal.Zero,
			},
			OrderItems: []*model.OrderItem{
				{
					ID: 11,
					Tax: model.Tax{
						Rate:   decimal.Zero,
						Amount: decimal.Zero,
					},
					Quantity:               1,
					UnitPrice:              ts.mustDecimal("45.00"),
					TipsAmount:             decimal.Zero,
					DiscountAmount:         decimal.Zero,
					SubTotalAmount:         ts.mustDecimal("45.00"),
					TotalAmount:            ts.mustDecimal("45.00"),
					RefundedAmount:         decimal.Zero,
					RefundedTaxAmount:      decimal.Zero,
					RefundedDiscountAmount: decimal.Zero,
				},
				{
					ID: 12,
					Tax: model.Tax{
						Rate:   decimal.Zero,
						Amount: decimal.Zero,
					},
					Quantity:               1,
					UnitPrice:              ts.mustDecimal("90.00"),
					TipsAmount:             decimal.Zero,
					DiscountAmount:         decimal.Zero,
					SubTotalAmount:         ts.mustDecimal("90.00"),
					TotalAmount:            ts.mustDecimal("90.00"),
					RefundedAmount:         decimal.Zero,
					RefundedTaxAmount:      decimal.Zero,
					RefundedDiscountAmount: decimal.Zero,
				},
			},
			OrderPayments: []*model.OrderPayment{
				{
					ID:                     22,
					CurrencyCode:           "USD",
					TotalAmount:            ts.mustDecimal("119.36"),
					Amount:                 ts.mustDecimal("115"),
					RefundedAmount:         decimal.Zero,
					ProcessingFee:          ts.mustDecimal("4.36"),
					ConvenienceFee:         ts.mustDecimal("4.36"),
					RefundedConvenienceFee: decimal.Zero,
					PaymentStatus:          orderpb.OrderPaymentStatus_ORDER_PAYMENT_STATUS_PAID,
				},
			},
		},
		nil,
		&ordersvcpb.RefundOrderRequest{
			OrderId:             2,
			BusinessId:          100001,
			StaffId:             200001,
			RefundReason:        "Unsatisfactory",
			RefundMode:          orderpb.RefundMode_REFUND_MODE_BY_ITEM,
			SourceOrderPayments: []*ordersvcpb.RefundOrderRequest_OrderPayment{{Id: 22}},
			RefundBy: &ordersvcpb.RefundOrderRequest_RefundByItem_{
				RefundByItem: &ordersvcpb.RefundOrderRequest_RefundByItem{
					RefundItems: []*ordersvcpb.RefundOrderRequest_RefundByItem_RefundItem{
						{
							OrderItemId:    11,
							RefundItemMode: orderpb.RefundItemMode_REFUND_ITEM_MODE_BY_AMOUNT,
							RefundItemModeParam: &ordersvcpb.RefundOrderRequest_RefundByItem_RefundItem_RefundAmount{
								RefundAmount: money.FromDecimal(ts.mustDecimal("45"), "USD"),
							},
						},
						{
							OrderItemId:    12,
							RefundItemMode: orderpb.RefundItemMode_REFUND_ITEM_MODE_BY_AMOUNT,
							RefundItemModeParam: &ordersvcpb.RefundOrderRequest_RefundByItem_RefundItem_RefundAmount{
								RefundAmount: money.FromDecimal(ts.mustDecimal("85"), "USD"),
							},
						},
					},
					RefundTips: money.FromDecimal(decimal.Zero, "USD"),
				},
			},
		},
		newMockOG(&model.OrderDetail{
			Order: &model.Order{
				ID:              1,
				Status:          orderpb.OrderStatus_COMPLETED,
				OrderVersion:    model.OrderVersionRefund,
				CurrencyCode:    "USD",
				TipsAmount:      decimal.Zero,
				TaxAmount:       decimal.Zero,
				DiscountAmount:  decimal.Zero,
				DepositAmount:   decimal.Zero,
				ConvenienceFee:  ts.mustDecimal("1.01"),
				SubTotalAmount:  ts.mustDecimal("20"),
				TipsBasedAmount: decimal.Zero,
				TotalAmount:     ts.mustDecimal("21.01"),
				PaidAmount:      ts.mustDecimal("21.01"),
				RemainAmount:    decimal.Zero,
				RefundedAmount:  decimal.Zero,
			},
			OrderItems: []*model.OrderItem{
				{
					ID: 91,
					Tax: model.Tax{
						Rate:   decimal.Zero,
						Amount: decimal.Zero,
					},
					Quantity:               1,
					UnitPrice:              ts.mustDecimal("20"),
					TipsAmount:             decimal.Zero,
					DiscountAmount:         decimal.Zero,
					SubTotalAmount:         ts.mustDecimal("20"),
					TotalAmount:            ts.mustDecimal("20"),
					RefundedAmount:         decimal.Zero,
					RefundedTaxAmount:      decimal.Zero,
					RefundedDiscountAmount: decimal.Zero,
				},
			},
			OrderPayments: []*model.OrderPayment{
				{
					ID:                     21,
					CurrencyCode:           "USD",
					TotalAmount:            ts.mustDecimal("21.01"),
					Amount:                 ts.mustDecimal("20"),
					RefundedAmount:         decimal.Zero,
					ProcessingFee:          ts.mustDecimal("1.01"),
					ConvenienceFee:         ts.mustDecimal("1.01"),
					RefundedConvenienceFee: decimal.Zero,
					PaymentStatus:          orderpb.OrderPaymentStatus_ORDER_PAYMENT_STATUS_PAID,
				},
			},
		}),
		true,
	)

	ts.Assert().NoError(err)
	equalRefundOrder(ts.Assert(), &model.RefundOrder{
		RefundMode:           orderpb.RefundMode_REFUND_MODE_BY_ITEM,
		CurrencyCode:         "USD",
		RefundTotalAmount:    ts.mustDecimal("119.36"),
		RefundItemSubTotal:   ts.mustDecimal("130"),
		RefundDiscountAmount: decimal.Zero,
		RefundDepositAmount:  ts.mustDecimal("15"),
		RefundTipsAmount:     decimal.Zero,
		RefundConvenienceFee: ts.mustDecimal("4.36"),
		RefundTaxAmount:      decimal.Zero,
	}, salesResult.RefundOrderDetail.RefundOrder)
	equalRefundOrderItem(ts.Assert(), &model.RefundOrderItem{
		RefundItemMode: orderpb.RefundItemMode_REFUND_ITEM_MODE_BY_AMOUNT,
		RefundTax: model.RefundTax{
			Rate:   decimal.Zero,
			Amount: decimal.Zero,
		},
		RefundTotalAmount:    ts.mustDecimal("45"),
		RefundQuantity:       0,
		RefundAmount:         ts.mustDecimal("45"),
		RefundDiscountAmount: decimal.Zero,
	}, salesResult.RefundOrderDetail.RefundOrderItems[0])
	equalRefundOrderItem(ts.Assert(), &model.RefundOrderItem{
		RefundItemMode: orderpb.RefundItemMode_REFUND_ITEM_MODE_BY_AMOUNT,
		RefundTax: model.RefundTax{
			Rate:   decimal.Zero,
			Amount: decimal.Zero,
		},
		RefundTotalAmount:    ts.mustDecimal("85"),
		RefundQuantity:       0,
		RefundAmount:         ts.mustDecimal("85"),
		RefundDiscountAmount: decimal.Zero,
	}, salesResult.RefundOrderDetail.RefundOrderItems[1])
	equalRefundOrderPayment(ts.Assert(), &model.RefundOrderPayment{
		OrderPaymentID:       22,
		CurrencyCode:         "USD",
		RefundAmount:         ts.mustDecimal("119.36"),
		RefundConvenienceFee: ts.mustDecimal("4.36"),
	}, salesResult.RefundOrderDetail.RefundOrderPayments[0])
	equalRefundOrder(ts.Assert(), &model.RefundOrder{
		RefundMode:           orderpb.RefundMode_REFUND_MODE_BY_ITEM,
		CurrencyCode:         "USD",
		RefundTotalAmount:    ts.mustDecimal("15.76"),
		RefundItemSubTotal:   ts.mustDecimal("15"),
		RefundDiscountAmount: decimal.Zero,
		RefundTipsAmount:     decimal.Zero,
		RefundConvenienceFee: ts.mustDecimal("0.76"),
		RefundTaxAmount:      decimal.Zero,
	}, deductedDepositResult.RefundOrderDetail.RefundOrder)
	equalRefundOrderItem(ts.Assert(), &model.RefundOrderItem{
		RefundItemMode: orderpb.RefundItemMode_REFUND_ITEM_MODE_BY_AMOUNT,
		RefundTax: model.RefundTax{
			Rate:   decimal.Zero,
			Amount: decimal.Zero,
		},
		RefundTotalAmount:    ts.mustDecimal("15"),
		RefundQuantity:       0,
		RefundAmount:         ts.mustDecimal("15"),
		RefundDiscountAmount: decimal.Zero,
	}, deductedDepositResult.RefundOrderDetail.RefundOrderItems[0])
	equalRefundOrderPayment(ts.Assert(), &model.RefundOrderPayment{
		OrderPaymentID:       21,
		CurrencyCode:         "USD",
		RefundAmount:         ts.mustDecimal("15.76"),
		RefundConvenienceFee: ts.mustDecimal("0.76"),
	}, deductedDepositResult.RefundOrderDetail.RefundOrderPayments[0])
}

// 退 sales order，该 order 使用了全部订金抵扣，并且订金金额超过了 sales order 金额，超出的金额已经退款，现在 by item 退款到订金单（真正退款的单子）和尾款单（实际退款 0）。
func (ts *RefundOrderPreviewTestSuite) TestRefundOrderService_previewRefundOrder_DepositDeductedAndOverpaidRefundedSalesRefundByItemToBothDest() {
	refundRepo := new(mocks.RefundOrderRepo)
	depositRepo := new(mocks.DepositChangeLogRepo)

	srv := &refundOrderService{
		refundRepo:           refundRepo,
		depositChangeLogRepo: depositRepo,
	}

	refundRepo.EXPECT().ListDetailByOrderID(mock.Anything, int64(1)).Return(
		[]*model.RefundOrderDetail{
			{
				RefundOrder: &model.RefundOrder{
					RefundMode:           orderpb.RefundMode_REFUND_MODE_BY_ITEM,
					CurrencyCode:         "USD",
					RefundTotalAmount:    ts.mustDecimal("46.69"),
					RefundItemSubTotal:   ts.mustDecimal("45"),
					RefundDiscountAmount: decimal.Zero,
					RefundTipsAmount:     decimal.Zero,
					RefundConvenienceFee: ts.mustDecimal("1.69"),
					RefundTaxAmount:      decimal.Zero,
				},
				RefundOrderItems: []*model.RefundOrderItem{
					{
						RefundItemMode: orderpb.RefundItemMode_REFUND_ITEM_MODE_BY_AMOUNT,
						RefundTax: model.RefundTax{
							Rate:   decimal.Zero,
							Amount: decimal.Zero,
						},
						RefundTotalAmount:    ts.mustDecimal("45"),
						RefundQuantity:       0,
						RefundAmount:         ts.mustDecimal("45"),
						RefundDiscountAmount: decimal.Zero,
					},
				},
				RefundOrderPayments: []*model.RefundOrderPayment{
					{
						OrderPaymentID:       21,
						CurrencyCode:         "USD",
						RefundAmount:         ts.mustDecimal("46.69"),
						RefundConvenienceFee: ts.mustDecimal("1.69"),
					},
				},
			},
		},
		nil,
	)
	refundRepo.EXPECT().ListDetailByOrderID(mock.Anything, int64(2)).Return(nil, nil)
	depositRepo.EXPECT().GetDeductionByDestOrderID(mock.Anything, int64(2)).Return(&model.DepositChangeLog{
		ID:             1,
		DepositOrderID: 1,
		ChangeType:     orderpb.DepositChangeType_DECREASE,
		Reason:         orderpb.DepositChangeReason_DEDUCTION,
		DestOrderID:    2,
	}, nil)
	salesResult, deductedDepositResult, err := srv.previewRefundOrder(
		context.Background(),
		&model.OrderDetail{
			Order: &model.Order{
				ID:              2,
				Status:          orderpb.OrderStatus_COMPLETED,
				OrderVersion:    model.OrderVersionRefund,
				CurrencyCode:    "USD",
				TipsAmount:      decimal.Zero,
				TaxAmount:       decimal.Zero,
				DiscountAmount:  decimal.Zero,
				DepositAmount:   ts.mustDecimal("90"),
				ConvenienceFee:  decimal.Zero,
				SubTotalAmount:  ts.mustDecimal("90"),
				TipsBasedAmount: decimal.Zero,
				TotalAmount:     decimal.Zero,
				PaidAmount:      decimal.Zero,
				RemainAmount:    decimal.Zero,
				RefundedAmount:  decimal.Zero,
			},
			OrderItems: []*model.OrderItem{
				{
					ID: 12,
					Tax: model.Tax{
						Rate:   decimal.Zero,
						Amount: decimal.Zero,
					},
					Quantity:               1,
					UnitPrice:              ts.mustDecimal("90.00"),
					TipsAmount:             decimal.Zero,
					DiscountAmount:         decimal.Zero,
					SubTotalAmount:         ts.mustDecimal("90.00"),
					TotalAmount:            ts.mustDecimal("90.00"),
					RefundedAmount:         decimal.Zero,
					RefundedTaxAmount:      decimal.Zero,
					RefundedDiscountAmount: decimal.Zero,
				},
			},
		},
		nil,
		&ordersvcpb.RefundOrderRequest{
			OrderId:             1,
			BusinessId:          100001,
			StaffId:             200001,
			RefundReason:        "Unsatisfactory",
			RefundMode:          orderpb.RefundMode_REFUND_MODE_BY_ITEM,
			SourceOrderPayments: []*ordersvcpb.RefundOrderRequest_OrderPayment{{Id: 22}},
			RefundBy: &ordersvcpb.RefundOrderRequest_RefundByItem_{
				RefundByItem: &ordersvcpb.RefundOrderRequest_RefundByItem{
					RefundItems: []*ordersvcpb.RefundOrderRequest_RefundByItem_RefundItem{
						{
							OrderItemId:    12,
							RefundItemMode: orderpb.RefundItemMode_REFUND_ITEM_MODE_BY_AMOUNT,
							RefundItemModeParam: &ordersvcpb.RefundOrderRequest_RefundByItem_RefundItem_RefundAmount{
								RefundAmount: money.FromDecimal(ts.mustDecimal("27"), "USD"),
							},
						},
					},
					RefundTips: money.FromDecimal(decimal.Zero, "USD"),
				},
			},
		},
		newMockOG(&model.OrderDetail{
			Order: &model.Order{
				ID:              1,
				Status:          orderpb.OrderStatus_COMPLETED,
				OrderVersion:    model.OrderVersionRefund,
				CurrencyCode:    "USD",
				TipsAmount:      decimal.Zero,
				TaxAmount:       decimal.Zero,
				DiscountAmount:  decimal.Zero,
				DepositAmount:   decimal.Zero,
				ConvenienceFee:  ts.mustDecimal("5.06"),
				SubTotalAmount:  ts.mustDecimal("135"),
				TipsBasedAmount: decimal.Zero,
				TotalAmount:     ts.mustDecimal("140.06"),
				PaidAmount:      ts.mustDecimal("140.06"),
				RemainAmount:    decimal.Zero,
				RefundedAmount:  decimal.Zero,
			},
			OrderItems: []*model.OrderItem{
				{
					ID: 91,
					Tax: model.Tax{
						Rate:   decimal.Zero,
						Amount: decimal.Zero,
					},
					Quantity:               1,
					UnitPrice:              ts.mustDecimal("135"),
					TipsAmount:             decimal.Zero,
					DiscountAmount:         decimal.Zero,
					SubTotalAmount:         ts.mustDecimal("135"),
					TotalAmount:            ts.mustDecimal("135"),
					RefundedAmount:         decimal.Zero,
					RefundedTaxAmount:      decimal.Zero,
					RefundedDiscountAmount: decimal.Zero,
				},
			},
			OrderPayments: []*model.OrderPayment{
				{
					ID:                     21,
					CurrencyCode:           "USD",
					TotalAmount:            ts.mustDecimal("140.06"),
					Amount:                 ts.mustDecimal("135"),
					RefundedAmount:         decimal.Zero,
					ProcessingFee:          ts.mustDecimal("5.06"),
					ConvenienceFee:         ts.mustDecimal("5.06"),
					RefundedConvenienceFee: decimal.Zero,
					PaymentStatus:          orderpb.OrderPaymentStatus_ORDER_PAYMENT_STATUS_PAID,
				},
			},
		}),
		true,
	)

	ts.Assert().NoError(err)
	equalRefundOrder(ts.Assert(), &model.RefundOrder{
		RefundMode:           orderpb.RefundMode_REFUND_MODE_BY_ITEM,
		CurrencyCode:         "USD",
		RefundTotalAmount:    decimal.Zero,
		RefundItemSubTotal:   ts.mustDecimal("27"),
		RefundDepositAmount:  ts.mustDecimal("27"),
		RefundDiscountAmount: decimal.Zero,
		RefundTipsAmount:     decimal.Zero,
		RefundConvenienceFee: decimal.Zero,
		RefundTaxAmount:      decimal.Zero,
	}, salesResult.RefundOrderDetail.RefundOrder)
	equalRefundOrderItem(ts.Assert(), &model.RefundOrderItem{
		RefundItemMode: orderpb.RefundItemMode_REFUND_ITEM_MODE_BY_AMOUNT,
		RefundTax: model.RefundTax{
			Rate:   decimal.Zero,
			Amount: decimal.Zero,
		},
		RefundTotalAmount:    ts.mustDecimal("27"),
		RefundQuantity:       0,
		RefundAmount:         ts.mustDecimal("27"),
		RefundDiscountAmount: decimal.Zero,
	}, salesResult.RefundOrderDetail.RefundOrderItems[0])
	ts.Assert().Empty(salesResult.RefundOrderDetail.RefundOrderPayments)
	equalRefundOrder(ts.Assert(), &model.RefundOrder{
		RefundMode:           orderpb.RefundMode_REFUND_MODE_BY_ITEM,
		CurrencyCode:         "USD",
		RefundTotalAmount:    ts.mustDecimal("28.01"),
		RefundItemSubTotal:   ts.mustDecimal("27"),
		RefundDiscountAmount: decimal.Zero,
		RefundTipsAmount:     decimal.Zero,
		RefundConvenienceFee: ts.mustDecimal("1.01"),
		RefundTaxAmount:      decimal.Zero,
	}, deductedDepositResult.RefundOrderDetail.RefundOrder)
	equalRefundOrderItem(ts.Assert(), &model.RefundOrderItem{
		RefundItemMode: orderpb.RefundItemMode_REFUND_ITEM_MODE_BY_AMOUNT,
		RefundTax: model.RefundTax{
			Rate:   decimal.Zero,
			Amount: decimal.Zero,
		},
		RefundTotalAmount:    ts.mustDecimal("27"),
		RefundQuantity:       0,
		RefundAmount:         ts.mustDecimal("27"),
		RefundDiscountAmount: decimal.Zero,
	}, deductedDepositResult.RefundOrderDetail.RefundOrderItems[0])
	equalRefundOrderPayment(ts.Assert(), &model.RefundOrderPayment{
		OrderPaymentID:       21,
		CurrencyCode:         "USD",
		RefundAmount:         ts.mustDecimal("28.01"),
		RefundConvenienceFee: ts.mustDecimal("1.01"),
	}, deductedDepositResult.RefundOrderDetail.RefundOrderPayments[0])
}

// 退 deposit order，已经被全额抵扣过，by payment 退款，同时退到订金单（真正退款的单子）和尾款单（实际退款 0）。
func (ts *RefundOrderPreviewTestSuite) TestRefundOrderService_previewRefundOrder_DeductedDepositRefundDepositOrderByPayment() {
	refundRepo := new(mocks.RefundOrderRepo)

	srv := &refundOrderService{
		refundRepo: refundRepo,
	}

	refundRepo.EXPECT().ListDetailByOrderID(mock.Anything, int64(1)).Return(nil, nil)
	refundRepo.EXPECT().ListDetailByOrderID(mock.Anything, int64(2)).Return(nil, nil)
	deductedDepositResult, salesResult, err := srv.previewRefundOrder(
		context.Background(),
		&model.OrderDetail{
			Order: &model.Order{
				ID:              1,
				Status:          orderpb.OrderStatus_COMPLETED,
				OrderVersion:    model.OrderVersionRefund,
				OrderType:       orderpb.OrderModel_DEPOSIT,
				CurrencyCode:    "USD",
				TipsAmount:      decimal.Zero,
				TaxAmount:       decimal.Zero,
				DiscountAmount:  decimal.Zero,
				DepositAmount:   decimal.Zero,
				ConvenienceFee:  ts.mustDecimal("1.37"),
				SubTotalAmount:  ts.mustDecimal("30"),
				TipsBasedAmount: decimal.Zero,
				TotalAmount:     ts.mustDecimal("31.37"),
				PaidAmount:      ts.mustDecimal("31.37"),
				RemainAmount:    decimal.Zero,
				RefundedAmount:  decimal.Zero,
			},
			OrderItems: []*model.OrderItem{
				{
					ID: 91,
					Tax: model.Tax{
						Rate:   decimal.Zero,
						Amount: decimal.Zero,
					},
					ItemType:               model.OrderItemTypeDeposit,
					Quantity:               1,
					UnitPrice:              ts.mustDecimal("30"),
					TipsAmount:             decimal.Zero,
					DiscountAmount:         decimal.Zero,
					SubTotalAmount:         ts.mustDecimal("30"),
					TotalAmount:            ts.mustDecimal("30"),
					RefundedAmount:         decimal.Zero,
					RefundedTaxAmount:      decimal.Zero,
					RefundedDiscountAmount: decimal.Zero,
				},
			},
			OrderPayments: []*model.OrderPayment{
				{
					ID:                     21,
					CurrencyCode:           "USD",
					TotalAmount:            ts.mustDecimal("31.37"),
					Amount:                 ts.mustDecimal("30"),
					RefundedAmount:         decimal.Zero,
					ProcessingFee:          ts.mustDecimal("1.37"),
					ConvenienceFee:         ts.mustDecimal("1.37"),
					RefundedConvenienceFee: decimal.Zero,
					PaymentStatus:          orderpb.OrderPaymentStatus_ORDER_PAYMENT_STATUS_PAID,
				},
			},
		},
		depositDetailFrom([]*model.DepositChangeLog{
			{
				ID:            2,
				ChangeType:    orderpb.DepositChangeType_DECREASE,
				Reason:        orderpb.DepositChangeReason_DEDUCTION,
				DestOrderID:   2,
				ChangedAmount: decimal.NewFromInt(30),
				Balance:       decimal.NewFromInt(0),
			},
			{
				ID:            1,
				ChangeType:    orderpb.DepositChangeType_INCREASE,
				Reason:        orderpb.DepositChangeReason_TOP_UP,
				ChangedAmount: decimal.NewFromInt(30),
				Balance:       decimal.NewFromInt(30),
			},
		}),
		&ordersvcpb.RefundOrderRequest{
			OrderId:             1,
			BusinessId:          100001,
			StaffId:             200001,
			RefundReason:        "Others",
			RefundMode:          orderpb.RefundMode_REFUND_MODE_BY_PAYMENT,
			SourceOrderPayments: []*ordersvcpb.RefundOrderRequest_OrderPayment{{Id: 21}},
			RefundBy: &ordersvcpb.RefundOrderRequest_RefundByPayment_{
				RefundByPayment: &ordersvcpb.RefundOrderRequest_RefundByPayment{
					RefundAmount: money.FromDecimal(ts.mustDecimal("20.91"), "USD"),
					RefundAmountFlags: &ordersvcpb.RefundOrderRequest_RefundByPayment_RefundAmountFlags{
						IsConvenienceFeeIncluded: true,
					},
					OrderPaymentIds: []int64{21},
				},
			},
		},
		&mockOG{
			orderDetail: &model.OrderDetail{
				Order: &model.Order{
					ID:              2,
					Status:          orderpb.OrderStatus_COMPLETED,
					OrderVersion:    model.OrderVersionRefund,
					OrderType:       orderpb.OrderModel_ORIGIN,
					CurrencyCode:    "USD",
					TipsAmount:      ts.mustDecimal("10"),
					TaxAmount:       decimal.Zero,
					DiscountAmount:  decimal.Zero,
					DepositAmount:   ts.mustDecimal("30"),
					ConvenienceFee:  ts.mustDecimal("3.83"),
					SubTotalAmount:  ts.mustDecimal("110"),
					TipsBasedAmount: decimal.Zero,
					TotalAmount:     ts.mustDecimal("113.83"),
					PaidAmount:      ts.mustDecimal("113.83"),
					RemainAmount:    decimal.Zero,
					RefundedAmount:  decimal.Zero,
				},
				OrderItems: []*model.OrderItem{
					{
						ID: 11,
						Tax: model.Tax{
							Rate:   decimal.Zero,
							Amount: decimal.Zero,
						},
						Quantity:               1,
						UnitPrice:              ts.mustDecimal("40"),
						TipsAmount:             decimal.Zero,
						DiscountAmount:         decimal.Zero,
						SubTotalAmount:         ts.mustDecimal("40"),
						TotalAmount:            ts.mustDecimal("40"),
						RefundedAmount:         decimal.Zero,
						RefundedTaxAmount:      decimal.Zero,
						RefundedDiscountAmount: decimal.Zero,
					},
					{
						ID: 12,
						Tax: model.Tax{
							Rate:   decimal.Zero,
							Amount: decimal.Zero,
						},
						Quantity:               1,
						UnitPrice:              ts.mustDecimal("90"),
						TipsAmount:             decimal.Zero,
						DiscountAmount:         decimal.Zero,
						SubTotalAmount:         ts.mustDecimal("90"),
						TotalAmount:            ts.mustDecimal("90"),
						RefundedAmount:         decimal.Zero,
						RefundedTaxAmount:      decimal.Zero,
						RefundedDiscountAmount: decimal.Zero,
					},
				},
				OrderPayments: []*model.OrderPayment{
					{
						ID:                     21,
						CurrencyCode:           "USD",
						TotalAmount:            ts.mustDecimal("113.83"),
						Amount:                 ts.mustDecimal("100"),
						RefundedAmount:         decimal.Zero,
						ProcessingFee:          ts.mustDecimal("3.83"),
						ConvenienceFee:         ts.mustDecimal("3.83"),
						RefundedConvenienceFee: decimal.Zero,
						PaymentStatus:          orderpb.OrderPaymentStatus_ORDER_PAYMENT_STATUS_PAID,
					},
				},
			},
		},
		false,
	)

	ts.Assert().NoError(err)
	equalRefundOrder(ts.Assert(), &model.RefundOrder{
		RefundMode:           orderpb.RefundMode_REFUND_MODE_BY_PAYMENT,
		CurrencyCode:         "USD",
		RefundTotalAmount:    decimal.Zero,
		RefundItemSubTotal:   ts.mustDecimal("18.61"),
		RefundDepositAmount:  ts.mustDecimal("20"),
		RefundDiscountAmount: decimal.Zero,
		RefundTipsAmount:     ts.mustDecimal("1.39"),
		RefundConvenienceFee: decimal.Zero,
		RefundTaxAmount:      decimal.Zero,
	}, salesResult.RefundOrderDetail.RefundOrder)
	equalRefundOrderItem(ts.Assert(), &model.RefundOrderItem{
		RefundItemMode: orderpb.RefundItemMode_REFUND_ITEM_MODE_BY_AMOUNT,
		RefundTax: model.RefundTax{
			Rate:   decimal.Zero,
			Amount: decimal.Zero,
		},
		RefundTotalAmount:    ts.mustDecimal("5.73"),
		RefundQuantity:       0,
		RefundAmount:         ts.mustDecimal("5.73"),
		RefundDiscountAmount: decimal.Zero,
	}, salesResult.RefundOrderDetail.RefundOrderItems[0])
	equalRefundOrderItem(ts.Assert(), &model.RefundOrderItem{
		RefundItemMode: orderpb.RefundItemMode_REFUND_ITEM_MODE_BY_AMOUNT,
		RefundTax: model.RefundTax{
			Rate:   decimal.Zero,
			Amount: decimal.Zero,
		},
		RefundTotalAmount:    ts.mustDecimal("12.88"),
		RefundQuantity:       0,
		RefundAmount:         ts.mustDecimal("12.88"),
		RefundDiscountAmount: decimal.Zero,
	}, salesResult.RefundOrderDetail.RefundOrderItems[1])
	ts.Assert().Empty(salesResult.RefundOrderDetail.RefundOrderPayments)
	equalRefundOrder(ts.Assert(), &model.RefundOrder{
		RefundMode:           orderpb.RefundMode_REFUND_MODE_BY_ITEM,
		CurrencyCode:         "USD",
		RefundTotalAmount:    ts.mustDecimal("20.91"),
		RefundItemSubTotal:   ts.mustDecimal("20"),
		RefundDiscountAmount: decimal.Zero,
		RefundTipsAmount:     decimal.Zero,
		RefundConvenienceFee: ts.mustDecimal("0.91"),
		RefundTaxAmount:      decimal.Zero,
	}, deductedDepositResult.RefundOrderDetail.RefundOrder)
	equalRefundOrderItem(ts.Assert(), &model.RefundOrderItem{
		RefundItemMode: orderpb.RefundItemMode_REFUND_ITEM_MODE_BY_AMOUNT,
		RefundTax: model.RefundTax{
			Rate:   decimal.Zero,
			Amount: decimal.Zero,
		},
		RefundTotalAmount:    ts.mustDecimal("20"),
		RefundQuantity:       0,
		RefundAmount:         ts.mustDecimal("20"),
		RefundDiscountAmount: decimal.Zero,
	}, deductedDepositResult.RefundOrderDetail.RefundOrderItems[0])
	equalRefundOrderPayment(ts.Assert(), &model.RefundOrderPayment{
		OrderPaymentID:       21,
		CurrencyCode:         "USD",
		RefundAmount:         ts.mustDecimal("20.91"),
		RefundConvenienceFee: ts.mustDecimal("0.91"),
	}, deductedDepositResult.RefundOrderDetail.RefundOrderPayments[0])
}

// 退 deposit order，已经被全额抵扣过，by payment 退剩余全款，同时退到订金单（真正退款的单子）和尾款单（实际退款 0）。
func (ts *RefundOrderPreviewTestSuite) TestRefundOrderService_previewRefundOrder_DeductedDepositRefundDepositOrderFullyByPayment() {
	refundRepo := new(mocks.RefundOrderRepo)

	srv := &refundOrderService{
		refundRepo: refundRepo,
	}

	refundRepo.EXPECT().ListDetailByOrderID(mock.Anything, int64(1)).Return(nil, nil)
	refundRepo.EXPECT().ListDetailByOrderID(mock.Anything, int64(2)).Return(nil, nil)
	deductedDepositResult, salesResult, err := srv.previewRefundOrder(
		context.Background(),
		&model.OrderDetail{
			Order: &model.Order{
				ID:              1,
				Status:          orderpb.OrderStatus_COMPLETED,
				OrderVersion:    model.OrderVersionRefund,
				OrderType:       orderpb.OrderModel_DEPOSIT,
				CurrencyCode:    "USD",
				TipsAmount:      decimal.Zero,
				TaxAmount:       decimal.Zero,
				DiscountAmount:  decimal.Zero,
				DepositAmount:   decimal.Zero,
				ConvenienceFee:  ts.mustDecimal("1.37"),
				SubTotalAmount:  ts.mustDecimal("30"),
				TipsBasedAmount: decimal.Zero,
				TotalAmount:     ts.mustDecimal("31.37"),
				PaidAmount:      ts.mustDecimal("31.37"),
				RemainAmount:    decimal.Zero,
				RefundedAmount:  decimal.Zero,
			},
			OrderItems: []*model.OrderItem{
				{
					ID: 91,
					Tax: model.Tax{
						Rate:   decimal.Zero,
						Amount: decimal.Zero,
					},
					ItemType:               model.OrderItemTypeDeposit,
					Quantity:               1,
					UnitPrice:              ts.mustDecimal("30"),
					TipsAmount:             decimal.Zero,
					DiscountAmount:         decimal.Zero,
					SubTotalAmount:         ts.mustDecimal("30"),
					TotalAmount:            ts.mustDecimal("30"),
					RefundedAmount:         decimal.Zero,
					RefundedTaxAmount:      decimal.Zero,
					RefundedDiscountAmount: decimal.Zero,
				},
			},
			OrderPayments: []*model.OrderPayment{
				{
					ID:                     21,
					CurrencyCode:           "USD",
					TotalAmount:            ts.mustDecimal("31.37"),
					Amount:                 ts.mustDecimal("30"),
					RefundedAmount:         decimal.Zero,
					ProcessingFee:          ts.mustDecimal("1.37"),
					ConvenienceFee:         ts.mustDecimal("1.37"),
					RefundedConvenienceFee: decimal.Zero,
					PaymentStatus:          orderpb.OrderPaymentStatus_ORDER_PAYMENT_STATUS_PAID,
				},
			},
		},
		depositDetailFrom([]*model.DepositChangeLog{
			{
				ID:            2,
				ChangeType:    orderpb.DepositChangeType_DECREASE,
				Reason:        orderpb.DepositChangeReason_DEDUCTION,
				DestOrderID:   2,
				ChangedAmount: decimal.NewFromInt(30),
				Balance:       decimal.NewFromInt(0),
			},
			{
				ID:            1,
				ChangeType:    orderpb.DepositChangeType_INCREASE,
				Reason:        orderpb.DepositChangeReason_TOP_UP,
				ChangedAmount: decimal.NewFromInt(30),
				Balance:       decimal.NewFromInt(30),
			},
		}),
		&ordersvcpb.RefundOrderRequest{
			OrderId:             1,
			BusinessId:          100001,
			StaffId:             200001,
			RefundReason:        "Others",
			RefundMode:          orderpb.RefundMode_REFUND_MODE_BY_PAYMENT,
			SourceOrderPayments: []*ordersvcpb.RefundOrderRequest_OrderPayment{{Id: 21}},
			RefundBy: &ordersvcpb.RefundOrderRequest_RefundByPayment_{
				RefundByPayment: &ordersvcpb.RefundOrderRequest_RefundByPayment{
					RefundAmount: money.FromDecimal(ts.mustDecimal("31.37"), "USD"),
					RefundAmountFlags: &ordersvcpb.RefundOrderRequest_RefundByPayment_RefundAmountFlags{
						IsConvenienceFeeIncluded: true,
					},
					OrderPaymentIds: []int64{21},
				},
			},
		},
		&mockOG{
			orderDetail: &model.OrderDetail{
				Order: &model.Order{
					ID:              2,
					Status:          orderpb.OrderStatus_COMPLETED,
					OrderVersion:    model.OrderVersionRefund,
					OrderType:       orderpb.OrderModel_ORIGIN,
					CurrencyCode:    "USD",
					TipsAmount:      ts.mustDecimal("10"),
					TaxAmount:       decimal.Zero,
					DiscountAmount:  decimal.Zero,
					DepositAmount:   ts.mustDecimal("30"),
					ConvenienceFee:  ts.mustDecimal("3.83"),
					SubTotalAmount:  ts.mustDecimal("110"),
					TipsBasedAmount: decimal.Zero,
					TotalAmount:     ts.mustDecimal("113.83"),
					PaidAmount:      ts.mustDecimal("113.83"),
					RemainAmount:    decimal.Zero,
					RefundedAmount:  decimal.Zero,
				},
				OrderItems: []*model.OrderItem{
					{
						ID: 11,
						Tax: model.Tax{
							Rate:   decimal.Zero,
							Amount: decimal.Zero,
						},
						Quantity:               1,
						UnitPrice:              ts.mustDecimal("40"),
						TipsAmount:             decimal.Zero,
						DiscountAmount:         decimal.Zero,
						SubTotalAmount:         ts.mustDecimal("40"),
						TotalAmount:            ts.mustDecimal("40"),
						RefundedAmount:         decimal.Zero,
						RefundedTaxAmount:      decimal.Zero,
						RefundedDiscountAmount: decimal.Zero,
					},
					{
						ID: 12,
						Tax: model.Tax{
							Rate:   decimal.Zero,
							Amount: decimal.Zero,
						},
						Quantity:               1,
						UnitPrice:              ts.mustDecimal("90"),
						TipsAmount:             decimal.Zero,
						DiscountAmount:         decimal.Zero,
						SubTotalAmount:         ts.mustDecimal("90"),
						TotalAmount:            ts.mustDecimal("90"),
						RefundedAmount:         decimal.Zero,
						RefundedTaxAmount:      decimal.Zero,
						RefundedDiscountAmount: decimal.Zero,
					},
				},
				OrderPayments: []*model.OrderPayment{
					{
						ID:                     21,
						CurrencyCode:           "USD",
						TotalAmount:            ts.mustDecimal("113.83"),
						Amount:                 ts.mustDecimal("100"),
						RefundedAmount:         decimal.Zero,
						ProcessingFee:          ts.mustDecimal("3.83"),
						ConvenienceFee:         ts.mustDecimal("3.83"),
						RefundedConvenienceFee: decimal.Zero,
						PaymentStatus:          orderpb.OrderPaymentStatus_ORDER_PAYMENT_STATUS_PAID,
					},
				},
			},
		},
		false,
	)

	ts.Assert().NoError(err)
	equalRefundOrder(ts.Assert(), &model.RefundOrder{
		RefundMode:           orderpb.RefundMode_REFUND_MODE_BY_PAYMENT,
		CurrencyCode:         "USD",
		RefundTotalAmount:    decimal.Zero,
		RefundItemSubTotal:   ts.mustDecimal("27.91"),
		RefundDepositAmount:  ts.mustDecimal("30"),
		RefundDiscountAmount: decimal.Zero,
		RefundTipsAmount:     ts.mustDecimal("2.09"),
		RefundConvenienceFee: decimal.Zero,
		RefundTaxAmount:      decimal.Zero,
	}, salesResult.RefundOrderDetail.RefundOrder)
	equalRefundOrderItem(ts.Assert(), &model.RefundOrderItem{
		RefundItemMode: orderpb.RefundItemMode_REFUND_ITEM_MODE_BY_AMOUNT,
		RefundTax: model.RefundTax{
			Rate:   decimal.Zero,
			Amount: decimal.Zero,
		},
		RefundTotalAmount:    ts.mustDecimal("8.59"),
		RefundQuantity:       0,
		RefundAmount:         ts.mustDecimal("8.59"),
		RefundDiscountAmount: decimal.Zero,
	}, salesResult.RefundOrderDetail.RefundOrderItems[0])
	equalRefundOrderItem(ts.Assert(), &model.RefundOrderItem{
		RefundItemMode: orderpb.RefundItemMode_REFUND_ITEM_MODE_BY_AMOUNT,
		RefundTax: model.RefundTax{
			Rate:   decimal.Zero,
			Amount: decimal.Zero,
		},
		RefundTotalAmount:    ts.mustDecimal("19.32"),
		RefundQuantity:       0,
		RefundAmount:         ts.mustDecimal("19.32"),
		RefundDiscountAmount: decimal.Zero,
	}, salesResult.RefundOrderDetail.RefundOrderItems[1])
	ts.Assert().Empty(salesResult.RefundOrderDetail.RefundOrderPayments)
	equalRefundOrder(ts.Assert(), &model.RefundOrder{
		RefundMode:           orderpb.RefundMode_REFUND_MODE_BY_ITEM,
		CurrencyCode:         "USD",
		RefundTotalAmount:    ts.mustDecimal("31.37"),
		RefundItemSubTotal:   ts.mustDecimal("30"),
		RefundDiscountAmount: decimal.Zero,
		RefundTipsAmount:     decimal.Zero,
		RefundConvenienceFee: ts.mustDecimal("1.37"),
		RefundTaxAmount:      decimal.Zero,
	}, deductedDepositResult.RefundOrderDetail.RefundOrder)
	equalRefundOrderItem(ts.Assert(), &model.RefundOrderItem{
		RefundItemMode: orderpb.RefundItemMode_REFUND_ITEM_MODE_BY_AMOUNT,
		RefundTax: model.RefundTax{
			Rate:   decimal.Zero,
			Amount: decimal.Zero,
		},
		RefundTotalAmount:    ts.mustDecimal("30"),
		RefundQuantity:       0,
		RefundAmount:         ts.mustDecimal("30"),
		RefundDiscountAmount: decimal.Zero,
	}, deductedDepositResult.RefundOrderDetail.RefundOrderItems[0])
	equalRefundOrderPayment(ts.Assert(), &model.RefundOrderPayment{
		OrderPaymentID:       21,
		CurrencyCode:         "USD",
		RefundAmount:         ts.mustDecimal("31.37"),
		RefundConvenienceFee: ts.mustDecimal("1.37"),
	}, deductedDepositResult.RefundOrderDetail.RefundOrderPayments[0])
}

// 退 sales order，已经被全额抵扣过，by payment 退款，退到尾款单。
func (ts *RefundOrderPreviewTestSuite) TestRefundOrderService_previewRefundOrder_DeductedDepositRefundSalesOrderByPayment() {
	refundRepo := new(mocks.RefundOrderRepo)

	srv := &refundOrderService{
		refundRepo: refundRepo,
	}

	refundRepo.EXPECT().ListDetailByOrderID(mock.Anything, int64(1)).Return(nil, nil)
	refundRepo.EXPECT().ListDetailByOrderID(mock.Anything, int64(2)).Return(nil, nil)
	salesResult, deductedDepositResult, err := srv.previewRefundOrder(
		context.Background(),
		&model.OrderDetail{
			Order: &model.Order{
				ID:              2,
				Status:          orderpb.OrderStatus_COMPLETED,
				OrderVersion:    model.OrderVersionRefund,
				OrderType:       orderpb.OrderModel_ORIGIN,
				CurrencyCode:    "USD",
				TipsAmount:      ts.mustDecimal("10"),
				TaxAmount:       decimal.Zero,
				DiscountAmount:  decimal.Zero,
				DepositAmount:   ts.mustDecimal("30"),
				ConvenienceFee:  ts.mustDecimal("3.83"),
				SubTotalAmount:  ts.mustDecimal("110"),
				TipsBasedAmount: decimal.Zero,
				TotalAmount:     ts.mustDecimal("113.83"),
				PaidAmount:      ts.mustDecimal("113.83"),
				RemainAmount:    decimal.Zero,
				RefundedAmount:  decimal.Zero,
			},
			OrderItems: []*model.OrderItem{
				{
					ID: 11,
					Tax: model.Tax{
						Rate:   decimal.Zero,
						Amount: decimal.Zero,
					},
					Quantity:               1,
					UnitPrice:              ts.mustDecimal("40"),
					TipsAmount:             decimal.Zero,
					DiscountAmount:         decimal.Zero,
					SubTotalAmount:         ts.mustDecimal("40"),
					TotalAmount:            ts.mustDecimal("40"),
					RefundedAmount:         decimal.Zero,
					RefundedTaxAmount:      decimal.Zero,
					RefundedDiscountAmount: decimal.Zero,
				},
				{
					ID: 12,
					Tax: model.Tax{
						Rate:   decimal.Zero,
						Amount: decimal.Zero,
					},
					Quantity:               1,
					UnitPrice:              ts.mustDecimal("90"),
					TipsAmount:             decimal.Zero,
					DiscountAmount:         decimal.Zero,
					SubTotalAmount:         ts.mustDecimal("90"),
					TotalAmount:            ts.mustDecimal("90"),
					RefundedAmount:         decimal.Zero,
					RefundedTaxAmount:      decimal.Zero,
					RefundedDiscountAmount: decimal.Zero,
				},
			},
			OrderPayments: []*model.OrderPayment{
				{
					ID:                     22,
					CurrencyCode:           "USD",
					TotalAmount:            ts.mustDecimal("113.83"),
					Amount:                 ts.mustDecimal("100"),
					RefundedAmount:         decimal.Zero,
					ProcessingFee:          ts.mustDecimal("3.83"),
					ConvenienceFee:         ts.mustDecimal("3.83"),
					RefundedConvenienceFee: decimal.Zero,
					PaymentStatus:          orderpb.OrderPaymentStatus_ORDER_PAYMENT_STATUS_PAID,
				},
			},
		},
		depositDetailFrom([]*model.DepositChangeLog{
			{
				ID:            2,
				ChangeType:    orderpb.DepositChangeType_DECREASE,
				Reason:        orderpb.DepositChangeReason_DEDUCTION,
				DestOrderID:   2,
				ChangedAmount: decimal.NewFromInt(30),
				Balance:       decimal.NewFromInt(0),
			},
			{
				ID:            1,
				ChangeType:    orderpb.DepositChangeType_INCREASE,
				Reason:        orderpb.DepositChangeReason_TOP_UP,
				ChangedAmount: decimal.NewFromInt(30),
				Balance:       decimal.NewFromInt(30),
			},
		}),
		&ordersvcpb.RefundOrderRequest{
			OrderId:             2,
			BusinessId:          100001,
			StaffId:             200001,
			RefundReason:        "Others",
			RefundMode:          orderpb.RefundMode_REFUND_MODE_BY_PAYMENT,
			SourceOrderPayments: []*ordersvcpb.RefundOrderRequest_OrderPayment{{Id: 22}},
			RefundBy: &ordersvcpb.RefundOrderRequest_RefundByPayment_{
				RefundByPayment: &ordersvcpb.RefundOrderRequest_RefundByPayment{
					RefundAmount: money.FromDecimal(ts.mustDecimal("20.7"), "USD"),
					RefundAmountFlags: &ordersvcpb.RefundOrderRequest_RefundByPayment_RefundAmountFlags{
						IsConvenienceFeeIncluded: true,
					},
					OrderPaymentIds: []int64{22},
				},
			},
		},
		nil,
		false,
	)

	ts.Assert().NoError(err)
	equalRefundOrder(ts.Assert(), &model.RefundOrder{
		RefundMode:           orderpb.RefundMode_REFUND_MODE_BY_PAYMENT,
		CurrencyCode:         "USD",
		RefundTotalAmount:    ts.mustDecimal("20.7"),
		RefundItemSubTotal:   ts.mustDecimal("18.56"),
		RefundDepositAmount:  decimal.Zero,
		RefundDiscountAmount: decimal.Zero,
		RefundTipsAmount:     ts.mustDecimal("1.44"),
		RefundConvenienceFee: ts.mustDecimal("0.7"),
		RefundTaxAmount:      decimal.Zero,
	}, salesResult.RefundOrderDetail.RefundOrder)
	equalRefundOrderItem(ts.Assert(), &model.RefundOrderItem{
		RefundItemMode: orderpb.RefundItemMode_REFUND_ITEM_MODE_BY_AMOUNT,
		RefundTax: model.RefundTax{
			Rate:   decimal.Zero,
			Amount: decimal.Zero,
		},
		RefundTotalAmount:    ts.mustDecimal("5.71"),
		RefundQuantity:       0,
		RefundAmount:         ts.mustDecimal("5.71"),
		RefundDiscountAmount: decimal.Zero,
	}, salesResult.RefundOrderDetail.RefundOrderItems[0])
	equalRefundOrderItem(ts.Assert(), &model.RefundOrderItem{
		RefundItemMode: orderpb.RefundItemMode_REFUND_ITEM_MODE_BY_AMOUNT,
		RefundTax: model.RefundTax{
			Rate:   decimal.Zero,
			Amount: decimal.Zero,
		},
		RefundTotalAmount:    ts.mustDecimal("12.85"),
		RefundQuantity:       0,
		RefundAmount:         ts.mustDecimal("12.85"),
		RefundDiscountAmount: decimal.Zero,
	}, salesResult.RefundOrderDetail.RefundOrderItems[1])
	equalRefundOrderPayment(ts.Assert(), &model.RefundOrderPayment{
		OrderPaymentID:       22,
		CurrencyCode:         "USD",
		RefundAmount:         ts.mustDecimal("20.7"),
		RefundConvenienceFee: ts.mustDecimal("0.7"),
	}, salesResult.RefundOrderDetail.RefundOrderPayments[0])
	ts.Assert().Nil(deductedDepositResult)
}

// 退 sales order，已经被全额抵扣过，by payment 退全部尾款，退到尾款单。
func (ts *RefundOrderPreviewTestSuite) TestRefundOrderService_previewRefundOrder_DeductedDepositRefundSalesOrderFullyByPayment() {
	refundRepo := new(mocks.RefundOrderRepo)

	srv := &refundOrderService{
		refundRepo: refundRepo,
	}

	refundRepo.EXPECT().ListDetailByOrderID(mock.Anything, int64(1)).Return(nil, nil)
	refundRepo.EXPECT().ListDetailByOrderID(mock.Anything, int64(2)).Return(nil, nil)
	salesResult, deductedDepositResult, err := srv.previewRefundOrder(
		context.Background(),
		&model.OrderDetail{
			Order: &model.Order{
				ID:              2,
				Status:          orderpb.OrderStatus_COMPLETED,
				OrderVersion:    model.OrderVersionRefund,
				OrderType:       orderpb.OrderModel_ORIGIN,
				CurrencyCode:    "USD",
				TipsAmount:      ts.mustDecimal("10"),
				TaxAmount:       decimal.Zero,
				DiscountAmount:  decimal.Zero,
				DepositAmount:   ts.mustDecimal("30"),
				ConvenienceFee:  ts.mustDecimal("3.83"),
				SubTotalAmount:  ts.mustDecimal("110"),
				TipsBasedAmount: decimal.Zero,
				TotalAmount:     ts.mustDecimal("113.83"),
				PaidAmount:      ts.mustDecimal("113.83"),
				RemainAmount:    decimal.Zero,
				RefundedAmount:  decimal.Zero,
			},
			OrderItems: []*model.OrderItem{
				{
					ID: 11,
					Tax: model.Tax{
						Rate:   decimal.Zero,
						Amount: decimal.Zero,
					},
					Quantity:               1,
					UnitPrice:              ts.mustDecimal("40"),
					TipsAmount:             decimal.Zero,
					DiscountAmount:         decimal.Zero,
					SubTotalAmount:         ts.mustDecimal("40"),
					TotalAmount:            ts.mustDecimal("40"),
					RefundedAmount:         decimal.Zero,
					RefundedTaxAmount:      decimal.Zero,
					RefundedDiscountAmount: decimal.Zero,
				},
				{
					ID: 12,
					Tax: model.Tax{
						Rate:   decimal.Zero,
						Amount: decimal.Zero,
					},
					Quantity:               1,
					UnitPrice:              ts.mustDecimal("90"),
					TipsAmount:             decimal.Zero,
					DiscountAmount:         decimal.Zero,
					SubTotalAmount:         ts.mustDecimal("90"),
					TotalAmount:            ts.mustDecimal("90"),
					RefundedAmount:         decimal.Zero,
					RefundedTaxAmount:      decimal.Zero,
					RefundedDiscountAmount: decimal.Zero,
				},
			},
			OrderPayments: []*model.OrderPayment{
				{
					ID:                     22,
					CurrencyCode:           "USD",
					TotalAmount:            ts.mustDecimal("113.83"),
					Amount:                 ts.mustDecimal("100"),
					RefundedAmount:         decimal.Zero,
					ProcessingFee:          ts.mustDecimal("3.83"),
					ConvenienceFee:         ts.mustDecimal("3.83"),
					RefundedConvenienceFee: decimal.Zero,
					PaymentStatus:          orderpb.OrderPaymentStatus_ORDER_PAYMENT_STATUS_PAID,
				},
			},
		},
		depositDetailFrom([]*model.DepositChangeLog{
			{
				ID:            2,
				ChangeType:    orderpb.DepositChangeType_DECREASE,
				Reason:        orderpb.DepositChangeReason_DEDUCTION,
				DestOrderID:   2,
				ChangedAmount: decimal.NewFromInt(30),
				Balance:       decimal.NewFromInt(0),
			},
			{
				ID:            1,
				ChangeType:    orderpb.DepositChangeType_INCREASE,
				Reason:        orderpb.DepositChangeReason_TOP_UP,
				ChangedAmount: decimal.NewFromInt(30),
				Balance:       decimal.NewFromInt(30),
			},
		}),
		&ordersvcpb.RefundOrderRequest{
			OrderId:             2,
			BusinessId:          100001,
			StaffId:             200001,
			RefundReason:        "Others",
			RefundMode:          orderpb.RefundMode_REFUND_MODE_BY_PAYMENT,
			SourceOrderPayments: []*ordersvcpb.RefundOrderRequest_OrderPayment{{Id: 22}},
			RefundBy: &ordersvcpb.RefundOrderRequest_RefundByPayment_{
				RefundByPayment: &ordersvcpb.RefundOrderRequest_RefundByPayment{
					RefundAmount: money.FromDecimal(ts.mustDecimal("113.83"), "USD"),
					RefundAmountFlags: &ordersvcpb.RefundOrderRequest_RefundByPayment_RefundAmountFlags{
						IsConvenienceFeeIncluded: true,
					},
					OrderPaymentIds: []int64{22},
				},
			},
		},
		nil,
		false,
	)

	ts.Assert().NoError(err)
	equalRefundOrder(ts.Assert(), &model.RefundOrder{
		RefundMode:           orderpb.RefundMode_REFUND_MODE_BY_PAYMENT,
		CurrencyCode:         "USD",
		RefundTotalAmount:    ts.mustDecimal("113.83"),
		RefundItemSubTotal:   ts.mustDecimal("102.09"),
		RefundDepositAmount:  decimal.Zero,
		RefundDiscountAmount: decimal.Zero,
		RefundTipsAmount:     ts.mustDecimal("7.91"),
		RefundConvenienceFee: ts.mustDecimal("3.83"),
		RefundTaxAmount:      decimal.Zero,
	}, salesResult.RefundOrderDetail.RefundOrder)
	equalRefundOrderItem(ts.Assert(), &model.RefundOrderItem{
		RefundItemMode: orderpb.RefundItemMode_REFUND_ITEM_MODE_BY_AMOUNT,
		RefundTax: model.RefundTax{
			Rate:   decimal.Zero,
			Amount: decimal.Zero,
		},
		RefundTotalAmount:    ts.mustDecimal("31.41"),
		RefundQuantity:       0,
		RefundAmount:         ts.mustDecimal("31.41"),
		RefundDiscountAmount: decimal.Zero,
	}, salesResult.RefundOrderDetail.RefundOrderItems[0])
	equalRefundOrderItem(ts.Assert(), &model.RefundOrderItem{
		RefundItemMode: orderpb.RefundItemMode_REFUND_ITEM_MODE_BY_AMOUNT,
		RefundTax: model.RefundTax{
			Rate:   decimal.Zero,
			Amount: decimal.Zero,
		},
		RefundTotalAmount:    ts.mustDecimal("70.68"),
		RefundQuantity:       0,
		RefundAmount:         ts.mustDecimal("70.68"),
		RefundDiscountAmount: decimal.Zero,
	}, salesResult.RefundOrderDetail.RefundOrderItems[1])
	equalRefundOrderPayment(ts.Assert(), &model.RefundOrderPayment{
		OrderPaymentID:       22,
		CurrencyCode:         "USD",
		RefundAmount:         ts.mustDecimal("113.83"),
		RefundConvenienceFee: ts.mustDecimal("3.83"),
	}, salesResult.RefundOrderDetail.RefundOrderPayments[0])
	ts.Assert().Nil(deductedDepositResult)
}

func (ts *RefundOrderPreviewTestSuite) TestRefundOrderService_buildRefundableItems_Deposit() {
	refundOrderService := &refundOrderService{}

	var refundableItems []*ordersvcpb.PreviewRefundOrderResponse_RefundableItem

	// 没有抵扣过，可以全退
	refundableItems = refundOrderService.buildRefundableItems(
		[]*model.OrderItem{
			{
				ID:             101,
				ItemType:       "deposit",
				CurrencyCode:   "USD",
				UnitPrice:      decimal.NewFromInt(10),
				SubTotalAmount: decimal.NewFromInt(10),
				TotalAmount:    decimal.NewFromInt(10),
			},
		},
		&model.DepositDetail{
			LatestChangeLog: &model.DepositChangeLog{
				Balance: decimal.NewFromInt(10),
			},
		},
	)
	ts.Len(refundableItems, 1)
	ts.True(proto.Equal(refundableItems[0].GetRefundableAmount(), money.FromFloat(10, "USD")))

	// 没有抵扣过，部分退过，不能超过剩余的 balance
	refundableItems = refundOrderService.buildRefundableItems(
		[]*model.OrderItem{
			{
				ID:             101,
				ItemType:       "deposit",
				CurrencyCode:   "USD",
				UnitPrice:      decimal.NewFromInt(10),
				SubTotalAmount: decimal.NewFromInt(10),
				TotalAmount:    decimal.NewFromInt(10),
				RefundedAmount: decimal.NewFromInt(7),
			},
		},
		&model.DepositDetail{
			LatestChangeLog: &model.DepositChangeLog{
				Balance: decimal.NewFromInt(3),
			},
		},
	)
	ts.Len(refundableItems, 1)
	ts.True(proto.Equal(refundableItems[0].GetRefundableAmount(), money.FromFloat(3, "USD")))

	// 抵扣完了，不能退
	refundableItems = refundOrderService.buildRefundableItems(
		[]*model.OrderItem{
			{
				ID:             101,
				ItemType:       "deposit",
				CurrencyCode:   "USD",
				UnitPrice:      decimal.NewFromInt(10),
				SubTotalAmount: decimal.NewFromInt(10),
				TotalAmount:    decimal.NewFromInt(10),
			},
		},
		&model.DepositDetail{
			LatestChangeLog: &model.DepositChangeLog{
				Balance: decimal.NewFromInt(0),
			},
		},
	)
	ts.Len(refundableItems, 1)
	ts.True(proto.Equal(refundableItems[0].GetRefundableAmount(), money.FromFloat(0, "USD")))

	// 抵扣了一部分，只能退没抵扣的部分
	refundableItems = refundOrderService.buildRefundableItems(
		[]*model.OrderItem{
			{
				ID:             101,
				ItemType:       "deposit",
				CurrencyCode:   "USD",
				UnitPrice:      decimal.NewFromInt(10),
				SubTotalAmount: decimal.NewFromInt(10),
				TotalAmount:    decimal.NewFromInt(10),
			},
		},
		&model.DepositDetail{
			LatestChangeLog: &model.DepositChangeLog{
				Balance: decimal.NewFromInt(3),
			},
		},
	)
	ts.Len(refundableItems, 1)
	ts.True(proto.Equal(refundableItems[0].GetRefundableAmount(), money.FromFloat(3, "USD")))

	// 抵扣了一部分，没抵扣的部分又退了一部分，只能抵扣剩下的
	refundableItems = refundOrderService.buildRefundableItems(
		[]*model.OrderItem{
			{
				ID:             101,
				ItemType:       "deposit",
				CurrencyCode:   "USD",
				UnitPrice:      decimal.NewFromInt(10),
				SubTotalAmount: decimal.NewFromInt(10),
				TotalAmount:    decimal.NewFromInt(10),
				RefundedAmount: decimal.NewFromInt(2),
			},
		},
		&model.DepositDetail{
			LatestChangeLog: &model.DepositChangeLog{
				Balance: decimal.NewFromInt(1),
			},
		},
	)
	ts.Len(refundableItems, 1)
	ts.True(proto.Equal(refundableItems[0].GetRefundableAmount(), money.FromFloat(1, "USD")))
}

func (ts *RefundOrderPreviewTestSuite) mustDecimal(str string) decimal.Decimal {
	dec, err := decimal.NewFromString(str)
	ts.Require().NoError(err)

	return dec
}

// TODO(Perqin, P1): Bad, should use mock but how to fix import cycle?
type mockOG struct {
	orderDetail *model.OrderDetail
}

func (m *mockOG) GetDetail(_ context.Context, _ int64) (*model.OrderDetail, error) {
	return m.orderDetail, nil
}

func newMockOG(orderDetail *model.OrderDetail) *mockOG {
	return &mockOG{
		orderDetail: orderDetail,
	}
}
