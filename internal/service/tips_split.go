package service

import (
	"context"

	"github.com/samber/lo"
	"github.com/shopspring/decimal"
	"go.uber.org/zap"
	money2 "google.golang.org/genproto/googleapis/type/money"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	"github.com/MoeGolibrary/go-lib/common/proto/money"
	"github.com/MoeGolibrary/go-lib/zlog"
	orderpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/order/v1"
	ordersvcv2pb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/order/v2"
	"github.com/MoeGolibrary/moego-svc-order-v2/internal/core"
	"github.com/MoeGolibrary/moego-svc-order-v2/internal/model"
	"github.com/MoeGolibrary/moego-svc-order-v2/internal/repo"
	"github.com/MoeGolibrary/moego-svc-order-v2/internal/repo/business"
	"github.com/MoeGolibrary/moego-svc-order-v2/internal/repo/grooming"
	"github.com/MoeGolibrary/moego-svc-order-v2/internal/service/helper"
)

type ComputedResult struct {
	CollectedOrdreIDs   []int64
	CollectedTips       decimal.Decimal
	BusinessTipAmount   *money2.Money
	StaffTipsConfigs    []*orderpb.StaffTipConfig
	EditPetDetailParams []*grooming.EditPetDetailStaffCommissionItem
	EditProductItems    map[int64]int64
}

type TipsSplitService interface {
	// get current status
	GetStatus(ctx context.Context, sourceType orderpb.OrderSourceType, sourceID int64) (bool, error)
	// clear current status
	DelStatus(ctx context.Context, sourceType orderpb.OrderSourceType, sourceID int64) error
	// set current status
	SetStatus(ctx context.Context, sourceType orderpb.OrderSourceType, sourceID int64) error

	Get(ctx context.Context, sourceID int64, sourceType orderpb.OrderSourceType) (*model.TipsSplit, error)
	GetTipsSplitForLegacy(
		ctx context.Context, sourceID int64, sourceType orderpb.OrderSourceType,
	) (*model.TipsSplit, error)
	GetTipsSplitDetails(ctx context.Context, tipsSplitID int64) ([]*model.TipsSplitDetail, error)
	EditStaffAndUpsertTipsSplit(
		ctx context.Context, req *ordersvcv2pb.EditStaffAndTipsSplitRequest,
	) (*model.TipsSplit, error)

	ComputeEditStaffAndTips(ctx context.Context, req *ordersvcv2pb.EditStaffAndTipsSplitRequest) (
		*ComputedResult, error,
	)

	// ListTipsSplitDetailsBySource 专门提供给老 Report 实时查询分配详情的接口.
	ListTipsSplitDetailsBySource(
		ctx context.Context, sourceIDToType map[int64]orderpb.OrderSourceType,
	) ([]*ordersvcv2pb.ListTipsSplitDetailsBySourceResponse_TipsSplitDetail, error)
}

func NewTipsSplitService(
	orderRepo repo.OrderRepo,
	orderItemRepo repo.OrderItemRepo,
	tipsSplitRepo repo.TipsSplitRepo,
	tipsSplitDetailRepo repo.TipsSplitDetailRepo,
	legacyTipsSplitRepo repo.LegacyOrderTipsSplitDetailRepo,
	txTipsSplitRepo repo.TxTipsSplitRepo,
	tipsSplitEngine helper.TipsSplitEngine,
	businessCli business.Client,
	groomingCli grooming.Client,
	tipsSplitStatusClient repo.TipsSplitStatusClient,
) TipsSplitService {
	return &tipsSplitService{
		orderRepo:             orderRepo,
		orderItemRepo:         orderItemRepo,
		tipsSplitRepo:         tipsSplitRepo,
		tipsSplitDetailRepo:   tipsSplitDetailRepo,
		legacyTipsSplitRepo:   legacyTipsSplitRepo,
		txTipsSplit:           txTipsSplitRepo,
		tipsSplitEngine:       tipsSplitEngine,
		businessClient:        businessCli,
		groomingClient:        groomingCli,
		tipsSplitStatusClient: tipsSplitStatusClient,
	}
}

type tipsSplitService struct {
	orderRepo             repo.OrderRepo
	orderItemRepo         repo.OrderItemRepo
	tipsSplitRepo         repo.TipsSplitRepo
	tipsSplitDetailRepo   repo.TipsSplitDetailRepo
	legacyTipsSplitRepo   repo.LegacyOrderTipsSplitDetailRepo
	txTipsSplit           repo.TxTipsSplitRepo
	tipsSplitEngine       helper.TipsSplitEngine
	businessClient        business.Client
	groomingClient        grooming.Client
	tipsSplitStatusClient repo.TipsSplitStatusClient
}

// DelStatus implements TipsSplitService.
func (svc *tipsSplitService) DelStatus(ctx context.Context, sourceType orderpb.OrderSourceType, sourceID int64) error {
	return svc.tipsSplitStatusClient.Del(ctx, sourceType, sourceID)
}

// GetStatus implements TipsSplitService.
func (svc *tipsSplitService) GetStatus(
	ctx context.Context,
	sourceType orderpb.OrderSourceType,
	sourceID int64,
) (bool, error) {
	return svc.tipsSplitStatusClient.Get(ctx, sourceType, sourceID)
}

// SetStatus implements TipsSplitService.
func (svc *tipsSplitService) SetStatus(ctx context.Context, sourceType orderpb.OrderSourceType, sourceID int64) error {
	return svc.tipsSplitStatusClient.Set(ctx, sourceType, sourceID)
}

// ComputeEditStaffAndTips only calculate.
func (svc *tipsSplitService) ComputeEditStaffAndTips(
	ctx context.Context,
	req *ordersvcv2pb.EditStaffAndTipsSplitRequest,
) (*ComputedResult, error) {
	// change staff temporally
	apptOrders, err := svc.orderRepo.ListByAppointment(ctx, req.SourceId)
	if err != nil {
		return nil, err
	}

	completedOrders := lo.Filter(
		apptOrders,
		func(order *model.Order, _ int) bool { return order.Status == orderpb.OrderStatus_COMPLETED },
	)

	if len(completedOrders) == 0 {
		return nil, status.Error(codes.NotFound, "no completed orders")
	}

	currencyCode := completedOrders[0].CurrencyCode

	totalCollectedTips := decimal.Zero
	completedOrderIDs := make([]int64, 0, len(completedOrders))
	// clollected all tips
	for _, currentOrder := range completedOrders {
		totalCollectedTips = totalCollectedTips.Add(currentOrder.GetTipsAmount())
		completedOrderIDs = append(completedOrderIDs, currentOrder.ID)
	}

	// get appt pet details
	// TODO(yunxiang): 迁移到新接口 PreviewOrderLineItems
	petDetails, err := svc.groomingClient.ListPetDetailsByGroomingID(ctx, req.BusinessId, req.SourceId)
	if err != nil {
		return nil, err
	}

	completedOrderItems, err := svc.orderItemRepo.ListItemsByOrderIDs(ctx, completedOrderIDs)
	if err != nil {
		return nil, err
	}

	// 过滤删除
	completedOrderItems = lo.Filter(
		completedOrderItems, func(it *model.OrderItem, _ int) bool {
			return !it.IsDeleted
		},
	)

	// filter petDetail by completed order service items.
	filteredPetDetails := svc.filterPetDetailsByOrderItems(
		petDetails,
		lo.Filter(
			completedOrderItems,
			func(item *model.OrderItem, _ int) bool { return item.IsService() },
		),
	)

	// check tips split config param
	if req.GetTipsSplitConfig() == nil ||
		req.GetTipsSplitConfig().SplitMethod == orderpb.SplitTipsMethod_SPLIT_TIPS_METHOD_UNSPECIFIED {
		bizPayrollSetting, bizErr := svc.businessClient.GetPayrollSetting(ctx, req.BusinessId)
		if bizErr != nil {
			return nil, bizErr
		}

		splitConfig := &orderpb.TipsSplitModel_TipsSplitConfig{
			SplitMethod: orderpb.SplitTipsMethod(bizPayrollSetting.SplitTipsMethod),
		}

		if !bizPayrollSetting.NeedSplitTipForBusiness() {
			splitConfig.BusinessTipAmount = money.FromDecimal(decimal.Zero, currencyCode)
		}

		req.TipsSplitConfig = splitConfig
	}

	// 未指定 BusinessTipAmount，需要自动计算.
	if req.GetTipsSplitConfig().GetBusinessTipAmount() == nil {
		businessTips, _ := svc.splitTipBetweenBusinessAndStaff(
			totalCollectedTips, completedOrderItems, filteredPetDetails,
		)
		req.TipsSplitConfig.BusinessTipAmount = money.FromDecimal(businessTips, currencyCode)
	}

	// change staff for each pet order line item， currently only support one staff
	editPetDetailParams := make([]*grooming.EditPetDetailStaffCommissionItem, 0, len(req.EditStaffs))
	editProductItems := make(map[int64]int64)

	if req.TipsSplitConfig.SplitMethod == orderpb.SplitTipsMethod_SPLIT_TIPS_METHOD_BY_EQUALLY {
		// 平分时需要额外查询是否包含 product item
		existingProdItems := lo.Filter(
			completedOrderItems,
			func(item *model.OrderItem, _ int) bool { return item.IsProduct() },
		)

		for _, orderItem := range existingProdItems {
			editProductItems[orderItem.ID] = orderItem.StaffID
		}
	}

	if len(req.EditStaffs) > 0 {
		zlog.Debug(ctx, "edit staff for each pet order line item", zap.Any("editStaffs", req.EditStaffs[0]))
		// collected all items id into list
		orderItemIDs := make([]int64, 0, len(completedOrders))
		// map <orderItemID, editOrderItemDef>
		itemChangedMap := make(map[int64]*orderpb.EditStaffOrderItemDef, len(completedOrders))

		for _, editStaff := range req.EditStaffs {
			for _, editOrderItemDef := range editStaff.EditStaffOrderItemDefs {
				if len(editOrderItemDef.Staffs) > 1 {
					return nil, status.Error(codes.InvalidArgument, "only one staff can be assigned to one order item")
				}

				orderItemIDs = append(orderItemIDs, editOrderItemDef.OrderItemId)
				itemChangedMap[editOrderItemDef.OrderItemId] = editOrderItemDef
			}
		}
		// change staff for each pet order line item and pet detail
		orderItems, getErr := svc.orderItemRepo.BatchGet(ctx, orderItemIDs)
		if getErr != nil {
			return nil, getErr
		}
		// 根据 order item 找到对应的 pet detail，更新其 staff id
		// object id + pet id 可以作为唯一键
		// TODO(yunxiang)： 基于 BD 之前在 Legacy 实际的玩法，这个已经不能做 UK 了，需要修复！
		for _, orderItem := range orderItems {
			editOrderItemDef, ok := itemChangedMap[orderItem.ID]
			if !ok {
				zlog.Warn(ctx, "edit staff item not found", zap.Int64("orderItemID", orderItem.ID))
				continue
			}

			if orderItem.IsProduct() {
				// Product 的 Staff 维护在订单，不在 Appointment。
				// 所以这里直接更新 order item，其他的走 Appointment 绕一圈来更新
				editProductItems[orderItem.ID] = 0
				if len(editOrderItemDef.Staffs) > 0 {
					editProductItems[orderItem.ID] = editOrderItemDef.Staffs[0].StaffId
				}

				continue
			}

			if orderItem.IsService() && len(editOrderItemDef.Staffs) == 0 {
				return nil, status.Error(codes.InvalidArgument, "service item must have at least one staff")
			}

			// 找到对应的 pet detail
			foundPetDetail, matchErr := helper.FindPetDetailByOrderItem(petDetails, orderItem)
			if matchErr != nil {
				return nil, matchErr
			}

			// TODO(yunxiang): 支持 Edit Multi-Staff.
			foundPetDetail.StaffID = editOrderItemDef.Staffs[0].StaffId
			// convert operation list
			operationItemList := make(
				[]*grooming.EditPetDetailStaffCommissionOperationItem,
				0,
				len(foundPetDetail.OperationList),
			)
			for _, operation := range foundPetDetail.OperationList {
				operationItemList = append(
					operationItemList, &grooming.EditPetDetailStaffCommissionOperationItem{
						StaffID:       operation.StaffID,
						Ratio:         operation.PriceRatio,
						Duration:      operation.Duration,
						OperationName: operation.OperationName,
					},
				)
			}
			// construct edit pet detail staff param
			editPetDetailParams = append(
				editPetDetailParams, &grooming.EditPetDetailStaffCommissionItem{
					OrderItemType:     orderItem.ItemType,
					BusinessID:        req.BusinessId,
					CompanyID:         req.CompanyId,
					StaffID:           foundPetDetail.StaffID,
					PetID:             foundPetDetail.PetID,
					ServiceID:         foundPetDetail.ServiceID,
					OrderItemID:       orderItem.ID,
					PetDetailID:       foundPetDetail.ID,
					OperationItemList: operationItemList,
				},
			)
		}
	}

	tipsSplitResult, err := svc.tipsSplitEngine.CalculateTipsSplit(
		filteredPetDetails,
		req.GetTipsSplitConfig(),
		totalCollectedTips,
		currencyCode,
		lo.Filter(
			lo.Values(editProductItems), func(value int64, _ int) bool {
				return value != 0
			},
		),
	)
	if err != nil {
		return nil, err
	}

	result := &ComputedResult{
		CollectedOrdreIDs:   completedOrderIDs,
		CollectedTips:       totalCollectedTips,
		BusinessTipAmount:   tipsSplitResult.BusinessTipAmount,
		StaffTipsConfigs:    tipsSplitResult.StaffConfigs,
		EditPetDetailParams: editPetDetailParams,
		EditProductItems:    editProductItems,
	}

	return result, nil
}

func (svc *tipsSplitService) filterPetDetailsByOrderItems(
	petDetails []*grooming.PetDetailDTO,
	orderItems []*model.OrderItem,
) []*grooming.PetDetailDTO {
	// 过滤掉不在 orderItems 中的 pet detail
	filteredPetDetails := make([]*grooming.PetDetailDTO, 0, len(petDetails))

	for _, petDetail := range petDetails {
		for _, orderItem := range orderItems {
			// V4 引入了精确匹配
			if orderItem.ExternalUUID != "" {
				if orderItem.ExternalUUID == petDetail.ExternalUUID {
					filteredPetDetails = append(filteredPetDetails, petDetail)
					break
				}

				continue
			}

			if orderItem.PetID == petDetail.PetID && orderItem.ObjectID == petDetail.ServiceID {
				filteredPetDetails = append(filteredPetDetails, petDetail)
				break
			}
		}
	}

	return filteredPetDetails
}

func (svc *tipsSplitService) splitTipBetweenBusinessAndStaff(
	tipAmount decimal.Decimal,
	items []*model.OrderItem, petDetails []*grooming.PetDetailDTO,
) (businessTips, staffTips decimal.Decimal) {
	// 此处传入的 petDetails 已经与 item 匹配过了，所以这里直接用 PetDetail 区分出有 Staff 的 Service 的 ID.
	// 不再进行二次匹配.
	serviceSubTotal := decimal.Zero
	serviceSubTotalWithStaff := decimal.Zero

	// 此处参考 order 的 tipsBasedAmount 算法， 只计入 Service 和 Evaluation.
	serviceItems := lo.Filter(
		items,
		func(item *model.OrderItem, _ int) bool { return item.IsService() || item.IsEvaluation() },
	)

	withStaffServiceIDs := make(map[int64]bool, len(petDetails))

	for _, pd := range petDetails {
		if helper.IsPetDetailAssignedStaff(pd) {
			withStaffServiceIDs[pd.ServiceID] = true
		}
	}

	for _, it := range serviceItems {
		serviceSubTotal = serviceSubTotal.Add(it.GetSubTotalAmount())

		if withStaffServiceIDs[it.ObjectID] {
			serviceSubTotalWithStaff = serviceSubTotalWithStaff.Add(it.GetSubTotalAmount())
		}
	}

	// 用包含了 Staff 的 Service 的 Subtotal 占比计算 Tips 占比.
	staffTipsRate := decimal.Zero

	if serviceSubTotal.IsPositive() {
		staffTipsRate = core.RoundRate(serviceSubTotalWithStaff.Div(serviceSubTotal))
	}

	staffTips = core.RoundPreTaxAmount(tipAmount.Mul(staffTipsRate))
	if staffTips.GreaterThan(tipAmount) {
		// 以防万一，如果计算出来的 Tips 超过了总的 Tips，则直接使用总的 Tips.
		staffTips = tipAmount
	}

	businessTips = tipAmount.Sub(staffTips)

	return businessTips, staffTips
}

// EditStaffAndUpsertTipsSplit implements TipsSplitService.
func (svc *tipsSplitService) EditStaffAndUpsertTipsSplit(
	ctx context.Context, req *ordersvcv2pb.EditStaffAndTipsSplitRequest,
) (*model.TipsSplit, error) {
	if req.SourceType != orderpb.OrderSourceType_APPOINTMENT {
		return nil, status.Error(codes.InvalidArgument, "not supported order source type")
	}

	orders, err := svc.orderRepo.ListByAppointment(ctx, req.SourceId)
	if err != nil {
		return nil, err
	}

	if len(orders) == 0 {
		return nil, status.Error(codes.NotFound, "no appointment related orders")
	}

	if orders[0].OrderVersion.Lt(model.OrderVersionCloseOrder) {
		return nil, status.Error(codes.FailedPrecondition, "not support legacy order")
	}

	if len(req.EditStaffs) > 0 {
		// call edit staff commission like api v3
		zlog.Debug(ctx, "edit staff commission", zap.Any("editStaffs", req.EditStaffs))
	}

	computedResult, err := svc.ComputeEditStaffAndTips(ctx, req)
	if err != nil {
		return nil, err
	}

	req.TipsSplitConfig.StaffConfigs = computedResult.StaffTipsConfigs

	zlog.Debug(ctx, "computed staff split detail", zap.Any("staffSplitDetail", computedResult.StaffTipsConfigs))

	err = svc.txTipsSplit.Tx(
		func(tx repo.TipsSplitTx) error {
			// 1. 先检查是否存在，如果存在则更新，否则创建
			existModel, findErr := tx.TipsSplit().GetBySourceIDAndType(ctx, req.SourceId, req.SourceType)

			switch {
			case findErr == nil:
				// 有 appt 层面分配记录 // 更新
				existModel.SplitConfig = req.TipsSplitConfig
				existModel.CollectedOrderIDs = computedResult.CollectedOrdreIDs
				existModel.CollectedTips = computedResult.CollectedTips
				existModel.ApplyBy = req.ApplyBy

				if _, updateErr := tx.TipsSplit().Update(ctx, existModel); updateErr != nil {
					return updateErr
				}
			case repo.IsNotFound(findErr):
				// 无 appt 层面分配记录, collected order level tips split records // create new tips split record
				newModel := &model.TipsSplit{
					BusinessID:        req.BusinessId,
					CompanyID:         req.CompanyId,
					ApplyBy:           req.ApplyBy,
					SourceID:          req.SourceId,
					SourceType:        req.SourceType,
					CollectedTips:     computedResult.CollectedTips,
					CollectedOrderIDs: computedResult.CollectedOrdreIDs,
					SplitConfig:       req.TipsSplitConfig,
					CurrencyCode:      orders[0].CurrencyCode,
				}
				if createErr := tx.TipsSplit().Create(ctx, newModel); createErr != nil {
					return createErr
				}

				existModel = newModel
				// 删除掉 order 级别的 split repord
				if findErr = tx.TipsSplit().DeleteLegacyTipSplitRecords(ctx, newModel.ID); findErr != nil {
					zlog.Debug(ctx, "failed to delete order level tips split record", zap.Error(findErr))
				}
			default:
				// 其他异常
				return findErr
			}

			// 更新 product order item
			if len(computedResult.EditProductItems) > 0 {
				for orderItemID, staffID := range computedResult.EditProductItems {
					if _, updateErr := tx.OrderItemRepo().UpdateStaffID(ctx, orderItemID, staffID); updateErr != nil {
						zlog.Debug(ctx, "failed to update product order item staff", zap.Error(updateErr))
						return updateErr
					}
				}
			}
			// 更新 tips split detail
			findErr = tx.TipsSplitDetailRepo().Upsert(ctx, existModel.ID, existModel.ToSplitDetailModels())
			if findErr != nil {
				return findErr
			}

			return nil
		},
	)
	if err != nil {
		return nil, err
	}
	// 更新外部 grooming staff: 同步 editStaff 到 server grooming
	if len(computedResult.EditPetDetailParams) > 0 {
		err = svc.groomingClient.UpdatePetDetailStaff(ctx, computedResult.EditPetDetailParams)
		if err != nil {
			zlog.Error(ctx, "failed to update pet detail staff", zap.Error(err))
			return nil, err
		}
	}
	// return updated model
	return svc.tipsSplitRepo.GetBySourceIDAndType(ctx, req.SourceId, req.SourceType)
}

// Get implements TipsSplitService.
func (svc *tipsSplitService) Get(ctx context.Context, sourceID int64, sourceType orderpb.OrderSourceType) (
	*model.TipsSplit, error,
) {
	tipsSplitModel, err := svc.tipsSplitRepo.GetBySourceIDAndType(ctx, sourceID, sourceType)

	switch {
	case err == nil:
		// 有 appt 层面分配记录
		return tipsSplitModel, nil
	case repo.IsNotFound(err):
		// 无 appt 层面分配记录, collected order level tips split records
		return svc.GetTipsSplitForLegacy(ctx, sourceID, sourceType)
	default:
		// 其他异常
		return nil, err
	}
}

// GetTipsSplitForLegacy implements TipsSplitService.
func (svc *tipsSplitService) GetTipsSplitForLegacy(
	ctx context.Context, sourceID int64, sourceType orderpb.OrderSourceType,
) (
	*model.TipsSplit, error,
) {
	orders, err := svc.orderRepo.ListBySource(ctx, sourceID, sourceType)
	if err != nil {
		zlog.Error(ctx, "failed to get related orders", zap.Error(err))
		return nil, err
	}

	if len(orders) == 0 {
		return nil, status.Error(codes.NotFound, "no related orders for target source type")
	}

	firstOrder := orders[0]

	apptOrders, err := svc.orderRepo.ListByAppointment(ctx, sourceID)
	if err != nil {
		return nil, err
	}

	completedOrders := lo.Filter(
		apptOrders,
		func(order *model.Order, _ int) bool { return order.Status == orderpb.OrderStatus_COMPLETED },
	)

	if len(completedOrders) == 0 {
		return nil, status.Error(codes.NotFound, "no related completed orders")
	}
	// 对所有的 order 进行汇总
	totalCollectedTips := decimal.Zero
	collectedOrdreIDs := make([]int64, 0, len(completedOrders))

	for _, order := range completedOrders {
		totalCollectedTips = totalCollectedTips.Add(order.GetTipsAmount())
		collectedOrdreIDs = append(collectedOrdreIDs, order.ID)
	}

	staffSplitDetailMap, businessTipAmount, err := svc.groomingClient.GetTipsSplitDetailsMap(
		ctx, collectedOrdreIDs, firstOrder.CurrencyCode,
	)
	if err != nil {
		zlog.Error(ctx, "failed to get legacy staff split details", zap.Error(err))
		return nil, err
	}

	splitTipsMethod := orderpb.SplitTipsMethod_SPLIT_TIPS_METHOD_BY_FIXED_AMOUNT

	if len(completedOrders) == 1 {
		// 没有 tips split record 定制记录，才按商家默认分配规则下发
		tipsSplitRecords, listErr := svc.tipsSplitRepo.ListLegacyTipSplitRecordsByOrderIDs(
			ctx,
			completedOrders[0].BusinessID,
			collectedOrdreIDs,
		)
		if listErr != nil {
			return nil, listErr
		}

		if len(tipsSplitRecords) == 0 {
			// 没有定制记录，则按商家默认分配规则下发
			bizPayrollSetting, bizErr := svc.businessClient.GetPayrollSetting(ctx, completedOrders[0].BusinessID)
			if bizErr != nil {
				return nil, bizErr
			}

			splitTipsMethod = orderpb.SplitTipsMethod(bizPayrollSetting.SplitTipsMethod)
		} else {
			// 有定制记录，则按定制记录下发
			splitTipsMethod = orderpb.SplitTipsMethod(tipsSplitRecords[0].SplitMethod)
		}
	}

	tipsSplitModel := &model.TipsSplit{
		BusinessID:        firstOrder.BusinessID,
		CompanyID:         firstOrder.CompanyID,
		SourceID:          sourceID,
		SourceType:        sourceType,
		CollectedTips:     totalCollectedTips,
		CollectedOrderIDs: collectedOrdreIDs,
		SplitConfig: &orderpb.TipsSplitModel_TipsSplitConfig{
			SplitMethod:       splitTipsMethod,
			StaffConfigs:      lo.Values(staffSplitDetailMap),
			BusinessTipAmount: money.FromDecimal(businessTipAmount, firstOrder.CurrencyCode),
		},
	}

	return tipsSplitModel, nil
}

// GetTipsSplitDetails implements TipsSplitService.
func (svc *tipsSplitService) GetTipsSplitDetails(ctx context.Context, tipsSplitID int64) (
	[]*model.TipsSplitDetail, error,
) {
	return svc.tipsSplitDetailRepo.ListByIDs(ctx, tipsSplitID)
}

func (svc *tipsSplitService) ListTipsSplitDetailsBySource(
	ctx context.Context, sourceIDToType map[int64]orderpb.OrderSourceType,
) ([]*ordersvcv2pb.ListTipsSplitDetailsBySourceResponse_TipsSplitDetail, error) {
	splits, err := svc.tipsSplitRepo.ListBySources(ctx, sourceIDToType)
	if err != nil {
		return nil, err
	}

	splitIDs := lo.Map(
		splits,
		func(it *model.TipsSplit, _ int) int64 { return it.ID },
	)

	allDetails, err := svc.tipsSplitDetailRepo.ListByIDs(ctx, splitIDs...)
	if err != nil {
		return nil, status.Errorf(codes.Internal, "failed to get tips split details: %v", err)
	}

	idToDetails := lo.GroupBy(
		allDetails,
		func(it *model.TipsSplitDetail) int64 { return it.TipsSplitID },
	)

	splitDetails := make([]*ordersvcv2pb.ListTipsSplitDetailsBySourceResponse_TipsSplitDetail, 0, len(splits))
	for _, split := range splits {
		splitDetails = append(
			splitDetails, &ordersvcv2pb.ListTipsSplitDetailsBySourceResponse_TipsSplitDetail{
				TipsSplit: split.ToPB(),
				TipsSplitDetails: lo.Map(
					idToDetails[split.ID],
					func(it *model.TipsSplitDetail, _ int) *orderpb.TipsSplitDetailModel { return it.ToPB() },
				),
			},
		)
	}

	return splitDetails, nil
}
