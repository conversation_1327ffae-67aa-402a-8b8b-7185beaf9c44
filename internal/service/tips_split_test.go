package service_test

import (
	"context"
	"testing"

	"github.com/shopspring/decimal"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/suite"

	orderpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/order/v1"
	mocks "github.com/MoeGolibrary/moego-svc-order-v2/internal/mocks/repo"
	businessMocks "github.com/MoeGolibrary/moego-svc-order-v2/internal/mocks/repo/business"
	groomingMocks "github.com/MoeGolibrary/moego-svc-order-v2/internal/mocks/repo/grooming"
	helperMocks "github.com/MoeGolibrary/moego-svc-order-v2/internal/mocks/service/helper"
	"github.com/MoeGolibrary/moego-svc-order-v2/internal/model"
	"github.com/MoeGolibrary/moego-svc-order-v2/internal/service"
)

type TipsSplitTestSuite struct {
	suite.Suite

	TipsSplitService      service.TipsSplitService
	orderRepo             *mocks.OrderRepo
	orderItemRepo         *mocks.OrderItemRepo
	tipsSplitRepo         *mocks.TipsSplitRepo
	tipsSplitDetailRepo   *mocks.TipsSplitDetailRepo
	legacyTipsSplitRepo   *mocks.LegacyOrderTipsSplitDetailRepo
	txTipsSplitRepo       *mocks.TxTipsSplitRepo
	tipsSplitEngine       *helperMocks.TipsSplitEngine
	businessCli           *businessMocks.Client
	groomingCli           *groomingMocks.Client
	tipsSplitStatusClient *mocks.TipsSplitStatusClient
}

func TestTipsSplit(t *testing.T) {
	suite.Run(t, new(TipsSplitTestSuite))
}

func (ts *TipsSplitTestSuite) SetupTest() {
	ts.orderRepo = new(mocks.OrderRepo)
	ts.orderItemRepo = new(mocks.OrderItemRepo)
	ts.tipsSplitRepo = new(mocks.TipsSplitRepo)
	ts.tipsSplitDetailRepo = new(mocks.TipsSplitDetailRepo)
	ts.legacyTipsSplitRepo = new(mocks.LegacyOrderTipsSplitDetailRepo)
	ts.txTipsSplitRepo = new(mocks.TxTipsSplitRepo)
	ts.tipsSplitEngine = new(helperMocks.TipsSplitEngine)
	ts.businessCli = new(businessMocks.Client)
	ts.groomingCli = new(groomingMocks.Client)
	ts.tipsSplitStatusClient = new(mocks.TipsSplitStatusClient)
	ts.TipsSplitService = service.NewTipsSplitService(
		ts.orderRepo,
		ts.orderItemRepo,
		ts.tipsSplitRepo,
		ts.tipsSplitDetailRepo,
		ts.legacyTipsSplitRepo,
		ts.txTipsSplitRepo,
		ts.tipsSplitEngine,
		ts.businessCli,
		ts.groomingCli,
		ts.tipsSplitStatusClient,
	)
}

func (ts *TipsSplitTestSuite) TestGetTipsSplitForLegacy_UseTipRecordSplitMethod_WhenOnlyOneCompletedOrder() {
	mockOrders := []*model.Order{
		{
			ID:         1,
			BusinessID: 100000,
			Status:     orderpb.OrderStatus_COMPLETED,
			TipsAmount: decimal.NewFromFloat(10.0),
			SourceType: "appointment",
			// Add other necessary fields
		},
	}
	mockTipsSplitRecord := &model.OrderTipsSplitRecord{
		ID:          1,
		OrderID:     1,
		SplitMethod: 2,
	}

	ts.orderRepo.EXPECT().
		ListBySource(mock.Anything, mock.Anything, mock.Anything).
		Return(mockOrders, nil)
	ts.orderRepo.EXPECT().ListByAppointment(mock.Anything, mock.Anything).
		Return(mockOrders, nil)
	ts.groomingCli.EXPECT().
		GetTipsSplitDetailsMap(mock.Anything, mock.Anything, mock.Anything).
		Return(nil, decimal.Zero, nil)
	ts.tipsSplitRepo.EXPECT().
		ListLegacyTipSplitRecordsByOrderIDs(mock.Anything, mock.Anything, mock.Anything).
		Return([]*model.OrderTipsSplitRecord{mockTipsSplitRecord}, nil)

	actualMod, _ := ts.TipsSplitService.GetTipsSplitForLegacy(
		context.Background(),
		1,
		orderpb.OrderSourceType_APPOINTMENT,
	)

	ts.Equal(orderpb.SplitTipsMethod_SPLIT_TIPS_METHOD_BY_EQUALLY, actualMod.SplitConfig.SplitMethod)
}
