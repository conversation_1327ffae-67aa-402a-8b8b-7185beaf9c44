//go:build wireinject
// +build wireinject

package wire

import (
	"context"

	"github.com/google/wire"
	"github.com/growthbook/growthbook-golang"

	"github.com/MoeGolibrary/go-lib/gorm"
	"github.com/MoeGolibrary/go-lib/grpc"
	"github.com/MoeGolibrary/go-lib/redis"
	"github.com/MoeGolibrary/go-lib/server"
	marketingsvcpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/marketing/v1"
	ordersvcpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/order/v1"
	ordersvcv2pb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/order/v2"
	temp_orderspb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/temp_order/v1"

	"github.com/MoeGolibrary/moego-svc-order-v2/config"
	"github.com/MoeGolibrary/moego-svc-order-v2/internal/controller"
	"github.com/MoeGolibrary/moego-svc-order-v2/internal/repo"
	"github.com/MoeGolibrary/moego-svc-order-v2/internal/repo/appointment"
	org "github.com/MoeGolibrary/moego-svc-order-v2/internal/repo/business"
	depositrulerepo "github.com/MoeGolibrary/moego-svc-order-v2/internal/repo/depositrule"
	"github.com/MoeGolibrary/moego-svc-order-v2/internal/repo/file"
	grooming "github.com/MoeGolibrary/moego-svc-order-v2/internal/repo/grooming"
	"github.com/MoeGolibrary/moego-svc-order-v2/internal/repo/message"
	"github.com/MoeGolibrary/moego-svc-order-v2/internal/repo/promotion"
	"github.com/MoeGolibrary/moego-svc-order-v2/internal/repo/subscription"
	"github.com/MoeGolibrary/moego-svc-order-v2/internal/service"
	"github.com/MoeGolibrary/moego-svc-order-v2/internal/service/handler"
	"github.com/MoeGolibrary/moego-svc-order-v2/internal/service/helper"
)

func NewServer() server.Server {
	wire.Build(
		helper.NewTipsSplitEngine,
		org.NewBusinessClient,
		message.NewMessageClient,
		grooming.NewGroomingClient,
		appointment.New,

		controller.NewOrderServer,
		controller.NewOrderServerV2,
		controller.NewExportServer,
		controller.NewTipsSplitServiceServer,
		controller.NewAssignItemAmountServer,
		controller.NewMigrationServer,

		handler.NewPaymentEventHandler,
		controller.NewDepositRuleServer,
		controller.NewOrderTaskServer,

		service.NewOrderService,
		service.NewTipsSplitService,
		service.NewRefundOrderService,
		service.NewOrderPaymentService,
		service.NewDepositOrderService,
		service.NewItemAssignService,
		service.NewDepositRulesService,
		service.NewPromotionService,
		service.NewMigrationService,

		repo.NewOrderRepo,
		repo.NewTipsSplitRepo,
		repo.NewTipsSplitDetailRepo,
		repo.NewLegacyOrderTipsSplitDetailRepo,
		repo.NewOrderItemRepo,
		repo.NewOrderPaymentRepo,
		repo.NewOrderLineDiscountRepo,
		repo.NewRefundOrderRepo,
		repo.NewRefundOrderItemRepo,
		repo.NewRefundOrderPaymentRepo,
		repo.NewDepositChangeLogRepoRepo,
		repo.NewTXRepo,
		repo.NewTxTipsSplitRepo,
		repo.NewOrderPromotionRepo,
		repo.NewOrderPromotionItemRepo,
		repo.NewOrderItemAssignedAmountRepo,
		file.New,
		depositrulerepo.NewDepositRuleRepo,
		depositrulerepo.NewBusinessRepo,
		depositrulerepo.NewCustomerRepo,

		repo.NewPaymentClient,
		repo.NewUserFlagClient,
		repo.NewTipsSplitStatusClient,
		depositrulerepo.NewDepositRuleRepo,
		depositrulerepo.NewBusinessRepo,
		depositrulerepo.NewCustomerRepo,
		promotion.New,
		subscription.New,

		newServer,
		newOrderDB,

		newRedisClient,

		newDiscountClient,

		newGrowthBookClient,

		// 为了兼容老版本数据才直接访问 Payment 相关数据
		// 后续无老版本订单产生后移除
		repo.NewLegacyPaymentRepo,
		repo.NewLegacyRefundRepo,
		newPaymentDB,
	)

	return server.Server(nil)
}

func newServer(
	serviceServer ordersvcpb.OrderServiceServer,
	serviceServerV2 ordersvcv2pb.OrderServiceServer,
	exportServer ordersvcv2pb.ExportServiceServer,
	tipsSplitServer ordersvcv2pb.SplitTipsServiceServer,
	assignItemAmountServer temp_orderspb.AssignItemAmountServiceServer,
	depositRuleServer ordersvcv2pb.DepositRuleServiceServer,
	orderTaskServer ordersvcv2pb.OrderTaskServiceServer,
	migrationServer ordersvcv2pb.MigrationServiceServer,
) server.Server {
	s := server.NewDefaultServer()
	ordersvcpb.RegisterOrderServiceServer(s, serviceServer)
	ordersvcv2pb.RegisterOrderServiceServer(s, serviceServerV2)
	ordersvcv2pb.RegisterExportServiceServer(s, exportServer)
	ordersvcv2pb.RegisterSplitTipsServiceServer(s, tipsSplitServer)
	ordersvcv2pb.RegisterDepositRuleServiceServer(s, depositRuleServer)
	ordersvcv2pb.RegisterOrderTaskServiceServer(s, orderTaskServer)
	ordersvcv2pb.RegisterMigrationServiceServer(s, migrationServer)

	temp_orderspb.RegisterAssignItemAmountServiceServer(s, assignItemAmountServer)

	return s
}

func newOrderDB() *gorm.DB {
	return gorm.Must(gorm.OpenPostgres(config.OrderDSN(), nil))
}

func newPaymentDB() *repo.PaymentDB {
	return (*repo.PaymentDB)(gorm.Must(gorm.OpenMySQL(config.PaymentDSN(), nil)))
}

func newRedisClient() redis.UniversalClient { return redis.NewClient(config.Redis()) }

func newDiscountClient() marketingsvcpb.DiscountCodeServiceClient {
	return grpc.NewClient("moego-svc-marketing:9090", marketingsvcpb.NewDiscountCodeServiceClient)
}

func newGrowthBookClient() *growthbook.Client {
	gb, err := growthbook.NewClient(context.Background(), config.GrowthBook()...)
	if err != nil {
		panic(err)
	}
	return gb
}
